pipeline:
  name: pr-style-checker
  identifier: pr_style_checker
  projectIdentifier: Rising_Wave_Cloud
  orgIdentifier: default
  tags: {}
  properties:
    ci:
      codebase:
        connectorRef: RisingWave_Github
        repoName: risingwave-cloud
        build: <+input>
  stages:
    - stage:
        name: style-checker
        identifier: style_checker
        type: CI
        spec:
          cloneCodebase: true
          platform:
            os: Linux
            arch: Amd64
          runtime:
            type: Cloud
            spec: {}
          execution:
            steps:
              - step:
                  name: docker-login
                  identifier: dockerlogin
                  template:
                    templateRef: dockerlogin
                    versionLabel: V1
              - step:
                  type: Run
                  name: checkout
                  identifier: checkout
                  spec:
                    shell: Bash
                    command: |-
                      git submodule init
                      git config submodule.CloudAgent.url https://<+secrets.getValue("github_ci_access_token")>@github.com/risingwavelabs/CloudAgent.git
                      git submodule update --recursive
              - step:
                  type: Action
                  name: Set up Golang
                  identifier: set_up_golang
                  spec:
                    uses: actions/setup-go@v5
                    with:
                      go-version: "1.24"
              - step:
                  type: Run
                  name: Files filter
                  identifier: filter
                  spec:
                    shell: Bash
                    command: |-
                      changed_files=""
                      if [[ <+trigger.prNumber> != null ]]; then
                        changed_files=$(curl -s -H "Authorization: token <+secrets.getValue("github_ci_access_token")>" \
                                  "https://api.github.com/repos/risingwavelabs/risingwave-cloud/pulls/<+trigger.prNumber>/files" | jq -r '.[].filename')
                      else
                        changed_files=$(curl -s -H "Authorization: token <+secrets.getValue("github_ci_access_token")>" \
                                  "https://api.github.com/repos/risingwavelabs/risingwave-cloud/compare/main...<+codebase.branch>" | jq -r '.files[].filename')
                      fi
                      only_portal_changed=true
                      num_changed_files=$(echo "$changed_files" | wc -l)
                      echo "changed $num_changed_files files: ${changed_files}"

                      for file in $changed_files; do
                        if [[ $file != portal/* ]]; then
                          only_portal_changed=false
                        fi

                        # Check if file is in /api directory or its subdirectories
                        if [[ $file == api/* ]]; then
                          echo "pr has api files: $file"
                          export api="true"
                        fi

                        file_extension="${file##*.}"
                        case $file_extension in
                              "sh")
                                  echo "pr has sh files: $file"
                                  export sh="true"
                                  ;;
                              "tf")
                                  echo "pr has tf files: $file"
                                  export tf="true"
                                  ;;
                              "go" | "mod" | "sum")
                                  echo "pr has go files: $file"
                                  export go="true"
                                  ;;
                              "jsonnet" | "libsonnet")
                                  echo "pr has jsonnet files: $file"
                                  export jsonnet="true"
                                  ;;
                        esac
                      done

                      if [[ "$only_portal_changed" == "true" ]]; then
                        # if all file changes are from portal, we could skip codegen
                        echo "pr only changed portal files, skipping codegen..."
                        export shouldCodegen="false"
                      else
                        echo "pr changed non-portal files, running codegen..."
                        export shouldCodegen="true"
                      fi
                    outputVariables:
                      - name: sh
                      - name: tf
                      - name: go
                      - name: jsonnet
                      - name: api
                      - name: shouldCodegen
              - parallel:
                  - step:
                      type: Action
                      name: Set up terraform
                      identifier: set_up_terraform
                      spec:
                        uses: hashicorp/setup-terraform@v3
                      when:
                        stageStatus: All
                        condition: <+execution.steps.filter.output.outputVariables.tf> == "true"
                  - step:
                      type: Run
                      name: Run api-checker
                      identifier: api_checker
                      spec:
                        shell: Bash
                        command: make api-fmt-ci
                        envVariables:
                          GITHUB_TOKEN: <+secrets.getValue("github_ci_access_token")>
                          PR_NUMNER: <+trigger.prNumber>
                      when:
                        stageStatus: All
                        condition: <+execution.steps.filter.output.outputVariables.api> == "true"
              - parallel:
                  - step:
                      type: Action
                      name: Run the sh-checker
                      identifier: sh_checker
                      spec:
                        uses: luizm/action-sh-checker@master
                        with:
                          sh_checker_comment: true
                          sh_checker_exclude: .terraform CloudAgent
                        env:
                          GITHUB_TOKEN: <+secrets.getValue("github_ci_access_token")>
                          SHFMT_OPTS: "-s"
                      when:
                        stageStatus: All
                        condition: <+execution.steps.filter.output.outputVariables.sh> == "true"
                  - step:
                      type: Run
                      name: Style check
                      identifier: style_check
                      spec:
                        shell: Bash
                        command: make lint-check-ci
                        envVariables:
                          GITHUB_TOKEN: <+secrets.getValue("github_ci_access_token")>
                          PR_NUMNER: <+trigger.prNumber>
                      when:
                        stageStatus: All
                        condition: <+execution.steps.filter.output.outputVariables.go> == "true"
                    contextType: Pipeline
                  - step:
                      type: Run
                      name: Run the tf-checker
                      identifier: tf_checker
                      spec:
                        shell: Bash
                        command: make tffmt-ci
                        envVariables:
                          GITHUB_TOKEN: <+secrets.getValue("github_ci_access_token")>
                          PR_NUMNER: <+trigger.prNumber>
                      when:
                        stageStatus: All
                        condition: <+execution.steps.filter.output.outputVariables.tf> == "true"
                    contextType: Pipeline
              - step:
                  type: Run
                  name: Codegen check
                  identifier: codegen_check
                  spec:
                    shell: Bash
                    command: make codegen-check-ci
                    envVariables:
                      GITHUB_TOKEN: <+secrets.getValue("github_ci_access_token")>
                      PR_NUMNER: <+trigger.prNumber>
                  when:
                    stageStatus: All
                    condition: <+execution.steps.filter.output.outputVariables.shouldCodegen> == "true"
                contextType: Pipeline
              - step:
                  type: Run
                  name: Run the jsonnet-checker
                  identifier: jsonnet_check
                  spec:
                    shell: Bash
                    command: make jsonnet-fmt-ci
                    envVariables:
                      GITHUB_TOKEN: <+secrets.getValue("github_ci_access_token")>
                      PR_NUMNER: <+trigger.prNumber>
                  when:
                    stageStatus: All
                    condition: <+execution.steps.filter.output.outputVariables.jsonnet> == "true"
              - step:
                  type: Run
                  name: Mod tidy check
                  identifier: mod_tify_check
                  spec:
                    shell: Bash
                    command: make mod-tidy-check-ci
                    envVariables:
                      GITHUB_TOKEN: <+secrets.getValue("github_ci_access_token")>
                      PR_NUMNER: <+trigger.prNumber>
                  when:
                    stageStatus: All
                    condition: <+execution.steps.filter.output.outputVariables.go> == "true"
          caching:
            enabled: false
            paths: []
