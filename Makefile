SHELL := /bin/bash
PROJECT_DIR=$(shell pwd)

RED    = \033[0;31m
GREEN  = \033[0;32m
YELLOW = \033[0;33m
BLUE   = \033[0;36m
RESET  = \033[0m

help: ## Display this help.
	@awk 'BEGIN {FS = ":.*##"; printf "\nUsage:\n  make ${BLUE}<target>${RESET}\n\n"} /^[a-zA-Z_0-9-]+:.*?##/ { printf "  ${BLUE}%-20s${RESET} %s\n", $$1, $$2 } /^##@/ { printf "\n\033[1m%s${RESET}\n", substr($$0, 5) } ' $(MAKEFILE_LIST)

##@ Code deployment

install: mod-tidy ## Install onebox to your local machine
	cd onebox && make install

install-root: mod-tidy ## Install onebox to your local machine as root
	cd onebox && make install-root

build: mod-tidy ## alias for install
	make install

req-rwc: 
ifeq (, $(shell which rwc))
	@printf "${RED}No rwc found. Consider building it via 'cd cli; make install'${RESET}\n"
	@exit 1
endif

wait-rw-ready: ## wait until no rw instance is in Creating mode
	@while [ $$(rwc cluster list | grep -c Creating) -gt 0 ]; do sleep 1 ; echo "cluster creating..."; done
	@echo "Cluster no longer creating..."

RAND := $(shell shuf -i 1-100 -n 1)
start-rw: check-owner ## uses rwc to create a RW instance in your local luster
	rwc context account set --url http://localhost:8180/api/v1
	rwc context region set --name local
	rwc auth register --account ${OWNER}@risingwave-labs.com --password Password123!
	rwc auth login --account ${OWNER}@risingwave-labs.com --password Password123!
	rwc cluster create --name ${OWNER}-test-${RAND} --tier Test --compute t-cn --meta t-mt --compactor t-cp --frontend t-ft --etcd t-et
	make wait-rw-ready
	rwc cluster list
	rwc cluster dbuser create --name ${OWNER}-test-${RAND} --username ${OWNER} --password Password123!
	sleep 1
	rwc cluster dbuser list --name ${OWNER}-test-${RAND}
	rwc cluster endpoint list --name ${OWNER}-test-${RAND}
	@printf "${GREEN}rw instance ready with User: ${OWNER}: Password123!${RESET}\n"

start-with-rw: check-owner req-rwc start ## starts cluster WITH RisingWave instance
	make start-rw

start-with-monitor-rw: start-with-monitor start-with-rw 

start-with-monitor: start ## starts cluster WITH monitoring stack
	onebox setup monitor 

start: install ## start a local cluster with aws
	onebox shutdown oneboxd
	onebox config path $(shell pwd)
	onebox setup -t 15
	onebox d

start-gcp: install ## start a local cluster with gcp
	onebox shutdown oneboxd
	onebox config path $(shell pwd)
	onebox config cloudplatform gcp
	onebox setup -t 15
	onebox d

##@ Code Generation

go-work: ## create a new go.work file for this project. Will fix error 'gopls was not able to find modules in your workspace'
	rm -f go.work
	go work init
	go work use -r .

submodule: ## init or upadte all submodules 
	git submodule update --recursive --init

mod-tidy: ## Go mod tidy 
	cd shared; go mod tidy
	cd mgmt; go mod tidy
	cd proxy; go mod tidy
	cd test; go mod tidy
	cd onebox/pkg/oneboxd; make gen-proto; make mock-gen; go mod tidy
	cd onebox; go mod tidy
	cd cli; go mod tidy
	cd account; go mod tidy
	cd admin; go mod tidy
	cd billing; go mod tidy
	cd pgweb; go mod tidy

mod-tidy-local: submodule mod-tidy ## Go mod tidy 

set-version: ## Set version of dependency, e.g. 'REQUIRED_VERSION=github.com/some/package@v1.2.3 make set-version'
ifneq ("$(REQUIRED_VERSION)", "")
		$(info "changing to $(REQUIRED_VERSION)")
		cd shared; go mod edit -require $(REQUIRED_VERSION)
		cd mgmt; go mod edit -require $(REQUIRED_VERSION)
		cd proxy; go mod edit -require $(REQUIRED_VERSION)
		cd test; go mod edit -require $(REQUIRED_VERSION)
		cd onebox/pkg/oneboxd; make gen-proto; go mod edit -require $(REQUIRED_VERSION)
		cd onebox; go mod edit -require $(REQUIRED_VERSION)
		cd cli; go mod edit -require $(REQUIRED_VERSION)
		cd account; go mod edit -require $(REQUIRED_VERSION)
		cd admin; go mod edit -require $(REQUIRED_VERSION)
		cd billing; go mod edit -require $(REQUIRED_VERSION)
		make mod-tidy
else
		@printf "${RED}Please provide a version using 'REQUIRED_VERSION=github.com/some/package@v1.2.3 make set-version'${RESET}\n"
		@exit 1
endif

SHARED_CODEGEN_CODEGEN_SUB_MODULES = shared
CODEGEN_SUB_MODULES = mgmt \
	account \
	cli \
	onebox \
	admin \
	billing \
	tools

# List the targets in the form of "module/$(module)/codegen", e.g., "module/share/codege"
SHARED_SUB_MODULE_CODEGEN_TARGETS = $(SHARED_CODEGEN_CODEGEN_SUB_MODULES:%=module/%/codegen)
SHARED_SUB_MODULE_CODEGEN_CLEAN_TARGETS = $(SHARED_CODEGEN_CODEGEN_SUB_MODULES:%=module/%/codegen-clean)
SUB_MODULE_CODEGEN_TARGETS = $(CODEGEN_SUB_MODULES:%=module/%/codegen)
SUB_MODULE_CODEGEN_CLEAN_TARGETS = $(CODEGEN_SUB_MODULES:%=module/%/codegen-clean)

# Declare that all non-sharing sub modules depends on the shared sub modules
$(SUB_MODULE_CODEGEN_TARGETS): $(SHARED_SUB_MODULE_CODEGEN_TARGETS)

# Define the recipe for all the sub modules
$(SUB_MODULE_CODEGEN_TARGETS) $(SHARED_SUB_MODULE_CODEGEN_TARGETS):
	@cd $(@:module/%/codegen=%); $(MAKE) codegen

# Define the recipe for cleaning the codegen files
$(SUB_MODULE_CODEGEN_CLEAN_TARGETS) $(SHARED_SUB_MODULE_CODEGEN_CLEAN_TARGETS):
	@cd $(@:module/%/codegen-clean=%); $(MAKE) codegen-clean

codegen: $(SUB_MODULE_CODEGEN_TARGETS) $(SHARED_SUB_MODULE_CODEGEN_TARGETS) ## Generate code in all sub modules
codegen-clean: $(SUB_MODULE_CODEGEN_CLEAN_TARGETS) $(SHARED_SUB_MODULE_CODEGEN_CLEAN_TARGETS) ## Clean codegen files in all sub modules

build-mjml: ## Build mjml 
	cd mjml; pnpm install; mkdir -p target; pnpm run build;

##@ CI Checks

codegen-check-ci: ## Fails if 'make codegen' creates diff
	git restore :/ && git clean -d -f
	make codegen-clean && make codegen
	git diff
	.github/comment_diff.sh "codegen report" "Some files changed after \`make codegen\`"

mod-tidy-check-ci: ## Fails if 'make mod-tidy' creates diff
	git restore :/ && git clean -d -f
	make mod-tidy
	.github/comment_diff.sh "mod tidy report" "Some files changed after \`make mod-tidy\`"

tffmt-ci: ## Fails if `make tffmt` creates diff
	git restore :/ && git clean -d -f
	make tffmt
	.github/comment_diff.sh "terraform fmt report" "Some files changed after \`make tffmt\`"

jsonnet-fmt-ci: ## Fails if `make jsonnet-fmt` creates diff
	git restore :/ && git clean -d -f
	make jsonnet-fmt
	.github/comment_diff.sh "jsonnet-fmt report" "Some files changed after \`make jsonnet-fmt\`"

api-fmt-ci: ## Fails if `make api-fmt` creates diff
	git restore :/ && git clean -d -f
	$(MAKE) api-fmt
	.github/comment_diff.sh "api-fmt report" "Some files changed after \`make api-fmt\`"

define lint_check_ci_message
#### lint-check report
Make lint failed!
Please use pre-commit[https://pre-commit.com/#quick-start].
endef
export lint_check_ci_message
lint-check-ci: ## Fails if `make lint-check` creates diff
	make lint-check || (.github/comment_on_github.sh "$$lint_check_ci_message"; exit 1; )


##@ Tests

reqs: 
ifeq (, $(shell which realpath))
	@printf "${RED}No realpath found. Consider installing it via 'brew install coreutils'${RESET}\n"
	@exit 1
endif

ut: reqs ## run unit tests
	cd shared; make ut
	cd mgmt; make ut
	cd account; make ut
	cd cli; make ut
	cd onebox; make ut
	cd admin; make ut
	cd proxy; make ut
	cd pgweb; make ut
	cd billing; make ut

OWNER ?= ""
check-owner:
ifeq ($(OWNER), "")
	@printf "${RED}please pass a test owner, using the variable OWNER${RESET}\n"
	@exit 1
endif

#
# If you would like to run against DEV use e.g. -stack=DEV -region=ap-southeast-1

e2e-test-basic: check-owner ## Run basic workflow e2e tests
	cd test/e2e; go test -v -run '^TestBasicWorkflow$$'   	-stack=TEST 	-region=us-east-1   		-timeout 30m -owner=$(OWNER) 
	cd test/e2e; go test -v -run '^TestBasicWorkflow$$'   	-stack=TEST 	-region=us-central1 		-timeout 30m -owner=$(OWNER)

e2e-test-scaling: check-owner ## Run scaling e2e tests
	cd test/e2e; go test -v -run '^TestScalingWorkflow$$' 	-stack=TEST 	-region=us-east-1   		-timeout 30m -owner=$(OWNER)
	cd test/e2e; go test -v -run '^TestScalingWorkflow$$' 	-stack=TEST 	-region=us-central1 		-timeout 30m -owner=$(OWNER)

e2e-test-extension: check-owner ## Run extensions e2e tests
	cd test/e2e; go test -v -run '^TestExtensionTenant$$' 	-stack=TEST 	-region=us-east-1    		-timeout 30m -owner=$(OWNER)
	cd test/e2e; go test -v -run '^TestExtensionTenant$$' 	-stack=TEST 	-region=us-central1  		-timeout 30m -owner=$(OWNER)

e2e-test-sbc: check-owner
	cd test/e2e; go test -v -run '^TestExtensionSbcTenant$$' -stack=DEV 	-region=ap-southeast-1 		-timeout 30m -owner=$(OWNER)

e2e-test-cloud: e2e-test-scaling e2e-test-basic extension check-owner ## Run all e2e tests

##@ Lint and other checks

# lint check
mgmt-lint-check:
	cd ./mgmt; make lint-check

account-lint-check:
	cd ./account; make lint-check

proxy-lint-check:
	cd ./proxy; make lint-check

admin-lint-check:
	cd ./admin; make lint-check

billing-lint-check:
	cd ./billing; make lint-check

cli-lint-check:
	cd ./cli; make lint-check

shared-lint-check:
	cd ./shared; make lint-check

lint-check: mgmt-lint-check account-lint-check proxy-lint-check admin-lint-check billing-lint-check cli-lint-check shared-lint-check ## Lint check all

# lint fix
mgmt-lint-fix:
	cd ./mgmt; make lint-fix

account-lint-fix:
	cd ./account; make lint-fix

proxy-lint-fix:
	cd ./proxy; make lint-fix

admin-lint-fix:
	cd ./admin; make lint-fix

billing-lint-fix:
	cd ./billing; make lint-fix

cli-lint-fix:
	cd ./cli; make lint-fix

shared-lint-fix:
	cd ./shared; make lint-fix

lint-fix: mgmt-lint-fix account-lint-fix proxy-lint-fix admin-lint-fix billing-lint-fix cli-lint-fix shared-lint-fix ## Lint fix all

shell_list=$(shell find . -type f -name "*.sh" -not -path "./*/.terraform/*" -not -path "./CloudAgent/*")
shellcheck: ## check sh scripts for errors
	@for var in $(shell_list); do shellcheck --format=gcc $$var; done

# Fixing imports needs to run before codegen, since it changes files which are auto-generated. We do not want format changes in auto-generated files
goimports-fix: ## fix all goimports
	$(shell find . -name "*.go" -type f -print0 | xargs -0 goimports -w -local github.com/risingwavelabs/risingwave-cloud)
	make codegen

shfmt: shellcheck ## format sh scripts
	GOBIN=$(PROJECT_DIR)/bin go install mvdan.cc/sh/v3/cmd/shfmt@latest
	@for var in $(shell_list); do shfmt -s -w $$var; done

tffmt: ## format terraform scripts
	terraform fmt --recursive

jsonnet-fmt: ## format jsonnet
	cd deploy/infra/center_grafana/resources/jsonnet; make fmt

api-fmt:
	cd ./api; make fmt

fmt: jsonnet-fmt tffmt shfmt api-fmt ## run all fmt targets
