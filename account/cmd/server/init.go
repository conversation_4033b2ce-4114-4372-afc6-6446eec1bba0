package server

import (
	"github.com/gin-gonic/gin"
	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/risingwave-cloud/account/config"
	"github.com/risingwavelabs/risingwave-cloud/account/email/client/ses"
	"github.com/risingwavelabs/risingwave-cloud/account/internal"
	"github.com/risingwavelabs/risingwave-cloud/account/model"
	"github.com/risingwavelabs/risingwave-cloud/account/services"
	"github.com/risingwavelabs/risingwave-cloud/shared/ginx"
	ginauth "github.com/risingwavelabs/risingwave-cloud/shared/ginx/auth"
	"github.com/risingwavelabs/risingwave-cloud/shared/logger"
)

func Init(asdbCredentialPath, svcConfigPath, appTokensPath, regionConfigPath string) (err error) {
	if err = config.Init(asdbCredentialPath, svcConfigPath, appTokensPath); err != nil {
		return
	}

	if err = config.InitRegionInfos(regionConfigPath); err != nil {
		return
	}
	// init logger
	if err = logger.InitLogger(&config.Conf.AccountService.Log); err != nil {
		return
	}

	// run migrations
	if err = model.Init(); err != nil {
		return
	}

	gin.SetMode(config.Conf.AccountService.Mode)

	return nil
}

func LoadAuthSecret() (services.AuthSecret, error) {
	if config.Conf.AccountService.Mode == "debug" {
		return []byte("test"), nil
	}
	jwtSecret := config.Conf.AccountService.JwtSecret
	if jwtSecret == "" {
		return []byte{}, eris.New("JWT_SECRET is not set")
	}
	return []byte(jwtSecret), nil
}

func LoadSESConfig() (ses.EmailClientConfig, error) {
	return ses.EmailClientConfig{
		Charset: "UTF-8",
		Region:  "us-east-2",
		// risingwave.cloud email domain is managed by rwcprod aws account.
		// Arn could be found at https://us-east-2.console.aws.amazon.com/ses/home?region=us-east-2#/identities/risingwave.cloud
		EmailFrom: "<EMAIL>",
		SourceArn: "arn:aws:ses:us-east-2:************:identity/risingwave.cloud",
	}, nil
}

func LoadVersion() ginx.Version {
	if internal.CurrentVersion == "" {
		return "dev"
	}
	return internal.CurrentVersion
}

func LoadAuthenticator(service services.AuthServiceInterface) ginauth.Authenticator {
	return ginauth.NewMultiSchemeAuthenticator(map[string]ginauth.Authenticator{
		"BearerAuth": ginauth.NewBearerAuthenticator(service.IsValidToken),
		"ApiKeyAuth": ginauth.NewAPIKeyAuthenticator(service.IsValidAPIKey),
	})
}
