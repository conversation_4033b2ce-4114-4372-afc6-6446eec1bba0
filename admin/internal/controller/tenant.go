package controller

import (
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/jinzhu/copier"
	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/risingwave-cloud/shared/ginx"
	"github.com/risingwavelabs/risingwave-cloud/shared/http/specresp"
	"github.com/risingwavelabs/risingwave-cloud/shared/paging"
	"github.com/risingwavelabs/risingwave-cloud/shared/ptr"
	"github.com/risingwavelabs/risingwave-cloud/shared/spec/admin_spec"
	"github.com/risingwavelabs/risingwave-cloud/shared/spec/mgmt_admin"
)

func (controller *AdminController) GetRegionRegionTenantTenantId(c *gin.Context, region string, tenantId uint64) {
	agent, err := controller.mgmtReg.GetOrFetch(c, region)
	if err != nil {
		RegionErrorResponse(c, err)
		return
	}
	tenant, err := agent.GetTenant(c, tenantId)
	if err != nil {
		specresp.ErrorThroughResponse(c, err)
		return
	}

	c.JSO<PERSON>(200, tenant)
}

func (controller *AdminController) DeleteRegionRegionTenantTenantId(c *gin.Context, region string, tenantId uint64) {
	agent, err := controller.mgmtReg.GetOrFetch(c, region)
	if err != nil {
		RegionErrorResponse(c, err)
		return
	}

	workflowID, err := agent.DeleteTenant(c, tenantId)
	if err != nil {
		specresp.ErrorThroughResponse(c, err)
		return
	}
	c.JSON(202, admin_spec.WorkflowIdResponse{
		WorkflowId: workflowID,
	})
}

func (controller *AdminController) GetRegionRegionTenants(c *gin.Context, region string, params admin_spec.GetRegionRegionTenantsParams) {
	agent, err := controller.mgmtReg.GetOrFetch(c, region)
	if err != nil {
		RegionErrorResponse(c, err)
		return
	}
	page := paging.HTTPPager.Paging(params.Offset, params.Limit)
	resp, err := agent.GetTenants(c, page)
	if err != nil {
		specresp.ErrorThroughResponse(c, err)
		return
	}

	c.JSON(200, resp)
}

func (controller *AdminController) GetRegionRegionTenantTenantIdExpire(c *gin.Context, region string, tenantId uint64) {
	agent, err := controller.mgmtReg.GetOrFetch(c, region)
	if err != nil {
		RegionErrorResponse(c, err)
		return
	}
	expireInfo, err := agent.GetTenantExpireInfo(c, tenantId)
	if err != nil {
		specresp.ErrorThroughResponse(c, err)
		return
	}
	var res *admin_spec.TenantExpireInfo
	if expireInfo != nil {
		res = &admin_spec.TenantExpireInfo{}
		err = copier.Copy(res, expireInfo)
		if err != nil {
			ginx.AdminInternalError(c, err)
			return
		}
	}
	c.JSON(200, res)
}

func (controller *AdminController) PostRegionRegionTenantTenantIdExpire(c *gin.Context, region string, tenantId uint64) {
	var req mgmt_admin.PostTenantExpireRequestBody
	if err := c.BindJSON(&req); err != nil {
		ginx.InvalidArgumentError(c, err)
		return
	}

	agent, err := controller.mgmtReg.GetOrFetch(c, region)
	if err != nil {
		RegionErrorResponse(c, err)
		return
	}
	err = agent.TriggerTenantExpire(c, tenantId, req.ExpireAt, req.DeleteAt)
	if err != nil {
		specresp.ErrorThroughResponse(c, err)
		return
	}
	ginx.Accepted(c, nil, "OK")
}

func (controller *AdminController) DeleteRegionRegionTenantTenantIdExpire(c *gin.Context, region string, tenantId uint64) {
	agent, err := controller.mgmtReg.GetOrFetch(c, region)
	if err != nil {
		RegionErrorResponse(c, err)
		return
	}
	err = agent.CancelTenantExpire(c, tenantId)
	if err != nil {
		specresp.ErrorThroughResponse(c, err)
		return
	}
	ginx.Accepted(c, nil, "OK")
}

func (controller *AdminController) PostRegionRegionTenantTenantIdStop(c *gin.Context, region string, tenantId uint64) {
	agent, err := controller.mgmtReg.GetOrFetch(c, region)
	if err != nil {
		RegionErrorResponse(c, err)
		return
	}
	workflowID, err := agent.TriggerTenantStop(c, tenantId)
	if err != nil {
		specresp.ErrorThroughResponse(c, err)
		return
	}
	c.JSON(202, admin_spec.WorkflowIdResponse{
		WorkflowId: workflowID,
	})
}

func (controller *AdminController) PostRegionRegionTenantTenantIdStart(c *gin.Context, region string, tenantId uint64) {
	agent, err := controller.mgmtReg.GetOrFetch(c, region)
	if err != nil {
		RegionErrorResponse(c, err)
		return
	}
	workflowID, err := agent.TriggerTenantStart(c, tenantId)
	if err != nil {
		specresp.ErrorThroughResponse(c, err)
		return
	}
	c.JSON(202, admin_spec.WorkflowIdResponse{
		WorkflowId: workflowID,
	})
}

func (controller *AdminController) PostRegionRegionTenantTenantIdRestart(c *gin.Context, region string, tenantId uint64) {
	agent, err := controller.mgmtReg.GetOrFetch(c, region)
	if err != nil {
		RegionErrorResponse(c, err)
		return
	}
	workflowID, err := agent.TriggerTenantRestart(c, tenantId)
	if err != nil {
		specresp.ErrorThroughResponse(c, err)
		return
	}
	c.JSON(202, admin_spec.WorkflowIdResponse{
		WorkflowId: workflowID,
	})
}

func (controller *AdminController) PostRegionRegionTenantTenantIdUpdateVersion(c *gin.Context, region string, tenantId uint64) {
	agent, err := controller.mgmtReg.GetOrFetch(c, region)
	if err != nil {
		RegionErrorResponse(c, err)
		return
	}

	var req admin_spec.PostRegionRegionTenantTenantIdUpdateVersionJSONRequestBody
	if err = c.BindJSON(&req); err != nil {
		ginx.InvalidArgumentError(c, err)
		return
	}
	workflowID, err := agent.TriggerTenantUpdateVersion(c, tenantId, req.Version, req.SkipBackup)
	if err != nil {
		specresp.ErrorThroughResponse(c, err)
		return
	}
	c.JSON(202, admin_spec.WorkflowIdResponse{
		WorkflowId: workflowID,
	})
}

func (controller *AdminController) PutRegionRegionTenantTenantIdConfigRisingwave(
	c *gin.Context, region string, tenantId uint64,
	params admin_spec.PutRegionRegionTenantTenantIdConfigRisingwaveParams,
) {
	agent, err := controller.mgmtReg.GetOrFetch(c, region)
	if err != nil {
		RegionErrorResponse(c, err)
		return
	}

	raw, err := ginx.RequestBody(c)
	if err != nil {
		ginx.AdminInternalError(c, eris.Wrap(err, "failed to to read risingwave config content from body"))
		return
	}

	workflowID, err := agent.TriggerTenantUpdateRisingwaveConfig(c, tenantId, &mgmt_admin.PutTenantTenantIdConfigRisingwaveParams{
		Component: params.Component,
		NodeGroup: params.NodeGroup,
	}, string(raw))
	if err != nil {
		specresp.ErrorThroughResponse(c, err)
		return
	}
	c.JSON(202, admin_spec.WorkflowIdResponse{
		WorkflowId: workflowID,
	})
}

func (controller *AdminController) PutRegionRegionTenantTenantIdConfigNoRestartRisingwave(
	c *gin.Context, region string, tenantId uint64,
	params admin_spec.PutRegionRegionTenantTenantIdConfigNoRestartRisingwaveParams,
) {
	agent, err := controller.mgmtReg.GetOrFetch(c, region)
	if err != nil {
		RegionErrorResponse(c, err)
		return
	}

	raw, err := ginx.RequestBody(c)
	if err != nil {
		ginx.AdminInternalError(c, eris.Wrap(err, "failed to to read risingwave config content from body"))
		return
	}

	workflowID, err := agent.TriggerTenantUpdateRisingwaveConfigNoRestart(c, tenantId, &mgmt_admin.PutTenantTenantIdConfigNoRestartRisingwaveParams{
		Component: params.Component,
		NodeGroup: params.NodeGroup,
	}, string(raw))
	if err != nil {
		specresp.ErrorThroughResponse(c, err)
		return
	}
	c.JSON(202, admin_spec.WorkflowIdResponse{
		WorkflowId: workflowID,
	})
}

func (controller *AdminController) PutRegionRegionTenantTenantIdConfigEtcd(c *gin.Context, region string, tenantId uint64) {
	agent, err := controller.mgmtReg.GetOrFetch(c, region)
	if err != nil {
		RegionErrorResponse(c, err)
		return
	}

	raw, err := ginx.RequestBody(c)
	if err != nil {
		ginx.AdminInternalError(c, eris.Wrap(err, "failed to to read etcd config content from body"))
		return
	}
	workflowID, err := agent.TriggerTenantUpdateEtcdConfig(c, tenantId, string(raw))
	if err != nil {
		specresp.ErrorThroughResponse(c, err)
		return
	}
	c.JSON(202, admin_spec.WorkflowIdResponse{
		WorkflowId: workflowID,
	})
}

func (controller *AdminController) PostRegionRegionTenantTenantIdResource(c *gin.Context, region string, tenantId uint64) {
	agent, err := controller.mgmtReg.GetOrFetch(c, region)
	if err != nil {
		RegionErrorResponse(c, err)
		return
	}

	var req mgmt_admin.PostTenantResourcesRequestBody // admin and mgmt_admin use the same PostTenantResourcesRequestBody
	if err = c.BindJSON(&req); err != nil {
		ginx.InvalidArgumentError(c, err)
		return
	}
	workflowID, err := agent.TriggerTenantUpdateResource(c, tenantId, req)
	if err != nil {
		specresp.ErrorThroughResponse(c, err)
		return
	}
	c.JSON(202, admin_spec.WorkflowIdResponse{
		WorkflowId: workflowID,
	})
}

func (controller *AdminController) PostRegionRegionTenantTenantIdStatus(c *gin.Context, region string, tenantId uint64) {
	agent, err := controller.mgmtReg.GetOrFetch(c, region)
	if err != nil {
		RegionErrorResponse(c, err)
		return
	}

	var req admin_spec.PostTenantStatusRequestBody
	if err = c.BindJSON(&req); err != nil {
		ginx.InvalidArgumentError(c, err)
		return
	}
	msg, err := agent.ResetTenantStatus(c, tenantId, req.Target, req.Previous)
	if err != nil {
		specresp.ErrorThroughResponse(c, err)
		return
	}

	c.JSON(200, gin.H{
		"msg": msg,
	})
}

func (controller *AdminController) PostRegionRegionTenantTenantIdTier(c *gin.Context, region string, tenantId uint64) {
	agent, err := controller.mgmtReg.GetOrFetch(c, region)
	if err != nil {
		RegionErrorResponse(c, err)
		return
	}

	var req admin_spec.PostTenantTierRequestBody
	if err = c.BindJSON(&req); err != nil {
		ginx.InvalidArgumentError(c, err)
		return
	}
	msg, err := agent.UpdateTenantTier(c, tenantId, req.Target, req.Previous)
	if err != nil {
		specresp.ErrorThroughResponse(c, err)
		return
	}

	c.JSON(200, gin.H{
		"msg": msg,
	})
}

func (controller *AdminController) GetRegionRegionTenantTenantIdEndpoint(c *gin.Context, region string, tenantId uint64) {
	agent, err := controller.mgmtReg.GetOrFetch(c, region)
	if err != nil {
		RegionErrorResponse(c, err)
		return
	}
	endpoint, err := agent.GetTenantRootEndpoint(c, tenantId)
	if err != nil {
		specresp.ErrorThroughResponse(c, err)
		return
	}
	c.JSON(200, endpoint)
}

func (controller *AdminController) PostRegionRegionTenantTenantIdMigrateMetaStore(c *gin.Context, region string, tenantId uint64) {
	agent, err := controller.mgmtReg.GetOrFetch(c, region)
	if err != nil {
		RegionErrorResponse(c, err)
		return
	}

	workflowID, err := agent.TriggerTenantMigrateMetaStore(c, tenantId)
	if err != nil {
		specresp.ErrorThroughResponse(c, err)
		return
	}
	c.JSON(202, admin_spec.WorkflowIdResponse{
		WorkflowId: workflowID,
	})
}

func (controller *AdminController) PostRegionRegionTenantTenantIdUpgradeMetaStore(c *gin.Context, region string, tenantId uint64, params admin_spec.PostRegionRegionTenantTenantIdUpgradeMetaStoreParams) {
	agent, err := controller.mgmtReg.GetOrFetch(c, region)
	if err != nil {
		RegionErrorResponse(c, err)
		return
	}

	workflowID, err := agent.TriggerTenantUpgradeMetaStore(c, tenantId, params.MetaStore)
	if err != nil {
		specresp.ErrorThroughResponse(c, err)
		return
	}
	c.JSON(202, admin_spec.WorkflowIdResponse{
		WorkflowId: workflowID,
	})
}

func (controller *AdminController) PostRegionRegionTenantTenantIdComputeCache(c *gin.Context, region string, tenantId uint64) {
	agent, err := controller.mgmtReg.GetOrFetch(c, region)
	if err != nil {
		RegionErrorResponse(c, err)
		return
	}

	var req mgmt_admin.PostTenantTenantIdComputeCacheJSONRequestBody
	if err = c.BindJSON(&req); err != nil {
		ginx.InvalidArgumentError(c, err)
		return
	}

	workflowID, err := agent.TriggerTenantUpdateComputeCache(c, tenantId, req)
	if err != nil {
		specresp.ErrorThroughResponse(c, err)
		return
	}
	c.JSON(202, admin_spec.WorkflowIdResponse{
		WorkflowId: workflowID,
	})
}

func (controller *AdminController) PostRegionRegionTenantTenantIdBackup(c *gin.Context, region string, tenantId uint64) {
	agent, err := controller.mgmtReg.GetOrFetch(c, region)
	if err != nil {
		RegionErrorResponse(c, err)
		return
	}

	workflowID, err := agent.TriggerTenantBackup(c, tenantId)
	if err != nil {
		specresp.ErrorThroughResponse(c, err)
		return
	}

	c.JSON(202, admin_spec.WorkflowIdResponse{
		WorkflowId: workflowID,
	})
}

func (controller *AdminController) PostRegionRegionTenantTenantIdBackupSnapshotIdRestore(c *gin.Context, region string, tenantID uint64, snapshotID uuid.UUID) { //nolint:revive
	agent, err := controller.mgmtReg.GetOrFetch(c, region)
	if err != nil {
		RegionErrorResponse(c, err)
		return
	}

	var req mgmt_admin.PostTenantTenantIdBackupSnapshotIdRestoreJSONRequestBody
	if err = c.BindJSON(&req); err != nil {
		ginx.InvalidArgumentError(c, err)
		return
	}

	resp, err := agent.TriggerTenantRestore(c, tenantID, snapshotID, req)
	if err != nil {
		specresp.ErrorThroughResponse(c, err)
		return
	}

	c.JSON(202, resp)
}

func (controller *AdminController) PostRegionRegionTenantTenantIdBackupSnapshotIdInPlaceRestore(c *gin.Context, region string, tenantID uint64, snapshotID uuid.UUID) {
	agent, err := controller.mgmtReg.GetOrFetch(c, region)
	if err != nil {
		RegionErrorResponse(c, err)
		return
	}

	workflowID, err := agent.TriggerTenantInplaceRestore(c, tenantID, snapshotID)
	if err != nil {
		specresp.ErrorThroughResponse(c, err)
		return
	}

	c.JSON(202, admin_spec.WorkflowIdResponse{
		WorkflowId: *workflowID,
	})
}

func (controller *AdminController) DeleteRegionRegionTenantTenantIdBackupSnapshotId(c *gin.Context, region string, tenantId uint64, snapshotId uuid.UUID) {
	agent, err := controller.mgmtReg.GetOrFetch(c, region)
	if err != nil {
		RegionErrorResponse(c, err)
		return
	}
	workflowID, err := agent.DeleteTenantBackup(c, tenantId, snapshotId)
	if err != nil {
		specresp.ErrorThroughResponse(c, err)
		return
	}
	c.JSON(202, admin_spec.OptionalWorkflowIdResponseBody{
		WorkflowId: workflowID,
		Msg:        ptr.Ptr("Deletion is in progress"),
	})
}
