package agent

import (
	"context"
	"time"

	"github.com/google/uuid"

	"github.com/risingwavelabs/risingwave-cloud/shared/http/specresp"
	"github.com/risingwavelabs/risingwave-cloud/shared/paging"
	"github.com/risingwavelabs/risingwave-cloud/shared/spec/mgmt_admin"
)

func (agent *MgmtAgent) GetTenant(ctx context.Context, tenantID uint64) (*mgmt_admin.Tenant, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return nil, err
	}
	resp, err := specresp.RequireSuccess(client.GetTenantTenantIdWithResponse(ctx, tenantID))
	if err != nil {
		return nil, err
	}
	return resp.JSON200, nil
}

func (agent *MgmtAgent) DeleteTenant(ctx context.Context, tenantID uint64) (uuid.UUID, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return uuid.UUID{}, err
	}
	resp, err := specresp.RequireSuccess(client.DeleteTenantTenantIdWithResponse(ctx, tenantID))
	if err != nil {
		return uuid.UUID{}, err
	}
	return resp.JSON202.WorkflowId, nil
}

func (agent *MgmtAgent) GetTenants(ctx context.Context, page paging.Page) (*mgmt_admin.TenantPage, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return nil, err
	}
	resp, err := specresp.RequireSuccess(client.GetTenantsWithResponse(ctx, &mgmt_admin.GetTenantsParams{
		Offset: &page.Offset,
		Limit:  &page.Limit,
	}))
	if err != nil {
		return nil, err
	}
	return resp.JSON200, nil
}

func (agent *MgmtAgent) GetTenantExpireInfo(ctx context.Context, tenantID uint64) (*mgmt_admin.TenantExpireInfo, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return nil, err
	}
	resp, err := specresp.RequireSuccess(client.GetTenantTenantIdExpireWithResponse(ctx, tenantID))
	if err != nil {
		return nil, err
	}
	return resp.JSON200, nil
}

func (agent *MgmtAgent) TriggerTenantExpire(ctx context.Context, tenantID uint64, expireAt *time.Time, deleteAt *time.Time) error {
	client, err := agent.getMgmtClient()
	if err != nil {
		return err
	}
	_, err = specresp.RequireSuccess(client.PostTenantTenantIdExpireWithResponse(ctx, tenantID, mgmt_admin.PostTenantExpireRequestBody{
		ExpireAt: expireAt,
		DeleteAt: deleteAt,
	}))
	if err != nil {
		return err
	}
	return nil
}

func (agent *MgmtAgent) CancelTenantExpire(ctx context.Context, tenantID uint64) error {
	client, err := agent.getMgmtClient()
	if err != nil {
		return err
	}
	_, err = specresp.RequireSuccess(client.DeleteTenantTenantIdExpireWithResponse(ctx, tenantID))
	if err != nil {
		return err
	}
	return nil
}

func (agent *MgmtAgent) TriggerTenantStop(ctx context.Context, tenantID uint64) (uuid.UUID, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return uuid.UUID{}, err
	}
	resp, err := specresp.RequireSuccess(client.PostTenantTenantIdStopWithResponse(ctx, tenantID))
	if err != nil {
		return uuid.UUID{}, err
	}
	return resp.JSON202.WorkflowId, nil
}

func (agent *MgmtAgent) TriggerTenantStart(ctx context.Context, tenantID uint64) (uuid.UUID, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return uuid.UUID{}, err
	}
	resp, err := specresp.RequireSuccess(client.PostTenantTenantIdStartWithResponse(ctx, tenantID))
	if err != nil {
		return uuid.UUID{}, err
	}
	return resp.JSON202.WorkflowId, nil
}

func (agent *MgmtAgent) TriggerTenantRestart(ctx context.Context, tenantID uint64) (uuid.UUID, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return uuid.UUID{}, err
	}
	resp, err := specresp.RequireSuccess(client.PostTenantTenantIdRestartWithResponse(ctx, tenantID))
	if err != nil {
		return uuid.UUID{}, err
	}
	return resp.JSON202.WorkflowId, nil
}

func (agent *MgmtAgent) TriggerTenantExtensionsCompactionEnable(ctx context.Context, tenantID uint64) (uuid.UUID, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return uuid.UUID{}, err
	}
	resp, err := specresp.RequireSuccess(client.PostTenantTenantIdExtensionsCompactionEnableWithResponse(ctx, tenantID))
	if err != nil {
		return uuid.UUID{}, err
	}
	return resp.JSON202.WorkflowId, nil
}

func (agent *MgmtAgent) TriggerTenantExtensionsCompactionDisable(ctx context.Context, tenantID uint64) (uuid.UUID, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return uuid.UUID{}, err
	}
	resp, err := specresp.RequireSuccess(client.PostTenantTenantIdExtensionsCompactionDisableWithResponse(ctx, tenantID))
	if err != nil {
		return uuid.UUID{}, err
	}
	return resp.JSON202.WorkflowId, nil
}

func (agent *MgmtAgent) TriggerTenantExtensionsCompactionUpdate(ctx context.Context, tenantID uint64, req mgmt_admin.PostTenantTenantIdExtensionsCompactionUpdateJSONRequestBody) (uuid.UUID, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return uuid.UUID{}, err
	}
	resp, err := specresp.RequireSuccess(client.PostTenantTenantIdExtensionsCompactionUpdateWithResponse(ctx, tenantID, req))
	if err != nil {
		return uuid.UUID{}, err
	}
	return resp.JSON202.WorkflowId, nil
}

func (agent *MgmtAgent) GetTenantExtensionsCompactionParameters(ctx context.Context, tenantID uint64) (*mgmt_admin.GetTenantExtensionCompactionParametersResponseBody, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return nil, err
	}
	resp, err := specresp.RequireSuccess(client.GetTenantTenantIdExtensionsCompactionParametersWithResponse(ctx, tenantID))
	if err != nil {
		return nil, err
	}
	return resp.JSON200, nil
}

func (agent *MgmtAgent) GetTenantExtensionsCompactionStatus(ctx context.Context, tenantID uint64) (*mgmt_admin.GetTenantExtensionCompactionStatusResponseBody, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return nil, err
	}
	resp, err := specresp.RequireSuccess(client.GetTenantTenantIdExtensionsCompactionStatusWithResponse(ctx, tenantID))
	if err != nil {
		return nil, err
	}
	return resp.JSON200, nil
}

func (agent *MgmtAgent) PostTenantExtensionsCompactionStatus(ctx context.Context, tenantID uint64, req mgmt_admin.PostTenantTenantIdExtensionsCompactionStatusJSONRequestBody) (*mgmt_admin.GetTenantExtensionCompactionStatusResponseBody, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return nil, err
	}
	resp, err := specresp.RequireSuccess(client.PostTenantTenantIdExtensionsCompactionStatusWithResponse(ctx, tenantID, req))
	if err != nil {
		return nil, err
	}
	return resp.JSON200, nil
}

func (agent *MgmtAgent) TriggerTenantExtensionsServerlessBackfillEnable(ctx context.Context, tenantID uint64) (uuid.UUID, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return uuid.UUID{}, err
	}

	resp, err := specresp.RequireSuccess(client.PostTenantTenantIdExtensionsServerlessbackfillEnableWithResponse(ctx, tenantID))
	if err != nil {
		return uuid.UUID{}, err
	}

	return resp.JSON202.WorkflowId, nil
}

func (agent *MgmtAgent) TriggerTenantExtensionsServerlessBackfillDisable(ctx context.Context, tenantID uint64) (uuid.UUID, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return uuid.UUID{}, err
	}

	resp, err := specresp.RequireSuccess(client.PostTenantTenantIdExtensionsServerlessbackfillDisableWithResponse(ctx, tenantID))
	if err != nil {
		return uuid.UUID{}, err
	}

	return resp.JSON202.WorkflowId, nil
}

func (agent *MgmtAgent) TriggerTenantUpdateVersion(ctx context.Context, tenantID uint64, version string, skipMetabackup *bool) (uuid.UUID, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return uuid.UUID{}, err
	}
	resp, err := specresp.RequireSuccess(client.PostTenantTenantIdUpdateVersionWithResponse(ctx, tenantID, mgmt_admin.PostTenantTenantIdUpdateVersionJSONRequestBody{
		Version:    version,
		SkipBackup: skipMetabackup,
	}))
	if err != nil {
		return uuid.UUID{}, err
	}
	return resp.JSON202.WorkflowId, nil
}

func (agent *MgmtAgent) TriggerTenantExtensionsServerlessBackfillUpdateVersion(ctx context.Context, tenantID uint64, version string) (uuid.UUID, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return uuid.UUID{}, err
	}

	resp, err := specresp.RequireSuccess(client.PostTenantTenantIdExtensionsServerlessbackfillVersionWithTextBodyWithResponse(ctx, tenantID, version))
	if err != nil {
		return uuid.UUID{}, err
	}

	return resp.JSON202.WorkflowId, nil
}

func (agent *MgmtAgent) TriggerTenantUpdateRisingwaveConfig(ctx context.Context, tenantID uint64, params *mgmt_admin.PutTenantTenantIdConfigRisingwaveParams, config string) (uuid.UUID, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return uuid.UUID{}, err
	}
	resp, err := specresp.RequireSuccess(client.PutTenantTenantIdConfigRisingwaveWithTextBodyWithResponse(ctx, tenantID, params, config))
	if err != nil {
		return uuid.UUID{}, err
	}
	return resp.JSON202.WorkflowId, nil
}

func (agent *MgmtAgent) TriggerTenantUpdateRisingwaveConfigNoRestart(ctx context.Context, tenantID uint64, params *mgmt_admin.PutTenantTenantIdConfigNoRestartRisingwaveParams, config string) (uuid.UUID, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return uuid.UUID{}, err
	}
	resp, err := specresp.RequireSuccess(client.PutTenantTenantIdConfigNoRestartRisingwaveWithTextBodyWithResponse(ctx, tenantID, params, config))
	if err != nil {
		return uuid.UUID{}, err
	}
	return resp.JSON202.WorkflowId, nil
}

func (agent *MgmtAgent) TriggerTenantUpdateEtcdConfig(ctx context.Context, tenantID uint64, config string) (uuid.UUID, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return uuid.UUID{}, err
	}
	resp, err := specresp.RequireSuccess(client.PutTenantTenantIdConfigEtcdWithTextBodyWithResponse(ctx, tenantID, config))
	if err != nil {
		return uuid.UUID{}, err
	}
	return resp.JSON202.WorkflowId, nil
}

func (agent *MgmtAgent) TriggerTenantUpdateResource(ctx context.Context, tenantID uint64, body mgmt_admin.PostTenantResourcesRequestBody) (uuid.UUID, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return uuid.UUID{}, err
	}
	resp, err := specresp.RequireSuccess(client.PostTenantTenantIdResourceWithResponse(ctx, tenantID, body))
	if err != nil {
		return uuid.UUID{}, err
	}
	return resp.JSON202.WorkflowId, nil
}

func (agent *MgmtAgent) ResetTenantStatus(ctx context.Context, tenantID uint64, target string, previous string) (string, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return "", err
	}
	resp, err := specresp.RequireSuccess(client.PostTenantTenantIdStatusWithResponse(ctx, tenantID, mgmt_admin.PostTenantStatusRequestBody{
		Target:   target,
		Previous: previous,
	}))
	if err != nil {
		return "", err
	}
	return resp.JSON200.Msg, nil
}

func (agent *MgmtAgent) UpdateTenantTier(ctx context.Context, tenantID uint64, target string, previous string) (string, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return "", err
	}
	resp, err := specresp.RequireSuccess(client.PostTenantTenantIdTierWithResponse(ctx, tenantID, mgmt_admin.PostTenantTierRequestBody{
		Target:   target,
		Previous: previous,
	}))
	if err != nil {
		return "", err
	}
	return resp.JSON200.Msg, nil
}

func (agent *MgmtAgent) GetTenantRootEndpoint(ctx context.Context, tenantID uint64) (*mgmt_admin.DatabaseConnectionUrl, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return nil, err
	}
	resp, err := specresp.RequireSuccess(client.GetTenantTenantIdEndpointWithResponse(ctx, tenantID))
	if err != nil {
		return nil, err
	}
	return resp.JSON200, nil
}

func (agent *MgmtAgent) DeletePrivateLink(ctx context.Context, tenantID uint64, privateLinkID uuid.UUID) (uuid.UUID, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return uuid.UUID{}, err
	}
	resp, err := specresp.RequireSuccess(client.DeleteTenantTenantIdPrivatelinkPrivateLinkIdWithResponse(ctx, tenantID, privateLinkID))
	if err != nil {
		return uuid.UUID{}, err
	}

	return resp.JSON202.WorkflowId, nil
}

func (agent *MgmtAgent) TriggerTenantMigrateMetaStore(ctx context.Context, tenantID uint64) (uuid.UUID, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return uuid.UUID{}, err
	}
	resp, err := specresp.RequireSuccess(client.PostTenantTenantIdMigrateMetaStoreWithResponse(ctx, tenantID))
	if err != nil {
		return uuid.UUID{}, err
	}

	return resp.JSON202.WorkflowId, nil
}

func (agent *MgmtAgent) TriggerTenantUpgradeMetaStore(ctx context.Context, tenantID uint64, metaStore string) (uuid.UUID, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return uuid.UUID{}, err
	}
	resp, err := specresp.RequireSuccess(client.PostTenantTenantIdUpgradeMetaStoreWithResponse(ctx, tenantID, &mgmt_admin.PostTenantTenantIdUpgradeMetaStoreParams{
		MetaStore: metaStore,
	}))
	if err != nil {
		return uuid.UUID{}, err
	}

	return resp.JSON202.WorkflowId, nil
}

func (agent *MgmtAgent) TriggerTenantUpdateComputeCache(ctx context.Context, tenantID uint64, req mgmt_admin.PostTenantTenantIdComputeCacheJSONRequestBody) (uuid.UUID, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return uuid.UUID{}, err
	}
	resp, err := specresp.RequireSuccess(client.PostTenantTenantIdComputeCacheWithResponse(ctx, tenantID, req))
	if err != nil {
		return uuid.UUID{}, err
	}
	return resp.JSON202.WorkflowId, nil
}

func (agent *MgmtAgent) TriggerTenantBackup(ctx context.Context, tenantID uint64) (uuid.UUID, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return uuid.UUID{}, err
	}
	resp, err := specresp.RequireSuccess(client.PostTenantTenantIdBackupWithResponse(ctx, tenantID))
	if err != nil {
		return uuid.UUID{}, err
	}
	return resp.JSON202.WorkflowId, nil
}

func (agent *MgmtAgent) DeleteTenantBackup(ctx context.Context, tenantID uint64, snapshotID uuid.UUID) (*uuid.UUID, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return nil, err
	}
	resp, err := specresp.RequireSuccess(client.DeleteTenantTenantIdBackupSnapshotIdWithResponse(ctx, tenantID, snapshotID))
	if err != nil {
		return nil, err
	}
	return resp.JSON202.WorkflowId, nil
}

func (agent *MgmtAgent) TriggerTenantRestore(ctx context.Context, tenantID uint64, snapshotID uuid.UUID, req mgmt_admin.PostTenantTenantIdBackupSnapshotIdRestoreJSONRequestBody) (*mgmt_admin.Tenant, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return nil, err
	}
	resp, err := specresp.RequireSuccess(client.PostTenantTenantIdBackupSnapshotIdRestoreWithResponse(ctx, tenantID, snapshotID, req))
	if err != nil {
		return nil, err
	}
	return resp.JSON202, nil
}

func (agent *MgmtAgent) TriggerTenantInplaceRestore(ctx context.Context, tenantID uint64, snapshotID uuid.UUID) (*uuid.UUID, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return nil, err
	}
	resp, err := specresp.RequireSuccess(client.PostTenantTenantIdBackupSnapshotIdInPlaceRestoreWithResponse(ctx, tenantID, snapshotID))
	if err != nil {
		return nil, err
	}
	return &resp.JSON202.WorkflowId, nil
}

func (agent *MgmtAgent) TriggerResourceGroupCreate(ctx context.Context, tenantID uint64, req mgmt_admin.CreateResourceGroupsRequestBody) (uuid.UUID, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return uuid.UUID{}, err
	}
	resp, err := specresp.RequireSuccess(client.PostTenantTenantIdResourceGroupsWithResponse(ctx, tenantID, req))
	if err != nil {
		return uuid.UUID{}, err
	}
	return resp.JSON202.WorkflowId, nil
}

func (agent *MgmtAgent) TriggerResourceGroupUpdate(ctx context.Context, tenantID uint64, resourceGroup string, req mgmt_admin.UpdateResourceGroupsRequestBody) (uuid.UUID, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return uuid.UUID{}, err
	}
	resp, err := specresp.RequireSuccess(client.PostTenantTenantIdResourceGroupsResourceGroupWithResponse(ctx, tenantID, resourceGroup, req))
	if err != nil {
		return uuid.UUID{}, err
	}
	return resp.JSON202.WorkflowId, nil
}

func (agent *MgmtAgent) TriggerResourceGroupDelete(ctx context.Context, tenantID uint64, resourceGroup string) (uuid.UUID, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return uuid.UUID{}, err
	}
	resp, err := specresp.RequireSuccess(client.DeleteTenantTenantIdResourceGroupsResourceGroupWithResponse(ctx, tenantID, resourceGroup))
	if err != nil {
		return uuid.UUID{}, err
	}
	return resp.JSON202.WorkflowId, nil
}

func (agent *MgmtAgent) GetExtensionIcebergCompaction(ctx context.Context, tenantID uint64) (*mgmt_admin.IcebergCompaction, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return nil, err
	}
	resp, err := specresp.RequireSuccess(client.GetTenantsTenantIdExtensionsIcebergCompactionWithResponse(ctx, tenantID))
	if err != nil {
		return nil, err
	}
	return resp.JSON200, nil
}

func (agent *MgmtAgent) PostExtensionIcebergCompaction(ctx context.Context, tenantID uint64, req mgmt_admin.PostTenantsTenantIdExtensionsIcebergCompactionJSONRequestBody) (uuid.UUID, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return uuid.UUID{}, err
	}
	resp, err := specresp.RequireSuccess(client.PostTenantsTenantIdExtensionsIcebergCompactionWithResponse(ctx, tenantID, req))
	if err != nil {
		return uuid.UUID{}, err
	}
	return resp.JSON202.WorkflowId, nil
}

func (agent *MgmtAgent) DeleteExtensionIcebergCompaction(ctx context.Context, tenantID uint64) (uuid.UUID, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return uuid.UUID{}, err
	}
	resp, err := specresp.RequireSuccess(client.DeleteTenantsTenantIdExtensionsIcebergCompactionWithResponse(ctx, tenantID))
	if err != nil {
		return uuid.UUID{}, err
	}
	return resp.JSON202.WorkflowId, nil
}

func (agent *MgmtAgent) PutExtensionIcebergCompaction(ctx context.Context, tenantID uint64, req mgmt_admin.PutTenantsTenantIdExtensionsIcebergCompactionJSONRequestBody) (uuid.UUID, error) {
	client, err := agent.getMgmtClient()
	if err != nil {
		return uuid.UUID{}, err
	}
	resp, err := specresp.RequireSuccess(client.PutTenantsTenantIdExtensionsIcebergCompactionWithResponse(ctx, tenantID, req))
	if err != nil {
		return uuid.UUID{}, err
	}
	return resp.JSON202.WorkflowId, nil
}
