openapi: 3.0.3

info:
  title: v1
  version: 1.0-alpha

servers:
  - url: /api/v1 # baseURL for kin-openapi openapifilter (should be consistent with spec.GinServerOptions)

tags:
  - name: admin
    description: Account admin service
  - name: auth
    description: Authenticate with the server
  - name: user
    description: Access to user's profile
  - name: tenants
    description: Operations about tenants
  - name: regions
    description: Access to available regions
  - name: configs
    description: RisingWave instance configuration templates
  - name: notification
    description: Messages and alerts
  - name: invitation
    description: Access to org's invitations
  - name: service-account
    description: Access to org's service accounts
  - name: sso
    description: sso management and sso login

paths:
  /:
    x-internal: true
    get:
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
  /version:
    x-internal: true
    get:
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"

  /regions:
    get:
      tags:
        - regions
      description: "Get all the regions currently available. NOTE: the id of the same region is not guaranteed to be consistent on each call."
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                # TODO wrap schema by object
                $ref: "acc_resource.yaml#/components/schemas/RegionArray"

  /auth/ping:
    get:
      tags:
        - auth
      description: Test JWT token
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/ControlClaims"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /auth0/callback:
    x-internal: true
    get:
      tags:
        - auth
      description: "Auth0 callback api for code exchange"
      parameters:
        - in: query
          required: true
          name: code
          schema:
            type: string
        - in: query
          name: state
          required: true
          schema:
            type: string
        - in: query
          name: error
          schema:
            type: string
        - in: query
          name: error_description
          schema:
            type: string
      responses:
        "307":
          description: HTTP Temporary Redirect

  /auth0/login:
    x-internal: true
    get:
      tags:
        - auth
      description: "Redirect to auth0 login page"
      parameters:
        - in: query
          name: returnTo
          required: true
          schema:
            type: string
        - in: query
          name: method
          required: true
          schema:
            type: string
        - in: query
          name: invitationToken
          schema:
            type: string
        - in: query
          name: utmUrl
          schema:
            type: string
      responses:
        "307":
          description: HTTP Temporary Redirect

  /auth/register:
    x-internal: true
    post:
      tags:
        - auth
      requestBody:
        required: true
        description: "Register an new account with username, email and password"
        content:
          application/json:
            schema:
              type: object
              required: [email, username, password]
              properties:
                email:
                  type: string
                  example: <EMAIL>
                username:
                  type: string
                  example: risingwave
                password:
                  type: string
                  example: "***********"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "403":
          $ref: "response.yaml#/components/responses/ForbiddenResponse"
        "422":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      x-audit-nodata: true

  /auth/email-register:
    x-internal: true
    post:
      tags:
        - auth
      requestBody:
        required: true
        description: "Register an new account with username, email and password"
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/PostAuthEmailRegisterRequestBody"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_body.yaml#/components/schemas/PostAuthEmailRegisterResponseBody"
        "403":
          $ref: "response.yaml#/components/responses/ForbiddenResponse"
        "422":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      x-audit-nodata: true

  /auth/refresh:
    x-internal: true
    post:
      tags:
        - auth
      requestBody:
        required: true
        description: "Refresh the jwt token"
        content:
          application/json:
            schema:
              type: object
              required: [refreshToken, userResourceId]
              properties:
                refreshToken:
                  type: string
                userResourceId:
                  type: string
                  format: uuid
      responses:
        "200":
          description: Refreshed JWT
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/Tokens"
        "403":
          $ref: "response.yaml#/components/responses/ForbiddenResponse"
      x-audit-ignore: true

  /auth/login:
    x-internal: true
    post:
      tags:
        - auth
      requestBody:
        required: true
        description: "Sign in with email and password"
        content:
          application/json:
            schema:
              type: object
              required: [email, password]
              properties:
                email:
                  type: string
                password:
                  type: string
      responses:
        "200":
          description: Login Successfully
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/UserAuth"
        "403":
          $ref: "response.yaml#/components/responses/ForbiddenResponse"
      x-audit-nodata: true

  /auth/logout:
    x-internal: true
    get:
      tags:
        - auth
      description: "Sign out and revoke user's auth0 session"
      parameters:
        - in: query
          name: refreshToken
          schema:
            type: string
      responses:
        "307":
          description: Redirect to Auth0 logout endpoint

  /auth/forgot-password:
    x-internal: true
    post:
      tags:
        - auth
      requestBody:
        required: true
        description: "Request to reset password"
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/ForgotPasswordRequestBody"
      responses:
        "202":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      x-audit-nodata: true

  # TODO: get or post /tenants should return or create a batch of tenants
  /tenants:
    x-internal: true
    get:
      tags:
        - tenants
      description: Get all the tenants owned by the org
      x-required-permission: tenants.list
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/TenantSizePage"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant/{nsID}:
    get:
      tags:
        - tenant
      summary: get a tenant by nsID
      parameters:
        - in: path
          required: true
          name: nsID
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/Tenant"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /configs:
    x-internal: true
    get:
      tags:
        - configs
      description: Get all configuration templates
      x-required-permission: rwConfigs.list
      parameters:
        - in: query
          name: name
          schema:
            type: string
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/ConfigPage"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    post:
      tags:
        - configs
      description: create a new configuration template
      x-required-permission: rwConfigs.create
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [name, content]
              properties:
                name:
                  type: string
                  example: config-1-rc
                content:
                  type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/Config"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /config/{configId}:
    x-internal: true
    delete:
      tags:
        - configs
      description: create a new configuration template
      x-required-permission: rwConfigs.get
      parameters:
        - in: path
          required: true
          name: configId
          schema:
            type: string
            format: uuid
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /user:
    x-internal: true
    get:
      tags:
        - user
      description: Get user info by using jwt
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/User"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []

    put:
      tags:
        - user
      description: Updated user email, username, password by using jwt
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/PutUserRequestBody"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/UserAuth"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "422":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      x-audit-nodata: true
      security:
        - BearerAuth: []

    delete:
      tags:
        - user
      description: Delete user account with JWT token
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "403":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []

  /user/{id}:
    x-internal: true
    delete:
      tags:
        - user
      description: delete user in org by id using jwt
      x-required-permission: users.delete
      parameters:
        - in: path
          required: true
          name: id
          schema:
            type: string
            format: uuid
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "403":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /user/password:
    x-internal: true
    put:
      tags:
        - user
      description: Change password with the current password and new password and confirm
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/UpdatePasswordRequestBody"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "403":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      x-audit-nodata: true
      security:
        - BearerAuth: []

  /user/auth-type:
    x-internal: true
    put:
      tags:
        - user
      description: Change login method to local and send password reset email
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/UpdateAuthTypeRequestBody"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "403":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []

  /auth-type/verify:
    x-internal: true
    post:
      tags:
        - user
      description: Switch the login method of password user to social
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/PostUserAuthTypeRequestBody"
      responses:
        "200":
          description: Login Successfully
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/UserAuth"
        "400":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "404":
          $ref: "response.yaml#/components/responses/DefaultResponse"

  /users:
    x-internal: true
    get:
      tags:
        - user
      description: Get users owned by org
      x-required-permission: users.list
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_body.yaml#/components/schemas/GetUsersResponseBody"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /org:
    x-internal: true
    get:
      tags:
        - org
      description: Get organization information using JWT.
      x-required-permission: orgs.get
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/Org"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    put:
      tags:
        - org
      description: Updates an existing organisation using JWT.
      x-required-permission: orgs.update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_resource.yaml#/components/schemas/OrgInfo"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    delete:
      tags:
        - org
      description: Deletes an organisation.
      x-required-permission: orgs.delete
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "403":
          # Cannot delete organisation with users or tenants.
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /org/{orgId}/signup-info:
    x-internal: true
    post:
      tags:
        - org
      description: Updates an existing organisation's signup info using JWT.
      x-required-permission: orgs.update
      parameters:
        - in: path
          required: true
          name: orgId
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/PostOrgSignupInfoRequestBody"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /invitations:
    x-internal: true
    post:
      tags:
        - invitation
      description: Invite new user into target organization.
      x-required-permission: invitations.create
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/CreateInvitationRequestBody"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/Invitation"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "404":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    get:
      tags:
        - invitation
      description: Get all invitations owned by the org
      x-required-permission: invitations.list
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_body.yaml#/components/schemas/GetInvitationsResponseBody"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /invitation/{id}:
    x-internal: true
    get:
      tags:
        - invitation
      description: Get an invitation's detail by the invitation id
      x-required-permission: invitations.get
      parameters:
        - in: path
          required: true
          name: id
          schema:
            type: integer
            format: uint64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/Invitation"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "404":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    delete:
      tags:
        - invitation
      description: delete an invitation by id
      x-required-permission: invitations.delete
      parameters:
        - in: path
          required: true
          name: id
          schema:
            type: integer
            format: uint64
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "404":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /invitations/{id}/resend:
    x-internal: true
    post:
      tags:
        - invitation
      description: Resend an invitation.
      x-required-permission: invitations.resend
      parameters:
        - in: path
          required: true
          name: id
          schema:
            type: integer
            format: uint64
      responses:
        "200":
          description: OK
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "404":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /notifications:
    x-internal: true
    get:
      tags:
        - notification
      description: Get all the notifications
      x-required-permission: notifications.list
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
        - in: query
          name: order
          schema:
            type: string
            enum:
              - createdAtAsc
              - createdAtDesc
            default: createdAtDesc
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_body.yaml#/components/schemas/GetUserNotificationsResponseBody"
      security:
        - BearerAuth: []

  /notifications/statuses:
    x-internal: true
    put:
      tags:
        - notification
      description: Put read statuses of user's notifications (Bulk), read will be true
      x-required-permission: notifications.update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/PutNotificationsStatusRequestBody"
      responses:
        "200":
          description: OK
      security:
        - BearerAuth: []

  /invitation-codes/redemption:
    x-internal: true
    post:
      description: Redeem invitation code to customize tenant configuration
      tags:
        - invitation-codes
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/RedeemInvitationCodeRequestBody"
      responses:
        "200":
          description: OK
        "400":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /invitation-codes/permission:
    x-internal: true
    get:
      description: Check if the user's organization has permission to customize tenant configuration
      tags:
        - invitation-codes
      responses:
        "200":
          description: OK
        "400":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /service-accounts:
    x-internal: true
    get:
      tags:
        - service-account
      description: List service accounts by org
      x-required-permission: serviceAccounts.list
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_body.yaml#/components/schemas/GetServiceAccountsResponseBody"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    post:
      description: Create a service account
      x-required-permission: serviceAccounts.create
      tags:
        - service-account
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/PostServiceAccountRequestBody"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/ServiceAccount"
        "400":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /service-account/{id}:
    x-internal: true
    parameters:
      - in: path
        required: true
        name: id
        schema:
          type: string
          format: uuid
    get:
      tags:
        - service-account
      description: get service account by id
      x-required-permission: serviceAccounts.get
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/ServiceAccount"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "404":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    put:
      tags:
        - service-account
      description: Updated service account's description
      x-required-permission: serviceAccounts.update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/PutServiceAccountRequestBody"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/ServiceAccount"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "404":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    delete:
      tags:
        - service-account
      description: Delete service account by id
      x-required-permission: serviceAccounts.delete
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "404":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /api-keys:
    x-internal: true
    get:
      tags:
        - api-keys
      parameters:
        - in: query
          name: principal
          required: true
          schema:
            type: string
            format: uuid
      description: List api keys in org by principal
      x-required-permission: apiKeys.list
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_body.yaml#/components/schemas/GetApiKeysResponseBody"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    post:
      description: Create an api key
      x-required-permission: apiKeys.create
      tags:
        - api-key
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/PostApiKeyRequestBody"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/ApiKey"
        "400":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      x-audit-nodata: true
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /api-key/{id}:
    x-internal: true
    parameters:
      - in: path
        required: true
        name: id
        schema:
          type: integer
          format: uint64
    get:
      tags:
        - api-key
      description: get api key by id
      x-required-permission: apiKeys.get
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/ApiKey"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "404":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    put:
      tags:
        - api-key
      description: Updated api key's description
      x-required-permission: apiKeys.update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/PutApiKeyRequestBody"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/ApiKey"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "404":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      x-audit-nodata: true
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    delete:
      tags:
        - api-key
      description: Delete api key by id
      x-required-permission: apiKeys.delete
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "404":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /org/{orgId}/sso:
    x-internal: true
    parameters:
      - in: path
        required: true
        name: orgId
        schema:
          type: string
          format: uuid
    get:
      tags:
        - sso
      description: get sso config by org
      x-required-permission: ssoConfigs.get
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/SsoConfig"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "404":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    post:
      description: Create sso config for org
      x-allowed-org-tiers: Advanced
      x-required-permission: ssoConfigs.create
      tags:
        - sso
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/PostSsoConfigRequestBody"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/SsoConfig"
        "400":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "404":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      x-audit-nodata: true
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    delete:
      tags:
        - sso
      description: Delete sso config of org
      x-required-permission: ssoConfigs.delete
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "404":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    put:
      tags:
        - sso
      description: Updated sso config
      x-allowed-org-tiers: Advanced
      x-required-permission: ssoConfigs.update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/PutSsoConfigRequestBody"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/SsoConfig"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "404":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      x-audit-nodata: true
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /user/{email}/invitation/{token}:
    x-internal: true
    parameters:
      - in: path
        required: true
        name: email
        schema:
          type: string
      - in: path
        required: true
        name: token
        schema:
          type: string
    get:
      tags:
        - invitation
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "404":
          $ref: "response.yaml#/components/responses/DefaultResponse"
  /privatelinks:
    get:
      tags:
        - privatelinks
      description: Get all the private links owned by the user
      x-required-permission: privateLinks.list
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/PrivateLinkSizePage"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /login/sso:
    x-internal: true
    get:
      tags:
        - sso
      description: Redirect to the sso login page by user email
      parameters:
        - in: query
          required: true
          name: email
          schema:
            type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_body.yaml#/components/schemas/GetLoginSsoResponseBody"
        "404":
          $ref: "response.yaml#/components/responses/DefaultResponse"

  /roles:
    x-internal: true
    get:
      tags:
        - role
      description: List all available roles
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_body.yaml#/components/schemas/GetRolesResponseBody"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "403":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /jwks:
    x-internal: true
    get:
      description: Return the public jwk for jwt verification
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"

  /oauth/token:
    x-internal: true
    post:
      description: Return the jwt for data plane oauth access
      x-required-permission: tenants.accessWithOAuth
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_body.yaml#/components/schemas/ExchangeDataPlaneAccessTokenResponseBody"
      x-audit-ignore: true
      security:
        - BearerAuth: []

  /authorize:
    x-internal: true
    post:
      tags:
        - rbac
      summary: "Authorize with principal and permissions"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/PostAuthorizeRequestBody"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "403":
          $ref: "response.yaml#/components/responses/ForbiddenResponse"
      security:
        - BearerAuth: []

  /role-bindings:
    x-internal: true
    get:
      tags:
        - role
      parameters:
        - in: query
          name: principal
          schema:
            type: string
            format: uuid
      description: List all role bindings in a org
      x-required-permission: roleBindings.list
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_body.yaml#/components/schemas/GetRoleBindingsResponseBody"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "403":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    put:
      tags:
        - role
      description: create role bindings in a org
      x-required-permission: roleBindings.update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/PutRoleBindingRequestBody"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/RoleBinding"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "403":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

components:
  schemas:
  responses:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    ApiKeyAuth:
      type: http
      scheme: basic
      description: Service account api key. Header format 'Basic base64(${key}:${secret})'
