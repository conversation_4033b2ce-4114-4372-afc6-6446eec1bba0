openapi: 3.0.3

info:
  title: v1
  version: 1.0-alpha

servers:
  - url: /api/v1 # baseURL for kin-openapi openapifilter (should be consistent with spec.GinServerOptions)

tags:
  - name: admin
    description: Operations about admin token
  - name: regions
    description: Access to available regions
  - name: auth
    description: Authenticate with the server
  - name: tenant
    description: Operations about tenants
  - name: privatelink
    description: Operations about private links
  - name: email
    description: Operations about email
  - name: config
    description: RisingWave instance configuration templates
  - name: notification
    description: Messages and alerts

paths:
  /:
    get:
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
  /version:
    get:
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"

  /oauth/token:
    post:
      description: Return the jwt for data plane oauth access, at org user level
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/ExchangeDataPlaneAccessTokenAdminRequestBody"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_body.yaml#/components/schemas/ExchangeDataPlaneAccessTokenResponseBody"

  /oauth/internal/token:
    post:
      description: Return the jwt for internal RW oauth access, at org level
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/ExchangeInternalAccessTokenAdminRequestBody"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_body.yaml#/components/schemas/ExchangeInternalAccessTokenResponseBody"

  /regions:
    get:
      tags:
        - regions
      summary: Get all the regions currently available
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/Regions"
  /regions/all:
    get:
      tags:
        - regions
      summary: Get all the regions, including non-ready regions
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/Regions"
  /auth/ping:
    get:
      tags:
        - auth
      description: Test JWT token
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/ControlClaims"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
  /tenant:
    get:
      tags:
        - tenant
      summary: get a tenant by tenant-ID and region
      parameters:
        - in: query
          name: tenantId
          schema:
            type: integer
            format: uint64
        - in: query
          name: region
          schema:
            type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/Tenant"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
    post:
      tags:
        - tenant
      summary: "Sync tenant info"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/PostTenantRequestBody"
      responses:
        "202":
          $ref: "response.yaml#/components/responses/DefaultResponse"
    delete:
      tags:
        - tenant
      summary: "Sync tenant info"
      parameters:
        - in: query
          name: tenantId
          schema:
            type: integer
            format: uint64
        - in: query
          name: region
          schema:
            type: string
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
  /tenant/{nsID}:
    get:
      tags:
        - tenant
      summary: get a tenant by nsID
      parameters:
        - in: path
          required: true
          name: nsID
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/Tenant"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
  /tenant/availability:
    post:
      tags:
        - tenant
      summary: "Check tenant whether can be created"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_resource.yaml#/components/schemas/TenantProperties"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/AvailabilityResponse"
  /tenant/{nsId}/billable:
    put:
      tags:
        - tenant
      summary: "Marks a tenant as billable."
      parameters:
        - in: path
          required: true
          name: nsId
          schema:
            type: string
            format: uuid
        - in: query
          required: false
          name: since
          schema:
            type: string
            format: date-time
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"

  /tenant/{nsId}/scale:
    post:
      tags:
        - tenant
      description:
        Notifies Account service about scaling of a tenant and if that scaling
        allows to run the tenant as trial tenant.
      parameters:
        - in: path
          required: true
          name: nsId
          schema:
            type: string
            format: uuid
        - in: query
          required: true
          name: allows_trial
          schema:
            type: boolean
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
  /tenant/{nsId}/tier:
    post:
      tags:
        - tenant
      description: Update tenant tier
      parameters:
        - in: path
          required: true
          name: nsId
          schema:
            type: string
            format: uuid
        - in: query
          required: true
          name: tier_id
          schema:
            type: string
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"

  /tenant/{nsId}/resources:
    post:
      tags:
        - tenant
      parameters:
        - in: path
          required: true
          name: nsId
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/PostTenantResourceRequestBody"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/TenantResource"
        "409":
          $ref: "response.yaml#/components/responses/DefaultResponse"
  /tenant/{nsId}/resources/{resourceName}:
    delete:
      tags:
        - tenant
      parameters:
        - in: path
          required: true
          name: nsId
          schema:
            type: string
            format: uuid
        - in: path
          required: true
          name: resourceName
          schema:
            type: string
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "404":
          $ref: "response.yaml#/components/responses/DefaultResponse"

  /tenants/updated:
    get:
      tags:
        - tenant
      summary: get a list of tenants which have been updated since the given timestamp
      parameters:
        - in: query
          name: unix_ts
          schema:
            type: integer
            format: int64
          required: true
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
          required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/TenantSizePage"

  /email:
    post:
      tags:
        - email
      summary: "Send email by template"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/PostEmailRequestBody"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
  /email/raw:
    post:
      tags:
        - email
      summary: "Send email"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/PostEmailRawRequestBody"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"

  /config/{configId}:
    get:
      tags:
        - config
      summary: get a config
      parameters:
        - in: path
          required: true
          name: configId
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/Config"
  /notification:
    post:
      tags:
        - notification
      description: Post a notification
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/PostNotificationRequestBody"
      responses:
        "200":
          description: OK

  /users:
    get:
      tags:
        - admin
      description: Get all users
      parameters:
        - in: query
          name: name
          schema:
            type: string
          description: use params to filter users by username or email
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/UserPage"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"

  /orgs/updated:
    get:
      tags:
        - org
      description: Get a list of organisations which have been updated since the given timestamp.
      parameters:
        - in: query
          name: unix_ts
          schema:
            type: integer
            format: int64
          required: true
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
          required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/OrgPage"

  /orgs/update-payment-methods:
    put:
      tags:
        - org
      description: Updates the number of payment methods for the given organisations.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/PutOrgsUpdatePMsBody"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"

  /org/users:
    get:
      tags:
        - admin
        - org
      description: Get all the users in the organization
      parameters:
        - in: query
          name: params
          schema:
            type: string
          description: use params to filter users by username or email
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/UserPage"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"

  /org/{orgId}/clusters:
    parameters:
      - in: path
        required: true
        name: orgId
        schema:
          type: string
          format: uuid
    get:
      summary: Get clusters of an org
      tags:
        - cluster
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/ManagedClusters"

  /org/{orgId}/tier-info:
    parameters:
      - in: path
        required: true
        name: orgId
        schema:
          type: string
          format: uuid
    get:
      tags:
        - org
      description:
        Returns information about the customer's account tier including
        available tenant operations.
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/OrgTierInfo"

  /org/{orgId}/trial-before:
    parameters:
      - in: path
        required: true
        name: orgId
        schema:
          type: string
          format: uuid
    get:
      tags:
        - org
      description: Returns the timestamp when of the given org's trial period ends.
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/TrialBefore"
    put:
      tags:
        - org
      description:
        Sets the timestamp when of the given org's trial period ends. Fails if
        the given date is in the past. Returns the resulting timestamp.
      parameters:
        - in: query
          name: trialBefore
          schema:
            type: string
            format: date-time
          required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/TrialBefore"
    post:
      tags:
        - org
      description:
        Adds the given days to the given org's trial period. Fails if the
        resulting timestamp is in the past. Returns the resulting timestamp.
      parameters:
        - in: query
          name: daysAdded
          schema:
            type: integer
          required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/TrialBefore"

  /org/{orgId}/auto-invoice:
    parameters:
      - in: path
        required: true
        name: orgId
        schema:
          type: string
          format: uuid
    put:
      tags:
        - org
      description: Sets if the invoices for this customer are finalised automatically.
      parameters:
        - in: query
          name: autoInvoice
          schema:
            type: boolean
          required: true
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"

  /org/{orgId}/block-reasons:
    parameters:
      - in: path
        required: true
        name: orgId
        schema:
          type: string
          format: uuid
    get:
      summary: Lists all reasons that block creation and start of tenants.
      tags:
        - org
      responses:
        "200":
          description: List of all reasons.
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/BlockTenantsReasons"

  /org/{orgId}/block-reason/{reason}:
    parameters:
      - in: path
        required: true
        name: orgId
        schema:
          type: string
          format: uuid
      - in: path
        required: true
        name: reason
        schema:
          $ref: "acc_resource.yaml#/components/schemas/BlockTenantsReason"
    put:
      summary: Adds the reason for blocking provision and restart of tenants.
      tags:
        - org
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
    delete:
      summary: Deletes the reason for blocking provision and restart of tenants.
      tags:
        - org
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"

  /cluster/{clusterId}:
    parameters:
      - in: path
        required: true
        name: clusterId
        schema:
          type: string
          format: uuid
    put:
      summary: Create or update cluster
      tags:
        - cluster
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/PutClusterRequestBody"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "403":
          $ref: "response.yaml#/components/responses/ForbiddenResponse"
    delete:
      summary: Delete cluster
      tags:
        - cluster
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "403":
          $ref: "response.yaml#/components/responses/ForbiddenResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"

  /clusters:
    get:
      tags:
        - cluster
      summary: Gets a list of managed clusters which have been updated since the given timestamp.
      parameters:
        - in: query
          name: updated_since
          description:
            Limits the result to clusters which have been updated since the given Unix-timestamp (inclusive).
            Is set to epoch if omitted.
          schema:
            type: integer
            format: int64
          required: false
        - in: query
          name: offset
          schema:
            type: integer
            format: int32
          required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/ManagedClusterPage"

  /invitation-codes:
    get:
      description: Get all invitation codes
      tags:
        - invitation-codes
      responses:
        "200":
          description: Get all invitation codes
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/InvitationCodes"
    post:
      description: Create invitation codes
      tags:
        - invitation-codes
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/CreateInvitationCodeRequestBody"
      responses:
        "200":
          description: Create invitation code returning
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/PlainInvitationCodes"
    delete:
      description: Soft delete an invitation code by code id
      parameters:
        - in: query
          name: id
          required: true
          schema:
            type: integer
            format: uint64
      responses:
        "200":
          description: OK
  /privatelinks:
    post:
      tags:
        - privatelink
      summary: "Sync privatelink info"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_resource.yaml#/components/schemas/PrivateLink"
      responses:
        "202":
          $ref: "response.yaml#/components/responses/DefaultResponse"

  /privatelinks/{id}:
    parameters:
      - in: path
        required: true
        name: id
        schema:
          type: string
          format: uuid
    delete:
      tags:
        - privatelink
      summary: "Delete tenant info"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"

  /privatelinks/{id}/billable:
    put:
      tags:
        - privatelink
      summary: "Marks a PrivateLink as billable."
      parameters:
        - in: path
          required: true
          name: id
          schema:
            type: string
            format: uuid
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"

  /privatelink-allowed-targets:
    get:
      summary: "Get all allowed private link targets"
      tags:
        - cluster
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/PrivateLinkAllowedTargetSizePage"
    put:
      tags:
        - privatelink
      summary: "Create/update allowed private link target"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_resource.yaml#/components/schemas/PrivateLinkAllowedTarget"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"

  /privatelink-allowed-targets/{target}:
    parameters:
      - in: path
        required: true
        name: target
        schema:
          type: string
    get:
      tags:
        - privatelink
      summary: "Get allowed private link target"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/PrivateLinkAllowedTarget"
    delete:
      tags:
        - privatelink
      summary: "Delete allowed private link target"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"

  /authorize:
    post:
      tags:
        - rbac
      summary: "Authorize with principal and permissions"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/PostAuthorizeRequestBody"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "403":
          $ref: "response.yaml#/components/responses/ForbiddenResponse"

  /recipients:
    get:
      tags:
        - notification
      summary: Gets all the recipients of an org which tenant id belongs to.
      parameters:
        - in: query
          name: tenant_id
          schema:
            type: string
            format: uuid
          required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/RecipientArray"

  /subscriptions:
    get:
      tags:
        - notification
      summary: Gets all the subscriptions by tenant id.
      parameters:
        - in: query
          name: tenant_id
          schema:
            type: string
            format: uuid
          required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/SubscriptionArray"

components:
  schemas:
  responses:
