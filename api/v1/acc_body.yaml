openapi: 3.0.3

info:
  title: v1
  version: 1.0-alpha
paths: {}
components:
  schemas:
    ##############################
    # Auth
    ##############################
    PutUserRequestBody:
      type: object
      properties:
        username:
          type: string
        email:
          type: string
        password:
          type: string

    SignupRequestBody:
      type: object
      required: [email, code, username, password]
      properties:
        email:
          type: string
          example: <EMAIL>
        code:
          type: string
          example: email validation code
        username:
          type: string
          example: risingwave
        password:
          type: string
          example: "***********"

    PostAuthEmailRegisterRequestBody:
      type: object
      required: [email, username, password]
      properties:
        email:
          type: string
          example: <EMAIL>
        username:
          type: string
          example: risingwave
        password:
          type: string
          example: "***********"
        countryCode:
          type: string
        companyName:
          type: string
        invitationToken:
          type: string
        utmUrl:
          type: string
        useCaseDescription:
          type: string

    PostAuthEmailRegisterResponseBody:
      type: object
      required: [user, org]
      properties:
        user:
          $ref: "acc_resource.yaml#/components/schemas/User"
        org:
          $ref: "acc_resource.yaml#/components/schemas/OrgInfo"

    ForgotPasswordRequestBody:
      type: object
      required: [email]
      properties:
        email:
          type: string

    UpdatePasswordRequestBody:
      type: object
      required: [currentPassword, newPassword, newPasswordConfirm]
      properties:
        currentPassword:
          type: string
        newPassword:
          type: string
        newPasswordConfirm:
          type: string

    UpdateAuthTypeRequestBody:
      type: object
      required: [newAuthType]
      properties:
        newAuthType:
          $ref: "acc_resource.yaml#/components/schemas/AuthType"

    EmailValidationRequestBody:
      type: object
      required: [email]
      properties:
        email:
          type: string
          example: <EMAIL>

    PostUserAuthTypeRequestBody:
      type: object
      required: [email, password, authType, refreshToken]
      properties:
        email:
          type: string
          example: <EMAIL>
        password:
          type: string
        authType:
          $ref: "acc_resource.yaml#/components/schemas/AuthType"
        refreshToken:
          type: string

    PostAuthorizeRequestBody:
      type: object
      required: [resourceType, resourceName, permissions, principal, orgId]
      properties:
        resourceType:
          type: string
        resourceName:
          type: string
        permissions:
          type: array
          items:
            type: string
        principal:
          type: string
          format: uuid
        orgId:
          type: string
          format: uuid

    ExchangeDataPlaneAccessTokenAdminRequestBody:
      type: object
      required: [userResourceId, orgId]
      properties:
        userResourceId:
          type: string
          format: uuid
        orgId:
          type: string
          format: uuid

    ExchangeDataPlaneAccessTokenResponseBody:
      type: object
      required: [token]
      properties:
        token:
          type: string

    ExchangeInternalAccessTokenAdminRequestBody:
      type: object
      required: [orgId]
      properties:
        orgId:
          type: string
          format: uuid

    ExchangeInternalAccessTokenResponseBody:
      type: object
      required: [token]
      properties:
        token:
          type: string

    ##############################
    # Org
    ##############################

    PostOrgSignupInfoRequestBody:
      type: object
      required: [company_name, country_code]
      properties:
        company_name:
          type: string
        country_code:
          type: string

    OrgPMUpdate:
      type: object
      required: [org_id, pm_count]
      properties:
        org_id:
          type: string
          format: uuid
        pm_count:
          type: integer
          format: int16

    PutOrgsUpdatePMsBody:
      type: object
      required: [updates, reset]
      properties:
        reset:
          type: boolean
        updates:
          type: array
          items:
            $ref: "#/components/schemas/OrgPMUpdate"

    ##############################
    # Tenant
    ##############################

    PostTenantRequestBody:
      allOf:
        - $ref: "acc_resource.yaml#/components/schemas/TenantProperties"
        - type: object
          required: [as_trial]
          properties:
            as_trial:
              type: boolean

    ##############################
    # Managed Cluster
    ##############################
    PutClusterRequestBody:
      $ref: "acc_resource.yaml#/components/schemas/ManagedCluster"

    ##############################
    # Notification
    ##############################
    PutNotificationsStatusRequestBody:
      type: object
      required: [id, status]
      properties:
        id:
          type: array
          items:
            type: integer
            format: int32
        status:
          type: string
          enum:
            - Deleted
            - Unread
            - Read

    GetUserNotificationsResponseBody:
      type: object
      required: [limit, offset, size, unreadNum, notifications]
      properties:
        limit:
          type: integer
          format: uint32
        offset:
          type: integer
          format: uint32
        unreadNum:
          type: integer
          format: uint64
        size:
          type: integer
          format: uint64
        notifications:
          $ref: "acc_resource.yaml#/components/schemas/NotificationArray"

    PostNotificationRequestBody:
      type: object
      required: [sender, toUserId, title, content, messageType]
      properties:
        sender:
          $ref: "acc_resource.yaml#/components/schemas/NotificationSender"
        toOrgId:
          type: array
          items:
            type: string
            format: uuid
        toUserId:
          type: array
          items:
            type: string
            format: uuid
        title:
          type: string
        content:
          type: string
        messageType:
          type: string
          enum:
            - Alert
            - Message
            - Notification
    PostTenantResourceRequestBody:
      type: object
      required: [resourceName, rwu_milli]
      properties:
        resourceName:
          type: string
        rwu_milli:
          type: integer
          format: int64

    PostEmailRequestBody:
      type: object
      required: [title, template, data]
      properties:
        userResourceIds:
          type: array
          items:
            type: string
            format: uuid
        userResourceId:
          type: string
          format: uuid
        orgId:
          type: string
          format: uuid
        email:
          type: string
        title:
          type: string
        template:
          type: string
        data:
          type: object
    PostEmailRawRequestBody:
      type: object
      required: [title, body]
      properties:
        userResourceId:
          type: string
          format: uuid
        orgId:
          type: string
          format: uuid
        email:
          type: string
        title:
          type: string
        body:
          type: string

    ##############################
    # InvitationCode
    ##############################
    RedeemInvitationCodeRequestBody:
      type: object
      required:
        - code
      properties:
        code:
          type: string

    CreateInvitationCodeRequestBody:
      type: object
      required:
        - number
      properties:
        number:
          type: integer

    ##############################
    # ServiceAccount
    ##############################
    PutServiceAccountRequestBody:
      type: object
      required:
        - description
      properties:
        description:
          type: string
    PostServiceAccountRequestBody:
      type: object
      required:
        - name
        - description
      properties:
        description:
          type: string
        name:
          type: string
        roleId:
          type: string
          format: uuid
    GetServiceAccountsResponseBody:
      type: object
      required: [service_accounts]
      properties:
        service_accounts:
          $ref: "acc_resource.yaml#/components/schemas/ServiceAccountWithRolesArray"

    ##############################
    # Role
    ##############################
    GetRolesResponseBody:
      type: object
      required: [roles]
      properties:
        roles:
          $ref: "acc_resource.yaml#/components/schemas/RoleArray"

    GetRoleBindingsResponseBody:
      type: object
      required: [role_bindings]
      properties:
        role_bindings:
          $ref: "acc_resource.yaml#/components/schemas/RoleBindingArray"

    PutRoleBindingRequestBody:
      type: object
      required:
        - principal
        - roleIds
        - principalType
      properties:
        principal:
          type: string
          format: uuid
        roleIds:
          type: array
          items:
            type: string
            format: uuid
        principalType:
          type: string

    ##############################
    # Invitation
    ##############################
    CreateInvitationRequestBody:
      type: object
      required:
        - email
      properties:
        email:
          type: string
        roleId:
          type: string
          format: uuid
    GetInvitationsResponseBody:
      type: object
      required: [invitations]
      properties:
        invitations:
          $ref: "acc_resource.yaml#/components/schemas/InvitationArray"

    ##############################
    # SsoConfig
    ##############################
    PutSsoConfigRequestBody:
      type: object
      required:
        [
          protocolBinding,
          signatureAlgorithm,
          signInEndpoint,
          signingCert,
          active,
        ]
      properties:
        protocolBinding:
          type: string
        signatureAlgorithm:
          type: string
        signInEndpoint:
          type: string
        signingCert:
          type: string
        active:
          type: boolean

    PostSsoConfigRequestBody:
      type: object
      required:
        [
          protocolBinding,
          signatureAlgorithm,
          signInEndpoint,
          signingCert,
          active,
          name,
        ]
      properties:
        protocolBinding:
          type: string
        signatureAlgorithm:
          type: string
        signInEndpoint:
          type: string
        signingCert:
          type: string
        active:
          type: boolean
        name:
          type: string

    GetLoginSsoResponseBody:
      type: object
      required: [ssoName]
      properties:
        ssoName:
          type: string

    ##############################
    # ApiKey
    ##############################
    PutApiKeyRequestBody:
      type: object
      required:
        - description
      properties:
        description:
          type: string
    PostApiKeyRequestBody:
      type: object
      required:
        - description
        - principal
      properties:
        description:
          type: string
        principal:
          type: string
          format: uuid
    GetApiKeysResponseBody:
      type: object
      required: [api_keys]
      properties:
        api_keys:
          $ref: "acc_resource.yaml#/components/schemas/ApiKeyArray"

    ##############################
    # User
    ##############################
    GetUsersResponseBody:
      type: object
      required:
        - users
      properties:
        users:
          $ref: "acc_resource.yaml#/components/schemas/UserWithRolesArray"
