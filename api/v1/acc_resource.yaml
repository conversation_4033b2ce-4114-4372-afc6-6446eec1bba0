openapi: 3.0.3

info:
  title: v1
  version: 1.0-alpha
paths: {}
components:
  schemas:
    ##############################
    # Region
    ##############################
    Region:
      type: object
      required:
        [
          id,
          regionName,
          isRegionReady,
          isBYOCOnly,
          platform,
          url,
          urlV2,
          adminUrl,
          pgwebUrl,
        ]
      properties:
        id:
          type: integer
          format: uint64
        regionName:
          type: string
        isRegionReady:
          type: boolean
        isBYOCOnly:
          type: boolean
        platform:
          type: string
        url:
          type: string
        urlV2:
          type: string
        adminUrl:
          type: string
        pgwebUrl:
          type: string
    RegionArray:
      type: array
      items:
        $ref: "#/components/schemas/Region"
    Regions:
      type: object
      required: [regions]
      properties:
        regions:
          $ref: "#/components/schemas/RegionArray"

    ##############################
    # User
    ##############################
    UserPage:
      type: object
      allOf:
        - $ref: "schema.yaml#/components/schemas/Page"
        - type: object
          required: [users]
          properties:
            users:
              $ref: "#/components/schemas/UserArray"
    User:
      type: object
      required: [id, username, email, org, authType, createdAt, resourceId]
      properties:
        id:
          type: integer
          format: uint64
        username:
          type: string
        email:
          type: string
        org:
          type: string
          format: uuid
        createdAt:
          type: string
          format: date-time
        lastLoginAt:
          type: string
          format: date-time
        authType:
          $ref: "acc_resource.yaml#/components/schemas/AuthType"
        resourceId:
          type: string
          format: uuid
        roles:
          $ref: "acc_resource.yaml#/components/schemas/RoleArray"
    UserArray:
      type: array
      items:
        $ref: "#/components/schemas/User"
    UserWithRoles:
      type: object
      required: [id, username, email, authType, createdAt, resourceId, roles]
      properties:
        id:
          type: integer
          format: uint64
        username:
          type: string
        email:
          type: string
        createdAt:
          type: string
          format: date-time
        lastLoginAt:
          type: string
          format: date-time
        authType:
          $ref: "acc_resource.yaml#/components/schemas/AuthType"
        resourceId:
          type: string
          format: uuid
        roles:
          type: array
          items:
            type: string
    UserWithRolesArray:
      type: array
      items:
        $ref: "#/components/schemas/UserWithRoles"

    ##############################
    # Invitation
    ##############################
    Invitation:
      type: object
      required:
        [id, email, orgId, name, createdAt, expiresAt, updatedAt, roleId]
      properties:
        id:
          type: integer
          format: uint64
        name:
          type: string
        email:
          type: string
        orgId:
          type: string
        createdAt:
          type: string
          format: date-time
        expiresAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        roleId:
          type: string
          format: uuid
    InvitationArray:
      type: array
      items:
        $ref: "#/components/schemas/Invitation"

    ##############################
    # Auth
    ##############################
    ControlClaims:
      type: object
      required: [claimsType, claims]
      properties:
        claimsType:
          type: string
          enum: [User, ServiceAccount]
        claims:
          oneOf:
            - $ref: "#/components/schemas/UserControlClaims"
            - $ref: "#/components/schemas/ServiceAccountClaims"
    UserControlClaims:
      type: object
      required: [email, username, userResourceId, orgId]
      properties:
        email:
          type: string
        username:
          type: string
        userResourceId:
          type: string
          format: uuid
        orgId:
          type: string
          format: uuid
    ServiceAccountClaims:
      type: object
      required: [serviceAccount, serviceAccountId, orgId]
      properties:
        serviceAccount:
          type: string
        serviceAccountId:
          type: string
          format: uuid
        orgId:
          type: string
          format: uuid

    Tokens:
      type: object
      properties:
        jwt:
          type: string
        refresh:
          type: string
    UserAuth:
      type: object
      allOf:
        - $ref: "#/components/schemas/User"
        - type: object
          required: [tokens]
          properties:
            tokens:
              $ref: "#/components/schemas/Tokens"

    SignUpCodeToken:
      type: object
      required: [token]
      properties:
        token:
          type: string

    ##############################
    # Tenant
    ##############################
    TenantProperties:
      type: object
      required: [id, userId, tenantName, tierId, region, orgId, nsId]
      properties:
        id:
          type: integer
          format: uint64
        userId:
          type: integer
          format: uint64
        tenantName:
          type: string
        region:
          type: string
        tierId:
          $ref: "mgmt_resource.yaml#/components/schemas/TierId"
        orgId:
          type: string
          format: uuid
        nsId:
          type: string
          format: uuid
    Tenant:
      type: object
      allOf:
        - $ref: "#/components/schemas/TenantProperties"
        - type: object
          required: [createdAt, updatedAt, trialBefore]
          properties:
            createdAt:
              type: string
              format: date-time
            updatedAt:
              type: string
              format: date-time
            trialBefore:
              type: string
              format: date-time
            billableSince:
              x-internal: true
              type: string
              format: date-time
            deactivatedAt:
              type: string
              format: date-time
    TenantArray:
      type: array
      items:
        $ref: "#/components/schemas/Tenant"
    Tenants:
      type: object
      required: [tenants]
      properties:
        tenants:
          $ref: "#/components/schemas/TenantArray"
    TenantSizePage:
      type: object
      allOf:
        - $ref: "schema.yaml#/components/schemas/Page"
        - $ref: "schema.yaml#/components/schemas/Size"
        - $ref: "#/components/schemas/Tenants"

    TenantResource:
      type: object
      required: [nsId, resourceName, rwu_milli]
      properties:
        nsId:
          type: string
          format: uuid
        resourceName:
          type: string
        rwu_milli:
          type: integer
          format: int64

    Config:
      x-internal: true
      type: object
      required: [id, userId, name, content, orgId]
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
          example: config-1-rc
        content:
          type: string
        userId:
          type: integer
          format: uint64
        orgId:
          type: string
          format: uuid
    ConfigArray:
      x-internal: true
      type: array
      items:
        $ref: "#/components/schemas/Config"
    ConfigPage:
      x-internal: true
      type: object
      allOf:
        - $ref: "schema.yaml#/components/schemas/Page"
        - type: object
          required: [configs]
          properties:
            configs:
              $ref: "#/components/schemas/ConfigArray"

    TenantNamespace:
      # {"region": "local", "namespace": "rwc-123-tenant"}]
      type: object
      required: [region, namespace]
      properties:
        region:
          type: string
        namespace:
          type: string

    ##############################
    # Private Link
    ##############################
    PrivateLink:
      type: object
      required: [id, tenantId, tenantNsId, orgId, connectionName, region]
      properties:
        connectionName:
          type: string
        id:
          type: string
          format: uuid
        tenantId:
          type: integer
          format: uint64
        tenantNsId:
          type: string
          format: uuid
        orgId:
          type: string
          format: uuid
        region:
          type: string
        target:
          type: string
    PrivateLinkArray:
      type: array
      items:
        $ref: "#/components/schemas/PrivateLink"
    PrivateLinkSizePage:
      type: object
      allOf:
        - $ref: "schema.yaml#/components/schemas/Page"
        - $ref: "schema.yaml#/components/schemas/Size"
        - type: object
          required: [privateLinks]
          properties:
            privateLinks:
              $ref: "#/components/schemas/PrivateLinkArray"
    PrivateLinkAllowedTarget:
      type: object
      required: [target, orgId]
      properties:
        target:
          type: string
        orgId:
          type: string
          format: uuid
    PrivateLinkAllowedTargetArray:
      type: array
      items:
        $ref: "#/components/schemas/PrivateLinkAllowedTarget"
    PrivateLinkAllowedTargetSizePage:
      type: object
      allOf:
        - $ref: "schema.yaml#/components/schemas/Page"
        - $ref: "schema.yaml#/components/schemas/Size"
        - type: object
          required: [privateLinkAllowedTargets]
          properties:
            privateLinkAllowedTargets:
              $ref: "#/components/schemas/PrivateLinkAllowedTargetArray"

    ##############################
    # Managed Cluster
    ##############################
    ManagedCluster:
      type: object
      required: [uuid, region, org, name, serving_type, created_at]
      properties:
        uuid:
          type: string
          format: uuid
        region:
          type: string
        org:
          type: string
          format: uuid
        name:
          type: string
        serving_type:
          type: string
        created_at:
          type: string
          format: date-time
        deleted_at:
          type: string
          format: date-time
    ManagedClusterArray:
      type: array
      items:
        $ref: "#/components/schemas/ManagedCluster"
    ManagedClusters:
      type: object
      required: [clusters]
      properties:
        clusters:
          $ref: "#/components/schemas/ManagedClusterArray"
    ManagedClusterPage:
      type: object
      allOf:
        - $ref: "schema.yaml#/components/schemas/Page"
        - $ref: "#/components/schemas/ManagedClusters"

    ##############################
    # Notification
    ##############################
    Notification:
      type: object
      required: [id, time, type, status, sender, title, content]
      properties:
        id:
          type: integer
          format: uint64
        time:
          type: string
          format: date-time
        type:
          type: string
          enum:
            - Alert
            - Message
            - Notification
        status:
          type: string
          enum:
            - Deleted
            - Unread
            - Read
        sender:
          $ref: "#/components/schemas/NotificationSender"
        content:
          type: string
        title:
          type: string
    NotificationArray:
      type: array
      items:
        $ref: "#/components/schemas/Notification"

    NotificationSender:
      type: object
      required: [name, email]
      properties:
        name:
          type: string
        email:
          type: string

    Subscription:
      type: object
      required: [id, recipientId, severity]
      properties:
        id:
          type: string
          format: uuid
        recipientId:
          type: string
          format: uuid
        severity:
          $ref: "#/components/schemas/AlertSeverity"
    SubscriptionArray:
      type: array
      items:
        $ref: "#/components/schemas/Subscription"
    Recipient:
      type: object
      required: [id, config, orgId, createdAt, updatedAt]
      properties:
        id:
          type: string
          format: uuid
        config:
          $ref: "#/components/schemas/RecipientConfig"
        orgId:
          type: string
          format: uuid
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    RecipientConfig:
      discriminator:
        propertyName: type
        mapping:
          email: "#/components/schemas/EmailRecipientConfig"
          user: "#/components/schemas/UserRecipientConfig"
          slack: "#/components/schemas/SlackRecipientConfig"
      oneOf:
        - $ref: "#/components/schemas/EmailRecipientConfig"
        - $ref: "#/components/schemas/UserRecipientConfig"
        - $ref: "#/components/schemas/SlackRecipientConfig"
    RecipientArray:
      type: array
      items:
        $ref: "#/components/schemas/Recipient"
    EmailRecipientConfig:
      type: object
      required: [type, email]
      properties:
        type:
          $ref: "#/components/schemas/RecipientType"
        email:
          type: string
    UserRecipientConfig:
      type: object
      required: [type, userId]
      properties:
        type:
          $ref: "#/components/schemas/RecipientType"
        userId:
          type: string
          format: uuid
    SlackRecipientConfig:
      type: object
      required: [type, webhookURL]
      properties:
        type:
          $ref: "#/components/schemas/RecipientType"
        webhookURL:
          type: string
    RecipientType:
      type: string
      enum:
        - email
        - slack
        - user
    AlertSeverity:
      type: string
      enum:
        - critical
        - warning

    ##############################
    # Organization
    ##############################
    OrgInfo:
      type: object
      required: [id]
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string

    Org:
      type: object
      required:
        [
          id,
          name,
          autoInvoice,
          updatedAt,
          hasInvitationCode,
          trialBefore,
          trialDays,
          pmCount,
        ]
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        autoInvoice:
          type: boolean
        updatedAt:
          type: string
          format: date-time
        deactivatedAt:
          type: string
          format: date-time
        hasInvitationCode:
          type: boolean
        trialBefore:
          type: string
          format: date-time
        trialDays:
          type: integer
          format: uint64
        pmCount:
          type: integer
          format: uint16

    OrgArray:
      type: array
      items:
        $ref: "#/components/schemas/Org"

    OrgPage:
      type: object
      allOf:
        - $ref: "schema.yaml#/components/schemas/Page"
        - type: object
          required: [orgs]
          properties:
            orgs:
              $ref: "#/components/schemas/OrgArray"

    BlockTenantsReason:
      type: string
      enum:
        - Admin
        - Billing

    BlockTenantsReasonArray:
      type: array
      items:
        $ref: "#/components/schemas/BlockTenantsReason"

    BlockTenantsReasons:
      type: object
      required: [reasons]
      properties:
        reasons:
          $ref: "#/components/schemas/BlockTenantsReasonArray"

    OrgTierInfo:
      type: object
      required:
        [
          has_payment_method,
          has_invitation_code,
          during_trial,
          has_trial_tenant,
          has_blockers,
        ]
      properties:
        has_payment_method:
          type: boolean
        has_invitation_code:
          type: boolean
        during_trial:
          type: boolean
        has_trial_tenant:
          type: boolean
        has_blockers:
          type: boolean

    TrialBefore:
      type: object
      required: [trial_before]
      properties:
        trial_before:
          type: string
          format: date-time

    ##############################
    # InvitationCode
    ##############################
    InvitationCode:
      type: object
      required: [id, code, status]
      properties:
        id:
          type: integer
          format: uint64
        code:
          type: string
        org_id:
          type: string
          format: uuid
        user_id:
          type: integer
          format: uint64
        status:
          type: string
          enum:
            - Used
            - Unused
            - Deleted
        created_at:
          type: string
          format: date-time
        redeemed_at:
          type: string
          format: date-time
    InvitationCodeArray:
      type: array
      items:
        $ref: "#/components/schemas/InvitationCode"
    InvitationCodes:
      type: object
      required: [invitation_codes]
      properties:
        invitation_codes:
          $ref: "acc_resource.yaml#/components/schemas/InvitationCodeArray"
    PlainInvitationCodes:
      type: object
      required: [invitation_codes]
      properties:
        invitation_codes:
          type: array
          items:
            type: string
    AuthType:
      type: string
      enum:
        - google-oauth2
        - github
        - windowslive
        - local
        - sso
    ServiceAccount:
      type: object
      required:
        [id, name, orgId, createdAt, updatedAt, description, apiKeyCount]
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        orgId:
          type: string
          format: uuid
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        description:
          type: string
        apiKeyCount:
          type: integer
          format: uint64
    ServiceAccountWithRoles:
      type: object
      required:
        [id, name, orgId, createdAt, updatedAt, description, apiKeyCount, roles]
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        orgId:
          type: string
          format: uuid
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        description:
          type: string
        apiKeyCount:
          type: integer
          format: uint64
        roles:
          type: array
          items:
            type: string
          example: ["OrganizationMember", "OrganizationAdmin"]
    ServiceAccountArray:
      type: array
      items:
        $ref: "#/components/schemas/ServiceAccount"
    ServiceAccountWithRolesArray:
      type: array
      items:
        $ref: "#/components/schemas/ServiceAccountWithRoles"
    SsoConfig:
      type: object
      required:
        [
          name,
          protocolBinding,
          signatureAlgorithm,
          signInEndpoint,
          signingCert,
          active,
          certSubject,
          certExpiresAt,
          acsUrl,
          entityId,
        ]
      properties:
        name:
          type: string
        protocolBinding:
          type: string
        signatureAlgorithm:
          type: string
        signInEndpoint:
          type: string
        signingCert:
          type: string
        active:
          type: boolean
        certSubject:
          type: string
        certExpiresAt:
          type: string
          format: date-time
        acsUrl:
          type: string
        entityId:
          type: string
    ApiKey:
      type: object
      required: [id, principal, key, createdAt, updatedAt, description, secret]
      properties:
        id:
          type: integer
          format: uint64
        principal:
          type: string
          format: uuid
        key:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        description:
          type: string
        secret:
          type: string
    ApiKeyArray:
      type: array
      items:
        $ref: "#/components/schemas/ApiKey"
    Role:
      type: object
      required: [id, name, description]
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        description:
          type: string
    RoleArray:
      type: array
      items:
        $ref: "#/components/schemas/Role"
    RoleBinding:
      type: object
      required: [principal, roleId, roleName]
      properties:
        principal:
          type: string
          format: uuid
        roleName:
          type: string
        roleId:
          type: string
          format: uuid
        principalType:
          type: string
    RoleBindingArray:
      type: array
      items:
        $ref: "#/components/schemas/RoleBinding"
