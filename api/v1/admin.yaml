openapi: 3.0.3

info:
  title: Admin Service Api
  description: Admin Service Api
  version: 1.0-alpha

servers:
  - url: /api/v1

tags:
  - name: default
  - name: cloudAdmins
  - name: tenants
  - name: tenants.diagnosis
  - name: tenants.extensions
  - name: tenants.snapshots
  - name: tenants.resourceGroups
  - name: workflows
  - name: k8sClusters
  - name: byocClusters
  - name: users
  - name: orgs
  - name: billing
  - name: invitationCodes

paths:
  /:
    get:
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
  /version:
    get:
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"

  /org:
    post:
      tags:
        - orgs
      description: Creates an organisation with the given Stripe ID.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "billing_resource.yaml#/components/schemas/StripeOrg"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]

  /org/{orgId}/skip-invoicing:
    parameters:
      - in: path
        name: orgId
        schema:
          type: string
          format: uuid

    get:
      security:
        - UserKeyAuth: ["Role:Viewer"]
      tags:
        - orgs
      description: Returns whether the organisation with the given ID is set to skip invoicing.
      responses:
        "200":
          description: "Indicates whether the organisation is set to skip invoicing."
          content:
            application/json:
              schema:
                $ref: "billing_resource.yaml#/components/schemas/GetOrgSkipInvoicingResult"
    put:
      security:
        - UserKeyAuth: ["Role:Editor"]
      tags:
        - orgs
      description: Sets whether the organisation with the given ID is set to skip invoicing.
      parameters:
        - in: query
          name: skip
          description: "Whether to set the organisation to skip invoicing."
          required: true
          schema:
            type: boolean
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"

  /org/{orgId}/trial-before:
    parameters:
      - in: path
        name: orgId
        schema:
          type: string
          format: uuid
        required: true
    get:
      tags:
        - orgs
      description: Returns the timestamp when of the given org's trial period ends.
      responses:
        "200":
          description: "trial before"
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/TrialBefore"
      security:
        - UserKeyAuth: ["Role:Viewer"]
    put:
      tags:
        - orgs
      description:
        Sets the timestamp when of the given org's trial period ends. Fails if
        the given date is in the past. Returns the resulting timestamp.
      parameters:
        - in: query
          name: trialBefore
          schema:
            type: string
            format: date-time
          required: true
      responses:
        "200":
          description: "trial before"
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/TrialBefore"
      security:
        - UserKeyAuth: ["Role:Editor"]
    post:
      tags:
        - orgs
      description:
        Adds the given days to the given org's trial period. Fails if the
        resulting timestamp is in the past. Returns the resulting timestamp.
      parameters:
        - in: query
          name: daysAdded
          schema:
            type: integer
          required: true
      responses:
        "200":
          description: "trial before"
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/TrialBefore"
      security:
        - UserKeyAuth: ["Role:Editor"]

  /org/{orgId}/auto-invoice:
    parameters:
      - in: path
        name: orgId
        schema:
          type: string
          format: uuid
        required: true
    put:
      tags:
        - orgs
      description: Sets if the invoices for this customer are finalised automatically.
      parameters:
        - in: query
          name: autoInvoice
          schema:
            type: boolean
          required: true
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]

  /regions:
    get:
      summary: Get all regions
      tags:
        - regions
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/Regions"
      security:
        - UserKeyAuth: ["Role:Viewer"]
  /region/{region}/tenant/{tenantId}:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    get:
      summary: Get a tenant
      tags:
        - tenants
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/Tenant"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - UserKeyAuth: ["Role:Viewer"]
    delete:
      summary: Delete a tenant
      tags:
        - tenants
      responses:
        "202":
          $ref: "response.yaml#/components/responses/WorkflowIdResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]
  /region/{region}/tenants:
    get:
      summary: Get all the tenants in the a region
      tags:
        - tenants
      parameters:
        - in: path
          name: region
          schema:
            type: string
          required: true
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/TenantPage"
      security:
        - UserKeyAuth: ["Role:Viewer"]
  /region/{region}/tenant/{tenantId}/expire:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    get:
      summary: Get the expire information
      tags:
        - tenants
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/TenantExpireInfo"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - UserKeyAuth: ["Role:Viewer"]
    post:
      summary: Update expiration lifecycle
      description: Update the expire time and delete time
      tags:
        - tenants
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostTenantExpireRequestBody"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]
        - UserKeyAuth: ["Role:TenantEditor"]
    delete:
      summary: Cancel the expire workflow
      description: The tenant will never expire
      tags:
        - tenants
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]
        - UserKeyAuth: ["Role:TenantEditor"]
  /region/{region}/tenant/{tenantId}/stop:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    post:
      tags:
        - tenants
      summary: Stop the tenant
      responses:
        "202":
          $ref: "response.yaml#/components/responses/WorkflowIdResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]
        - UserKeyAuth: ["Role:TenantEditor"]
  /region/{region}/tenant/{tenantId}/start:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    post:
      tags:
        - tenants
      summary: Start the tenant
      responses:
        "202":
          $ref: "response.yaml#/components/responses/WorkflowIdResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]
        - UserKeyAuth: ["Role:TenantEditor"]
  /region/{region}/tenant/{tenantId}/restart:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    post:
      tags:
        - tenants
      summary: Restart the tenant
      responses:
        "202":
          $ref: "response.yaml#/components/responses/WorkflowIdResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]
        - UserKeyAuth: ["Role:TenantEditor"]
  /region/{region}/tenant/{tenantId}/updateVersion:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    post:
      tags:
        - tenants
      summary: Update the tenant version
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostTenantUpdateVersionRequestBody"
      responses:
        "202":
          $ref: "response.yaml#/components/responses/WorkflowIdResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]
        - UserKeyAuth: ["Role:TenantEditor"]
  /region/{region}/tenant/{tenantId}/config/risingwave:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
      - in: query
        name: component
        schema:
          type: string
      - in: query
        name: nodeGroup
        schema:
          type: string
    put:
      tags:
        - tenants
      summary: Update the tenant config
      requestBody:
        required: true
        content:
          text/plain:
            schema:
              type: string
            example: "[storage]\nmeta_cache_capacity_mb = 1\n[system]\nsstable_size_mb = 1\n"
      responses:
        "202":
          $ref: "response.yaml#/components/responses/WorkflowIdResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]
        - UserKeyAuth: ["Role:TenantEditor"]
  /region/{region}/tenant/{tenantId}/configNoRestart/risingwave:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
      - in: query
        name: component
        schema:
          type: string
      - in: query
        name: nodeGroup
        schema:
          type: string
    put:
      tags:
        - tenants
      summary: Update the tenant config
      requestBody:
        required: true
        content:
          text/plain:
            schema:
              type: string
            example: "[storage]\nmeta_cache_capacity_mb = 1\n[system]\nsstable_size_mb = 1\n"
      responses:
        "202":
          $ref: "response.yaml#/components/responses/WorkflowIdResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]
        - UserKeyAuth: ["Role:TenantEditor"]
  /region/{region}/tenant/{tenantId}/config/etcd:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    put:
      tags:
        - tenants
      summary: Update the tenant config
      requestBody:
        required: true
        content:
          text/plain:
            schema:
              type: string
            example: "ETCD_QUOTA_BACKEND_BYTES: '1000000000'\nETCD_MAX_REQUEST_BYTES: '100000000'\n"
      responses:
        "202":
          $ref: "response.yaml#/components/responses/WorkflowIdResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]
        - UserKeyAuth: ["Role:TenantEditor"]
  /region/{region}/tenant/{tenantId}/resource:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    post:
      tags:
        - tenants
      summary: Update a tenant resource
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostTenantResourcesRequestBody"
      responses:
        "202":
          $ref: "response.yaml#/components/responses/WorkflowIdResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]
        - UserKeyAuth: ["Role:TenantEditor"]
  /region/{region}/tenant/{tenantId}/status:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    post:
      tags:
        - tenants
      summary: Reset tenant status
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostTenantStatusRequestBody"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]
        - UserKeyAuth: ["Role:TenantEditor"]
  /region/{region}/tenant/{tenantId}/tier:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    post:
      tags:
        - tenants
      summary: Update tenant tier
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostTenantTierRequestBody"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]
        - UserKeyAuth: ["Role:TenantEditor"]

  /region/{region}/tenant/{tenantId}/endpoint:
    get:
      summary: Get the root endpoint of the specified tenant from the given region
      tags:
        - tenants
      parameters:
        - in: path
          name: region
          schema:
            type: string
          required: true
        - in: path
          name: tenantId
          schema:
            type: integer
            format: uint64
          required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "mgmt_resource.yaml#/components/schemas/DatabaseConnectionUrl"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - UserKeyAuth: ["Role:Admin"] # must be an admin to connect to the database

  /region/{region}/tenant/{tenantId}/migrateMetaStore:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    post:
      tags:
        - tenants
      summary: Migrate the risingwave meta store
      responses:
        "202":
          $ref: "response.yaml#/components/responses/WorkflowIdResponse"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]
        - UserKeyAuth: ["Role:TenantEditor"]

  /region/{region}/tenant/{tenantId}/upgradeMetaStore:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
      - in: query
        name: metaStore
        schema:
          type: string
          default: dedicated_pg
        required: true
    post:
      tags:
        - tenants
      summary: Upgrade the risingwave meta store
      responses:
        "202":
          $ref: "response.yaml#/components/responses/WorkflowIdResponse"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]
        - UserKeyAuth: ["Role:TenantEditor"]

  /region/{region}/tenant/{tenantId}/computeCache:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    post:
      tags:
        - tenants
      summary: update compute cache for tenant
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostTenantComputeCacheRequestBody"
      responses:
        "202":
          $ref: "response.yaml#/components/responses/WorkflowIdResponse"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]
        - UserKeyAuth: ["Role:TenantEditor"]

  /region/{region}/tenant/{tenantId}/extensions/compaction/enable:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    post:
      tags:
        - tenants.extensions
      summary: Enable the risingwave extensions for serverless compaction
      responses:
        "202":
          $ref: "response.yaml#/components/responses/WorkflowIdResponse"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]
        - UserKeyAuth: ["Role:TenantEditor"]

  /region/{region}/tenant/{tenantId}/extensions/compaction/disable:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    post:
      tags:
        - tenants.extensions
      summary: Disable the risingwave extensions for serverless compaction
      responses:
        "202":
          $ref: "response.yaml#/components/responses/WorkflowIdResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]
        - UserKeyAuth: ["Role:TenantEditor"]

  /region/{region}/tenant/{tenantId}/extensions/compaction/update:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    post:
      tags:
        - tenants.extensions
      summary: Update the risingwave extensions for serverless compaction
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostTenantExtensionCompactionRequestBody"
      responses:
        "202":
          $ref: "response.yaml#/components/responses/WorkflowIdResponse"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]
        - UserKeyAuth: ["Role:TenantEditor"]

  /region/{region}/tenant/{tenantId}/extensions/compaction/parameters:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    get:
      tags:
        - tenants.extensions
      summary: Get the parameters of the risingwave extensions for serverless compaction
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/GetTenantExtensionCompactionParametersResponseBody"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - UserKeyAuth: ["Role:Viewer"]
        - UserKeyAuth: ["Role:TenantViewer"]

  /region/{region}/tenant/{tenantId}/extensions/compaction/status:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    get:
      tags:
        - tenants.extensions
      summary: Get the status of the risingwave extensions for serverless compaction
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/GetTenantExtensionCompactionStatusResponseBody"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - UserKeyAuth: ["Role:Viewer"]
        - UserKeyAuth: ["Role:TenantViewer"]
    post:
      tags:
        - tenants.extensions
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostTenantExtensionCompactionStatusRequestBody"
      summary: Get the status of the risingwave extensions for serverless compaction
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/GetTenantExtensionCompactionStatusResponseBody"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]
        - UserKeyAuth: ["Role:TenantEditor"]

  /region/{region}/tenant/{tenantId}/extensions/iceberg-compaction:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    get:
      tags:
        - tenants.extensions
      summary: Get iceberg compaction status
      responses:
        "200":
          description: "Iceberg compaction status"
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/IcebergCompaction"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]
        - UserKeyAuth: ["Role:TenantEditor"]

    post:
      tags:
        - tenants.extensions
      summary: Enable iceberg compaction
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                config:
                  type: string
                resources:
                  $ref: "mgmt_body.yaml#/components/schemas/ComponentResourceRequest"
      responses:
        "202":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]
        - UserKeyAuth: ["Role:TenantEditor"]

    put:
      tags:
        - tenants.extensions
      summary: Update iceberg compaction
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                config:
                  type: string
                resources:
                  $ref: "mgmt_body.yaml#/components/schemas/ComponentResourceRequest"
      responses:
        "202":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]
        - UserKeyAuth: ["Role:TenantEditor"]

    delete:
      tags:
        - tenants.extensions
      summary: Disable iceberg compaction
      responses:
        "202":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]
        - UserKeyAuth: ["Role:TenantEditor"]

  /region/{region}/tenant/{tenantId}/extensions/serverlessbackfill/enable:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    post:
      tags:
        - tenants.extensions
      summary: Enable the risingwave extensions for serverless backfilling
      responses:
        "202":
          $ref: "response.yaml#/components/responses/WorkflowIdResponse"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]
        - UserKeyAuth: ["Role:TenantEditor"]

  /region/{region}/tenant/{tenantId}/extensions/serverlessbackfill/disable:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    post:
      tags:
        - tenants.extensions
      summary: Disable the risingwave extensions for serverless backfilling
      responses:
        "202":
          $ref: "response.yaml#/components/responses/WorkflowIdResponse"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]
        - UserKeyAuth: ["Role:TenantEditor"]

  /region/{region}/tenant/{tenantId}/extensions/serverlessbackfill/version:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    post:
      tags:
        - tenants.extensions
      summary: Update the risingwave extensions for serverless backfilling to version. Version format like 1.0.0.
      requestBody:
        required: true
        content:
          text/plain:
            schema:
              type: string
            example: "1.2.3"
      responses:
        "202":
          $ref: "response.yaml#/components/responses/WorkflowIdResponse"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]
        - UserKeyAuth: ["Role:TenantEditor"]

  /users:
    get:
      summary: Get all users
      tags:
        - users
      parameters:
        - in: query
          name: name
          schema:
            type: string
          description: use params to filter users by username or email
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/UserPage"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - UserKeyAuth: ["Role:Viewer"]

  /cloudAdmins:
    get:
      description: Get all active cloud admins
      tags:
        - cloudAdmins
      responses:
        "200":
          description: All active cloud admins
          content:
            application/json:
              schema:
                $ref: "admin_resource.yaml#/components/schemas/CloudAdmins"
      security: # UserKeyAuth or ServiceKeyAuth
        - UserKeyAuth: ["Role:Viewer"]
        - ServiceKeyAuth: []
    post:
      description: Grant cloud admin permission
      tags:
        - cloudAdmins
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "admin_body.yaml#/components/schemas/GrantCloudAdminsRequestBody"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - UserKeyAuth: ["Role:RbacEditor"]
  /cloudAdmins/sync:
    post:
      description: Sync oncalls and cloud admins
      tags:
        - cloudAdmins
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security: # UserKeyAuth or ServiceKeyAuth
        - UserKeyAuth: ["Role:RbacEditor"]

  /invitation-codes:
    get:
      summary: Get all invitation codes
      tags:
        - invitationCodes
      responses:
        "200":
          description: Get all invitation codes
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/InvitationCodes"
      security:
        - UserKeyAuth: ["Role:Viewer"]
    post:
      summary: Create invitation codes
      tags:
        - invitationCodes
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/CreateInvitationCodeRequestBody"
      responses:
        "200":
          description: Create invitation code returning
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/PlainInvitationCodes"
      security:
        - UserKeyAuth: ["Role:Editor"]
        - UserKeyAuth: ["Role:InvitationCodeEditor"]
    delete:
      summary: Soft delete an invitation code
      tags:
        - invitationCodes
      parameters:
        - in: query
          name: id
          required: true
          schema:
            type: integer
            format: uint64
      responses:
        "200":
          description: OK
      security:
        - UserKeyAuth: ["Role:Editor"]
        - UserKeyAuth: ["Role:InvitationCodeEditor"]

  /notification:
    post:
      tags:
        - notifications
      summary: Post a notification
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/PostNotificationRequestBody"
      responses:
        "200":
          description: OK
      security:
        - UserKeyAuth: ["Role:Editor"]
  /email/raw:
    post:
      tags:
        - notifications
      summary: "Send email"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/PostEmailRawRequestBody"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]

  /region/{region}/workflows:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
    get:
      summary: Get workflows created recently
      tags:
        - workflows
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/WorkflowPage"
      security:
        - UserKeyAuth: ["Role:Viewer"]
  /region/{region}/workflow/{id}:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
    get:
      summary: Get workflow with events
      tags:
        - workflows
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/WorkflowWithEvents"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - UserKeyAuth: ["Role:Viewer"]
  /region/{region}/workflow/{id}/cancel:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
    post:
      summary: Cancel a running workflow
      tags:
        - workflows
      responses:
        "200":
          description: OK
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]
  /region/{region}/workflow/{id}/resume:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
    post:
      summary: Resume a cancelled workflow
      tags:
        - workflows
      responses:
        "200":
          description: OK
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]
  /region/{region}/workflow/{id}/rerun:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
    post:
      description: Rerun a finished workflow
      tags:
        - workflows
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostWorkflowRerunRequestBody"
      responses:
        "202":
          $ref: "response.yaml#/components/responses/WorkflowIdResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]
  /region/{region}/workflow/{id}/schedule:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
    post:
      description: Schedule a running workflow's delay
      tags:
        - workflows
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostWorkflowScheduleRequestBody"
      responses:
        "200":
          description: OK
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]

  /region/{region}/clusters:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
    get:
      summary: Get all clusters
      tags:
        - k8sClusters
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/ManagedClusters"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security: # UserKeyAuth or ServiceKeyAuth
        - UserKeyAuth: ["Role:Viewer"]
        - ServiceKeyAuth: []
    post:
      summary: Create or update cluster by name
      tags:
        - k8sClusters
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostClusterRequestBody"
      responses:
        "200":
          description: OK
      security: # UserKeyAuth or ServiceKeyAuth
        - UserKeyAuth: ["Role:Admin"]
        - ServiceKeyAuth: []

  /region/{region}/cluster/{name}:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: name
        schema:
          type: string
        required: true
    get:
      summary: Get a cluster
      tags:
        - k8sClusters
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/ManagedCluster"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security: # UserKeyAuth or ServiceKeyAuth
        - UserKeyAuth: ["Role:Viewer"]
        - ServiceKeyAuth: []
    post:
      summary: Create or update cluster
      tags:
        - k8sClusters
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostClusterRequestBody"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security: # UserKeyAuth or ServiceKeyAuth
        - UserKeyAuth: ["Role:Admin"]
        - ServiceKeyAuth: []
    delete:
      summary: delete cluster
      tags:
        - k8sClusters
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security: # UserKeyAuth or ServiceKeyAuth
        - UserKeyAuth: ["Role:Admin"]
        - ServiceKeyAuth: []

  /region/{region}/byoc-clusters:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
    get:
      summary: Get all clusters
      tags:
        - byocClusters
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/ManagedClusters"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security: # UserKeyAuth or ServiceKeyAuth
        - UserKeyAuth: ["Role:Viewer"]
        - ServiceKeyAuth: []

  /region/{region}/byoc-cluster/{id}/access:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: id
        description: id of cluster row
        schema:
          type: integer
          format: uint64
        required: true
    post:
      summary: Get temporary access token for the requested cluster
      tags:
        - byocClusters
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostByocClusterAccessRequestBody"
      responses:
        "200":
          description: Accepted
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/ClusterAccessInfo"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security: # UserKeyAuth or ServiceKeyAuth
        - UserKeyAuth: ["Role:Admin"]
        - ServiceKeyAuth: []

  /region/{region}/byoc-cluster/{id}/update:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: id
        description: id of cluster row
        schema:
          type: integer
          format: uint64
        required: true
    post:
      summary: update the BYOC cluster version, if version is not specified, update it to the default version
      tags:
        - byocClusters
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostByocClusterUpdateRequestBody"
      responses:
        "202":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "404":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security: # UserKeyAuth or ServiceKeyAuth
        - UserKeyAuth: ["Role:Admin"]
        - ServiceKeyAuth: []

  /region/{region}/clusters/{uuid}/prometheus/api/v1/query:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: uuid
        schema:
          type: string
          format: uuid
        required: true
    get:
      tags:
        - k8sClusters
      responses:
        200:
          description: "prometheus result"
          content:
            application/json:
              schema:
                type: object
      security: # UserKeyAuth or ServiceKeyAuth
        - UserKeyAuth: ["Role:Viewer"]
        - ServiceKeyAuth: []
    post:
      tags:
        - k8sClusters
      responses:
        200:
          description: "prometheus result"
          content:
            application/json:
              schema:
                type: object
      security: # UserKeyAuth or ServiceKeyAuth
        - UserKeyAuth: ["Role:Viewer"]
        - ServiceKeyAuth: []

  /privatelink-allowed-targets:
    get:
      summary: "Get all allowed private link targets"
      tags:
        - privatelinks
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/PrivateLinkAllowedTargetSizePage"
    put:
      tags:
        - privatelinks
      summary: "Create/update allowed private link target"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_resource.yaml#/components/schemas/PrivateLinkAllowedTarget"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"

  /privatelink-allowed-targets/{target}:
    parameters:
      - in: path
        name: target
        schema:
          type: string
        required: true
    get:
      tags:
        - privatelinks
      summary: "Get allowed private link target"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/PrivateLinkAllowedTarget"
    delete:
      tags:
        - privatelinks
      summary: "Delete allowed private link target"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"

  /region/{region}/tenant/{tenantId}/privatelink/{privateLinkId}:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
      - in: path
        name: privateLinkId
        schema:
          type: string
          format: uuid
        required: true
    delete:
      summary: Delete a tenant's private link by id
      tags:
        - privatelinks
      responses:
        "202":
          $ref: "response.yaml#/components/responses/WorkflowIdResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - UserKeyAuth: ["Role:Editor"]

  /billing/prometheus/{name}:
    parameters:
      - in: path
        name: name
        schema:
          type: string
        required: true
    get:
      tags:
        - billing
      description: Returns data about the Prometheus endpoint with the given name.
      responses:
        "200":
          description: "prometheus info"
          content:
            application/json:
              schema:
                $ref: "billing_resource.yaml#/components/schemas/PrometheusInfo"
      security:
        - ServiceKeyAuth: []
    put:
      tags:
        - billing
      description: Creates or updates the Prometheus endpoint with the given name.
      requestBody:
        description: Data about the resulting Prometheus endpoint.
        required: true
        content:
          application/json:
            schema:
              $ref: "billing_resource.yaml#/components/schemas/PrometheusInfo"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - ServiceKeyAuth: []
    delete:
      tags:
        - billing
      description: Deletes the Prometheus endpoint with the given name.
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - ServiceKeyAuth: []

  /region/{region}/tenant/{tenantId}/diagnosis/report/generate:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    post:
      summary: generate a RW Diagnosis Report
      description: generate a RW Diagnosis Report from a RW tenant, persist it in a cloud storage bucket, and return its info
      tags:
        - tenants.diagnosis
      operationId: genDiagReport
      responses:
        "200":
          description: "Generated Report Info Response"
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/RwDiagReport"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security: # UserKeyAuth or ServiceKeyAuth
        - UserKeyAuth: ["Role:TenantEditor"]
        - ServiceKeyAuth: []

  /region/{region}/tenant/{tenantId}/diagnosis/report/{reportId}:
    get:
      summary: fetch the content of the RW Diagnosis Report
      description: fetch the content of the pre-generated RW Diagnosis Report if available from a cloud storage bucket
      tags:
        - tenants.diagnosis
      operationId: getDiagReport
      parameters:
        - in: path
          name: region
          schema:
            type: string
          required: true
        - in: path
          name: tenantId
          schema:
            type: integer
            format: uint64
          required: true
        - in: path
          name: reportId
          schema:
            type: integer
            format: uint64
          required: true
      responses:
        "200":
          description: "Generated Report Info Response"
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/RwDiagReport"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security: # UserKeyAuth or ServiceKeyAuth
        - UserKeyAuth: ["Role:Viewer"]
        - ServiceKeyAuth: []

  /region/{region}/tenant/{tenantId}/diagnosis/report/range:
    get:
      summary: list a set of RW Diagnosis Reports within a specified range of time
      description: list a set of RW Diagnosis Reports (excluding content) within a specified range of time from a cloud storage bucket
      tags:
        - tenants.diagnosis
      operationId: listDiagReportTimeRange
      parameters:
        - in: path
          name: region
          schema:
            type: string
          required: true
        - in: path
          name: tenantId
          schema:
            type: integer
            format: uint64
          required: true
        - in: query
          name: startTime
          description: RFC3339/RFC3339Nano formatted starting time (inclusive)
          schema:
            type: string
            format: date-time
            nullable: true
            default: null
        - in: query
          name: endTime
          description: RFC3339/RFC3339Nano formatted ending time (inclusive)
          schema:
            type: string
            format: date-time
            nullable: true
            default: null
        - in: query
          name: limit
          description: pagination - how many report metas to return
          schema:
            type: integer
            format: uint64
            nullable: true
            default: null
        - in: query
          name: offset
          description: pagination - how many report metas to skip
          schema:
            type: integer
            format: uint64
            nullable: true
            default: 0
      responses:
        "200":
          description: "Retrieved set of Reports Response"
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/RwDiagReportMetaPage"
      security: # UserKeyAuth or ServiceKeyAuth
        - UserKeyAuth: ["Role:Viewer"]
        - ServiceKeyAuth: []

  /region/{region}/tenant/{tenantId}/backup:
    post:
      summary: create a backup snapshot for the meta store of the tenant
      tags:
        - tenants.snapshots
      parameters:
        - in: path
          name: region
          schema:
            type: string
          required: true
        - in: path
          name: tenantId
          schema:
            type: integer
            format: uint64
          required: true
      responses:
        "202":
          $ref: "response.yaml#/components/responses/WorkflowIdResponse"
      security:
        - UserKeyAuth: ["Role:TenantEditor"]

  /region/{region}/tenant/{tenantId}/backup/{snapshotId}:
    delete:
      summary: delete a backup snapshot for the meta store of the tenant
      tags:
        - tenants.snapshots
      parameters:
        - in: path
          name: region
          schema:
            type: string
          required: true
        - in: path
          name: tenantId
          schema:
            type: integer
            format: uint64
          required: true
        - in: path
          name: snapshotId
          schema:
            type: string
            format: uuid
          required: true
      responses:
        "202":
          description: "Workflow ID response"
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/OptionalWorkflowIdResponseBody"
      security:
        - UserKeyAuth: ["Role:TenantEditor"]

  /region/{region}/tenant/{tenantId}/backup/{snapshotId}/restore:
    post:
      summary: Restore to a new cluster based on the snapshot id
      tags:
        - tenants.snapshots
      parameters:
        - in: path
          name: region
          schema:
            type: string
          required: true
        - in: path
          name: tenantId
          schema:
            type: integer
            format: uint64
          required: true
        - in: path
          name: snapshotId
          schema:
            type: string
            format: uuid
          required: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostTenantRestoreRequestBody"
      responses:
        "202":
          description: Starting to restore tenant from this snapshot.
      security:
        - UserKeyAuth: ["Role:Admin"]

  /region/{region}/tenant/{tenantId}/backup/{snapshotId}/in-place-restore:
    post:
      summary: In place restore to a new cluster based on the snapshot id
      tags:
        - tenants.snapshots
      parameters:
        - in: path
          name: region
          schema:
            type: string
          required: true
        - in: path
          name: tenantId
          schema:
            type: integer
            format: uint64
          required: true
        - in: path
          name: snapshotId
          schema:
            type: string
            format: uuid
          required: true
      responses:
        "202":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - UserKeyAuth: ["Role:Admin"]

  /region/{region}/tenant/{tenantId}/resourceGroups:
    post:
      tags:
        - tenants.resourceGroups
      summary: Create resource group
      parameters:
        - in: path
          name: region
          schema:
            type: string
          required: true
        - in: path
          name: tenantId
          schema:
            type: integer
            format: uint64
          required: true
      requestBody:
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/CreateResourceGroupsRequestBody"
      responses:
        "202":
          $ref: "response.yaml#/components/responses/WorkflowIdResponse"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - UserKeyAuth: ["Role:TenantEditor"]
  /region/{region}/tenant/{tenantId}/resourceGroups/{resourceGroup}:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
      - in: path
        name: resourceGroup
        schema:
          type: string
        required: true
    post:
      tags:
        - tenants.resourceGroups
      summary: Update resource group
      requestBody:
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/UpdateResourceGroupsRequestBody"
      responses:
        "202":
          $ref: "response.yaml#/components/responses/WorkflowIdResponse"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - UserKeyAuth: ["Role:TenantEditor"]
    delete:
      tags:
        - tenants.resourceGroups
      summary: Delete resource group
      responses:
        "202":
          $ref: "response.yaml#/components/responses/WorkflowIdResponse"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - UserKeyAuth: ["Role:TenantEditor"]

components:
  securitySchemes:
    UserKeyAuth:
      type: http
      scheme: bearer
    ServiceKeyAuth:
      type: http
      scheme: bearer
