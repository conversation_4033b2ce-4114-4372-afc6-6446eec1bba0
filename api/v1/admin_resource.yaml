openapi: 3.0.3

info:
  title: v1
  version: 1.0-alpha
paths: {}
components:
  schemas:
    CloudAdmin:
      type: object
      required: [email, expiredAt]
      properties:
        email:
          type: string
        expiredAt:
          type: string
          format: date-time
    CloudAdminArray:
      type: array
      items:
        $ref: "#/components/schemas/CloudAdmin"
    CloudAdmins:
      type: object
      required: [cloudAdmins]
      properties:
        cloudAdmins:
          $ref: "#/components/schemas/CloudAdminArray"
