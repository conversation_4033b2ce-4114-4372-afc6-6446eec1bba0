openapi: 3.0.3

info:
  title: v1
  version: 1.0-alpha

servers:
  - url: /api/v1 # baseURL for kin-openapi openapifilter (should be consistent with spec.GinServerOptions)

tags:

paths:
  # Dummy APIs for testing.
  /ping:
    get:
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
  /ping/auth:
    get:
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /hourly-cost:
    get:
      description: Returns the current hourly cost for a 'Standard' RW-cluster.
      parameters:
        - in: query
          name: region
          schema:
            type: string
          required: true
        - in: query
          name: tier_id
          schema:
            $ref: "mgmt_resource.yaml#/components/schemas/TierId"
          required: true
        - in: query
          name: rwu_count
          schema:
            type: number
            format: double
          required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "billing_resource.yaml#/components/schemas/GetHourlyCostResult"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /cluster/{nsId}/cost-breakdown:
    get:
      description:
        Returns the given cluster's (estimated) cost for each charged metric of
        each day during the given month and year. If month or year are omitted,
        the current month or year is used.
      parameters:
        - in: path
          required: true
          name: nsId
          schema:
            type: string
            format: uuid
        - in: query
          name: year
          schema:
            type: integer
          required: false
        - in: query
          name: month
          schema:
            type: integer
            minimum: 1
            maximum: 12
          required: false
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "billing_resource.yaml#/components/schemas/CostBreakdown"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /cluster/usage:
    get:
      description:
        Lists the recorded usage of the given cluster for the given metric in reverse chronological
        order (i.e. newest first). A result contains at most 1024 entries. The query may return
        less than the maximum number of entries to ensure that all nodes of the same time-frame are
        submitted together.
      x-required-permission: billingUsage.get
      parameters:
        - in: query
          name: cluster_id
          schema:
            type: string
            format: uuid
          required: true
        - in: query
          name: metric
          schema:
            $ref: "billing_resource.yaml#/components/schemas/Metric"
          required: true
        - in: query
          # Exclusive. Optional; uses current time if not specified.
          name: max_start_time
          schema:
            type: string
            format: date-time
          required: false
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "billing_resource.yaml#/components/schemas/ClusterUsage"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /org/billing-details:
    get:
      description:
        Returns the billing information (the address used on invoices) of the
        logged in organisation.
      x-required-permission: billingProfile.get
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "billing_resource.yaml#/components/schemas/BillingDetails"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    put:
      description:
        Updates the billing information (the address used on invoices) of the
        logged in organisation. Note that this requests updates all properties
        even if they are omitted. In that case, the request will delete their
        current values.
      x-required-permission: billingProfile.update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "billing_resource.yaml#/components/schemas/BillingDetails"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /org/invoice:
    get:
      description:
        Returns the invoice with the given ID with all its data. If the ID is omitted or
        empty, it returns a preliminary invoice for the current billing cycle.
      x-required-permission: invoices.get
      parameters:
        - in: query
          name: id
          schema:
            type: string
          required: false
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "billing_resource.yaml#/components/schemas/FullInvoice"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /org/invoice/pdf:
    get:
      description: Redirects to the invoice's PDF on Stripe.
      x-required-permission: invoices.get
      parameters:
        - in: query
          name: id
          schema:
            type: string
          required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "billing_resource.yaml#/components/schemas/Url"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /org/invoice/pay:
    get:
      description: Redirects to the invoice's payment page on Stripe.
      parameters:
        - in: query
          name: id
          schema:
            type: string
          required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "billing_resource.yaml#/components/schemas/Url"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /org/invoices/list:
    get:
      description:
        Lists the customer's invoices in reverse chronological order (i.e. newest first).
        A result contains at most 1024 entries. It does not contain preliminary invoices.
        The query may return less than the maximum number of entries to ensure that all
        invoices of the same billing cycle are submitted together.
      x-required-permission: invoices.list
      parameters:
        - in: query
          # The billing cycle of the newest invoice in the result (inclusive).
          # Uses the current billing cycle if omitted.
          name: max_cycle
          schema:
            type: integer
            format: int16
          required: false
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "billing_resource.yaml#/components/schemas/InvoiceList"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /org/invoices/unpaid:
    get:
      description: Returns the customer's oldest unpaid invoice.
      x-required-permission: invoices.get
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "billing_resource.yaml#/components/schemas/GetOrgInvoicesUnpaidResult"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /org/payments:
    get:
      description: Lists all stored payment methods.
      x-required-permission: payments.list
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "billing_resource.yaml#/components/schemas/GetOrgPaymentsResult"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    delete:
      description: Deletes a stored payment method.
      x-required-permission: payments.delete
      parameters:
        - in: query
          name: idHash
          schema:
            type: string
          required: true
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /org/payments/new:
    post:
      description: Creates a new Setup Intent with Stripe.
      x-required-permission: payments.create
      parameters:
        - in: query
          # The key is forwarded to Stripe and is valid for 24 hours.
          # See https://stripe.com/docs/api/idempotent_requests.
          name: idempotencyKey
          schema:
            type: string
            format: uuid
          required: false
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "billing_resource.yaml#/components/schemas/SetupIntent"
      x-audit-nodata: true
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /org/payments/default:
    put:
      description: Makes a payment method the default for their respective user.
        Requires a Setup Intend ID (after successfully creating a new payment)
        or a hashed ID of an existing payment method.
      x-required-permission: payments.setDefault
      parameters:
        - in: query
          name: setiId
          schema:
            type: string
        - in: query
          name: idHash
          schema:
            type: string
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

components:
  schemas:
  responses:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    ApiKeyAuth:
      type: http
      in: basic
      description: Service account api key. Header format 'Basic base64(${key}:${secret})'
