openapi: 3.0.3

info:
  title: v1
  version: 1.0-alpha

servers:
  - url: /api/v1 # baseURL for kin-openapi openapifilter (should be consistent with spec.GinServerOptions)

tags:

paths:
  /:
    get:
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"

  /version:
    get:
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"

  /regions:
    get:
      description: Lists all stored regions.
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "billing_resource.yaml#/components/schemas/GetRegionsResult"

  /region/{name}:
    parameters:
      - in: path
        name: name
        schema:
          type: string
    get:
      description: Returns data about the Prometheus endpoint with the given name.
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "billing_resource.yaml#/components/schemas/PrometheusInfo"
    put:
      description: Creates or updates the Prometheus endpoint with the given name.
      requestBody:
        description: Data about the resulting Prometheus endpoint.
        required: true
        content:
          application/json:
            schema:
              $ref: "billing_resource.yaml#/components/schemas/PrometheusInfo"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
    delete:
      description: Deletes the region with the given name.
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"

  /prices:
    get:
      description: Returns the prices at a given date.
      parameters:
        - in: query
          name: date
          description: Optional.
            States the date for which prices are determined.
            Uses current date/time if not specified.
          schema:
            type: string
            format: date-time
          required: false
      requestBody:
        description: Optional.
          Specifies a filter to limit which prices are returned.
          If attributes are empty or omitted, then they are not used for filtering.
        required: false
        content:
          application/json:
            schema:
              $ref: "billing_resource.yaml#/components/schemas/PriceKey"
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "billing_resource.yaml#/components/schemas/GetPricesResult"
    put:
      description: Creates or updates a set of prices.
        If an existing price is updated, all its levels are replaced.
        Cannot affect current or past prices.
      requestBody:
        content:
          application/json:
            schema:
              $ref: "billing_resource.yaml#/components/schemas/Price"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"

  /org:
    post:
      tags:
        - org
      description: Creates an organisation with the given Stripe ID.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "billing_resource.yaml#/components/schemas/StripeOrg"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"

  /org/{id}/skip-invoicing:
    parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid

    get:
      tags:
        - org
      description: Returns whether the organisation with the given ID is set to skip invoicing.
      responses:
        "200":
          description: "Indicates whether the organisation is set to skip invoicing."
          content:
            application/json:
              schema:
                $ref: "billing_resource.yaml#/components/schemas/GetOrgSkipInvoicingResult"
    put:
      tags:
        - org
      description: Sets whether the organisation with the given ID is set to skip invoicing.
      parameters:
        - in: query
          name: skip
          description: "Whether to set the organisation to skip invoicing."
          required: true
          schema:
            type: boolean
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
