openapi: 3.0.3

info:
  title: v1
  version: 1.0-alpha
paths: {}

components:
  schemas:
    #
    # --- Prices and Tiers ---

    PriceKey:
      type: object
      required: [name, region, tier]
      properties:
        name:
          type: string
        region:
          type: string
        tier:
          $ref: "#/components/schemas/TierKey"

    PriceLevel:
      type: object
      required: [min_amount, price_usd_micro, per_amount]
      properties:
        min_amount:
          type: integer
          format: int64
        price_usd_micro:
          type: integer
          format: int64
        per_amount:
          type: integer
          format: int64

    Price:
      allOf:
        - $ref: "#/components/schemas/PriceKey"
        - type: object
          required: [start_cycle, levels]
          properties:
            start_cycle:
              type: integer
              format: int16
            levels:
              type: array
              items:
                $ref: "#/components/schemas/PriceLevel"

    PriceArray:
      type: array
      items:
        $ref: "#/components/schemas/Price"

    GetPricesResult:
      type: object
      required: [prices]
      properties:
        prices:
          $ref: "#/components/schemas/PriceArray"

    TierKey:
      type: object
      required: [tier_id, org_id]
      properties:
        tier_id:
          type: string
        org_id:
          type: string
          format: uuid

    #
    # --- Contact/Billing Information ---

    StripeOrg:
      type: object
      required: [name, stripe_id]
      properties:
        name:
          type: string
        stripe_id:
          type: string

    Address:
      type: object
      required: [line1, line2, city, state, country, postal_code]
      properties:
        line1:
          type: string
        line2:
          type: string
        city:
          type: string
        state:
          type: string
        country:
          # Two-letter country code (ISO 3166-1 alpha-2).
          # See https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2.
          type: string
          pattern: "^[A-Z][A-Z]$"
        postal_code:
          type: string

    BillingDetails:
      type: object
      required: [name, phone, address]
      properties:
        name:
          type: string
        phone:
          type: string
        address:
          $ref: "#/components/schemas/Address"

    #
    # --- Payments ---

    SetupIntent:
      type: object
      required: [client_secret, public_key]
      properties:
        client_secret:
          type: string
          example: seti_*****_secret_*****
        public_key:
          type: string
          example: pk_*****

    PaymentMethod:
      type: object
      required: [id_hash, is_default, created, type]
      properties:
        id_hash:
          type: string
        is_default:
          type: boolean
        created:
          type: string
          format: date-time
        type:
          type: string
        billing_details:
          $ref: "#/components/schemas/BillingDetails"
        card:
          $ref: "#/components/schemas/Card"
        # Add more methods here as attribute after defining a type for them.

    PaymentMethodArray:
      type: array
      items:
        $ref: "#/components/schemas/PaymentMethod"

    GetOrgPaymentsResult:
      type: object
      required: [max_number, payment_methods]
      properties:
        max_number:
          type: integer
        payment_methods:
          $ref: "#/components/schemas/PaymentMethodArray"

    Card:
      type: object
      required: [brand, last4, exp_month, exp_year, funding]
      properties:
        brand:
          type: string
        last4:
          type: string
        exp_month:
          type: integer
        exp_year:
          type: integer
        funding:
          type: string
          enum:
            - credit
            - debit
            - prepaid
            - unknown

    #
    # --- Invoices ---

    BillingPeriod:
      type: object
      required: [start, end]
      properties:
        start:
          type: string
          format: date-time
          description: The first second of the billing period.
        end:
          type: string
          format: date-time
          description: The last second (inclusive) of the billing period.

    BasicInvoice:
      type: object
      required:
        [stripe_id, number, org_id, billing_period, total_usd_cent, status]
      properties:
        stripe_id:
          # Is empty if invoice is preliminary.
          type: string
        number:
          # The number shown on the invoice.
          # Is empty if invoice is preliminary.
          type: string
        org_id:
          type: string
          format: uuid
        billing_period:
          $ref: "#/components/schemas/BillingPeriod"
        total_usd_cent:
          type: integer
          format: int64
        status:
          $ref: "#/components/schemas/InvoiceState"
        due_date:
          # Null if invoice is preliminary.
          type: string
          format: date-time
        paid_at:
          # Null if invoice has not been paid.
          type: string
          format: date-time

    FullInvoice:
      type: object
      allOf:
        - $ref: "#/components/schemas/BasicInvoice"
        - type: object
          required: [lines]
          properties:
            lines:
              type: array
              items:
                $ref: "#/components/schemas/InvoiceLine"

    InvoiceLine:
      type: object
      required:
        [cluster_id, cluster_name, metric_name, metric_total, price_usd_cent]
      properties:
        cluster_id:
          type: string
          format: uuid
        cluster_name:
          type: string
        metric_name:
          type: string
        metric_total:
          type: integer
          format: uint64
        price_usd_cent:
          type: integer
          format: int64

    InvoiceList:
      type: object
      required: [invoices, next_cycle]
      properties:
        invoices:
          type: array
          items:
            $ref: "#/components/schemas/BasicInvoice"
        next_cycle:
          # States the billing cycle of the next-older invoice.
          # Is 0 if there are no older invoices.
          type: integer
          format: int16

    InvoiceState:
      # See https://stripe.com/docs/invoicing/overview#invoice-statuses for more.
      type: string
      enum:
        - preliminary # Not defined by Stripe. States that invoice has not been created.
        - open
        - overdue # Not defined by Stripe. States that invoice is `open` and past its due date.
        - paid
        - void
        # Stripe also defines 'draft' and 'uncollectible'.
        # We will not show that to the user.

    GetOrgInvoicesUnpaidResult:
      type: object
      properties:
        invoice:
          $ref: "billing_resource.yaml#/components/schemas/BasicInvoice"

    Url:
      type: object
      required: [url]
      properties:
        url:
          type: string

    #
    # --- Measured Metrics ---

    Metric:
      type: string
      enum:
        [
          nodes_meta,
          nodes_compute,
          nodes_compactor,
          meta_store,
          storage,
          s3_read_requests,
          s3_write_requests,
          throughput,
          traffic_in,
          traffic_out,
          traffic_nat_in,
          traffic_nat_out,
          traffic_pl_in,
          traffic_pl_out,
          pl_time,
        ]

    MetricUnit:
      type: string
      enum: [core_seconds, kb_seconds, bytes, requests, seconds]

    MeasuredMetric:
      type: object
      required: [start_time, end_time, value]
      properties:
        start_time:
          type: string
          format: date-time
        end_time:
          type: string
          format: date-time
        value:
          type: integer
          format: uint64

    ClusterUsage:
      type: object
      required: [cluster_id, metric, unit, usage, contains_oldest]
      properties:
        cluster_id:
          type: string
          format: uuid
        metric:
          $ref: "#/components/schemas/Metric"
        unit:
          $ref: "#/components/schemas/MetricUnit"
        usage:
          type: array
          items:
            $ref: "#/components/schemas/MeasuredMetric"
        contains_oldest:
          type: boolean

    CostBreakdown:
      type: object
      required: [cluster_id, year, month, metric_costs]
      properties:
        cluster_id:
          type: string
          format: uuid
        year:
          type: integer
        month:
          type: integer
          minimum: 1
          maximum: 12
        metric_costs:
          type: array
          items:
            $ref: "#/components/schemas/CostBreakdownMetric"

    CostBreakdownMetric:
      description:
        A clusters (estimated) cost for each day of a billing cycle. The length
        of `day_costs_usd_cent` is equal to the number of days of the cycle. The
        first entry stores the cost for the first day and so on. For example,
        for January, `len(day_costs_usd_cent) == 31` and `day_costs_usd_cent[5]`
        stores the cost for the 6th of January.
      type: object
      required: [metric, day_costs_usd_cent]
      properties:
        metric:
          $ref: "#/components/schemas/Metric"
        day_costs_usd_cent:
          type: array
          items:
            type: integer
            format: int64

    #
    # --- Regions and Prometheus Endpoints ---

    PrometheusInfo:
      description: Used to create or update a Prometheus endpoint.
      type: object
      required: [prometheus_url]
      properties:
        prometheus_url:
          type: string
        prefer_global_src:
          type: boolean
          default: false

    Region:
      type: object
      required: [name, prometheus_url, last_queried, prefer_global_src]
      properties:
        name:
          type: string
        prometheus_url:
          type: string
        last_queried:
          type: string
          format: date-time
        prefer_global_src:
          type: boolean

    RegionArray:
      type: array
      items:
        $ref: "#/components/schemas/Region"

    GetRegionsResult:
      type: object
      required: [regions]
      properties:
        regions:
          $ref: "#/components/schemas/RegionArray"

    #
    # --- Estimate Price ---

    ClusterConfiguration:
      type: object
      required: [region, tier_id, meta_cores, compute_cores, compactor_cores]
      properties:
        region:
          type: string
        tier_id:
          $ref: "mgmt_resource.yaml#/components/schemas/TierId"
        meta_cores:
          type: integer
          format: uint64
        compute_cores:
          type: integer
          format: uint64
        compactor_cores:
          type: integer
          format: uint64

    GetHourlyCostResult:
      type: object
      required: [total_usd_cent]
      properties:
        total_usd_cent:
          type: integer
          format: int64

    GetOrgSkipInvoicingResult:
      type: object
      required: [skip_invoicing]
      properties:
        skip_invoicing:
          type: boolean
