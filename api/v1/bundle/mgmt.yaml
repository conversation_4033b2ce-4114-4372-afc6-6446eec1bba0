# Code generated by redocly/cli DO NOT EDIT.
openapi: 3.0.3
info:
  title: v1
  version: 1.0-alpha
servers:
  - url: /api/v1
tags:
  - name: tenants
    description: Operations about tenants
  - name: tenant
    description: Operations about tenants
  - name: dbusers
    description: RisingWave DB users
  - name: metrics
    description: Operations about metrics
  - name: log
    description: Operations about logs
paths:
  /:
    x-internal: true
    get:
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
  /version:
    x-internal: true
    get:
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
  /tiers:
    get:
      responses:
        '200':
          description: Get tiers Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Tiers'
        '500':
          description: Internal server error
  /availability:
    x-internal: true
    get:
      tags:
        - tenant
      description: Check the availability of the tenant name
      parameters:
        - in: query
          name: tenantName
          required: true
          schema:
            type: string
      responses:
        '200':
          $ref: '#/components/responses/AvailabilityResponse'
        '422':
          $ref: '#/components/responses/AvailabilityResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /rootca:
    get:
      tags:
        - rootca
      description: Get the root CA file of the RisingWave clusters in this region
      responses:
        '200':
          description: Get root CA file successfully
        '500':
          description: Internal server error
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants:
    post:
      tags:
        - tenants
      summary: Create tenants with tenantName
      x-required-permission: tenants.create
      x-tenant-operation: Provision
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TenantRequestRequestBody'
      responses:
        '202':
          description: Create tenant response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateTenantResponseBody'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '422':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    get:
      tags:
        - tenants
      summary: Get all the tenants owned by the user
      x-required-permission: tenants.list
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TenantSizePage'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant:
    parameters:
      - in: query
        name: tenantId
        schema:
          type: integer
          format: uint64
      - in: query
        name: tenantName
        schema:
          type: string
    get:
      tags:
        - tenant
      summary: get a tenant by tenantId or tenantName
      x-required-permission: tenants.get
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Tenant'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    delete:
      tags:
        - tenant
      summary: delete a tenant by tenantId or tenantName
      x-required-permission: tenants.delete
      x-tenant-operation: Delete
      responses:
        '202':
          $ref: '#/components/responses/DefaultResponse'
        '403':
          $ref: '#/components/responses/DefaultResponse'
        '404':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant/{tenantId}/resource:
    post:
      tags:
        - tenant
      summary: update a tenant resource by tenantId
      x-required-permission: tenants.updateResource
      x-tenant-operation: Scale
      parameters:
        - in: path
          required: true
          name: tenantId
          schema:
            type: integer
            format: uint64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostTenantResourcesRequestBody'
      responses:
        '202':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '422':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant/{tenantId}/databases:
    x-internal: true
    get:
      tags:
        - MViews
      x-required-permission: databases.list
      parameters:
        - in: path
          name: tenantId
          schema:
            type: integer
            format: uint64
          required: true
      responses:
        '200':
          description: Use the SHOW DATABASES command to show all databases.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetDatabasesResponseBody'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant/{tenantId}/backup:
    x-internal: true
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    get:
      tags:
        - backup
      x-required-permission: backups.list
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
      responses:
        '200':
          description: Get all available backup snapshots.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BackupSnapshotsSizePage'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    post:
      tags:
        - backup
      x-required-permission: backups.start
      responses:
        '202':
          description: The backup procedure is started.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostSnapshotResponseBody'
        '409':
          description: The tenant status should be running before starting the backup procedure.
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant/{tenantId}/backup/{snapshotId}:
    x-internal: true
    delete:
      tags:
        - backup
      x-required-permission: backups.delete
      parameters:
        - in: path
          name: tenantId
          schema:
            type: integer
            format: uint64
          required: true
        - in: path
          name: snapshotId
          schema:
            type: string
            format: uuid
          required: true
      responses:
        '202':
          description: The snapshot deletion procedure is started.
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant/{tenantId}/backup/{snapshotId}/restore:
    x-internal: true
    post:
      tags:
        - backup
      x-required-permission: backups.restoreSnapshot
      parameters:
        - in: path
          name: tenantId
          schema:
            type: integer
            format: uint64
          required: true
        - in: path
          name: snapshotId
          schema:
            type: string
            format: uuid
          required: true
      responses:
        '202':
          description: Starting to restore data from this snapshot.
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{tenantId}/unquiesce:
    post:
      tags:
        - backups
      x-required-permission: backups.restoreSnapshot
      summary: Resume a quiesced tenant.
      parameters:
        - in: path
          name: tenantId
          schema:
            type: integer
            format: uint64
          required: true
      responses:
        '202':
          description: Starting to resume the tenant.
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant/{tenantId}/info:
    x-internal: true
    get:
      tags:
        - info
      x-required-permission: tenants.get
      parameters:
        - in: path
          name: tenantId
          schema:
            type: integer
            format: uint64
          required: true
      responses:
        '200':
          description: Show the cluster info of this tenant.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TenantClusterInfoResponseBody'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant/{tenantId}/metrics:
    x-internal: true
    get:
      tags:
        - metrics
      x-required-permission: tenants.getMetrics
      parameters:
        - in: path
          name: tenantId
          schema:
            type: integer
            format: uint64
          required: true
      responses:
        '200':
          description: Pull the risingwave metrics of this tenant.
          content:
            text/plain:
              schema:
                type: string
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant/tags:
    get:
      tags:
        - tenant
      responses:
        '200':
          description: get the latest risingwave cloud image tag
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetImageTagResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant/{tenantId}/relations/getCounts:
    x-internal: true
    get:
      tags:
        - relations
      x-required-permission: relations.get
      parameters:
        - in: path
          name: tenantId
          schema:
            type: integer
            format: uint64
          required: true
      responses:
        '200':
          description: Get relation counts
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RelationCounts'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant/{tenantId}/privatelinks:
    parameters:
      - in: path
        required: true
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      tags:
        - tenant
      x-required-permission: privateLinks.create
      summary: for a tenant to link to the source/sink in the user's VPC
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostPrivateLinkRequestBody'
      responses:
        '202':
          description: Create privatelink response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostPrivateLinkResponseBody'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant/{tenantId}/privatelink/{privateLinkId}:
    parameters:
      - in: path
        required: true
        name: tenantId
        schema:
          type: integer
          format: uint64
      - in: path
        required: true
        name: privateLinkId
        schema:
          type: string
          format: uuid
    get:
      tags:
        - tenant
      x-required-permission: privateLinks.get
      summary: get a private link by id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrivateLink'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    delete:
      tags:
        - tenant
      x-required-permission: privateLinks.delete
      summary: delete a private link by id
      responses:
        '202':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant/{tenantId}/start:
    x-internal: true
    put:
      tags:
        - tenant
      x-required-permission: tenants.update
      x-tenant-operation: Start
      summary: Start a cluster that has been stopped
      parameters:
        - in: path
          required: true
          name: tenantId
          schema:
            type: integer
            format: uint64
      responses:
        '202':
          description: cluster start has been initiated
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '409':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant/{tenantId}/stop:
    x-internal: true
    put:
      tags:
        - tenant
      x-required-permission: tenants.update
      x-tenant-operation: Stop
      summary: Stop a running a cluster
      parameters:
        - in: path
          required: true
          name: tenantId
          schema:
            type: integer
            format: uint64
      responses:
        '202':
          $ref: '#/components/responses/DefaultResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '409':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant/{tenantId}/update-cfg/{configId}:
    x-internal: true
    put:
      tags:
        - tenant
      x-required-permission: tenants.update
      summary: Apply new RisingWave cluster configuration.
      parameters:
        - in: path
          required: true
          name: tenantId
          schema:
            type: integer
            format: uint64
        - in: path
          required: true
          name: configId
          schema:
            type: string
            format: uuid
      responses:
        '202':
          description: Restarting of compute nodes has been initiated.
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '409':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant/{tenantId}/config/etcd:
    put:
      tags:
        - tenant
      x-required-permission: tenants.update
      summary: Apply new etcd configuration.
      parameters:
        - in: path
          required: true
          name: tenantId
          schema:
            type: integer
            format: uint64
      requestBody:
        description: 'etcd env config content. e.g. ETCD_MAX_REQUEST_BYTES: "1024"\nETCD_QUOTA_BACKEND_BYTES: "1024"'
        required: true
        content:
          text/plain:
            schema:
              type: string
      responses:
        '202':
          description: successfully send asynchronous request to apply new etcd configuration.
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '409':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant/{tenantId}/config/risingwave:
    put:
      tags:
        - tenant
      x-required-permission: tenants.update
      summary: Apply new risingwave configuration.
      parameters:
        - in: path
          required: true
          name: tenantId
          schema:
            type: integer
            format: uint64
      requestBody:
        description: risingwave.toml config content.
        required: true
        content:
          text/plain:
            schema:
              type: string
      responses:
        '202':
          description: successfully send asynchronous request to apply new risingwave configuration.
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '409':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant/{tenantId}/updateVersion:
    post:
      tags:
        - tenant
      x-required-permission: tenants.update
      summary: update the tenant rw version to latest
      parameters:
        - in: path
          required: true
          name: tenantId
          schema:
            type: integer
            format: uint64
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                version:
                  type: string
      responses:
        '202':
          $ref: '#/components/responses/DefaultResponse'
        '404':
          $ref: '#/components/responses/DefaultResponse'
        '409':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant/dbusers:
    get:
      tags:
        - dbusers
      x-required-permission: databaseUsers.list
      parameters:
        - in: query
          name: tenantId
          schema:
            type: integer
            format: uint64
          required: true
      responses:
        '200':
          description: Database users Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DBUsers'
        '404':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    post:
      tags:
        - dbusers
      x-required-permission: databaseUsers.create
      summary: Create a database user with options SUPERUSER/NOSUPERUSER, CREATEDB/NOCREATEDB, CREATEUSER/NOCREATEUSER PASSWORD
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateDBUserRequestBody'
      responses:
        '200':
          description: create db users success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DBUser'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      x-audit-nodata: true
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    put:
      tags:
        - dbusers
      x-required-permission: databaseUsers.update
      summary: Alter db user's password
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateDBUserRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      x-audit-nodata: true
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    delete:
      tags:
        - dbusers
      x-required-permission: databaseUsers.delete
      summary: delete database user by name
      parameters:
        - in: query
          name: tenantId
          schema:
            type: integer
            format: uint64
          required: true
        - in: query
          name: username
          schema:
            type: string
          required: true
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /endpoints:
    get:
      tags:
        - tenants
      x-required-permission: endpoints.list
      summary: Get an endpoint of tenant by tenantName or tenantId
      parameters:
        - in: query
          name: tenantName
          schema:
            type: string
        - in: query
          name: tenantId
          schema:
            type: integer
            format: uint64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Endpoint'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /metrics:
    x-internal: true
    get:
      tags:
        - metrics
      x-required-permission: metrics.get
      summary: Get metrics by tenant ID
      parameters:
        - in: query
          name: metric
          schema:
            type: string
          required: true
        - in: query
          name: tenantId
          required: true
          schema:
            type: integer
            format: uint64
        - in: query
          name: component
          schema:
            type: string
        - in: query
          name: startTime
          schema:
            type: string
            format: date-time
        - in: query
          name: endTime
          schema:
            type: string
            format: date-time
        - in: query
          name: period
          schema:
            type: integer
            format: uint64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Metrics'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '416':
          $ref: '#/components/responses/RangeNotSatisfiable'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /log/queryError:
    get:
      summary: Query error logs over a range of time
      description: Wrapper of Loki HTTP API - https://grafana.com/docs/loki/latest/reference/api/#query-loki-over-a-range-of-time
      tags:
        - log
      x-required-permission: log.get
      operationId: queryErrLog
      parameters:
        - in: query
          name: tenantId
          required: true
          schema:
            type: integer
            format: uint64
        - in: query
          name: target
          required: true
          schema:
            type: string
            enum:
              - source
              - sink
              - table
              - target
              - name
              - message
            description: use line_contains to filter logs, e.g. `"source_id":1`, `"target":"high_join_amplification"`, `"error":"failed to parse json payload"` or `"name":"executor"`
        - in: query
          name: targetId
          required: true
          schema:
            type: string
            description: targetId to filter logs, could be sourceId, sinkId, target message, actor/executor name, error message
        - in: query
          name: start
          schema:
            type: string
            format: date-time
            description: RFC3339/RFC3339Nano formatted date-time string (start means >=), defaults to one hour ago
            nullable: true
            default: null
        - in: query
          name: end
          schema:
            type: string
            format: date-time
            description: RFC3339/RFC3339Nano formatted date-time string (end means <), defaults to now
            nullable: true
            default: null
        - in: query
          name: direction
          schema:
            type: string
            enum:
              - forward
              - backward
            default: backward
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            minimum: 1
            default: 100
      responses:
        '200':
          description: ErrLogQueryResult
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrLogQueryResult'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '500':
          description: Internal server error
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/status:
    x-internal: true
    get:
      tags:
        - tenants
      x-required-permission: tenants.list
      description: Get the number of different status tenants
      parameters: []
      responses:
        '200':
          description: array of tenants with different status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TenantStatusCounts'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /source/ping:
    x-internal: true
    post:
      tags:
        - sources
      description: Ping kafka source to check the server's availability
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostSourceRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/AvailabilityResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
      x-audit-nodata: true
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant/{tenantId}/sources/fetchKafkaTopic:
    x-internal: true
    post:
      tags:
        - sources
      x-required-permission: sources.fetchKafkaTopic
      description: Fetch kafka topics
      parameters:
        - in: path
          required: true
          name: tenantId
          schema:
            type: integer
            format: uint64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostSourcesFetchKafkaTopicRequestBody'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostSourcesFetchKafkaTopicResponseBody'
      x-audit-nodata: true
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant/{tenantId}/sources/ping:
    x-internal: true
    post:
      tags:
        - sources
      description: Ping kafka source to check the server's availability
      parameters:
        - in: path
          required: true
          name: tenantId
          schema:
            type: integer
            format: uint64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostSourceRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/AvailabilityResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
      x-audit-nodata: true
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant/{tenantId}/execution:
    x-internal: true
    post:
      tags:
        - sources
      description: Execute a sql with provided rw credentials
      parameters:
        - in: path
          required: true
          name: tenantId
          schema:
            type: integer
            format: uint64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SqlExecutionRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      x-audit-nodata: true
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant/{tenantId}/relation:
    x-internal: true
    delete:
      tags:
        - sources
        - sinks
        - MViews
      description: drop relation (matviews, tables, sources, sinks, ...) in user's risingwave cluster
      parameters:
        - in: path
          required: true
          name: tenantId
          schema:
            type: integer
            format: uint64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DropTenantRelationBody'
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      x-audit-nodata: true
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant/{tenantId}/extensions/serverlessbackfill/enable:
    x-internal: true
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      x-allowed-tenant-tiers: Invited,BYOC
      x-tenant-id-path-param-locator: tenantId
      tags:
        - tenant
      summary: Enable the risingwave extensions for serverless backfilling
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowIdResponseBody'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant/{tenantId}/extensions/serverlessbackfill/disable:
    x-internal: true
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      tags:
        - tenant
      summary: Disable the risingwave extensions for serverless backfilling
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowIdResponseBody'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant/{tenantId}/extensions/serverlessbackfill/version:
    x-internal: true
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      x-allowed-tenant-tiers: Invited,BYOC
      x-tenant-id-path-param-locator: tenantId
      tags:
        - tenant
      summary: Update the risingwave extensions for serverless backfilling to version. Version format like 1.0.0.
      requestBody:
        required: true
        content:
          text/plain:
            schema:
              type: string
              example: 1.2.3
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowIdResponseBody'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /byoc-clusters:
    x-internal: true
    post:
      x-allowed-org-tiers: Advanced
      tags:
        - clusters
      summary: Create a BYOC cluster
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostByocClustersRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '409':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    get:
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
      tags:
        - clusters
      summary: List BYOC clusters
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagedClustersSizePage'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /byoc-cluster/{name}:
    parameters:
      - in: path
        required: true
        name: name
        schema:
          type: string
    put:
      x-internal: true
      x-allowed-org-tiers: Advanced
      tags:
        - clusters
      summary: Update a BYOC cluster
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PutByocClusterRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '404':
          $ref: '#/components/responses/DefaultResponse'
        '409':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    get:
      tags:
        - clusters
      summary: Get a BYOC cluster
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagedCluster'
        '404':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    delete:
      x-internal: true
      tags:
        - clusters
      summary: Delete a BYOC cluster
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '404':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /byoc-cluster/{name}/manualUpdate:
    x-internal: true
    parameters:
      - in: path
        required: true
        name: name
        schema:
          type: string
    post:
      x-allowed-org-tiers: Advanced
      tags:
        - clusters
      summary: update the BYOC cluster version, if version is not specified, update it to the default version
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostByocClusterUpdateRequestBody'
      responses:
        '202':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/DefaultResponse'
        '404':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /byoc-cluster/{name}/update:
    x-internal: true
    parameters:
      - in: path
        required: true
        name: name
        schema:
          type: string
    post:
      x-allowed-org-tiers: Advanced
      tags:
        - clusters
      summary: update the BYOC cluster version, if version is not specified, update it to the default version
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostByocClusterUpdateRequestBody'
      responses:
        '202':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/DefaultResponse'
        '404':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
components:
  responses:
    ServiceUnavailableResponse:
      description: The current service is unavailable
      content:
        application/json:
          schema:
            type: object
            required:
              - msg
            properties:
              msg:
                type: string
    DefaultResponse:
      description: Default responses returning msg
      content:
        application/json:
          schema:
            type: object
            required:
              - msg
            properties:
              msg:
                type: string
    AvailabilityResponse:
      description: Availability response
      content:
        application/json:
          schema:
            type: object
            required:
              - available
              - msg
            properties:
              available:
                type: boolean
              msg:
                type: string
    BadRequestResponse:
      description: 400 Bad Request
      content:
        application/json:
          schema:
            type: object
            required:
              - msg
            properties:
              msg:
                type: string
    NotFoundResponse:
      description: 404 Not Found
      content:
        application/json:
          schema:
            type: object
            required:
              - msg
            properties:
              msg:
                type: string
    FailedPreconditionResponse:
      description: 400 Failed Precondition
      content:
        application/json:
          schema:
            type: object
            required:
              - msg
            properties:
              msg:
                type: string
    RangeNotSatisfiable:
      description: 416 Range Not Satisfiable
      content:
        application/json:
          schema:
            type: object
            required:
              - msg
            properties:
              msg:
                type: string
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    ApiKeyAuth:
      type: http
      scheme: basic
      description: Service account api key. Header format 'Basic base64(${key}:${secret})'
  schemas:
    TierId:
      type: string
      enum:
        - Free
        - Invited
        - Developer-Free
        - Developer-Basic
        - Developer-Test
        - Standard
        - BYOC
        - Test
        - Benchmark
    AvailableComponentType:
      type: object
      required:
        - id
        - cpu
        - memory
        - maximum
      properties:
        id:
          type: string
        cpu:
          type: string
        memory:
          type: string
        maximum:
          type: integer
    AvailableMetaStoreEtcd:
      type: object
      required:
        - nodes
        - maximumSizeGiB
      properties:
        nodes:
          type: array
          items:
            $ref: '#/components/schemas/AvailableComponentType'
        maximumSizeGiB:
          type: integer
    AvailableMetaStorePostgreSql:
      type: object
      required:
        - nodes
        - maximumSizeGiB
      properties:
        nodes:
          type: array
          items:
            $ref: '#/components/schemas/AvailableComponentType'
        maximumSizeGiB:
          type: integer
    AvailableMetaStoreAwsRds:
      type: object
      properties:
        enabled:
          type: boolean
    AvailableMetaStoreGcpCloudSql:
      type: object
      properties:
        enabled:
          type: boolean
    AvailableMetaStoreAzrPostgres:
      type: object
      properties:
        enabled:
          type: boolean
    AvailableMetaStoreSharingPg:
      type: object
      properties:
        enabled:
          type: boolean
    AvailableMetaStore:
      type: object
      properties:
        etcd:
          $ref: '#/components/schemas/AvailableMetaStoreEtcd'
        postgresql:
          $ref: '#/components/schemas/AvailableMetaStorePostgreSql'
        aws_rds:
          $ref: '#/components/schemas/AvailableMetaStoreAwsRds'
        gcp_cloudsql:
          $ref: '#/components/schemas/AvailableMetaStoreGcpCloudSql'
        azr_postgres:
          $ref: '#/components/schemas/AvailableMetaStoreAzrPostgres'
        sharing_pg:
          $ref: '#/components/schemas/AvailableMetaStoreSharingPg'
    Tier:
      type: object
      required:
        - name
        - availableStandaloneNodes
        - availableComputeNodes
        - availableCompactorNodes
        - availableFrontendNodes
        - availableMetaNodes
        - maximumComputeNodeFileCacheSizeGiB
        - validityPeriod
        - retentionPeriod
      properties:
        id:
          $ref: '#/components/schemas/TierId'
        availableStandaloneNodes:
          type: array
          items:
            $ref: '#/components/schemas/AvailableComponentType'
        availableComputeNodes:
          type: array
          items:
            $ref: '#/components/schemas/AvailableComponentType'
        availableCompactorNodes:
          type: array
          items:
            $ref: '#/components/schemas/AvailableComponentType'
        availableFrontendNodes:
          type: array
          items:
            $ref: '#/components/schemas/AvailableComponentType'
        availableMetaNodes:
          type: array
          items:
            $ref: '#/components/schemas/AvailableComponentType'
        maximumComputeNodeFileCacheSizeGiB:
          type: integer
        validityPeriod:
          type: integer
        retentionPeriod:
          type: integer
        availableMetaStore:
          $ref: '#/components/schemas/AvailableMetaStore'
    TierArray:
      type: array
      items:
        $ref: '#/components/schemas/Tier'
    Tiers:
      type: object
      required:
        - tiers
      properties:
        tiers:
          $ref: '#/components/schemas/TierArray'
    Page:
      type: object
      required:
        - limit
        - offset
      properties:
        limit:
          type: integer
          format: uint64
        offset:
          type: integer
          format: uint64
    Size:
      type: object
      required:
        - size
      properties:
        size:
          type: integer
          format: uint64
    ComponentResource:
      type: object
      required:
        - componentTypeId
        - replica
        - cpu
        - memory
      properties:
        componentTypeId:
          type: string
        cpu:
          type: string
        memory:
          type: string
        replica:
          type: integer
    TenantResourceComponents:
      type: object
      properties:
        standalone:
          $ref: '#/components/schemas/ComponentResource'
        compute:
          $ref: '#/components/schemas/ComponentResource'
        compactor:
          $ref: '#/components/schemas/ComponentResource'
        frontend:
          $ref: '#/components/schemas/ComponentResource'
        meta:
          $ref: '#/components/schemas/ComponentResource'
        etcd:
          $ref: '#/components/schemas/ComponentResource'
    TenantResourceComputeCache:
      type: object
      required:
        - sizeGb
      properties:
        sizeGb:
          type: integer
    MetaStoreType:
      type: string
      enum:
        - etcd
        - postgresql
        - aws_rds
        - gcp_cloudsql
        - azr_postgres
        - sharing_pg
    MetaStoreEtcd:
      type: object
      required:
        - resource
        - sizeGb
      properties:
        resource:
          $ref: '#/components/schemas/ComponentResource'
        sizeGb:
          type: integer
    MetaStorePostgreSql:
      type: object
      required:
        - resource
        - sizeGb
      properties:
        resource:
          $ref: '#/components/schemas/ComponentResource'
        sizeGb:
          type: integer
    MetaStoreAwsRds:
      type: object
      required:
        - instanceClass
        - sizeGb
      properties:
        instanceClass:
          type: string
        sizeGb:
          type: integer
    MetaStoreGcpCloudSql:
      type: object
      required:
        - tier
        - sizeGb
      properties:
        tier:
          type: string
        sizeGb:
          type: integer
    MetaStoreAzrPostgres:
      type: object
      required:
        - sku
        - sizeGb
      properties:
        sku:
          type: string
        sizeGb:
          type: integer
    MetaStoreSharingPg:
      type: object
      required:
        - instanceId
      properties:
        instanceId:
          type: string
    TenantResourceMetaStore:
      type: object
      required:
        - type
        - rwu
      properties:
        type:
          $ref: '#/components/schemas/MetaStoreType'
        rwu:
          type: string
        etcd:
          $ref: '#/components/schemas/MetaStoreEtcd'
        postgresql:
          $ref: '#/components/schemas/MetaStorePostgreSql'
        aws_rds:
          $ref: '#/components/schemas/MetaStoreAwsRds'
        gcp_cloudsql:
          $ref: '#/components/schemas/MetaStoreGcpCloudSql'
        azr_postgres:
          $ref: '#/components/schemas/MetaStoreAzrPostgres'
        sharing_pg:
          $ref: '#/components/schemas/MetaStoreSharingPg'
    TenantResourceGroup:
      type: object
      required:
        - name
        - resource
        - computeCache
      properties:
        name:
          type: string
        resource:
          $ref: '#/components/schemas/ComponentResource'
        computeCache:
          $ref: '#/components/schemas/TenantResourceComputeCache'
    TenantResourceGroupArray:
      type: array
      items:
        $ref: '#/components/schemas/TenantResourceGroup'
    TenantResource:
      type: object
      required:
        - components
        - computeCache
      properties:
        components:
          $ref: '#/components/schemas/TenantResourceComponents'
        etcdVolumeSizeGiB:
          type: integer
        computeCache:
          $ref: '#/components/schemas/TenantResourceComputeCache'
        metaStore:
          $ref: '#/components/schemas/TenantResourceMetaStore'
        resourceGroups:
          $ref: '#/components/schemas/TenantResourceGroupArray'
    Tenant:
      type: object
      required:
        - id
        - userId
        - tenantName
        - region
        - status
        - tier
        - resources
        - createdAt
        - imageTag
        - latestImageTag
        - rw_config
        - updatedAt
        - health_status
        - nsId
        - orgId
        - etcd_config
        - usageType
      properties:
        id:
          type: integer
          format: uint64
        userId:
          type: integer
          format: uint64
        orgId:
          type: string
          format: uuid
        tenantName:
          type: string
        region:
          type: string
        resources:
          $ref: '#/components/schemas/TenantResource'
        status:
          type: string
          enum:
            - Creating
            - Running
            - Deleting
            - Failed
            - Stopped
            - Stopping
            - Starting
            - Expired
            - ConfigUpdating
            - Upgrading
            - Updating
            - Snapshotting
            - ExtensionCompactionEnabling
            - ExtensionCompactionDisabling
            - ExtensionServerlessBackfillUpdate
            - ExtensionServerlessBackfillEnabling
            - ExtensionServerlessBackfillDisabling
            - MetaMigrating
            - Restoring
            - Quiesced
            - ResourceGroupsUpdating
        tier:
          $ref: '#/components/schemas/TierId'
        usageType:
          type: string
          enum:
            - general
            - pipeline
        imageTag:
          type: string
          example: v0.1.12
        latestImageTag:
          type: string
          example: v0.1.12
        rw_config:
          type: string
        etcd_config:
          type: string
        health_status:
          type: string
          enum:
            - Unknown
            - Healthy
            - Unhealthy
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        nsId:
          type: string
          format: uuid
        clusterName:
          type: string
        upcomingSnapshotTime:
          type: string
          format: date-time
    TenantArray:
      type: array
      items:
        $ref: '#/components/schemas/Tenant'
    TenantSizePage:
      type: object
      allOf:
        - $ref: '#/components/schemas/Page'
        - $ref: '#/components/schemas/Size'
        - type: object
          required:
            - tenants
          properties:
            tenants:
              $ref: '#/components/schemas/TenantArray'
    ComponentResourceRequest:
      type: object
      required:
        - componentTypeId
        - replica
      properties:
        componentTypeId:
          type: string
        replica:
          type: integer
    TenantResourceRequestComponents:
      type: object
      properties:
        standalone:
          $ref: '#/components/schemas/ComponentResourceRequest'
        compute:
          $ref: '#/components/schemas/ComponentResourceRequest'
        compactor:
          $ref: '#/components/schemas/ComponentResourceRequest'
        frontend:
          $ref: '#/components/schemas/ComponentResourceRequest'
        meta:
          $ref: '#/components/schemas/ComponentResourceRequest'
        etcd:
          $ref: '#/components/schemas/ComponentResourceRequest'
    TenantResourceRequestMetaStoreEtcd:
      type: object
      allOf:
        - $ref: '#/components/schemas/ComponentResourceRequest'
        - type: object
          required:
            - sizeGb
          properties:
            sizeGb:
              type: integer
    TenantResourceRequestMetaStorePostgreSql:
      type: object
      allOf:
        - $ref: '#/components/schemas/ComponentResourceRequest'
        - type: object
          required:
            - sizeGb
          properties:
            sizeGb:
              type: integer
    TenantResourceRequestMetaStoreAwsRds:
      type: object
      required:
        - instanceClass
        - sizeGb
      properties:
        instanceClass:
          type: string
        sizeGb:
          type: integer
    TenantResourceRequestMetaStore:
      type: object
      required:
        - type
      properties:
        type:
          $ref: '#/components/schemas/MetaStoreType'
        etcd:
          $ref: '#/components/schemas/TenantResourceRequestMetaStoreEtcd'
        postgresql:
          $ref: '#/components/schemas/TenantResourceRequestMetaStorePostgreSql'
        aws_rds:
          $ref: '#/components/schemas/TenantResourceRequestMetaStoreAwsRds'
    TenantResourceRequest:
      type: object
      required:
        - components
        - computeFileCacheSizeGiB
      properties:
        components:
          $ref: '#/components/schemas/TenantResourceRequestComponents'
        etcdVolumeSizeGiB:
          type: integer
        computeFileCacheSizeGiB:
          type: integer
        metaStore:
          $ref: '#/components/schemas/TenantResourceRequestMetaStore'
    TenantRequestRequestBody:
      type: object
      required:
        - tenantName
      properties:
        tenantName:
          type: string
        sku:
          type: string
        imageTag:
          type: string
          example: latest
        clusterName:
          type: string
        tier:
          $ref: '#/components/schemas/TierId'
        resources:
          $ref: '#/components/schemas/TenantResourceRequest'
        etcdConfig:
          type: string
        configId:
          type: string
          format: uuid
        rwConfig:
          description: if config ID is not provided, use this config. currently used in tf plugin
          type: string
        usageType:
          type: string
          enum:
            - general
            - pipeline
          default: general
    CreateTenantResponseBody:
      type: object
      required:
        - tenantId
        - tenantName
      properties:
        tenantId:
          type: integer
          format: uint64
        tenantName:
          type: string
    PostTenantResourcesRequestBody:
      type: object
      properties:
        meta:
          $ref: '#/components/schemas/ComponentResourceRequest'
        frontend:
          $ref: '#/components/schemas/ComponentResourceRequest'
        compute:
          $ref: '#/components/schemas/ComponentResourceRequest'
        compactor:
          $ref: '#/components/schemas/ComponentResourceRequest'
        standalone:
          $ref: '#/components/schemas/ComponentResourceRequest'
    GetDatabasesResponseBody:
      type: object
      required:
        - databases
      properties:
        databases:
          type: array
          items:
            type: string
    BackupSnapshotItem:
      type: object
      required:
        - id
        - created_at_unix_mills
        - status
      properties:
        id:
          type: string
          format: uuid
        created_at_unix_mills:
          type: integer
          format: int64
        meta_snapshot_id:
          type: integer
          format: int64
        status:
          type: string
        type:
          type: string
        rw_version:
          type: string
    BackupSnapshotsSizePage:
      x-internal: true
      type: object
      allOf:
        - $ref: '#/components/schemas/Page'
        - $ref: '#/components/schemas/Size'
        - type: object
          required:
            - items
          properties:
            items:
              type: array
              items:
                $ref: '#/components/schemas/BackupSnapshotItem'
    PostSnapshotResponseBody:
      type: object
      required:
        - workflowId
        - snapshotId
      properties:
        workflowId:
          type: string
          format: uuid
        snapshotId:
          type: string
          format: uuid
    TenantClusterInfoResponseBody:
      type: object
      required:
        - tenantId
        - tenantName
        - clusterInfo
      properties:
        tenantId:
          type: integer
          format: uint64
        tenantName:
          type: string
        clusterInfo:
          type: string
    GetImageTagResponse:
      type: object
      required:
        - imageTag
      properties:
        imageTag:
          type: string
    RelationCounts:
      type: object
      required:
        - table
        - materialized_view
        - source
        - sink
      properties:
        table:
          type: integer
          format: uint32
        materialized_view:
          type: integer
          format: uint32
        source:
          type: integer
          format: uint32
        sink:
          type: integer
          format: uint32
    PostPrivateLinkRequestBody:
      type: object
      required:
        - target
        - connectionName
      properties:
        target:
          type: string
        connectionName:
          type: string
    PostPrivateLinkResponseBody:
      type: object
      required:
        - id
        - connectionName
      properties:
        id:
          type: string
          format: uuid
        connectionName:
          type: string
    PrivateLink:
      type: object
      required:
        - id
        - tenantId
        - status
        - connectionState
        - connectionName
      properties:
        connectionName:
          type: string
        id:
          type: string
          format: uuid
        tenantId:
          type: integer
          format: int64
        status:
          type: string
          enum:
            - CREATING
            - CREATED
            - DELETING
            - ERROR
            - UNKNOWN
        connectionState:
          type: string
          enum:
            - STATUS_UNSPECIFIED
            - PENDING
            - ACCEPTED
            - REJECTED
            - CLOSED
        target:
          type: string
        endpoint:
          type: string
    DBUser:
      type: object
      required:
        - usesysid
        - username
        - usecreatedb
        - usesuper
        - usecreateuser
        - canlogin
      properties:
        usesysid:
          type: integer
          format: uint64
        username:
          type: string
        usecreatedb:
          type: boolean
        usesuper:
          type: boolean
        usecreateuser:
          type: boolean
        canlogin:
          type: boolean
    DBUserArray:
      type: array
      items:
        $ref: '#/components/schemas/DBUser'
    DBUsers:
      type: object
      properties:
        dbusers:
          $ref: '#/components/schemas/DBUserArray'
    UpdateDBUserRequestBody:
      type: object
      required:
        - tenantId
        - username
        - password
      properties:
        tenantId:
          type: integer
          format: uint64
        username:
          type: string
        password:
          type: string
    CreateDBUserRequestBody:
      type: object
      required:
        - tenantId
        - username
        - password
        - superuser
        - createdb
      properties:
        tenantId:
          type: integer
          format: uint64
        username:
          type: string
        password:
          type: string
        superuser:
          type: boolean
        createdb:
          type: boolean
        createuser:
          type: boolean
    Endpoint:
      type: object
      required:
        - id
        - tenantId
        - host
        - port
        - database
        - options
        - internalHost
        - internalPort
      properties:
        id:
          type: integer
          format: int64
        tenantId:
          type: integer
          format: int64
        host:
          type: string
        port:
          type: integer
        database:
          type: string
        options:
          type: string
        internalHost:
          type: string
        internalPort:
          type: integer
    LabelItem:
      type: object
      required:
        - key
        - value
      properties:
        key:
          type: string
        value:
          type: string
    MetricPoint:
      type: object
      required:
        - timestamp
        - value
      properties:
        timestamp:
          type: string
          format: date-time
        value:
          type: number
          format: double
    MetricItem:
      type: object
      required:
        - labels
        - values
      properties:
        labels:
          type: array
          items:
            $ref: '#/components/schemas/LabelItem'
        values:
          type: array
          items:
            $ref: '#/components/schemas/MetricPoint'
    Metrics:
      type: object
      required:
        - name
        - items
      properties:
        name:
          type: string
        items:
          type: array
          items:
            $ref: '#/components/schemas/MetricItem'
    ErrLogQueryResult:
      type: object
      required:
        - status
        - values
      properties:
        status:
          type: string
        values:
          type: array
          items:
            type: array
            items:
              type: string
            minItems: 2
            maxItems: 2
    TenantStatusCount:
      type: object
      required:
        - status
        - count
      properties:
        count:
          type: integer
          format: int64
        status:
          type: string
    TenantStatusCountArray:
      type: array
      items:
        $ref: '#/components/schemas/TenantStatusCount'
    TenantStatusCounts:
      type: object
      required:
        - status
      properties:
        status:
          $ref: '#/components/schemas/TenantStatusCountArray'
    KafkaConfig:
      type: object
      required:
        - server
      properties:
        server:
          type: string
        securityProtocol:
          type: string
          enum:
            - SASL_SSL
            - SSL
            - SASL_PLAINTEXT
        saslMechanisms:
          type: string
          enum:
            - OAUTHBEARER
            - GSSAPI
            - PLAIN
            - SCRAM-SHA-256
            - SCRAM-SHA-512
        saslUsername:
          type: string
        saslPassword:
          type: string
        caCertificate:
          type: string
    PostSourceRequestBody:
      type: object
      required:
        - type
        - config
      properties:
        type:
          type: string
          enum:
            - kafka
        config:
          oneOf:
            - $ref: '#/components/schemas/KafkaConfig'
    PostSourcesFetchKafkaTopicRequestBody:
      type: object
      required:
        - kafka_config
      properties:
        kafka_config:
          $ref: '#/components/schemas/KafkaConfig'
    PostSourcesFetchKafkaTopicResponseBody:
      type: object
      required:
        - topics
      properties:
        topics:
          type: array
          items:
            type: string
    SqlExecutionRequestBody:
      type: object
      required:
        - database
        - username
        - password
        - query
      properties:
        username:
          type: string
        password:
          type: string
        database:
          type: string
        query:
          type: string
        timeout:
          type: integer
          description: timeout in ms
          default: 5000
    DropTenantRelationBody:
      type: object
      required:
        - database
        - relname
        - username
        - password
      properties:
        username:
          type: string
        relname:
          type: string
        password:
          type: string
        database:
          type: string
    WorkflowIdResponseBody:
      type: object
      required:
        - workflowId
      properties:
        workflowId:
          type: string
          format: uuid
    ClusterStatus:
      type: string
      enum:
        - Uninitialized
        - Provisioned
        - Ready
        - Terminating
        - Deleted
        - PendingResourceDeletion
        - Updating
        - Failed
    ManagedCluster:
      required:
        - id
        - org
        - name
        - status
        - master_url
        - cluster_service_account
        - token
        - serving_type
        - settings
      properties:
        id:
          type: integer
          format: uint64
        org:
          type: string
          format: uuid
        name:
          type: string
        status:
          $ref: '#/components/schemas/ClusterStatus'
        master_url:
          type: string
        cluster_service_account:
          type: string
        token:
          type: string
        serving_type:
          type: string
        settings:
          type: object
          additionalProperties:
            type: string
    ManagedClustersSizePage:
      x-internal: true
      type: object
      allOf:
        - $ref: '#/components/schemas/Page'
        - $ref: '#/components/schemas/Size'
        - type: object
          required:
            - clusters
          properties:
            clusters:
              type: array
              items:
                $ref: '#/components/schemas/ManagedCluster'
    PostByocClustersRequestBody:
      required:
        - name
        - settings
      properties:
        name:
          type: string
        version:
          type: string
        settings:
          type: object
          additionalProperties:
            type: string
    PutByocClusterRequestBody:
      required:
        - name
        - status
        - settings
      properties:
        name:
          type: string
        status:
          $ref: '#/components/schemas/ClusterStatus'
        settings:
          type: object
          additionalProperties:
            type: string
    PostByocClusterUpdateRequestBody:
      type: object
      properties:
        version:
          type: string
        customSettings:
          description: base64 encoded custom settings
          type: string
