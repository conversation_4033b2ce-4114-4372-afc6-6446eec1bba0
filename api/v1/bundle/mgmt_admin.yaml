# Code generated by redocly/cli DO NOT EDIT.
openapi: 3.0.3
info:
  title: v1
  version: 1.0-alpha
servers:
  - url: /api/v1
tags:
  - name: tenants
    description: Operations about tenants
  - name: diagnosis
    description: Operations about RW Diagnosis Report
paths:
  /:
    get:
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
  /version:
    get:
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
  /tenant/{tenantId}:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    get:
      summary: Get a tenant
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Tenant'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
    delete:
      summary: Delete a tenant
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowIdResponseBody'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
  /tenant/{tenantId}/privatelink/{privateLinkId}:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
      - in: path
        required: true
        name: privateLinkId
        schema:
          type: string
          format: uuid
    delete:
      summary: Delete a tenant's private link by id
      tags:
        - privateLinks
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowIdResponseBody'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
  /tenants:
    get:
      tags:
        - tenants
      summary: Get all the tenants in the Management System
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TenantPage'
  /tenant/{tenantId}/endpoint:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    get:
      summary: Get the root endpoint of the specified tenant
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DatabaseConnectionUrl'
        '401':
          $ref: '#/components/responses/DefaultResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
  /tenant/{tenantId}/expire:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    get:
      summary: Get the expire information
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TenantExpireInfo'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
    post:
      summary: Update expiration lifecycle
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostTenantExpireRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
    delete:
      summary: Cancel expiration workflow
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
  /tenant/{tenantId}/stop:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      tags:
        - tenant
      summary: Stop the tenant
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowIdResponseBody'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
  /tenant/{tenantId}/start:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      tags:
        - tenant
      summary: Start the tenant
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowIdResponseBody'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
  /tenant/{tenantId}/restart:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      tags:
        - tenant
      summary: Restart the tenant
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowIdResponseBody'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
  /tenant/{tenantId}/updateVersion:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      tags:
        - tenant
      summary: Update the tenant version
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostTenantUpdateVersionRequestBody'
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowIdResponseBody'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/DefaultResponse'
  /tenant/{tenantId}/config/risingwave:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
      - in: query
        name: component
        schema:
          type: string
      - in: query
        name: nodeGroup
        schema:
          type: string
    put:
      tags:
        - tenant
      summary: Update the tenant config
      requestBody:
        required: true
        content:
          text/plain:
            schema:
              type: string
              description: toml risingwave config
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowIdResponseBody'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/DefaultResponse'
  /tenant/{tenantId}/configNoRestart/risingwave:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
      - in: query
        name: component
        schema:
          type: string
      - in: query
        name: nodeGroup
        schema:
          type: string
    put:
      tags:
        - tenant
      summary: Update the tenant config
      requestBody:
        required: true
        content:
          text/plain:
            schema:
              type: string
              description: toml risingwave config
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowIdResponseBody'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/DefaultResponse'
  /tenant/{tenantId}/config/etcd:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    put:
      tags:
        - tenant
      summary: Update the etcd config
      requestBody:
        required: true
        content:
          text/plain:
            schema:
              type: string
            example: |
              ETCD_QUOTA_BACKEND_BYTES: '1000000000'
              ETCD_MAX_REQUEST_BYTES: '100000000'
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowIdResponseBody'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
  /tenant/{tenantId}/resource:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      tags:
        - tenant
      summary: Update a tenant resource
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostTenantResourcesRequestBody'
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowIdResponseBody'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
  /tenant/{tenantId}/computeCache:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      tags:
        - tenant
      summary: update compute cache for tenant
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostTenantComputeCacheRequestBody'
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowIdResponseBody'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
  /tenant/{tenantId}/status:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      tags:
        - tenant
      summary: Reset tenant status
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostTenantStatusRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
  /tenant/{tenantId}/tier:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      tags:
        - tenant
      summary: Update tenant tier
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostTenantTierRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
  /tenant/{tenantId}/resourceGroups:
    post:
      tags:
        - ResourceGroups
      summary: Create resource group
      parameters:
        - in: path
          name: tenantId
          schema:
            type: integer
            format: uint64
          required: true
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateResourceGroupsRequestBody'
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowIdResponseBody'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
  /tenant/{tenantId}/resourceGroups/{resourceGroup}:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
      - in: path
        name: resourceGroup
        schema:
          type: string
        required: true
    post:
      tags:
        - ResourceGroups
      summary: Update resource group
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateResourceGroupsRequestBody'
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowIdResponseBody'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
    delete:
      tags:
        - ResourceGroups
      summary: Delete resource group
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowIdResponseBody'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
  /tenant/{tenantId}/extensions/compaction/enable:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      tags:
        - tenant
      summary: Enable the risingwave extensions for serverless compaction
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowIdResponseBody'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
  /tenant/{tenantId}/extensions/compaction/disable:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      tags:
        - tenant
      summary: Disable the risingwave extensions for serverless compaction
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowIdResponseBody'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
  /tenant/{tenantId}/extensions/compaction/update:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      tags:
        - tenant
      summary: Update the risingwave extensions for serverless compaction
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostTenantExtensionCompactionRequestBody'
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowIdResponseBody'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
  /tenant/{tenantId}/extensions/compaction/parameters:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    get:
      tags:
        - tenant
      summary: Get the parameters of the risingwave extensions for serverless compaction
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetTenantExtensionCompactionParametersResponseBody'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
  /tenant/{tenantId}/extensions/compaction/status:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    get:
      tags:
        - tenant
      summary: Get the status of the risingwave extensions for serverless compaction
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetTenantExtensionCompactionStatusResponseBody'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
    post:
      tags:
        - tenant
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostTenantExtensionCompactionStatusRequestBody'
      summary: Get the status of the risingwave extensions for serverless compaction
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetTenantExtensionCompactionStatusResponseBody'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
  /tenant/{tenantId}/extensions/serverlessbackfill/enable:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      tags:
        - tenant
      summary: Enable the risingwave extensions for serverless backfilling
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowIdResponseBody'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/DefaultResponse'
  /tenant/{tenantId}/extensions/serverlessbackfill/disable:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      tags:
        - tenant
      summary: Disable the risingwave extensions for serverless backfilling
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowIdResponseBody'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/DefaultResponse'
  /tenant/{tenantId}/extensions/serverlessbackfill/version:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      tags:
        - tenant
      summary: Update the risingwave extensions for serverless backfilling to version. Version format like 1.0.0.
      requestBody:
        required: true
        content:
          text/plain:
            schema:
              type: string
            example: 1.2.3
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowIdResponseBody'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/DefaultResponse'
  /tenants/{tenantId}/extensions/iceberg-compaction:
    parameters:
      - in: path
        required: true
        name: tenantId
        schema:
          type: integer
          format: uint64
    get:
      tags:
        - tenants
      summary: Get iceberg compaction status
      responses:
        '200':
          description: Iceberg compaction status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IcebergCompaction'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
    post:
      tags:
        - tenants
      summary: Enable iceberg compaction
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                config:
                  type: string
                resources:
                  $ref: '#/components/schemas/ComponentResourceRequest'
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowIdResponseBody'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
    put:
      tags:
        - tenants
      summary: Update iceberg compaction
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                config:
                  type: string
                resources:
                  $ref: '#/components/schemas/ComponentResourceRequest'
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowIdResponseBody'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
    delete:
      tags:
        - tenants
      summary: Disable iceberg compaction
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowIdResponseBody'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
  /tenant/{tenantId}/migrateMetaStore:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      tags:
        - tenant
      summary: Migrate the risingwave meta store
      responses:
        '202':
          $ref: '#/components/responses/WorkflowIdResponse'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
  /tenant/{tenantId}/upgradeMetaStore:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
      - in: query
        name: metaStore
        schema:
          type: string
        required: true
    post:
      tags:
        - tenant
      responses:
        '202':
          $ref: '#/components/responses/WorkflowIdResponse'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
  /workflows:
    get:
      description: Get all workflows order by createdAt desc
      tags:
        - workflow
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowPage'
  /workflow/{id}:
    parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
    get:
      description: Get workflow with events
      tags:
        - workflow
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowWithEvents'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
  /workflow/{id}/cancel:
    parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
    post:
      description: Cancel a workflow
      tags:
        - workflow
      responses:
        '200':
          description: OK
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
  /workflow/{id}/resume:
    parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
    post:
      description: Resume a cancelled workflow
      tags:
        - workflow
      responses:
        '200':
          description: OK
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
  /workflow/{id}/rerun:
    parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
    post:
      description: Rerun a finished workflow
      tags:
        - workflow
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostWorkflowRerunRequestBody'
      responses:
        '202':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowIdResponseBody'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
  /workflow/{id}/schedule:
    parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
    post:
      description: Schedule a running workflow's delay
      tags:
        - workflow
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostWorkflowScheduleRequestBody'
      responses:
        '200':
          description: OK
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
  /clusters:
    get:
      summary: Get all clusters
      tags:
        - cluster
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagedClusters'
    post:
      summary: Create or update cluster by name
      tags:
        - cluster
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostClusterRequestBody'
      responses:
        '200':
          description: OK
  /cluster/{name}:
    parameters:
      - in: path
        name: name
        schema:
          type: string
    get:
      summary: Get a cluster
      tags:
        - cluster
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagedCluster'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
    post:
      summary: Create or update cluster
      tags:
        - cluster
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostClusterRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
    delete:
      summary: delete cluster
      tags:
        - cluster
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
  /byoc-clusters:
    get:
      summary: Get all clusters
      tags:
        - cluster
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagedClusters'
  /byoc-cluster/{id}/access:
    parameters:
      - in: path
        name: id
        description: id of cluster row
        schema:
          type: integer
          format: uint64
    post:
      summary: Get temporary access token for the requested cluster
      tags:
        - cluster
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostByocClusterAccessRequestBody'
      responses:
        '200':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClusterAccessInfo'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
  /byoc-cluster/{id}/update:
    parameters:
      - in: path
        name: id
        description: id of cluster row
        schema:
          type: integer
          format: uint64
    post:
      summary: update the BYOC cluster version, if version is not specified, update it to the default version
      tags:
        - cluster
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostByocClusterUpdateRequestBody'
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowIdResponseBody'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
  /clusters/{uuid}/prometheus/api/v1/query:
    parameters:
      - in: path
        name: uuid
        schema:
          type: string
          format: uuid
    get:
      responses:
        '200': {}
        '412':
          $ref: '#/components/responses/ClusterStatusResponse'
    post:
      responses:
        '200': {}
        '412':
          $ref: '#/components/responses/ClusterStatusResponse'
  /clusters/{uuid}/prometheus/api/v1/query_range:
    parameters:
      - in: path
        name: uuid
        schema:
          type: string
          format: uuid
    get:
      responses:
        '200': {}
        '412':
          $ref: '#/components/responses/ClusterStatusResponse'
    post:
      responses:
        '200': {}
        '412':
          $ref: '#/components/responses/ClusterStatusResponse'
  /clusters/{uuid}/prometheus/api/v1/series:
    parameters:
      - in: path
        name: uuid
        schema:
          type: string
          format: uuid
    get:
      responses:
        '200': {}
        '412':
          $ref: '#/components/responses/ClusterStatusResponse'
    post:
      responses:
        '200': {}
        '412':
          $ref: '#/components/responses/ClusterStatusResponse'
  /clusters/{uuid}/prometheus/api/v1/label/{labelName}/values:
    parameters:
      - in: path
        name: uuid
        schema:
          type: string
          format: uuid
      - in: path
        name: labelName
        schema:
          type: string
    get:
      responses:
        '200': {}
        '412':
          $ref: '#/components/responses/ClusterStatusResponse'
    post:
      responses:
        '200': {}
        '412':
          $ref: '#/components/responses/ClusterStatusResponse'
  /tenant/{tenantId}/diagnosis/report/generate:
    parameters:
      - in: path
        required: true
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      summary: generate a RW Diagnosis Report
      description: generate a RW Diagnosis Report from a RW tenant, persist it in a cloud storage bucket, and return its info
      tags:
        - diagnosis
        - tenant
      operationId: genDiagReport
      responses:
        '200':
          description: Generated Report Info Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RwDiagReport'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
  /tenant/{tenantId}/diagnosis/report/{reportId}:
    get:
      summary: fetch the content of the RW Diagnosis Report
      description: fetch the content of the pre-generated RW Diagnosis Report if available from a cloud storage bucket
      tags:
        - diagnosis
        - tenant
      operationId: getDiagReport
      parameters:
        - in: path
          required: true
          name: tenantId
          schema:
            type: integer
            format: uint64
        - in: path
          required: true
          name: reportId
          schema:
            type: integer
            format: uint64
      responses:
        '200':
          description: Retrieved Report Info Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RwDiagReport'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
  /tenant/{tenantId}/diagnosis/report/range:
    get:
      summary: list a set of RW Diagnosis Reports within a specified range of time
      description: list a set of RW Diagnosis Reports (excluding content) within a specified range of time from a cloud storage bucket
      tags:
        - diagnosis
        - tenant
      operationId: listDiagReportTimeRange
      parameters:
        - in: path
          required: true
          name: tenantId
          schema:
            type: integer
            format: uint64
        - in: query
          name: startTime
          description: RFC3339/RFC3339Nano formatted starting time (inclusive)
          schema:
            type: string
            format: date-time
            nullable: true
            default: null
        - in: query
          name: endTime
          description: RFC3339/RFC3339Nano formatted ending time (inclusive)
          schema:
            type: string
            format: date-time
            nullable: true
            default: null
        - in: query
          name: limit
          description: pagination - how many report metas to return
          schema:
            type: integer
            format: uint64
            nullable: true
            default: null
        - in: query
          name: offset
          description: pagination - how many report metas to skip
          schema:
            type: integer
            format: uint64
            nullable: true
            default: 0
      responses:
        '200':
          description: Retrieved set of Reports Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RwDiagReportMetaPage'
  /tenant/{tenantId}/backup:
    post:
      summary: create a backup snapshot for the meta store of the tenant
      tags:
        - backup
        - tenant
      parameters:
        - in: path
          required: true
          name: tenantId
          schema:
            type: integer
            format: uint64
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostSnapshotResponseBody'
  /tenant/{tenantId}/backup/{snapshotId}/restore:
    post:
      summary: Restore to a new cluster based on the snapshot id
      tags:
        - backup
        - tenant
      parameters:
        - in: path
          required: true
          name: tenantId
          schema:
            type: integer
            format: uint64
        - in: path
          required: true
          name: snapshotId
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostTenantRestoreRequestBody'
      responses:
        '202':
          description: Starting to restore data from this snapshot.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Tenant'
  /tenant/{tenantId}/backup/{snapshotId}/in-place-restore:
    post:
      summary: Restore to a new cluster based on the snapshot id
      tags:
        - backup
        - tenant
      parameters:
        - in: path
          required: true
          name: tenantId
          schema:
            type: integer
            format: uint64
        - in: path
          required: true
          name: snapshotId
          schema:
            type: string
            format: uuid
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowIdResponseBody'
  /tenant/{tenantId}/backup/{snapshotId}:
    delete:
      summary: delete a backup snapshot for the meta store of the tenant
      tags:
        - backup
        - tenant
      parameters:
        - in: path
          required: true
          name: tenantId
          schema:
            type: integer
            format: uint64
        - in: path
          required: true
          name: snapshotId
          schema:
            type: string
            format: uuid
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OptionalWorkflowIdResponseBody'
  /webhooks/alert:
    post:
      summary: receive alerts reported from alert manager
      tags:
        - alerts
        - notifications
      responses:
        '200':
          description: OK
  /alertNotifications:
    post:
      summary: create alert notification
      tags:
        - alerts
        - notifications
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostAlertNotificationRequestBody'
      responses:
        '200':
          description: OK
components:
  schemas:
    ComponentResource:
      type: object
      required:
        - componentTypeId
        - replica
        - cpu
        - memory
      properties:
        componentTypeId:
          type: string
        cpu:
          type: string
        memory:
          type: string
        replica:
          type: integer
    TenantResourceComponents:
      type: object
      properties:
        standalone:
          $ref: '#/components/schemas/ComponentResource'
        compute:
          $ref: '#/components/schemas/ComponentResource'
        compactor:
          $ref: '#/components/schemas/ComponentResource'
        frontend:
          $ref: '#/components/schemas/ComponentResource'
        meta:
          $ref: '#/components/schemas/ComponentResource'
        etcd:
          $ref: '#/components/schemas/ComponentResource'
    TenantResourceComputeCache:
      type: object
      required:
        - sizeGb
      properties:
        sizeGb:
          type: integer
    MetaStoreType:
      type: string
      enum:
        - etcd
        - postgresql
        - aws_rds
        - gcp_cloudsql
        - azr_postgres
        - sharing_pg
    MetaStoreEtcd:
      type: object
      required:
        - resource
        - sizeGb
      properties:
        resource:
          $ref: '#/components/schemas/ComponentResource'
        sizeGb:
          type: integer
    MetaStorePostgreSql:
      type: object
      required:
        - resource
        - sizeGb
      properties:
        resource:
          $ref: '#/components/schemas/ComponentResource'
        sizeGb:
          type: integer
    MetaStoreAwsRds:
      type: object
      required:
        - instanceClass
        - sizeGb
      properties:
        instanceClass:
          type: string
        sizeGb:
          type: integer
    MetaStoreGcpCloudSql:
      type: object
      required:
        - tier
        - sizeGb
      properties:
        tier:
          type: string
        sizeGb:
          type: integer
    MetaStoreAzrPostgres:
      type: object
      required:
        - sku
        - sizeGb
      properties:
        sku:
          type: string
        sizeGb:
          type: integer
    MetaStoreSharingPg:
      type: object
      required:
        - instanceId
      properties:
        instanceId:
          type: string
    TenantResourceMetaStore:
      type: object
      required:
        - type
        - rwu
      properties:
        type:
          $ref: '#/components/schemas/MetaStoreType'
        rwu:
          type: string
        etcd:
          $ref: '#/components/schemas/MetaStoreEtcd'
        postgresql:
          $ref: '#/components/schemas/MetaStorePostgreSql'
        aws_rds:
          $ref: '#/components/schemas/MetaStoreAwsRds'
        gcp_cloudsql:
          $ref: '#/components/schemas/MetaStoreGcpCloudSql'
        azr_postgres:
          $ref: '#/components/schemas/MetaStoreAzrPostgres'
        sharing_pg:
          $ref: '#/components/schemas/MetaStoreSharingPg'
    TenantResourceGroup:
      type: object
      required:
        - name
        - resource
        - computeCache
      properties:
        name:
          type: string
        resource:
          $ref: '#/components/schemas/ComponentResource'
        computeCache:
          $ref: '#/components/schemas/TenantResourceComputeCache'
    TenantResourceGroupArray:
      type: array
      items:
        $ref: '#/components/schemas/TenantResourceGroup'
    TenantResource:
      type: object
      required:
        - components
        - computeCache
      properties:
        components:
          $ref: '#/components/schemas/TenantResourceComponents'
        etcdVolumeSizeGiB:
          type: integer
        computeCache:
          $ref: '#/components/schemas/TenantResourceComputeCache'
        metaStore:
          $ref: '#/components/schemas/TenantResourceMetaStore'
        resourceGroups:
          $ref: '#/components/schemas/TenantResourceGroupArray'
    TierId:
      type: string
      enum:
        - Free
        - Invited
        - Developer-Free
        - Developer-Basic
        - Developer-Test
        - Standard
        - BYOC
        - Test
        - Benchmark
    Tenant:
      type: object
      required:
        - id
        - userId
        - tenantName
        - region
        - status
        - tier
        - resources
        - createdAt
        - imageTag
        - latestImageTag
        - rw_config
        - updatedAt
        - health_status
        - nsId
        - orgId
        - etcd_config
        - usageType
      properties:
        id:
          type: integer
          format: uint64
        userId:
          type: integer
          format: uint64
        orgId:
          type: string
          format: uuid
        tenantName:
          type: string
        region:
          type: string
        resources:
          $ref: '#/components/schemas/TenantResource'
        status:
          type: string
          enum:
            - Creating
            - Running
            - Deleting
            - Failed
            - Stopped
            - Stopping
            - Starting
            - Expired
            - ConfigUpdating
            - Upgrading
            - Updating
            - Snapshotting
            - ExtensionCompactionEnabling
            - ExtensionCompactionDisabling
            - ExtensionServerlessBackfillUpdate
            - ExtensionServerlessBackfillEnabling
            - ExtensionServerlessBackfillDisabling
            - MetaMigrating
            - Restoring
            - Quiesced
            - ResourceGroupsUpdating
        tier:
          $ref: '#/components/schemas/TierId'
        usageType:
          type: string
          enum:
            - general
            - pipeline
        imageTag:
          type: string
          example: v0.1.12
        latestImageTag:
          type: string
          example: v0.1.12
        rw_config:
          type: string
        etcd_config:
          type: string
        health_status:
          type: string
          enum:
            - Unknown
            - Healthy
            - Unhealthy
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        nsId:
          type: string
          format: uuid
        clusterName:
          type: string
        upcomingSnapshotTime:
          type: string
          format: date-time
    WorkflowIdResponseBody:
      type: object
      required:
        - workflowId
      properties:
        workflowId:
          type: string
          format: uuid
    Page:
      type: object
      required:
        - limit
        - offset
      properties:
        limit:
          type: integer
          format: uint64
        offset:
          type: integer
          format: uint64
    TenantArray:
      type: array
      items:
        $ref: '#/components/schemas/Tenant'
    TenantPage:
      type: object
      allOf:
        - $ref: '#/components/schemas/Page'
        - type: object
          required:
            - tenants
          properties:
            tenants:
              $ref: '#/components/schemas/TenantArray'
    DatabaseConnectionUrl:
      type: object
      required:
        - url
      properties:
        url:
          type: string
          example: postgresql://identifier;username:password@host:port/database
    TenantExpireInfo:
      type: object
      required:
        - expireAt
        - deleteAt
      properties:
        expireAt:
          type: string
          format: date-time
          nullable: true
        deleteAt:
          type: string
          format: date-time
          nullable: true
    PostTenantExpireRequestBody:
      type: object
      properties:
        expireAt:
          type: string
          format: date-time
        deleteAt:
          type: string
          format: date-time
    PostTenantUpdateVersionRequestBody:
      type: object
      required:
        - version
      properties:
        version:
          type: string
        skipBackup:
          type: boolean
          default: false
    ComponentResourceRequest:
      type: object
      required:
        - componentTypeId
        - replica
      properties:
        componentTypeId:
          type: string
        replica:
          type: integer
    PostTenantResourcesRequestBody:
      type: object
      properties:
        meta:
          $ref: '#/components/schemas/ComponentResourceRequest'
        frontend:
          $ref: '#/components/schemas/ComponentResourceRequest'
        compute:
          $ref: '#/components/schemas/ComponentResourceRequest'
        compactor:
          $ref: '#/components/schemas/ComponentResourceRequest'
        standalone:
          $ref: '#/components/schemas/ComponentResourceRequest'
    PostTenantComputeCacheRequestBody:
      type: object
      properties:
        computeCacheSpec:
          $ref: '#/components/schemas/TenantResourceComputeCache'
        resourceGroups:
          type: array
          items:
            type: object
            required:
              - name
              - computeCacheSpec
            properties:
              name:
                type: string
              computeCacheSpec:
                $ref: '#/components/schemas/TenantResourceComputeCache'
    PostTenantStatusRequestBody:
      type: object
      required:
        - target
        - previous
      properties:
        target:
          type: string
        previous:
          type: string
    PostTenantTierRequestBody:
      type: object
      required:
        - target
        - previous
      properties:
        target:
          type: string
        previous:
          type: string
    CreateResourceGroupsRequestBody:
      type: object
      required:
        - name
        - resource
      properties:
        name:
          type: string
        resource:
          $ref: '#/components/schemas/ComponentResourceRequest'
        fileCacheSizeGb:
          type: integer
    UpdateResourceGroupsRequestBody:
      type: object
      required:
        - resource
      properties:
        resource:
          $ref: '#/components/schemas/ComponentResourceRequest'
    PostTenantExtensionCompactionCompactorRequestBody:
      type: object
      required:
        - CpuRequest
        - CpuLimit
        - MemoryRequest
        - MemoryLimit
      properties:
        CpuRequest:
          type: string
        CpuLimit:
          type: string
        MemoryRequest:
          type: string
        MemoryLimit:
          type: string
    PostTenantExtensionCompactionScalerRequestBody:
      type: object
      required:
        - PollingInterval
        - CollectInterval
        - ScaleDownToZeroRequiredTimes
        - ScaleDownToNRequiredTimes
        - CoolDownPeriod
        - MinReplicas
        - MaxReplicas
        - DesiredReplicas
        - DefaultParallelism
        - ExpirationExpireTime
        - DefaultCapacityReservedBuffer
      properties:
        PollingInterval:
          type: integer
        CollectInterval:
          type: integer
        ScaleDownToZeroRequiredTimes:
          type: integer
        ScaleDownToNRequiredTimes:
          type: integer
        CoolDownPeriod:
          type: integer
        MinReplicas:
          type: integer
        MaxReplicas:
          type: integer
        DesiredReplicas:
          type: integer
        DefaultParallelism:
          type: integer
        ExpirationExpireTime:
          type: integer
        DefaultCapacityReservedBuffer:
          type: integer
    PostTenantExtensionCompactionRequestBody:
      type: object
      properties:
        Compactor:
          $ref: '#/components/schemas/PostTenantExtensionCompactionCompactorRequestBody'
        Scaler:
          $ref: '#/components/schemas/PostTenantExtensionCompactionScalerRequestBody'
    GetTenantExtensionCompactionParametersResponseBody:
      type: object
      required:
        - parameters
      properties:
        Compactor:
          $ref: '#/components/schemas/PostTenantExtensionCompactionCompactorRequestBody'
        Scaler:
          $ref: '#/components/schemas/PostTenantExtensionCompactionScalerRequestBody'
    GetTenantExtensionCompactionStatusResponseBody:
      type: object
      required:
        - status
      properties:
        status:
          type: string
    PostTenantExtensionCompactionStatusRequestBody:
      type: object
      required:
        - previous
        - target
      properties:
        previous:
          type: string
        target:
          type: string
    IcebergCompaction:
      type: object
      required:
        - status
      properties:
        status:
          type: string
        config:
          type: string
        resources:
          $ref: '#/components/schemas/ComponentResource'
    Workflow:
      type: object
      required:
        - id
        - workflowType
        - fsmState
        - runningState
        - context
        - createdAt
        - updatedAt
      properties:
        id:
          type: string
          format: uuid
        workflowType:
          type: string
        fsmState:
          type: string
        runningState:
          type: string
          enum:
            - RunningWorkflow
            - FailedWorkflow
            - CompletedWorkflow
            - CancelledWorkflow
        context: {}
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        lockedAt:
          type: string
          format: date-time
        delayAt:
          type: string
          format: date-time
    WorkflowArray:
      type: array
      items:
        $ref: '#/components/schemas/Workflow'
    Workflows:
      type: object
      required:
        - workflows
      properties:
        workflows:
          $ref: '#/components/schemas/WorkflowArray'
    WorkflowPage:
      type: object
      allOf:
        - $ref: '#/components/schemas/Page'
        - $ref: '#/components/schemas/Workflows'
    WorkflowEvent:
      required:
        - id
        - workflowId
        - type
        - timestamp
        - attributes
      properties:
        id:
          type: integer
          format: uint64
        workflowId:
          type: string
          format: uuid
        type:
          type: string
        timestamp:
          type: string
          format: date-time
        attributes: {}
    WorkflowEventArray:
      type: array
      items:
        $ref: '#/components/schemas/WorkflowEvent'
    WorkflowWithEvents:
      type: object
      required:
        - workflow
        - events
      properties:
        workflow:
          $ref: '#/components/schemas/Workflow'
        events:
          $ref: '#/components/schemas/WorkflowEventArray'
    PostWorkflowRerunRequestBody:
      type: object
      properties:
        context:
          type: object
          description: context will be merged into the previous context
        fromState:
          type: string
        delayAt:
          type: string
          format: date-time
    PostWorkflowScheduleRequestBody:
      type: object
      properties:
        delayAt:
          type: string
          format: date-time
          nullable: true
    ClusterStatus:
      type: string
      enum:
        - Uninitialized
        - Provisioned
        - Ready
        - Terminating
        - Deleted
        - PendingResourceDeletion
        - Updating
        - Failed
    ManagedCluster:
      required:
        - id
        - org
        - name
        - status
        - master_url
        - cluster_service_account
        - token
        - serving_type
        - settings
      properties:
        id:
          type: integer
          format: uint64
        org:
          type: string
          format: uuid
        name:
          type: string
        status:
          $ref: '#/components/schemas/ClusterStatus'
        master_url:
          type: string
        cluster_service_account:
          type: string
        token:
          type: string
        serving_type:
          type: string
        settings:
          type: object
          additionalProperties:
            type: string
    ManagedClusterArray:
      type: array
      items:
        $ref: '#/components/schemas/ManagedCluster'
    ManagedClusters:
      type: object
      required:
        - clusters
      properties:
        clusters:
          $ref: '#/components/schemas/ManagedClusterArray'
    PostClusterRequestBody:
      required:
        - name
        - master_url
        - cluster_service_account
        - token
        - serving_type
        - settings
      properties:
        name:
          type: string
        master_url:
          type: string
        cluster_service_account:
          type: string
        token:
          type: string
        serving_type:
          type: string
        settings:
          type: object
          additionalProperties:
            type: string
    PostByocClusterAccessRequestBody:
      required:
        - timeoutMins
      properties:
        timeoutMins:
          type: integer
          description: timeout in minutes
    ClusterAccessInfo:
      required:
        - endpoint
        - caCertBase64
        - token
      properties:
        endpoint:
          type: string
        caCertBase64:
          type: string
        token:
          type: string
    PostByocClusterUpdateRequestBody:
      type: object
      properties:
        version:
          type: string
        customSettings:
          description: base64 encoded custom settings
          type: string
    ClusterReadyStatus:
      type: string
      enum:
        - Before
        - Interrupted
        - After
    RwDiagReportMeta:
      type: object
      required:
        - id
        - tenantId
        - processedAt
      properties:
        id:
          type: integer
          description: can be unix timestamp seconds, or other things
          format: uint64
        tenantId:
          type: integer
          format: uint64
        processedAt:
          type: string
          format: date-time
    RwDiagReport:
      type: object
      required:
        - meta
        - presignedGetUrl
      properties:
        meta:
          $ref: '#/components/schemas/RwDiagReportMeta'
        presignedGetUrl:
          type: string
    RwDiagReportMetaPage:
      type: object
      required:
        - metas
      properties:
        metas:
          type: array
          items:
            $ref: '#/components/schemas/RwDiagReportMeta'
        limit:
          type: integer
          format: uint64
        offset:
          type: integer
          format: uint64
    PostSnapshotResponseBody:
      type: object
      required:
        - workflowId
        - snapshotId
      properties:
        workflowId:
          type: string
          format: uuid
        snapshotId:
          type: string
          format: uuid
    PostTenantRestoreRequestBody:
      type: object
      description: configuration for new tenant to be created in restore command
      required:
        - newTenantName
      properties:
        newTenantName:
          type: string
    OptionalWorkflowIdResponseBody:
      type: object
      properties:
        workflowId:
          type: string
          format: uuid
        msg:
          type: string
    PostAlertNotificationRequestBody:
      type: object
      required:
        - name
        - properties
      properties:
        name:
          type: string
        properties:
          type: object
          additionalProperties:
            type: string
  responses:
    DefaultResponse:
      description: Default responses returning msg
      content:
        application/json:
          schema:
            type: object
            required:
              - msg
            properties:
              msg:
                type: string
    NotFoundResponse:
      description: 404 Not Found
      content:
        application/json:
          schema:
            type: object
            required:
              - msg
            properties:
              msg:
                type: string
    BadRequestResponse:
      description: 400 Bad Request
      content:
        application/json:
          schema:
            type: object
            required:
              - msg
            properties:
              msg:
                type: string
    FailedPreconditionResponse:
      description: 400 Failed Precondition
      content:
        application/json:
          schema:
            type: object
            required:
              - msg
            properties:
              msg:
                type: string
    WorkflowIdResponse:
      description: Workflow ID response
      content:
        application/json:
          schema:
            type: object
            required:
              - workflowId
            properties:
              workflowId:
                type: string
                format: uuid
    ClusterStatusResponse:
      content:
        application/json:
          schema:
            type: object
            required:
              - ready_status
            properties:
              ready_status:
                $ref: '#/components/schemas/ClusterReadyStatus'
