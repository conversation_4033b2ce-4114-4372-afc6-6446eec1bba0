openapi: 3.0.3

info:
  title: v1
  version: 1.0-alpha

servers:
  - url: /api/v1 # baseURL for kin-openapi openapifilter (should be consistent with spec.GinServerOptions)

tags:
  - name: tenants
    description: Operations about tenants
  - name: diagnosis
    description: Operations about RW Diagnosis Report

paths:
  /:
    get:
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
  /version:
    get:
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"

  /tenant/{tenantId}:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    get:
      summary: Get a tenant
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/Tenant"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
    delete:
      summary: Delete a tenant
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/WorkflowIdResponseBody"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
  /tenant/{tenantId}/privatelink/{privateLinkId}:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
      - in: path
        required: true
        name: privateLinkId
        schema:
          type: string
          format: uuid
    delete:
      summary: Delete a tenant's private link by id
      tags:
        - privateLinks
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/WorkflowIdResponseBody"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"

  /tenants:
    get:
      tags:
        - tenants
      summary: Get all the tenants in the Management System
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/TenantPage"

  /tenant/{tenantId}/endpoint:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    get:
      summary: Get the root endpoint of the specified tenant
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/DatabaseConnectionUrl"
        "401":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"

  /tenant/{tenantId}/expire:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    get:
      summary: Get the expire information
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/TenantExpireInfo"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
    post:
      summary: Update expiration lifecycle
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostTenantExpireRequestBody"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
    delete:
      summary: Cancel expiration workflow
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"

  /tenant/{tenantId}/stop:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      tags:
        - tenant
      summary: Stop the tenant
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/WorkflowIdResponseBody"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
  /tenant/{tenantId}/start:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      tags:
        - tenant
      summary: Start the tenant
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/WorkflowIdResponseBody"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
  /tenant/{tenantId}/restart:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      tags:
        - tenant
      summary: Restart the tenant
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/WorkflowIdResponseBody"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
  /tenant/{tenantId}/updateVersion:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      tags:
        - tenant
      summary: Update the tenant version
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostTenantUpdateVersionRequestBody"
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/WorkflowIdResponseBody"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/DefaultResponse"

  /tenant/{tenantId}/config/risingwave:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
      - in: query
        name: component
        schema:
          type: string
      - in: query
        name: nodeGroup
        schema:
          type: string
    put:
      tags:
        - tenant
      summary: Update the tenant config
      requestBody:
        required: true
        content:
          text/plain:
            schema:
              type: string
              description: toml risingwave config
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/WorkflowIdResponseBody"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/DefaultResponse"
  /tenant/{tenantId}/configNoRestart/risingwave:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
      - in: query
        name: component
        schema:
          type: string
      - in: query
        name: nodeGroup
        schema:
          type: string
    put:
      tags:
        - tenant
      summary: Update the tenant config
      requestBody:
        required: true
        content:
          text/plain:
            schema:
              type: string
              description: toml risingwave config
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/WorkflowIdResponseBody"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/DefaultResponse"
  /tenant/{tenantId}/config/etcd:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    put:
      tags:
        - tenant
      summary: Update the etcd config
      requestBody:
        required: true
        content:
          text/plain:
            schema:
              type: string
            example: "ETCD_QUOTA_BACKEND_BYTES: '1000000000'\nETCD_MAX_REQUEST_BYTES: '100000000'\n"
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/WorkflowIdResponseBody"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
  /tenant/{tenantId}/resource:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      tags:
        - tenant
      summary: Update a tenant resource
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostTenantResourcesRequestBody"
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/WorkflowIdResponseBody"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"

  /tenant/{tenantId}/computeCache:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      tags:
        - tenant
      summary: update compute cache for tenant
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostTenantComputeCacheRequestBody"
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/WorkflowIdResponseBody"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"

  /tenant/{tenantId}/status:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      tags:
        - tenant
      summary: Reset tenant status
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostTenantStatusRequestBody"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
  /tenant/{tenantId}/tier:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      tags:
        - tenant
      summary: Update tenant tier
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostTenantTierRequestBody"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"

  /tenant/{tenantId}/resourceGroups:
    post:
      tags:
        - ResourceGroups
      summary: Create resource group
      parameters:
        - in: path
          name: tenantId
          schema:
            type: integer
            format: uint64
          required: true
      requestBody:
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/CreateResourceGroupsRequestBody"
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/WorkflowIdResponseBody"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
  /tenant/{tenantId}/resourceGroups/{resourceGroup}:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
      - in: path
        name: resourceGroup
        schema:
          type: string
        required: true
    post:
      tags:
        - ResourceGroups
      summary: Update resource group
      requestBody:
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/UpdateResourceGroupsRequestBody"
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/WorkflowIdResponseBody"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
    delete:
      tags:
        - ResourceGroups
      summary: Delete resource group
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/WorkflowIdResponseBody"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"

  /tenant/{tenantId}/extensions/compaction/enable:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      tags:
        - tenant
      summary: Enable the risingwave extensions for serverless compaction
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/WorkflowIdResponseBody"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
  /tenant/{tenantId}/extensions/compaction/disable:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      tags:
        - tenant
      summary: Disable the risingwave extensions for serverless compaction
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/WorkflowIdResponseBody"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
  /tenant/{tenantId}/extensions/compaction/update:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      tags:
        - tenant
      summary: Update the risingwave extensions for serverless compaction
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostTenantExtensionCompactionRequestBody"
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/WorkflowIdResponseBody"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"

  /tenant/{tenantId}/extensions/compaction/parameters:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    get:
      tags:
        - tenant
      summary: Get the parameters of the risingwave extensions for serverless compaction
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/GetTenantExtensionCompactionParametersResponseBody"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"

  /tenant/{tenantId}/extensions/compaction/status:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    get:
      tags:
        - tenant
      summary: Get the status of the risingwave extensions for serverless compaction
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/GetTenantExtensionCompactionStatusResponseBody"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
    post:
      tags:
        - tenant
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostTenantExtensionCompactionStatusRequestBody"
      summary: Get the status of the risingwave extensions for serverless compaction
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/GetTenantExtensionCompactionStatusResponseBody"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
  /tenant/{tenantId}/extensions/serverlessbackfill/enable:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      tags:
        - tenant
      summary: Enable the risingwave extensions for serverless backfilling
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/WorkflowIdResponseBody"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/DefaultResponse"

  /tenant/{tenantId}/extensions/serverlessbackfill/disable:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      tags:
        - tenant
      summary: Disable the risingwave extensions for serverless backfilling
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/WorkflowIdResponseBody"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/DefaultResponse"

  /tenant/{tenantId}/extensions/serverlessbackfill/version:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      tags:
        - tenant
      summary: Update the risingwave extensions for serverless backfilling to version. Version format like 1.0.0.
      requestBody:
        required: true
        content:
          text/plain:
            schema:
              type: string
            example: "1.2.3"
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/WorkflowIdResponseBody"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/DefaultResponse"

  /tenants/{tenantId}/extensions/iceberg-compaction:
    parameters:
      - in: path
        required: true
        name: tenantId
        schema:
          type: integer
          format: uint64

    get:
      tags:
        - tenants
      summary: Get iceberg compaction status
      responses:
        "200":
          description: "Iceberg compaction status"
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/IcebergCompaction"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"

    post:
      tags:
        - tenants
      summary: Enable iceberg compaction
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                config:
                  type: string
                resources:
                  $ref: "mgmt_body.yaml#/components/schemas/ComponentResourceRequest"
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/WorkflowIdResponseBody"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"

    put:
      tags:
        - tenants
      summary: Update iceberg compaction
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                config:
                  type: string
                resources:
                  $ref: "mgmt_body.yaml#/components/schemas/ComponentResourceRequest"
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/WorkflowIdResponseBody"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"

    delete:
      tags:
        - tenants
      summary: Disable iceberg compaction
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/WorkflowIdResponseBody"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"

  /tenant/{tenantId}/migrateMetaStore:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      tags:
        - tenant
      summary: Migrate the risingwave meta store
      responses:
        "202":
          $ref: "response.yaml#/components/responses/WorkflowIdResponse"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
  /tenant/{tenantId}/upgradeMetaStore:
    parameters:
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
      - in: query
        name: metaStore
        schema:
          type: string
        required: true
    post:
      tags:
        - tenant
      responses:
        "202":
          $ref: "response.yaml#/components/responses/WorkflowIdResponse"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"

  /workflows:
    get:
      description: Get all workflows order by createdAt desc
      tags:
        - workflow
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/WorkflowPage"
  /workflow/{id}:
    parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
    get:
      description: Get workflow with events
      tags:
        - workflow
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/WorkflowWithEvents"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
  /workflow/{id}/cancel:
    parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
    post:
      description: Cancel a workflow
      tags:
        - workflow
      responses:
        "200":
          description: OK
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
  /workflow/{id}/resume:
    parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
    post:
      description: Resume a cancelled workflow
      tags:
        - workflow
      responses:
        "200":
          description: OK
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
  /workflow/{id}/rerun:
    parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
    post:
      description: Rerun a finished workflow
      tags:
        - workflow
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostWorkflowRerunRequestBody"
      responses:
        "202":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/WorkflowIdResponseBody"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
  /workflow/{id}/schedule:
    parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
    post:
      description: Schedule a running workflow's delay
      tags:
        - workflow
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostWorkflowScheduleRequestBody"
      responses:
        "200":
          description: OK
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"

  /clusters:
    get:
      summary: Get all clusters
      tags:
        - cluster
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/ManagedClusters"
    post:
      summary: Create or update cluster by name
      tags:
        - cluster
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostClusterRequestBody"
      responses:
        "200":
          description: OK

  /cluster/{name}:
    parameters:
      - in: path
        name: name
        schema:
          type: string
    get:
      summary: Get a cluster
      tags:
        - cluster
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/ManagedCluster"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
    post:
      summary: Create or update cluster
      tags:
        - cluster
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostClusterRequestBody"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
    delete:
      summary: delete cluster
      tags:
        - cluster
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"

  /byoc-clusters:
    get:
      summary: Get all clusters
      tags:
        - cluster
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/ManagedClusters"

  /byoc-cluster/{id}/access:
    parameters:
      - in: path
        name: id
        description: id of cluster row
        schema:
          type: integer
          format: uint64
    post:
      summary: Get temporary access token for the requested cluster
      tags:
        - cluster
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostByocClusterAccessRequestBody"
      responses:
        "200":
          description: Accepted
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/ClusterAccessInfo"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"

  /byoc-cluster/{id}/update:
    parameters:
      - in: path
        name: id
        description: id of cluster row
        schema:
          type: integer
          format: uint64
    post:
      summary: update the BYOC cluster version, if version is not specified, update it to the default version
      tags:
        - cluster
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostByocClusterUpdateRequestBody"
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/WorkflowIdResponseBody"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
  /clusters/{uuid}/prometheus/api/v1/query:
    parameters:
      - in: path
        name: uuid
        schema:
          type: string
          format: uuid
    get:
      responses:
        "200": {}
        "412":
          $ref: "mgmt_resource.yaml#/components/schemas/ClusterStatusResponse"
    post:
      responses:
        "200": {}
        "412":
          $ref: "mgmt_resource.yaml#/components/schemas/ClusterStatusResponse"

  /clusters/{uuid}/prometheus/api/v1/query_range:
    parameters:
      - in: path
        name: uuid
        schema:
          type: string
          format: uuid
    get:
      responses:
        "200": {}
        "412":
          $ref: "mgmt_resource.yaml#/components/schemas/ClusterStatusResponse"
    post:
      responses:
        "200": {}
        "412":
          $ref: "mgmt_resource.yaml#/components/schemas/ClusterStatusResponse"

  /clusters/{uuid}/prometheus/api/v1/series:
    parameters:
      - in: path
        name: uuid
        schema:
          type: string
          format: uuid
    get:
      responses:
        "200": {}
        "412":
          $ref: "mgmt_resource.yaml#/components/schemas/ClusterStatusResponse"
    post:
      responses:
        "200": {}
        "412":
          $ref: "mgmt_resource.yaml#/components/schemas/ClusterStatusResponse"

  /clusters/{uuid}/prometheus/api/v1/label/{labelName}/values:
    parameters:
      - in: path
        name: uuid
        schema:
          type: string
          format: uuid
      - in: path
        name: labelName
        schema:
          type: string
    get:
      responses:
        "200": {}
        "412":
          $ref: "mgmt_resource.yaml#/components/schemas/ClusterStatusResponse"
    post:
      responses:
        "200": {}
        "412":
          $ref: "mgmt_resource.yaml#/components/schemas/ClusterStatusResponse"

  /tenant/{tenantId}/diagnosis/report/generate:
    parameters:
      - in: path
        required: true
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      summary: generate a RW Diagnosis Report
      description: generate a RW Diagnosis Report from a RW tenant, persist it in a cloud storage bucket, and return its info
      tags:
        - diagnosis
        - tenant
      operationId: genDiagReport
      responses:
        "200":
          description: "Generated Report Info Response"
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/RwDiagReport"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"

  /tenant/{tenantId}/diagnosis/report/{reportId}:
    get:
      summary: fetch the content of the RW Diagnosis Report
      description: fetch the content of the pre-generated RW Diagnosis Report if available from a cloud storage bucket
      tags:
        - diagnosis
        - tenant
      operationId: getDiagReport
      parameters:
        - in: path
          required: true
          name: tenantId
          schema:
            type: integer
            format: uint64
        - in: path
          required: true
          name: reportId
          schema:
            type: integer
            format: uint64
      responses:
        "200":
          description: "Retrieved Report Info Response"
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/RwDiagReport"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"

  /tenant/{tenantId}/diagnosis/report/range:
    get:
      summary: list a set of RW Diagnosis Reports within a specified range of time
      description: list a set of RW Diagnosis Reports (excluding content) within a specified range of time from a cloud storage bucket
      tags:
        - diagnosis
        - tenant
      operationId: listDiagReportTimeRange
      parameters:
        - in: path
          required: true
          name: tenantId
          schema:
            type: integer
            format: uint64
        - in: query
          name: startTime
          description: RFC3339/RFC3339Nano formatted starting time (inclusive)
          schema:
            type: string
            format: date-time
            nullable: true
            default: null
        - in: query
          name: endTime
          description: RFC3339/RFC3339Nano formatted ending time (inclusive)
          schema:
            type: string
            format: date-time
            nullable: true
            default: null
        - in: query
          name: limit
          description: pagination - how many report metas to return
          schema:
            type: integer
            format: uint64
            nullable: true
            default: null
        - in: query
          name: offset
          description: pagination - how many report metas to skip
          schema:
            type: integer
            format: uint64
            nullable: true
            default: 0
      responses:
        "200":
          description: "Retrieved set of Reports Response"
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/RwDiagReportMetaPage"

  /tenant/{tenantId}/backup:
    post:
      summary: create a backup snapshot for the meta store of the tenant
      tags:
        - backup
        - tenant
      parameters:
        - in: path
          required: true
          name: tenantId
          schema:
            type: integer
            format: uint64
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/PostSnapshotResponseBody"

  /tenant/{tenantId}/backup/{snapshotId}/restore:
    post:
      summary: Restore to a new cluster based on the snapshot id
      tags:
        - backup
        - tenant
      parameters:
        - in: path
          required: true
          name: tenantId
          schema:
            type: integer
            format: uint64
        - in: path
          required: true
          name: snapshotId
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostTenantRestoreRequestBody"
      responses:
        "202":
          description: Starting to restore data from this snapshot.
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/Tenant"

  /tenant/{tenantId}/backup/{snapshotId}/in-place-restore:
    post:
      summary: Restore to a new cluster based on the snapshot id
      tags:
        - backup
        - tenant
      parameters:
        - in: path
          required: true
          name: tenantId
          schema:
            type: integer
            format: uint64
        - in: path
          required: true
          name: snapshotId
          schema:
            type: string
            format: uuid
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/WorkflowIdResponseBody"

  /tenant/{tenantId}/backup/{snapshotId}:
    delete:
      summary: delete a backup snapshot for the meta store of the tenant
      tags:
        - backup
        - tenant
      parameters:
        - in: path
          required: true
          name: tenantId
          schema:
            type: integer
            format: uint64
        - in: path
          required: true
          name: snapshotId
          schema:
            type: string
            format: uuid
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/OptionalWorkflowIdResponseBody"

  /webhooks/alert:
    post:
      summary: receive alerts reported from alert manager
      tags:
        - alerts
        - notifications
      responses:
        "200":
          description: OK

  /alertNotifications:
    post:
      summary: create alert notification
      tags:
        - alerts
        - notifications
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostAlertNotificationRequestBody"
      responses:
        "200":
          description: OK

components:
  schemas:
  responses:
