openapi: 3.0.3

info:
  title: v1
  version: 1.0-alpha
paths: {}
components:
  schemas:
    ##############################
    # Tier
    ##############################
    TierId:
      type: string
      enum:
        - Free
        - Invited
        - Developer-Free
        - Developer-Basic
        - Developer-Test
        - Standard
        - BYOC
        - Test
        - Benchmark
    Tier:
      type: object
      required:
        - name
        - availableStandaloneNodes
        - availableComputeNodes
        - availableCompactorNodes
        - availableFrontendNodes
        - availableMetaNodes
        - maximumComputeNodeFileCacheSizeGiB
        - validityPeriod
        - retentionPeriod
      properties:
        id:
          $ref: "#/components/schemas/TierId"
        availableStandaloneNodes:
          type: array
          items:
            $ref: "#/components/schemas/AvailableComponentType"
        availableComputeNodes:
          type: array
          items:
            $ref: "#/components/schemas/AvailableComponentType"
        availableCompactorNodes:
          type: array
          items:
            $ref: "#/components/schemas/AvailableComponentType"
        availableFrontendNodes:
          type: array
          items:
            $ref: "#/components/schemas/AvailableComponentType"
        availableMetaNodes:
          type: array
          items:
            $ref: "#/components/schemas/AvailableComponentType"
        maximumComputeNodeFileCacheSizeGiB:
          type: integer
        validityPeriod:
          type: integer
        retentionPeriod:
          type: integer
        availableMetaStore:
          $ref: "#/components/schemas/AvailableMetaStore"
    TierArray:
      type: array
      items:
        $ref: "#/components/schemas/Tier"
    Tiers:
      type: object
      required: [tiers]
      properties:
        tiers:
          $ref: "mgmt_resource.yaml#/components/schemas/TierArray"
    AvailableComponentType:
      type: object
      required: [id, cpu, memory, maximum]
      properties:
        id:
          type: string
        cpu:
          type: string
        memory:
          type: string
        maximum:
          type: integer
    AvailableMetaStore:
      type: object
      properties:
        etcd:
          $ref: "mgmt_resource.yaml#/components/schemas/AvailableMetaStoreEtcd"
        postgresql:
          $ref: "mgmt_resource.yaml#/components/schemas/AvailableMetaStorePostgreSql"
        aws_rds:
          $ref: "mgmt_resource.yaml#/components/schemas/AvailableMetaStoreAwsRds"
        gcp_cloudsql:
          $ref: "mgmt_resource.yaml#/components/schemas/AvailableMetaStoreGcpCloudSql"
        azr_postgres:
          $ref: "mgmt_resource.yaml#/components/schemas/AvailableMetaStoreAzrPostgres"
        sharing_pg:
          $ref: "mgmt_resource.yaml#/components/schemas/AvailableMetaStoreSharingPg"

    AvailableMetaStoreEtcd:
      type: object
      required: [nodes, maximumSizeGiB]
      properties:
        nodes:
          type: array
          items:
            $ref: "#/components/schemas/AvailableComponentType"
        maximumSizeGiB:
          type: integer
    AvailableMetaStorePostgreSql:
      type: object
      required: [nodes, maximumSizeGiB]
      properties:
        nodes:
          type: array
          items:
            $ref: "#/components/schemas/AvailableComponentType"
        maximumSizeGiB:
          type: integer
    AvailableMetaStoreAwsRds:
      type: object
      properties:
        enabled:
          type: boolean
    AvailableMetaStoreGcpCloudSql:
      type: object
      properties:
        enabled:
          type: boolean
    AvailableMetaStoreAzrPostgres:
      type: object
      properties:
        enabled:
          type: boolean
    AvailableMetaStoreSharingPg:
      type: object
      properties:
        enabled:
          type: boolean

    ##############################
    # Tenant
    ##############################
    Tenant:
      type: object
      required:
        - id
        - userId
        - tenantName
        - region
        - status
        - tier
        - resources
        - createdAt
        - imageTag
        - latestImageTag
        - rw_config
        - updatedAt
        - health_status
        - nsId
        - orgId
        - etcd_config
        - usageType
      properties:
        id:
          type: integer
          format: uint64
        userId:
          type: integer
          format: uint64
        orgId:
          type: string
          format: uuid
        tenantName:
          type: string
        region:
          type: string
        resources:
          $ref: "#/components/schemas/TenantResource"
        status:
          type: string
          enum:
            - Creating
            - Running
            - Deleting
            - Failed
            - Stopped
            - Stopping
            - Starting
            - Expired
            - ConfigUpdating
            - Upgrading
            - Updating
            - Snapshotting
            - ExtensionCompactionEnabling
            - ExtensionCompactionDisabling
            - ExtensionServerlessBackfillUpdate
            - ExtensionServerlessBackfillEnabling
            - ExtensionServerlessBackfillDisabling
            - MetaMigrating
            - Restoring
            - Quiesced
            - ResourceGroupsUpdating
        tier:
          $ref: "#/components/schemas/TierId"
        usageType:
          type: string
          enum:
            - general
            - pipeline
        imageTag:
          type: string
          example: v0.1.12
        latestImageTag:
          type: string
          example: v0.1.12
        rw_config:
          type: string
        etcd_config:
          type: string
        health_status:
          type: string
          enum:
            - Unknown
            - Healthy
            - Unhealthy
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        nsId:
          type: string
          format: uuid
        clusterName:
          type: string
        upcomingSnapshotTime:
          type: string
          format: date-time
    TenantArray:
      type: array
      items:
        $ref: "#/components/schemas/Tenant"
    TenantPage:
      type: object
      allOf:
        - $ref: "schema.yaml#/components/schemas/Page"
        - type: object
          required: [tenants]
          properties:
            tenants:
              $ref: "#/components/schemas/TenantArray"
    TenantSizePage:
      type: object
      allOf:
        - $ref: "schema.yaml#/components/schemas/Page"
        - $ref: "schema.yaml#/components/schemas/Size"
        - type: object
          required: [tenants]
          properties:
            tenants:
              $ref: "#/components/schemas/TenantArray"
    TenantResourceComputeCache:
      type: object
      required:
        - sizeGb
      properties:
        sizeGb:
          type: integer
    TenantResource:
      type: object
      required: [components, computeCache]
      properties:
        components:
          $ref: "#/components/schemas/TenantResourceComponents"
        etcdVolumeSizeGiB:
          type: integer
        computeCache:
          $ref: "#/components/schemas/TenantResourceComputeCache"
        metaStore:
          $ref: "#/components/schemas/TenantResourceMetaStore"
        resourceGroups:
          $ref: "#/components/schemas/TenantResourceGroupArray"
    TenantResourceComponents:
      type: object
      properties:
        standalone:
          $ref: "#/components/schemas/ComponentResource"
        compute:
          $ref: "#/components/schemas/ComponentResource"
        compactor:
          $ref: "#/components/schemas/ComponentResource"
        frontend:
          $ref: "#/components/schemas/ComponentResource"
        meta:
          $ref: "#/components/schemas/ComponentResource"
        etcd:
          $ref: "#/components/schemas/ComponentResource"
    ComponentResource:
      type: object
      required: [componentTypeId, replica, cpu, memory]
      properties:
        componentTypeId:
          type: string
        cpu:
          type: string
        memory:
          type: string
        replica:
          type: integer
    TenantResourceMetaStore:
      type: object
      required: [type, rwu]
      properties:
        type:
          $ref: "#/components/schemas/MetaStoreType"
        rwu:
          type: string
        etcd:
          $ref: "#/components/schemas/MetaStoreEtcd"
        postgresql:
          $ref: "#/components/schemas/MetaStorePostgreSql"
        aws_rds:
          $ref: "#/components/schemas/MetaStoreAwsRds"
        gcp_cloudsql:
          $ref: "#/components/schemas/MetaStoreGcpCloudSql"
        azr_postgres:
          $ref: "#/components/schemas/MetaStoreAzrPostgres"
        sharing_pg:
          $ref: "#/components/schemas/MetaStoreSharingPg"
    MetaStoreType:
      type: string
      enum:
        - etcd
        - postgresql
        - aws_rds
        - gcp_cloudsql
        - azr_postgres
        - sharing_pg
    MetaStoreEtcd:
      type: object
      required: [resource, sizeGb]
      properties:
        resource:
          $ref: "#/components/schemas/ComponentResource"
        sizeGb:
          type: integer
    MetaStorePostgreSql:
      type: object
      required: [resource, sizeGb]
      properties:
        resource:
          $ref: "#/components/schemas/ComponentResource"
        sizeGb:
          type: integer
    MetaStoreAwsRds:
      type: object
      required: [instanceClass, sizeGb]
      properties:
        instanceClass:
          type: string
        sizeGb:
          type: integer
    MetaStoreGcpCloudSql:
      type: object
      required: [tier, sizeGb]
      properties:
        tier:
          type: string
        sizeGb:
          type: integer
    MetaStoreAzrPostgres:
      type: object
      required: [sku, sizeGb]
      properties:
        sku:
          type: string
        sizeGb:
          type: integer
    MetaStoreSharingPg:
      type: object
      required: [instanceId]
      properties:
        instanceId:
          type: string
    TenantResourceGroupArray:
      type: array
      items:
        $ref: "#/components/schemas/TenantResourceGroup"
    TenantResourceGroup:
      type: object
      required: [name, resource, computeCache]
      properties:
        name:
          type: string
        resource:
          $ref: "#/components/schemas/ComponentResource"
        computeCache:
          $ref: "#/components/schemas/TenantResourceComputeCache"

    TenantStatusCount:
      type: object
      required: [status, count]
      properties:
        count:
          type: integer
          format: int64
        status:
          type: string
    TenantStatusCountArray:
      type: array
      items:
        $ref: "#/components/schemas/TenantStatusCount"
    TenantStatusCounts:
      type: object
      required: [status]
      properties:
        status:
          $ref: "mgmt_resource.yaml#/components/schemas/TenantStatusCountArray"

    IcebergCompaction:
      type: object
      required: [status]
      properties:
        status:
          type: string
        config:
          type: string
        resources:
          $ref: "#/components/schemas/ComponentResource"

    ##############################
    # DBUser
    ##############################
    DBUser:
      type: object
      required:
        [usesysid, username, usecreatedb, usesuper, usecreateuser, canlogin]
      properties:
        usesysid:
          type: integer
          format: uint64
        username:
          type: string
        usecreatedb:
          type: boolean
        usesuper:
          type: boolean
        usecreateuser:
          type: boolean
        canlogin:
          type: boolean
    DBUserArray:
      type: array
      items:
        $ref: "#/components/schemas/DBUser"
    DBUsers:
      type: object
      properties:
        dbusers:
          $ref: "#/components/schemas/DBUserArray"

    ##############################
    # MatView and Endpoint
    ##############################
    MatView:
      type: object
      required:
        - databases
        - databaseName
        - definition
        - matViewId
        - matViewName
        - matViewSchema
        - matViewOwner
        - matViewGraph
        - fragmentIds
      properties:
        databases:
          type: string
        databaseName:
          type: string
        definition:
          type: string
        matViewId:
          type: integer
          format: int64
        matViewName:
          type: string
        matViewSchema:
          type: string
        matViewOwner:
          type: string
        matViewGraph:
          type: string
        fragmentIds:
          type: array
          items:
            type: integer
            format: int64
        columns:
          $ref: "#/components/schemas/ColumnDescArray"
        createdAt:
          type: string
          format: date-time
        size:
          type: string
    MatViewArray:
      type: array
      items:
        $ref: "#/components/schemas/MatView"
    MatViews:
      type: object
      required: [matViews]
      properties:
        matViews:
          $ref: "#/components/schemas/MatViewArray"

    Endpoint:
      type: object
      required:
        [
          id,
          tenantId,
          host,
          port,
          database,
          options,
          internalHost,
          internalPort,
        ]
      properties:
        id:
          type: integer
          format: int64
        tenantId:
          type: integer
          format: int64
        host:
          type: string
        port:
          type: integer
        database:
          type: string
        options:
          type: string
        internalHost:
          type: string
        internalPort:
          type: integer

    DatabaseConnectionUrl:
      type: object
      required: [url]
      properties:
        url:
          type: string
          example: postgresql://identifier;username:password@host:port/database

    RelationCounts:
      type: object
      required: [table, materialized_view, source, sink]
      properties:
        table:
          type: integer
          format: uint32
        materialized_view:
          type: integer
          format: uint32
        source:
          type: integer
          format: uint32
        sink:
          type: integer
          format: uint32

    ##############################
    # Private Link
    ##############################
    PrivateLink:
      type: object
      required: [id, tenantId, status, connectionState, connectionName]
      properties:
        connectionName:
          type: string
        id:
          type: string
          format: uuid
        tenantId:
          type: integer
          format: int64
        status:
          type: string
          enum: [CREATING, CREATED, DELETING, ERROR, UNKNOWN]
        connectionState:
          type: string
          enum: [STATUS_UNSPECIFIED, PENDING, ACCEPTED, REJECTED, CLOSED]
        target:
          type: string
        endpoint:
          type: string
    PrivateLinkArray:
      type: array
      items:
        $ref: "#/components/schemas/PrivateLink"
    PrivateLinkSizePage:
      type: object
      allOf:
        - $ref: "schema.yaml#/components/schemas/Page"
        - $ref: "schema.yaml#/components/schemas/Size"
        - type: object
          required: [privateLinks]
          properties:
            privateLinks:
              $ref: "#/components/schemas/PrivateLinkArray"

    ##############################
    # Metrics
    ##############################
    MetricPoint:
      type: object
      required: [timestamp, value]
      properties:
        timestamp:
          type: string
          format: date-time
        value:
          type: number
          format: double

    LabelItem:
      type: object
      required: [key, value]
      properties:
        key:
          type: string
        value:
          type: string

    MetricItem:
      type: object
      required: [labels, values]
      properties:
        labels:
          type: array
          items:
            $ref: "#/components/schemas/LabelItem"
        values:
          type: array
          items:
            $ref: "#/components/schemas/MetricPoint"

    Metrics:
      type: object
      required: [name, items]
      properties:
        name:
          type: string
        items:
          type: array
          items:
            $ref: "#/components/schemas/MetricItem"

    ##############################
    # Log
    ##############################
    ErrLogQueryResult:
      type: object
      required: [status, values]
      properties:
        status:
          type: string
        values:
          type: array
          items:
            type: array
            items:
              type: string
            minItems: 2
            maxItems: 2

    ##############################
    # Tenant Expiration
    ##############################
    TenantExpireInfo:
      type: object
      required: [expireAt, deleteAt]
      properties:
        expireAt:
          type: string
          format: date-time
          nullable: true
        deleteAt:
          type: string
          format: date-time
          nullable: true

    ##############################
    # Source
    ##############################
    KafkaConfig:
      type: object
      required:
        - server
      properties:
        server:
          type: string
        securityProtocol: # SASL_SSL
          type: string
          enum: [SASL_SSL, SSL, SASL_PLAINTEXT]
        saslMechanisms: # PLAIN
          type: string
          enum: [OAUTHBEARER, GSSAPI, PLAIN, SCRAM-SHA-256, SCRAM-SHA-512]
        saslUsername: # API KEY
          type: string
        saslPassword: # API SECRET
          type: string
        caCertificate: # PEM encoded CA certificate
          type: string

    ColumnDesc:
      x-internal: true
      type: object
      required: [name, type]
      properties:
        name:
          type: string
        type:
          type: string
        fields:
          $ref: "#/components/schemas/ColumnDescArray"
    ColumnDescArray:
      x-internal: true
      type: array
      items:
        $ref: "#/components/schemas/ColumnDesc"
    ColumnDescs:
      x-internal: true
      type: object
      required: [columns]
      properties:
        columns:
          $ref: "#/components/schemas/ColumnDescArray"

    TenantSourceDetail:
      type: object
      required: [schema, columns]
      properties:
        schema:
          $ref: "#/components/schemas/SourceInfo"
        columns:
          $ref: "#/components/schemas/ColumnDescArray"

    TenantSourcesInfo:
      type: object
      required:
        - sources
      properties:
        sources:
          type: array
          items:
            $ref: "#/components/schemas/SourceInfo"

    TenantRelationsInfo:
      type: object
      required:
        - relations
      properties:
        relations:
          type: array
          items:
            $ref: "#/components/schemas/RelationInfo"

    RelationInfo:
      type: object
      required: [relname, reltype]
      properties:
        relname:
          type: string
        reltype:
          type: string

    TenantSinksInfo:
      type: object
      required:
        - sinks
      properties:
        sinks:
          type: array
          items:
            $ref: "#/components/schemas/SinkInfo"

    SourceInfo:
      type: object
      required: [id, sourceName, isSchemaRegistry, hasPrimaryKey]
      properties:
        id:
          type: integer
          format: int32
        sourceName:
          type: string
        connectorType:
          type: string
        topic:
          type: string
        endpoint:
          type: string
        scan.startup.mode: # earliest, latest
          type: string
        scan.startup.timestamp_millis: # UNIX timestamp (milliseconds)
          type: string
        connection.name: # private link connection name
          type: string
        webLocation:
          type: string
        healthCheck:
          type: string
          enum: [Active, Failed, Unknown]
        throughput:
          type: number
          format: double
        dataFormat:
          type: string
          # !: enum is ignored since we need to use RegExp to parse source definition
          # enum: [AVRO, UPSERT_AVRO, JSON, UPSERT_JSON, PROTOBUF, DEBEZIUM_JSON, CSV, MAXWELL]
        isSchemaRegistry:
          type: boolean
          default: false
        hasPrimaryKey:
          type: boolean
          default: false

    SinkInfo:
      type: object
      required: [id, sinkName, connectorType]
      properties:
        id:
          type: integer
          format: int32
        sinkName:
          type: string
        connectorType:
          type: string
        endpoint:
          type: string
        type:
          type: string # upsert, debezium, force_append_only
        force_append_only:
          type: string # true when type is force_append_only
        primary_key:
          type: string
        healthCheck:
          type: string
          enum: [Active, Failed]
          default: Active
        throughput:
          type: number
          format: double

    ##############################
    # Workflow
    ##############################
    Workflow:
      type: object
      required:
        [
          id,
          workflowType,
          fsmState,
          runningState,
          context,
          createdAt,
          updatedAt,
        ]
      properties:
        id:
          type: string
          format: uuid
        workflowType:
          type: string
        fsmState:
          type: string
        runningState:
          type: string
          enum:
            - RunningWorkflow
            - FailedWorkflow
            - CompletedWorkflow
            - CancelledWorkflow
        context: {} # Any value
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        lockedAt:
          type: string
          format: date-time
        delayAt:
          type: string
          format: date-time
    WorkflowArray:
      type: array
      items:
        $ref: "#/components/schemas/Workflow"
    Workflows:
      type: object
      required: [workflows]
      properties:
        workflows:
          $ref: "#/components/schemas/WorkflowArray"
    WorkflowPage:
      type: object
      allOf:
        - $ref: "schema.yaml#/components/schemas/Page"
        - $ref: "#/components/schemas/Workflows"

    WorkflowEvent:
      required: [id, workflowId, type, timestamp, attributes]
      properties:
        id:
          type: integer
          format: uint64
        workflowId:
          type: string
          format: uuid
        type:
          type: string
        timestamp:
          type: string
          format: date-time
        attributes: {} # Any value
    WorkflowEventArray:
      type: array
      items:
        $ref: "#/components/schemas/WorkflowEvent"
    WorkflowWithEvents:
      type: object
      required: [workflow, events]
      properties:
        workflow:
          $ref: "#/components/schemas/Workflow"
        events:
          $ref: "#/components/schemas/WorkflowEventArray"

    ##############################
    # Managed Cluster
    ##############################
    ClusterStatus:
      type: string
      enum:
        [
          Uninitialized,
          Provisioned,
          Ready,
          Terminating,
          Deleted,
          PendingResourceDeletion,
          Updating,
          Failed,
        ]

    ClusterReadyStatus:
      type: string
      enum:
        # The has never been ready and may be ready in the future.
        - Before
        # The was ready before and may be ready in the future.
        - Interrupted
        # The was ready before and will never be ready again.
        - After

    ClusterAccessInfo:
      required: [endpoint, caCertBase64, token]
      properties:
        endpoint:
          type: string
        caCertBase64:
          type: string
        token:
          type: string

    ClusterStatusResponse:
      content:
        application/json:
          schema:
            type: object
            required: [ready_status]
            properties:
              ready_status:
                $ref: "#/components/schemas/ClusterReadyStatus"

    ManagedCluster:
      required:
        [
          id,
          org,
          name,
          status,
          master_url,
          cluster_service_account,
          token,
          serving_type,
          settings,
        ]
      properties:
        id:
          type: integer
          format: uint64
        org:
          type: string
          format: uuid
        name:
          type: string
        status:
          $ref: "#/components/schemas/ClusterStatus"
        master_url:
          type: string
        cluster_service_account:
          type: string
        token:
          type: string
        serving_type:
          type: string
        settings: # map[string]string
          type: object
          additionalProperties:
            type: string
    ManagedClusterArray:
      type: array
      items:
        $ref: "#/components/schemas/ManagedCluster"
    ManagedClusters:
      type: object
      required: [clusters]
      properties:
        clusters:
          $ref: "#/components/schemas/ManagedClusterArray"
    ManagedClustersSizePage:
      x-internal: true
      type: object
      allOf:
        - $ref: "schema.yaml#/components/schemas/Page"
        - $ref: "schema.yaml#/components/schemas/Size"
        - type: object
          required: [clusters]
          properties:
            clusters:
              type: array
              items:
                $ref: "#/components/schemas/ManagedCluster"

    BackupSnapshotItem:
      type: object
      required: [id, created_at_unix_mills, status]
      properties:
        id:
          type: string
          format: uuid
        created_at_unix_mills:
          type: integer
          format: int64
        meta_snapshot_id:
          type: integer
          format: int64
        status:
          type: string
        type:
          type: string
        rw_version:
          type: string

    BackupSnapshotsSizePage:
      x-internal: true
      type: object
      allOf:
        - $ref: "schema.yaml#/components/schemas/Page"
        - $ref: "schema.yaml#/components/schemas/Size"
        - type: object
          required: [items]
          properties:
            items:
              type: array
              items:
                $ref: "#/components/schemas/BackupSnapshotItem"

    ##############################
    # RW Diagnosis Report
    ##############################
    RwDiagReportMeta:
      type: object
      required: [id, tenantId, processedAt]
      properties:
        id:
          type: integer
          description: can be unix timestamp seconds, or other things
          format: uint64
        tenantId:
          type: integer
          format: uint64
        processedAt:
          type: string
          format: date-time

    RwDiagReport:
      type: object
      required: [meta, presignedGetUrl]
      properties:
        meta:
          $ref: "#/components/schemas/RwDiagReportMeta"
        presignedGetUrl:
          type: string

    RwDiagReportMetaPage:
      type: object
      required: [metas]
      properties:
        metas:
          type: array
          items:
            $ref: "#/components/schemas/RwDiagReportMeta"
        limit:
          type: integer
          format: uint64
        offset:
          type: integer
          format: uint64
