REDOCLY_VERSION ?= 1.34.3
PRETTIER_VERSION ?= 3.6.2

MODULE_API_V1_MAKEFILE_PATH := $(abspath $(lastword $(MAKEFILE_LIST)))
MODULE_API_V1_ROOT ?= $(abspath $(dir $(MODULE_API_V1_MAKEFILE_PATH)))
MODULE_API_V1_PARENT_PATH ?= $(abspath $(dir $(MODULE_API_V1_ROOT)))

API_V1_SOURCE_DIR := $(MODULE_API_V1_ROOT)
API_V1_BUNDLE_DIR := $(API_V1_SOURCE_DIR)/bundle
API_V1_ROOTS := $(API_V1_SOURCE_DIR)/mgmt.yaml \
	$(API_V1_SOURCE_DIR)/mgmt_admin.yaml \
	$(API_V1_SOURCE_DIR)/billing.yaml \
	$(API_V1_SOURCE_DIR)/billing_admin.yaml \
	$(API_V1_SOURCE_DIR)/acc.yaml \
	$(API_V1_SOURCE_DIR)/acc_admin.yaml \
	$(API_V1_SOURCE_DIR)/admin.yaml \
	$(API_V1_SOURCE_DIR)/webconsole.yaml
API_V1_TARGETS := $(patsubst $(API_V1_SOURCE_DIR)/%,$(API_V1_SOURCE_DIR)/bundle/%,$(API_V1_ROOTS))
API_V1_PUBLIC_ROOTS := $(API_V1_SOURCE_DIR)/mgmt.yaml \
	$(API_V1_SOURCE_DIR)/acc.yaml
API_V1_PUBLIC_TARGETS := $(patsubst $(API_V1_SOURCE_DIR)/%,$(API_V1_SOURCE_DIR)/bundle/public/%,$(API_V1_PUBLIC_ROOTS))
API_V1_SHARE_SOURCES = $(filter-out $(API_V1_ROOTS),$(wildcard $(API_V1_SOURCE_DIR)/*.yaml))

.PHONY: module/api/v1/build
module/api/v1/build: $(API_V1_TARGETS) $(API_V1_PUBLIC_TARGETS)

.PHONY: module/api/v1/clean
module/api/v1/clean:
	@echo "(api/v1) Cleaning API v1 bundles"
	@rm -rf $(API_V1_BUNDLE_DIR)

.PHONY: module/api/v1/fmt
module/api/v1/fmt:
	@docker run --rm -u $(id -u):$(id -g) -v "$(MODULE_API_V1_PARENT_PATH):/data" tmknom/prettier:$(PRETTIER_VERSION) --write '/data/v1/*.yaml'

$(API_V1_TARGETS): $(API_V1_SOURCE_DIR)/bundle/%.yaml: $(API_V1_SOURCE_DIR)/%.yaml $(API_V1_SHARE_SOURCES)
	@mkdir -p $(API_V1_BUNDLE_DIR)
	@echo "(api/v1) Building API v1 bundle for $<"
	@rm -f $@.tmp
	@echo "# Code generated by redocly/cli DO NOT EDIT." > $@.tmp
	@docker run \
		--rm \
		-v $(MODULE_API_V1_PARENT_PATH):/data \
		-v $(MODULE_API_V1_PARENT_PATH)/redocly-configs:/config \
		redocly/cli:$(REDOCLY_VERSION) \
		bundle "/data/$(<:$(MODULE_API_V1_PARENT_PATH)/%=%)" >> $@.tmp
	@mv $@.tmp $@

$(API_V1_PUBLIC_TARGETS): $(API_V1_SOURCE_DIR)/bundle/public/%.yaml: $(API_V1_SOURCE_DIR)/bundle/%.yaml
	@mkdir -p $(API_V1_BUNDLE_DIR)
	@echo "(api/v1) Building public API v1 bundle for $<"
	@rm -f $@.tmp.yaml $@.tmp
	@# With remove internal operations
	@docker run \
		--rm \
		-v $(MODULE_API_V1_PARENT_PATH):/data \
		-v $(MODULE_API_V1_PARENT_PATH)/redocly-configs:/config \
		redocly/cli:$(REDOCLY_VERSION) \
		bundle "/data/$(<:$(MODULE_API_V1_PARENT_PATH)/%=%)" \
		--config /config/remove_internal.yaml \
		-o "/data/$(@:$(MODULE_API_V1_PARENT_PATH)/%=%).tmp.yaml"
	@# With remove unused operations
	@docker run \
		--rm \
		-v $(MODULE_API_V1_PARENT_PATH):/data \
		-v $(MODULE_API_V1_PARENT_PATH)/redocly-configs:/config \
		redocly/cli:$(REDOCLY_VERSION) \
		bundle "/data/$(@:$(MODULE_API_V1_PARENT_PATH)/%=%).tmp.yaml" \
		--config /config/remove_unused.yaml \
		-o "/data/$(@:$(MODULE_API_V1_PARENT_PATH)/%=%).tmp.yaml"
	@# Add header to the file
	@echo "# Code generated by redocly cli DO NOT EDIT." | cat - $@.tmp.yaml > $@.tmp && mv $@.tmp $@
	@rm -f $@.tmp.yaml

