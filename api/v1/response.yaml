openapi: 3.0.3

info:
  title: v1
  version: 1.0-alpha
paths: {}
components:
  responses:
    DefaultResponse:
      description: "Default responses returning msg"
      content:
        application/json:
          schema:
            type: object
            required: [msg]
            properties:
              msg:
                type: string
    AvailabilityResponse:
      description: "Availability response"
      content:
        application/json:
          schema:
            type: object
            required: [available, msg]
            properties:
              available:
                type: boolean
              msg:
                type: string

    WorkflowIdResponse:
      description: "Workflow ID response"
      content:
        application/json:
          schema:
            type: object
            required: [workflowId]
            properties:
              workflowId:
                type: string
                format: uuid

    BadRequestResponse:
      description: "400 Bad Request"
      content:
        application/json:
          schema:
            type: object
            required: [msg]
            properties:
              msg:
                type: string

    ForbiddenResponse:
      description: "403 Forbidden"
      content:
        application/json:
          schema:
            type: object
            required: [msg]
            properties:
              msg:
                type: string

    NotFoundResponse:
      description: "404 Not Found"
      content:
        application/json:
          schema:
            type: object
            required: [msg]
            properties:
              msg:
                type: string

    RangeNotSatisfiable:
      description: "416 Range Not Satisfiable"
      content:
        application/json:
          schema:
            type: object
            required: [msg]
            properties:
              msg:
                type: string

    FailedPreconditionResponse:
      description: "400 Failed Precondition"
      content:
        application/json:
          schema:
            type: object
            required: [msg]
            properties:
              msg:
                type: string
