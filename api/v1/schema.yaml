openapi: 3.0.3

info:
  title: v1
  version: 1.0-alpha
paths: {}
components:
  schemas:
    Page:
      type: object
      required: [limit, offset]
      properties:
        limit:
          type: integer
          format: uint64
        offset:
          type: integer
          format: uint64
    Size:
      type: object
      required: [size]
      properties:
        size:
          type: integer
          format: uint64
