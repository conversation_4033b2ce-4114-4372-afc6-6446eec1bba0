openapi: 3.0.3

info:
  title: v2
  version: 2.0-alpha
paths: {}
components:
  schemas:
    ##############################
    # Notification
    ##############################
    PutNotificationsStatusRequestBody:
      type: object
      required: [status]
      properties:
        status:
          type: string
          enum:
            - Deleted
            - Unread
            - Read

    PostNotificationRequestBody:
      type: object
      required: [sender, toUserId, title, content, messageType]
      properties:
        sender:
          $ref: "acc_resource.yaml#/components/schemas/NotificationSender"
        toOrgId:
          type: array
          items:
            type: string
            format: uuid
        toUserId:
          type: array
          items:
            type: string
            format: uuid
        title:
          type: string
        content:
          type: string
        messageType:
          type: string
          enum:
            - Alert
            - Message
            - Notification

    PostSubscriptionRequestBody:
      type: object
      required: [recipientId, alertLevel]
      properties:
        alertLevel:
          type: string
        recipientId:
          type: string
          format: uuid

    PostRecipientRequestBody:
      type: object
      required: [config]
      properties:
        config:
          $ref: "acc_resource.yaml#/components/schemas/RecipientConfig"

    PutRecipientSubscriptionRequestBody:
      type: object
      required: [severities]
      properties:
        severities:
          type: array
          items:
            $ref: "acc_resource.yaml#/components/schemas/AlertSeverity"

    ##############################
    # ServiceAccount
    ##############################
    PutServiceAccountRequestBody:
      type: object
      required:
        - description
      properties:
        description:
          type: string
    PostServiceAccountRequestBody:
      type: object
      required:
        - name
        - description
        - roleId
      properties:
        description:
          type: string
        name:
          type: string
        roleId:
          type: string
          format: uuid

    ##############################
    # Role
    ##############################
    PutRoleBindingRequestBody:
      type: object
      required:
        - principal
        - roleIds
        - principalType
      properties:
        principal:
          type: string
          format: uuid
        roleIds:
          type: array
          items:
            type: string
            format: uuid
        principalType:
          type: string

    ##############################
    # Invitation
    ##############################
    CreateInvitationRequestBody:
      type: object
      required:
        - email
        - roleId
      properties:
        email:
          type: string
        roleId:
          type: string
          format: uuid

    ##############################
    # ApiKey
    ##############################
    PutApiKeyRequestBody:
      type: object
      required:
        - description
      properties:
        description:
          type: string
    PostApiKeyRequestBody:
      type: object
      required:
        - description
        - principal
      properties:
        description:
          type: string
        principal:
          type: string
          format: uuid

    ##############################
    # User
    ##############################
    GetUsersResponseBody:
      type: object
      required:
        - users
      properties:
        users:
          $ref: "acc_resource.yaml#/components/schemas/UserWithRolesArray"

    ##############################
    # Org
    ##############################
    PutOrgRequestBody:
      type: object
      required: [name]
      properties:
        name:
          type: string
