openapi: 3.0.3

info:
  title: v2
  version: 2.0-alpha
paths: {}
components:
  schemas:
    ##############################
    # User
    ##############################
    UserPagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: "schema.yaml#/components/schemas/Pagination"
        - type: object
          required: [users]
          properties:
            users:
              $ref: "#/components/schemas/UserArray"
    UserArray:
      type: array
      items:
        $ref: "#/components/schemas/User"
    User:
      type: object
      required: [id, username, email, authType, createdAt, resourceId, roles]
      properties:
        id:
          type: integer
          format: uint64
        username:
          type: string
        email:
          type: string
        createdAt:
          type: string
          format: date-time
        lastLoginAt:
          type: string
          format: date-time
        authType:
          $ref: "acc_resource.yaml#/components/schemas/AuthType"
        resourceId:
          type: string
          format: uuid
        roles:
          type: array
          items:
            type: string
    AuthType:
      type: string
      enum:
        - google-oauth2
        - github
        - windowslive
        - local
        - sso

    ##############################
    # Invitation
    ##############################
    InvitationPagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: "schema.yaml#/components/schemas/Pagination"
        - type: object
          required: [invitations]
          properties:
            invitations:
              $ref: "#/components/schemas/InvitationArray"
    Invitation:
      type: object
      required:
        [id, email, orgId, name, createdAt, expiresAt, updatedAt, roleId]
      properties:
        id:
          type: integer
          format: uint64
        name:
          type: string
        email:
          type: string
        orgId:
          type: string
        createdAt:
          type: string
          format: date-time
        expiresAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        roleId:
          type: string
          format: uuid
    InvitationArray:
      type: array
      items:
        $ref: "#/components/schemas/Invitation"

    ##############################
    # Notification
    ##############################
    NotificationPagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: "schema.yaml#/components/schemas/Pagination"
        - type: object
          required: [notifications, unreadNum]
          properties:
            notifications:
              $ref: "#/components/schemas/NotificationArray"
            unreadNum:
              type: integer
              format: uint64
    Notification:
      type: object
      required: [id, time, type, status, sender, title, content]
      properties:
        id:
          type: integer
          format: uint64
        time:
          type: string
          format: date-time
        type:
          type: string
          enum:
            - Alert
            - Message
            - Notification
        status:
          type: string
          enum:
            - Deleted
            - Unread
            - Read
        sender:
          $ref: "#/components/schemas/NotificationSender"
        content:
          type: string
        title:
          type: string
    NotificationArray:
      type: array
      items:
        $ref: "#/components/schemas/Notification"
    NotificationSender:
      type: object
      required: [name, email]
      properties:
        name:
          type: string
        email:
          type: string

    Subscription:
      type: object
      required: [id, recipientId, severity]
      properties:
        id:
          type: string
          format: uuid
        recipientId:
          type: string
          format: uuid
        severity:
          $ref: "#/components/schemas/AlertSeverity"
    SubscriptionArray:
      type: array
      items:
        $ref: "#/components/schemas/Subscription"
    Recipient:
      type: object
      required: [id, config, orgId, createdAt, updatedAt]
      properties:
        id:
          type: string
          format: uuid
        config:
          $ref: "#/components/schemas/RecipientConfig"
        orgId:
          type: string
          format: uuid
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    RecipientConfig:
      discriminator:
        propertyName: type
        mapping:
          email: "#/components/schemas/EmailRecipientConfig"
          user: "#/components/schemas/UserRecipientConfig"
          slack: "#/components/schemas/SlackRecipientConfig"
      oneOf:
        - $ref: "#/components/schemas/EmailRecipientConfig"
        - $ref: "#/components/schemas/UserRecipientConfig"
        - $ref: "#/components/schemas/SlackRecipientConfig"
    RecipientArray:
      type: array
      items:
        $ref: "#/components/schemas/Recipient"
    EmailRecipientConfig:
      type: object
      required: [type, email]
      properties:
        type:
          $ref: "#/components/schemas/RecipientType"
        email:
          type: string
    UserRecipientConfig:
      type: object
      required: [type, userId]
      properties:
        type:
          $ref: "#/components/schemas/RecipientType"
        userId:
          type: string
          format: uuid
    SlackRecipientConfig:
      type: object
      required: [type, webhookURL]
      properties:
        type:
          $ref: "#/components/schemas/RecipientType"
        webhookURL:
          type: string
    RecipientType:
      type: string
      enum:
        - email
        - slack
        - user
    AlertSeverity:
      type: string
      enum:
        - critical
        - warning

    ##############################
    # Service Account
    ##############################
    ServiceAccountPagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: "schema.yaml#/components/schemas/Pagination"
        - type: object
          required: [serviceAccounts]
          properties:
            serviceAccounts:
              $ref: "#/components/schemas/ServiceAccountArray"
    ServiceAccount:
      type: object
      required:
        [id, name, orgId, createdAt, updatedAt, description, apiKeyCount, roles]
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        orgId:
          type: string
          format: uuid
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        description:
          type: string
        apiKeyCount:
          type: integer
          format: uint64
        roles:
          type: array
          items:
            type: string
          example: ["OrganizationMember", "OrganizationAdmin"]
    ServiceAccountArray:
      type: array
      items:
        $ref: "#/components/schemas/ServiceAccount"
    ApiKeyPagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: "schema.yaml#/components/schemas/Pagination"
        - type: object
          required: [apiKeys]
          properties:
            apiKeys:
              $ref: "#/components/schemas/ApiKeyArray"
    ApiKey:
      type: object
      required: [id, principal, key, createdAt, updatedAt, description, secret]
      properties:
        id:
          type: integer
          format: uint64
        principal:
          type: string
          format: uuid
        key:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        description:
          type: string
        secret:
          type: string
    ApiKeyArray:
      type: array
      items:
        $ref: "#/components/schemas/ApiKey"

    ##############################
    # Role
    ##############################
    RolePagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: "schema.yaml#/components/schemas/Pagination"
        - type: object
          required: [roles]
          properties:
            roles:
              $ref: "#/components/schemas/RoleArray"
    Role:
      type: object
      required: [id, name, description]
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        description:
          type: string
    RoleArray:
      type: array
      items:
        $ref: "#/components/schemas/Role"
    RoleBindingPagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: "schema.yaml#/components/schemas/Pagination"
        - type: object
          required: [roleBindings]
          properties:
            roleBindings:
              $ref: "#/components/schemas/RoleBindingArray"
    RoleBinding:
      type: object
      required: [principal, roleId, roleName]
      properties:
        principal:
          type: string
          format: uuid
        roleName:
          type: string
        roleId:
          type: string
          format: uuid
        principalType:
          type: string
    RoleBindingArray:
      type: array
      items:
        $ref: "#/components/schemas/RoleBinding"

    ##############################
    # Org
    ##############################
    Org:
      type: object
      required: [orgId, name, createdAt, updatedAt]
      properties:
        orgId:
          type: string
          format: uuid
        name:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    ##############################
    # Alert
    ##############################
    AlertType:
      type: object
      required: [name, severity, category, description]
      properties:
        name:
          type: string
        severity:
          $ref: "acc_resource.yaml#/components/schemas/AlertSeverity"
        category:
          type: string
        description:
          type: string

    AlertTypeArray:
      type: array
      items:
        $ref: "#/components/schemas/AlertType"
