openapi: 3.0.3

info:
  title: v2
  version: 2.0-alpha

servers:
  - url: /api/v2 # baseURL for kin-openapi openapifilter (should be consistent with spec.GinServerOptions)

tags:
  - name: invoices
    description: Operations about invoices
  - name: details
    description: Operations about billing-details
  - name: clusters
    description: Operations about tenants

paths:
  # Dummy APIs for testing.
  /billing/invoices:
    get:
      tags:
        - invoices
      description:
        Lists the customer's invoices in reverse chronological order (i.e. newest first).
        A result contains at most 1024 entries. It does not contain preliminary invoices.
        The query may return less than the maximum number of entries to ensure that all
        invoices of the same billing cycle are submitted together.
      x-required-permission: invoices.list
      parameters:
        - in: query
          # The billing cycle of the newest invoice in the result (inclusive).
          # Uses the current billing cycle if omitted.
          name: max_cycle
          schema:
            type: integer
            format: int16
          required: false
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "billing_resource.yaml#/components/schemas/InvoiceList"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /billing/invoices/{invoiceID}:
    get:
      tags:
        - invoices
      description:
        Returns the invoice with the given ID with all its data. If the ID is omitted or
        empty, it returns a preliminary invoice for the current billing cycle.
      x-required-permission: invoices.get
      parameters:
        - in: path
          name: invoiceID
          schema:
            type: string
          required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "billing_resource.yaml#/components/schemas/FullInvoice"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /billing/invoices/getUnpaid:
    get:
      tags:
        - invoices
      description: Returns the customer's oldest unpaid invoice.
      x-required-permission: invoices.get
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "billing_resource.yaml#/components/schemas/GetOrgInvoicesUnpaidResult"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /billing/invoices/{invoiceID}/pdf:
    get:
      tags:
        - invoices
      description: Redirects to the invoice's PDF on Stripe.
      x-required-permission: invoices.get
      parameters:
        - in: path
          name: invoiceID
          schema:
            type: string
          required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "billing_resource.yaml#/components/schemas/Url"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /billing/details:
    get:
      tags:
        - details
      description:
        Returns the billing information (the address used on invoices) of the
        logged in organisation.
      x-required-permission: billingProfile.get
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "billing_resource.yaml#/components/schemas/BillingDetails"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    put:
      tags:
        - details
      description:
        Updates the billing information (the address used on invoices) of the
        logged in organisation. Note that this requests updates all properties
        even if they are omitted. In that case, the request will delete their
        current values.
      x-required-permission: billingProfile.update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "billing_resource.yaml#/components/schemas/BillingDetails"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /billing/clusters/{nsId}/usage:
    get:
      tags:
        - clusters
      description:
        Lists the recorded usage of the given tenant for the given metric in reverse chronological
        order (i.e. newest first). A result contains at most 1024 entries. The query may return
        less than the maximum number of entries to ensure that all nodes of the same time-frame are
        submitted together.
      x-required-permission: billingUsage.get
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: query
          name: metric
          schema:
            $ref: "billing_resource.yaml#/components/schemas/Metric"
          required: true
        - in: query
          # Exclusive. Optional; uses current time if not specified.
          name: max_start_time
          schema:
            type: string
            format: date-time
          required: false
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "billing_resource.yaml#/components/schemas/ClusterUsage"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    ApiKeyAuth:
      type: http
      in: basic
      description: Service account api key. Header format 'Basic base64(${key}:${secret})'
