openapi: 3.0.3

info:
  title: v2
  version: 2.0-alpha
paths: {}

components:
  schemas:
    #
    # --- Contact/Billing Information ---

    Address:
      type: object
      required: [line1, line2, city, state, country, postal_code]
      properties:
        line1:
          type: string
        line2:
          type: string
        city:
          type: string
        state:
          type: string
        country:
          # Two-letter country code (ISO 3166-1 alpha-2).
          # See https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2.
          type: string
          pattern: "^[A-Z][A-Z]$"
        postal_code:
          type: string

    BillingDetails:
      type: object
      required: [name, phone, address]
      properties:
        name:
          type: string
        phone:
          type: string
        address:
          $ref: "#/components/schemas/Address"

    #
    # --- Invoices ---

    BillingPeriod:
      type: object
      required: [start, end]
      properties:
        start:
          type: string
          format: date-time
          description: The first second of the billing period.
        end:
          type: string
          format: date-time
          description: The last second (inclusive) of the billing period.

    BasicInvoice:
      type: object
      required:
        [stripe_id, number, org_id, billing_period, total_usd_cent, status]
      properties:
        stripe_id:
          # Is empty if invoice is preliminary.
          type: string
        number:
          # The number shown on the invoice.
          # Is empty if invoice is preliminary.
          type: string
        org_id:
          type: string
          format: uuid
        billing_period:
          $ref: "#/components/schemas/BillingPeriod"
        total_usd_cent:
          type: integer
          format: int64
        status:
          $ref: "#/components/schemas/InvoiceState"
        due_date:
          # Null if invoice is preliminary.
          type: string
          format: date-time
        paid_at:
          # Null if invoice has not been paid.
          type: string
          format: date-time

    FullInvoice:
      type: object
      allOf:
        - $ref: "#/components/schemas/BasicInvoice"
        - type: object
          required: [lines]
          properties:
            lines:
              type: array
              items:
                $ref: "#/components/schemas/InvoiceLine"

    InvoiceLine:
      type: object
      required:
        [cluster_id, cluster_name, metric_name, metric_total, price_usd_cent]
      properties:
        cluster_id:
          type: string
          format: uuid
        cluster_name:
          type: string
        metric_name:
          type: string
        metric_total:
          type: integer
          format: uint64
        price_usd_cent:
          type: integer
          format: int64

    InvoiceList:
      type: object
      required: [invoices, next_cycle]
      properties:
        invoices:
          type: array
          items:
            $ref: "#/components/schemas/BasicInvoice"
        next_cycle:
          # States the billing cycle of the next-older invoice.
          # Is 0 if there are no older invoices.
          type: integer
          format: int16

    InvoiceState:
      # See https://stripe.com/docs/invoicing/overview#invoice-statuses for more.
      type: string
      enum:
        - preliminary # Not defined by Stripe. States that invoice has not been created.
        - open
        - overdue # Not defined by Stripe. States that invoice is `open` and past its due date.
        - paid
        - void
        # Stripe also defines 'draft' and 'uncollectible'.
        # We will not show that to the user.

    GetOrgInvoicesUnpaidResult:
      type: object
      properties:
        invoice:
          $ref: "billing_resource.yaml#/components/schemas/BasicInvoice"

    Url:
      type: object
      required: [url]
      properties:
        url:
          type: string

    #
    # --- Measured Metrics ---

    Metric:
      type: string
      enum: [nodes_meta, nodes_compute, nodes_compactor, storage, throughput]

    MetricUnit:
      type: string
      enum: [core_seconds, kb_seconds, bytes]

    MeasuredMetric:
      type: object
      required: [start_time, end_time, value]
      properties:
        start_time:
          type: string
          format: date-time
        end_time:
          type: string
          format: date-time
        value:
          type: integer
          format: uint64

    ClusterUsage:
      type: object
      required: [nsId, metric, unit, usage, contains_oldest]
      properties:
        nsId:
          type: string
          format: uuid
        metric:
          $ref: "#/components/schemas/Metric"
        unit:
          $ref: "#/components/schemas/MetricUnit"
        usage:
          type: array
          items:
            $ref: "#/components/schemas/MeasuredMetric"
        contains_oldest:
          type: boolean
