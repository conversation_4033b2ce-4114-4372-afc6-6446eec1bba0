# Code generated by redocly/cli DO NOT EDIT.
openapi: 3.0.3
info:
  title: v2
  version: 2.0-alpha
servers:
  - url: /api/v2
tags:
  - name: tenants
    description: Operations about tenants
  - name: databaseUsers
    description: Operations about RisingWave DB users
  - name: privateLinks
    description: Operations about privateLinks
  - name: MViews
    description: Operations about MViews
  - name: tables
    description: Operations about tables
  - name: backups
    description: Operations about backups
  - name: sources
    description: Operations about sources
  - name: sinks
    description: Operations about sinks
  - name: byocClusters
    description: Operations about byoc Clusters
  - name: secrets
    description: Operations about secrets
  - name: metrics
    description: Operations about metrics
paths:
  /tenants:
    post:
      tags:
        - tenants
      summary: Create tenants with tenantName
      x-required-permission: tenants.create
      x-tenant-operation: Provision
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TenantRequestRequestBody'
      responses:
        '202':
          description: Create tenant response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Tenant'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '409':
          $ref: '#/components/responses/AlreadyExistsResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    get:
      tags:
        - tenants
      summary: List all the tenants owned by the org
      x-required-permission: tenants.list
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 10
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TenantPagination'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/endpoint:
    get:
      tags:
        - tenants
      x-required-permission: endpoints.list
      summary: Get an endpoint of tenant by nsId
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Endpoint'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}:
    parameters:
      - in: path
        name: nsId
        schema:
          type: string
          format: uuid
        required: true
    get:
      tags:
        - tenants
      summary: Get a tenant by nsId
      x-required-permission: tenants.get
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Tenant'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    delete:
      tags:
        - tenants
      summary: Delete a tenant by nsId
      x-required-permission: tenants.delete
      x-tenant-operation: Delete
      responses:
        '202':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/updateResource:
    post:
      tags:
        - tenants
      summary: Update a tenant resource by nsId
      x-required-permission: tenants.updateResource
      x-tenant-operation: Scale
      parameters:
        - in: path
          required: true
          name: nsId
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostTenantResourcesRequestBody'
      responses:
        '202':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '409':
          $ref: '#/components/responses/AlreadyExistsResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/updateVersion:
    post:
      tags:
        - tenants
      x-required-permission: tenants.update
      summary: Update the tenant rw version to latest
      parameters:
        - in: path
          required: true
          name: nsId
          schema:
            type: string
            format: uuid
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostTenantUpdateVersionRequestBody'
      responses:
        '202':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '409':
          $ref: '#/components/responses/AlreadyExistsResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/start:
    post:
      tags:
        - tenants
      x-required-permission: tenants.update
      x-tenant-operation: Start
      summary: Start a cluster that has been stopped
      parameters:
        - in: path
          required: true
          name: nsId
          schema:
            type: string
            format: uuid
      responses:
        '202':
          description: cluster start has been initiated
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/stop:
    post:
      tags:
        - tenants
      x-required-permission: tenants.update
      x-tenant-operation: Stop
      summary: Stop a running a cluster
      parameters:
        - in: path
          required: true
          name: nsId
          schema:
            type: string
            format: uuid
      responses:
        '202':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/restart:
    post:
      tags:
        - tenants
      x-required-permission: tenants.update
      x-tenant-operation: Restart
      summary: Restart a cluster
      parameters:
        - in: path
          required: true
          name: nsId
          schema:
            type: string
            format: uuid
      responses:
        '202':
          description: cluster restart has been initiated
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/resourceGroups:
    parameters:
      - in: path
        required: true
        name: nsId
        schema:
          type: string
          format: uuid
    get:
      tags:
        - resourceGroups
      x-required-permission: resourceGroups.list
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetResourceGroupsResponseBody'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    post:
      tags:
        - resourceGroups
      x-required-permission: resourceGroups.create
      x-allowed-tenant-tiers: Invited,BYOC
      x-tenant-ns-id-path-param-locator: nsId
      summary: Create resource group
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateResourceGroupsRequestBody'
      responses:
        '202':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/resourceGroups/{resourceGroup}:
    parameters:
      - in: path
        required: true
        name: nsId
        schema:
          type: string
          format: uuid
      - in: path
        required: true
        name: resourceGroup
        schema:
          type: string
    post:
      tags:
        - resourceGroups
      x-required-permission: resourceGroups.update
      x-allowed-tenant-tiers: Invited,BYOC
      x-tenant-ns-id-path-param-locator: nsId
      summary: Update resource group
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateResourceGroupsRequestBody'
      responses:
        '202':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    delete:
      tags:
        - resourceGroups
      x-required-permission: resourceGroups.delete
      summary: Delete resource group
      responses:
        '202':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases:
    parameters:
      - in: path
        name: nsId
        schema:
          type: string
          format: uuid
        required: true
    get:
      tags:
        - tenants
      x-required-permission: databases.list
      summary: List all databases in tenant by nsId
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 10
      responses:
        '200':
          description: Use the SHOW DATABASES command to show all databases.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DatabasesPagination'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    post:
      tags:
        - tenants
      x-required-permission: databases.create
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateDatabaseRequestBody'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Database'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}:
    parameters:
      - in: path
        name: nsId
        schema:
          type: string
          format: uuid
        required: true
      - in: path
        name: databaseName
        schema:
          type: string
        required: true
    delete:
      tags:
        - tenants
      x-required-permission: databases.delete
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/executeSQL:
    post:
      tags:
        - tenants
      summary: Execute a sql with provided rw credentials in tenant by nsId
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SqlExecutionRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/querySQL:
    post:
      tags:
        - tenants
      summary: Execute a sql with and return the sql result
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SqlQueryRequestBody'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SqlQueryResponseBody'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/relations:
    parameters:
      - in: path
        name: nsId
        schema:
          type: string
          format: uuid
        required: true
      - in: path
        name: databaseName
        schema:
          type: string
        required: true
    get:
      tags:
        - tenants
      summary: List all relations in user's risingwave cluster
      description: List all relations with their names and types including materialized view, sink, table, and source
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TenantRelationsInfo'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/relations/{relName}:
    parameters:
      - in: path
        name: nsId
        schema:
          type: string
          format: uuid
        required: true
      - in: path
        name: databaseName
        schema:
          type: string
        required: true
      - in: path
        name: relName
        schema:
          type: string
        required: true
    delete:
      tags:
        - tenants
      summary: Delete a relation in user's risingwave cluster
      description: Drop relation (matviews, tables, sources, sinks, ...) in user's risingwave cluster
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databaseUsers:
    parameters:
      - in: path
        required: true
        name: nsId
        schema:
          type: string
          format: uuid
    get:
      tags:
        - databaseUsers
      x-required-permission: databaseUsers.list
      summary: List all databaseUsers by nsId
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 10
      responses:
        '200':
          description: Database users Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DBUsersPagination'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    post:
      tags:
        - databaseUsers
      x-audit-nodata: true
      x-required-permission: databaseUsers.create
      summary: Create a database user with options SUPERUSER/NOSUPERUSER, CREATEDB/NOCREATEDB, CREATEUSER/NOCREATEUSER, PASSWORD
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateDBUserRequestBody'
      responses:
        '200':
          description: create db users success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DBUser'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '409':
          $ref: '#/components/responses/AlreadyExistsResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databaseUsers/{dbuserName}:
    parameters:
      - in: path
        required: true
        name: nsId
        schema:
          type: string
          format: uuid
      - in: path
        required: true
        name: dbuserName
        schema:
          type: string
    put:
      tags:
        - databaseUsers
      x-audit-nodata: true
      x-required-permission: databaseUsers.update
      summary: Alter db user's password
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateDBUserRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    delete:
      tags:
        - databaseUsers
      x-required-permission: databaseUsers.delete
      summary: Delete database user by name
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/privatelinks:
    parameters:
      - in: path
        required: true
        name: nsId
        schema:
          type: string
          format: uuid
    post:
      tags:
        - privateLinks
      x-required-permission: privateLinks.create
      summary: Create a privateLink to the source/sink in the user's VPC
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostPrivateLinkRequestBody'
      responses:
        '202':
          description: Create privatelink response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostPrivateLinkResponseBody'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/privatelinks/{privateLinkId}:
    parameters:
      - in: path
        required: true
        name: nsId
        schema:
          type: string
          format: uuid
      - in: path
        required: true
        name: privateLinkId
        schema:
          type: string
          format: uuid
    get:
      tags:
        - privateLinks
      x-required-permission: privateLinks.get
      summary: Get a private link by id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrivateLink'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    delete:
      tags:
        - privateLinks
      x-required-permission: privateLinks.delete
      summary: Delete a private link by id
      responses:
        '202':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/tables:
    get:
      tags:
        - tables
      x-required-permission: tables.list
      summary: List all tables in tenant by nsId and databaseName
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 10
        - in: query
          name: schema
          schema:
            type: string
      responses:
        '200':
          description: Tables Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TablesPagination'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/tables/{tableName}:
    get:
      tags:
        - tables
      x-required-permission: tables.get
      summary: Get table by name in tenant
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: path
          name: tableName
          schema:
            type: string
          required: true
      responses:
        '200':
          description: Get table by name
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TableDetail'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    delete:
      tags:
        - tables
      x-required-permission: tables.delete
      summary: delete table by name in tenant
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: path
          name: tableName
          schema:
            type: string
          required: true
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/tables/{tableName}/dependencies:
    x-internal: true
    get:
      tags:
        - tables
      x-required-permission: tables.getDependencies
      summary: Get table downstream dependencies from risingwave by table name
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: path
          name: tableName
          schema:
            type: string
          required: true
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 10
        - in: query
          name: type
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RwDependencyPagination'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/tables/{tableName}/rowCount:
    x-internal: true
    get:
      tags:
        - metrics
      x-required-permission: tables.getMetrics
      summary: Get table row count by table name
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: path
          name: tableName
          schema:
            type: string
          required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RowCount'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/tables/{tableName}/throughput:
    x-internal: true
    get:
      tags:
        - metrics
      x-required-permission: tables.getMetrics
      summary: Get table throughput by table name
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: path
          name: tableName
          schema:
            type: string
          required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Throughput'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/tables/{tableName}/dataLatency:
    x-internal: true
    get:
      tags:
        - metrics
      x-required-permission: tables.getMetrics
      summary: Get table data latency by table name
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: path
          name: tableName
          schema:
            type: string
          required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataLatency'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/tables/{tableName}/metrics:
    x-internal: true
    get:
      tags:
        - metrics
      x-required-permission: tables.getMetrics
      summary: Get table metrics by table name and metrics name
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: path
          name: tableName
          schema:
            type: string
          required: true
        - in: query
          name: startTime
          schema:
            type: string
            format: date-time
        - in: query
          name: endTime
          schema:
            type: string
            format: date-time
        - in: query
          name: period
          schema:
            type: integer
            format: uint64
        - in: query
          name: metrics
          schema:
            type: string
            enum:
              - throughput
              - dataLatency
          required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Metrics'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/matviews:
    get:
      tags:
        - MViews
      x-required-permission: materializedViews.list
      summary: List all materialized views in tenant by nsId
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 10
      responses:
        '200':
          description: Materialized Views Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MatViewsPagination'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/matviews:
    get:
      tags:
        - MViews
      x-required-permission: materializedViews.list
      summary: List all materialized views in tenant by nsId and databaseName
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: query
          name: schema
          schema:
            type: string
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 10
      responses:
        '200':
          description: Materialized Views Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MatViewsPagination'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/matviews/{matViewName}:
    get:
      tags:
        - MViews
      x-required-permission: materializedViews.get
      summary: Get materialized view by name in tenant
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: path
          name: matViewName
          schema:
            type: string
          required: true
      responses:
        '200':
          description: Get materialized view by name
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MatView'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    delete:
      tags:
        - MViews
      x-required-permission: materializedViews.delete
      summary: delete materialized view by name in tenant
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: path
          name: matViewName
          schema:
            type: string
          required: true
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/matviews/{matViewName}/dependencies:
    x-internal: true
    get:
      tags:
        - MViews
      x-required-permission: materializedViews.getDependencies
      summary: Get materializedView dependencies from risingwave by materializedView name
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: path
          name: matViewName
          schema:
            type: string
          required: true
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 10
        - in: query
          name: dependencyDirection
          schema:
            type: string
            enum:
              - downstream
              - upstream
          required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RwDependencyPagination'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/matviews/{matViewName}/rowCount:
    x-internal: true
    get:
      tags:
        - metrics
      x-required-permission: materializedViews.getMetrics
      summary: Get materializedView row count by materializedView name
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: path
          name: matViewName
          schema:
            type: string
          required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RowCount'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/matviews/{matViewName}/throughput:
    x-internal: true
    get:
      tags:
        - metrics
      x-required-permission: materializedViews.getMetrics
      summary: Get materializedView throughput by materializedView name
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: path
          name: matViewName
          schema:
            type: string
          required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Throughput'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/matviews/{matViewName}/dataLatency:
    x-internal: true
    get:
      tags:
        - metrics
      x-required-permission: materializedViews.getMetrics
      summary: Get materializedView data latency by materializedView name
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: path
          name: matViewName
          schema:
            type: string
          required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataLatency'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/matviews/{matViewName}/metrics:
    x-internal: true
    get:
      tags:
        - metrics
      x-required-permission: materializedViews.getMetrics
      summary: Get materializedView metrics by materializedView name and metrics name
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: path
          name: matViewName
          schema:
            type: string
          required: true
        - in: query
          name: startTime
          schema:
            type: string
            format: date-time
        - in: query
          name: endTime
          schema:
            type: string
            format: date-time
        - in: query
          name: period
          schema:
            type: integer
            format: uint64
        - in: query
          name: metrics
          schema:
            type: string
            enum:
              - throughput
              - dataLatency
          required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Metrics'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/matviews/{matViewName}/progress:
    x-internal: true
    parameters:
      - in: path
        name: nsId
        schema:
          type: string
          format: uuid
        required: true
      - in: path
        name: databaseName
        schema:
          type: string
        required: true
      - in: path
        name: matViewName
        schema:
          type: string
        required: true
    get:
      tags:
        - MViews
      x-required-permission: materializedViews.getProgress
      summary: Get mv ddl progress in a tenant by mv name
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TenantDdlProgress'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/backups:
    parameters:
      - in: path
        name: nsId
        schema:
          type: string
          format: uuid
        required: true
    get:
      tags:
        - backups
      x-required-permission: backups.list
      summary: Get all available backup snapshots
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 10
      responses:
        '200':
          description: Get all available backup snapshots.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BackupSnapshotsPagination'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    post:
      tags:
        - backups
      x-required-permission: backups.create
      summary: Start a new backup procedure
      responses:
        '202':
          description: The backup procedure is started.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostSnapshotResponseBody'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/backups/{snapshotId}:
    delete:
      tags:
        - backups
      x-required-permission: backups.delete
      summary: Delete a backup snapshot
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: snapshotId
          schema:
            type: string
            format: uuid
          required: true
      responses:
        '202':
          description: The snapshot deletion procedure is started.
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/backups/{snapshotId}/restore:
    post:
      tags:
        - backups
      x-required-permission: backups.restoreSnapshot
      summary: Restore a backup snapshot
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: snapshotId
          schema:
            type: string
            format: uuid
          required: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostTenantRestoreRequestBody'
      responses:
        '202':
          description: Starting to restore data from this snapshot.
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/backups/{snapshotId}/in-place-restore:
    post:
      tags:
        - backups
      x-required-permission: backups.restoreSnapshot
      summary: Restore a backup snapshot
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: snapshotId
          schema:
            type: string
            format: uuid
          required: true
      responses:
        '202':
          description: Starting to restore data from this snapshot.
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/unquiesce:
    post:
      tags:
        - backups
      x-required-permission: backups.restoreSnapshot
      summary: Resume a quiesced tenant.
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
      responses:
        '202':
          description: Starting to resume the tenant.
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/sources/fetchSchema:
    post:
      tags:
        - sources
      x-required-permission: sources.fetchSchema
      summary: Fetch source schema
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostSourcesFetchSchemaRequestBody'
      responses:
        '200':
          description: schema definition of the source
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ColumnDescs'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/sources/fetchKafkaSchema:
    post:
      tags:
        - sources
      x-required-permission: sources.fetchSchema
      summary: Fetch kafka row and parse schema
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostSourcesFetchKafkaSchemaRequestBody'
      responses:
        '200':
          description: schema definition of the source
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ColumnDescs'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/sources/ping:
    post:
      tags:
        - sources
      x-required-permission: sources.ping
      summary: Check connectivity of a source to a tenant
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostSourcesPingRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/AvailabilityResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/sources:
    get:
      tags:
        - sources
      x-required-permission: sources.list
      summary: Get all sources by nsId, dbname, dbuser and dbpwd
      description: Get all sources by posting nsId, dbname, dbuser and dbpwd (also used for db login)
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: query
          name: schema
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TenantSourcesInfo'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/sources/{sourceName}:
    parameters:
      - in: path
        name: nsId
        schema:
          type: string
          format: uuid
        required: true
      - in: path
        name: sourceName
        schema:
          type: string
        required: true
      - in: path
        name: databaseName
        schema:
          type: string
        required: true
    get:
      tags:
        - sources
      x-required-permission: sources.get
      summary: Get source schema from risingwave by source name
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TenantSourceDetail'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    delete:
      tags:
        - sources
      x-required-permission: sources.delete
      summary: delete source from risingwave by source name
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/sources/{sourceName}/dependencies:
    x-internal: true
    get:
      tags:
        - sources
      x-required-permission: sources.getDependencies
      summary: Get source dependencies from risingwave by source name
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: sourceName
          schema:
            type: string
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 10
        - in: query
          name: type
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RwDependencyPagination'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/sources/{sourceName}/metrics:
    x-internal: true
    get:
      tags:
        - metrics
        - sources
      x-required-permission: sources.getMetrics
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: sourceName
          schema:
            type: string
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: query
          name: startTime
          schema:
            type: string
            format: date-time
        - in: query
          name: endTime
          schema:
            type: string
            format: date-time
        - in: query
          name: period
          schema:
            type: integer
            format: uint64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Metrics'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/sources/{sourceName}/throughput:
    x-internal: true
    get:
      tags:
        - metrics
        - sources
      x-required-permission: sources.getMetrics
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: sourceName
          schema:
            type: string
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Throughput'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/sinks:
    get:
      tags:
        - sinks
      x-required-permission: sinks.list
      summary: Get all sinks by tenant id
      description: Get all sinks by posting tenantId, dbname, dbuser and dbpwd (also used for db login)
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: query
          name: schema
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TenantSinksInfo'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/sinks/{sinkName}:
    x-internal: true
    parameters:
      - in: path
        name: nsId
        schema:
          type: string
          format: uuid
        required: true
      - in: path
        name: databaseName
        schema:
          type: string
        required: true
      - in: path
        name: sinkName
        schema:
          type: string
        required: true
    get:
      tags:
        - sinks
      x-required-permission: sinks.get
      summary: Get sink info and schema in a tenant by sink name
      description: Get sink info schema by tenant id, database name and sink name.
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TenantSinkDetail'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    delete:
      tags:
        - sinks
      x-required-permission: sinks.delete
      summary: delete sink from risingwave by sink name
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/sinks/{sinkName}/dependencies:
    x-internal: true
    get:
      tags:
        - sinks
      x-required-permission: sinks.getDependencies
      summary: Get sink dependencies from risingwave by sink name
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: sinkName
          schema:
            type: string
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 10
        - in: query
          name: type
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RwDependencyPagination'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/sinks/{sinkName}/metrics:
    x-internal: true
    get:
      tags:
        - metrics
        - sinks
      x-required-permission: sinks.getMetrics
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: sinkName
          schema:
            type: string
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: query
          name: startTime
          schema:
            type: string
            format: date-time
        - in: query
          name: endTime
          schema:
            type: string
            format: date-time
        - in: query
          name: period
          schema:
            type: integer
            format: uint64
        - in: query
          name: metrics
          schema:
            type: string
            enum:
              - throughput
              - dataLatency
          required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Metrics'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/sinks/{sinkName}/throughput:
    x-internal: true
    get:
      tags:
        - metrics
        - sinks
      x-required-permission: sinks.getMetrics
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: sinkName
          schema:
            type: string
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Throughput'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/sinks/{sinkName}/dataLatency:
    x-internal: true
    get:
      tags:
        - metrics
        - sinks
      x-required-permission: sinks.getMetrics
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: sinkName
          schema:
            type: string
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataLatency'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/sinks/{sinkName}/progress:
    x-internal: true
    parameters:
      - in: path
        name: nsId
        schema:
          type: string
          format: uuid
        required: true
      - in: path
        name: databaseName
        schema:
          type: string
        required: true
      - in: path
        name: sinkName
        schema:
          type: string
        required: true
    get:
      tags:
        - sinks
      x-required-permission: sinks.getProgress
      summary: Get sink ddl progress in a tenant by sink name
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TenantDdlProgress'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/schemas:
    x-internal: true
    get:
      tags:
        - schemas
      x-required-permission: schemas.list
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TenantSchemasInfo'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/caCert:
    parameters:
      - in: path
        name: nsId
        schema:
          type: string
          format: uuid
        required: true
    get:
      summary: get the CA cert for the BYOC cluster version
      responses:
        '200':
          description: OK
          content:
            application/x-pem-file:
              schema:
                type: string
                format: binary
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /byoc-clusters:
    post:
      x-allowed-org-tiers: Advanced
      tags:
        - byocClusters
      summary: Create a BYOC cluster
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostByocClustersRequestBody'
      responses:
        '200':
          description: Create BYOC cluster response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagedCluster'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '409':
          $ref: '#/components/responses/AlreadyExistsResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    get:
      tags:
        - byocClusters
      summary: List BYOC clusters
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 10
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagedClustersPagination'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /byoc-clusters/{name}:
    parameters:
      - in: path
        required: true
        name: name
        schema:
          type: string
    put:
      x-allowed-org-tiers: Advanced
      tags:
        - byocClusters
      summary: Update a BYOC cluster
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PutByocClusterRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    get:
      tags:
        - byocClusters
      summary: Get a BYOC cluster
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagedCluster'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    delete:
      tags:
        - byocClusters
      summary: Delete a BYOC cluster
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /byoc-clusters/{name}/manualUpdate:
    parameters:
      - in: path
        required: true
        name: name
        schema:
          type: string
    post:
      x-allowed-org-tiers: Advanced
      tags:
        - byocClusters
      summary: update the BYOC cluster version, if version is not specified, update it to the default version
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostByocClusterUpdateRequestBody'
      responses:
        '202':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /byoc-clusters/{name}/update:
    parameters:
      - in: path
        required: true
        name: name
        schema:
          type: string
    post:
      x-allowed-org-tiers: Advanced
      tags:
        - byocClusters
      summary: update the BYOC cluster version, if version is not specified, update it to the default version
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostByocClusterUpdateRequestBody'
      responses:
        '202':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/secrets:
    parameters:
      - in: path
        required: true
        name: nsId
        schema:
          type: string
          format: uuid
      - in: path
        required: true
        name: databaseName
        schema:
          type: string
    get:
      tags:
        - secrets
      x-required-permission: secrets.list
      summary: List all secrets by nsId
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 10
      responses:
        '200':
          description: Secrets Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SecretsPagination'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    post:
      tags:
        - secrets
      x-audit-nodata: true
      x-required-permission: secrets.create
      summary: Create a database secret
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSecretRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '409':
          $ref: '#/components/responses/AlreadyExistsResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/secrets/{name}:
    parameters:
      - in: path
        required: true
        name: nsId
        schema:
          type: string
          format: uuid
      - in: path
        required: true
        name: databaseName
        schema:
          type: string
      - in: path
        required: true
        name: name
        schema:
          type: string
    delete:
      tags:
        - secrets
      x-required-permission: secrets.delete
      summary: Delete secret by name
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/secrets/{secretName}/references:
    x-internal: true
    get:
      tags:
        - secrets
      x-required-permission: secrets.getReferences
      summary: Get references from risingwave by secret name
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: secretName
          schema:
            type: string
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 10
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RwSecretReferencesPagination'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/prometheus/api/v1/query:
    parameters:
      - in: path
        required: true
        name: nsId
        schema:
          type: string
          format: uuid
    get:
      tags:
        - prometheus
      x-required-permission: prometheus.get
      x-audit-nodata: true
      summary: Instant query
      parameters:
        - in: query
          name: query
          required: true
          schema:
            type: string
        - in: query
          name: time
          schema:
            type: string
        - in: query
          name: timeout
          schema:
            type: string
        - in: query
          name: limit
          schema:
            type: integer
      responses:
        '200':
          $ref: '#/components/responses/PrometheusAPIResponse'
        '422':
          $ref: '#/components/responses/PrometheusAPIResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    post:
      tags:
        - prometheus
      x-required-permission: prometheus.get
      x-audit-nodata: true
      summary: Instant query
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              required:
                - query
              properties:
                query:
                  type: string
                time:
                  type: string
                  nullable: true
                timeout:
                  type: string
                  nullable: true
                limit:
                  type: integer
                  nullable: true
      responses:
        '200':
          $ref: '#/components/responses/PrometheusAPIResponse'
        '422':
          $ref: '#/components/responses/PrometheusAPIResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/prometheus/api/v1/query_range:
    parameters:
      - in: path
        required: true
        name: nsId
        schema:
          type: string
          format: uuid
    get:
      tags:
        - prometheus
      x-required-permission: prometheus.get
      x-audit-nodata: true
      summary: Range query
      parameters:
        - in: query
          name: query
          required: true
          schema:
            type: string
        - in: query
          name: start
          required: true
          schema:
            type: string
        - in: query
          name: end
          required: true
          schema:
            type: string
        - in: query
          name: step
          required: true
          schema:
            type: string
        - in: query
          name: timeout
          schema:
            type: string
        - in: query
          name: limit
          schema:
            type: integer
      responses:
        '200':
          $ref: '#/components/responses/PrometheusAPIResponse'
        '422':
          $ref: '#/components/responses/PrometheusAPIResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    post:
      tags:
        - prometheus
      x-required-permission: prometheus.get
      x-audit-nodata: true
      summary: Range query
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              required:
                - query
                - start
                - end
                - step
              properties:
                query:
                  type: string
                start:
                  type: string
                end:
                  type: string
                step:
                  type: string
                timeout:
                  type: string
                  nullable: true
                limit:
                  type: integer
                  nullable: true
      responses:
        '200':
          $ref: '#/components/responses/PrometheusAPIResponse'
        '422':
          $ref: '#/components/responses/PrometheusAPIResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/prometheus/api/v1/series:
    parameters:
      - in: path
        required: true
        name: nsId
        schema:
          type: string
          format: uuid
    get:
      tags:
        - prometheus
      x-required-permission: prometheus.get
      x-audit-nodata: true
      summary: Query series
      parameters:
        - in: query
          name: match[]
          required: true
          schema:
            type: array
            items:
              type: string
        - in: query
          name: start
          schema:
            type: string
        - in: query
          name: end
          schema:
            type: string
        - in: query
          name: limit
          schema:
            type: integer
      responses:
        '200':
          $ref: '#/components/responses/PrometheusAPIResponse'
        '422':
          $ref: '#/components/responses/PrometheusAPIResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    post:
      tags:
        - prometheus
      x-required-permission: prometheus.get
      x-audit-nodata: true
      summary: Query series
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              required:
                - match[]
              properties:
                match[]:
                  type: array
                  items:
                    type: string
                start:
                  type: string
                  nullable: true
                end:
                  type: string
                  nullable: true
                limit:
                  type: integer
                  nullable: true
      responses:
        '200':
          $ref: '#/components/responses/PrometheusAPIResponse'
        '422':
          $ref: '#/components/responses/PrometheusAPIResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/prometheus/api/v1/labels:
    parameters:
      - in: path
        required: true
        name: nsId
        schema:
          type: string
          format: uuid
    get:
      tags:
        - prometheus
      x-required-permission: prometheus.get
      x-audit-nodata: true
      summary: Query labels
      parameters:
        - in: query
          name: match[]
          schema:
            type: array
            items:
              type: string
        - in: query
          name: start
          schema:
            type: string
        - in: query
          name: end
          schema:
            type: string
        - in: query
          name: limit
          schema:
            type: integer
      responses:
        '200':
          $ref: '#/components/responses/PrometheusAPIResponse'
        '422':
          $ref: '#/components/responses/PrometheusAPIResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    post:
      tags:
        - prometheus
      x-required-permission: prometheus.get
      x-audit-nodata: true
      summary: Query labels
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              properties:
                match[]:
                  type: array
                  items:
                    type: string
                  nullable: true
                start:
                  type: string
                  nullable: true
                end:
                  type: string
                  nullable: true
                limit:
                  type: integer
                  nullable: true
      responses:
        '200':
          $ref: '#/components/responses/PrometheusAPIResponse'
        '422':
          $ref: '#/components/responses/PrometheusAPIResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/prometheus/api/v1/label/{labelName}/values:
    parameters:
      - in: path
        required: true
        name: nsId
        schema:
          type: string
          format: uuid
      - in: path
        required: true
        name: labelName
        schema:
          type: string
    get:
      tags:
        - prometheus
      x-required-permission: prometheus.get
      x-audit-nodata: true
      summary: Query values
      parameters:
        - in: query
          name: match[]
          schema:
            type: array
            items:
              type: string
        - in: query
          name: start
          schema:
            type: string
        - in: query
          name: end
          schema:
            type: string
        - in: query
          name: limit
          schema:
            type: integer
      responses:
        '200':
          $ref: '#/components/responses/PrometheusAPIResponse'
        '422':
          $ref: '#/components/responses/PrometheusAPIResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/prometheus/api/v1/rules:
    parameters:
      - in: path
        required: true
        name: nsId
        schema:
          type: string
          format: uuid
    get:
      tags:
        - prometheus
      x-required-permission: prometheus.get
      x-audit-nodata: true
      summary: Dummy rules
      responses:
        '200':
          $ref: '#/components/responses/PrometheusAPIResponse'
        '422':
          $ref: '#/components/responses/PrometheusAPIResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/metrics:
    x-internal: true
    parameters:
      - in: path
        required: true
        name: nsId
        schema:
          type: string
          format: uuid
    get:
      tags:
        - metrics
      x-required-permission: tenants.getMetrics
      responses:
        '200':
          description: Pull the risingwave metrics of this tenant.
          content:
            text/plain:
              schema:
                type: string
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/extensions/iceberg-compaction:
    parameters:
      - in: path
        required: true
        name: nsId
        schema:
          type: string
          format: uuid
    get:
      tags:
        - tenants
      x-required-permission: tenants.get
      x-audit-nodata: true
      summary: Get iceberg compaction status
      responses:
        '200':
          description: Iceberg compaction status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IcebergCompaction'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    post:
      x-allowed-tenant-tiers: Invited,BYOC
      x-tenant-ns-id-path-param-locator: nsId
      tags:
        - tenants
      x-required-permission: tenants.update
      x-audit-nodata: true
      summary: Enable iceberg compaction
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                config:
                  type: string
                resources:
                  $ref: '#/components/schemas/ComponentResourceRequest'
      responses:
        '202':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    put:
      tags:
        - tenants
      x-required-permission: tenants.update
      x-allowed-tenant-tiers: Invited,BYOC
      x-tenant-ns-id-path-param-locator: nsId
      x-audit-nodata: true
      summary: Update iceberg compaction
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                config:
                  type: string
                resources:
                  $ref: '#/components/schemas/ComponentResourceRequest'
      responses:
        '202':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    delete:
      tags:
        - tenants
      x-required-permission: tenants.update
      x-audit-nodata: true
      summary: Disable iceberg compaction
      responses:
        '202':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/testAlert:
    parameters:
      - in: path
        name: nsId
        schema:
          type: string
          format: uuid
    post:
      tags:
        - tenant
        - alert
      summary: Update the test alert on tenant
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostTenantsTestAlertRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-KEY
      description: Service account api key. Header format '${key}:${secret}'
  schemas:
    Page:
      type: object
      required:
        - limit
        - offset
      properties:
        limit:
          type: integer
          format: uint64
        offset:
          type: integer
          format: uint64
    Size:
      type: object
      required:
        - size
      properties:
        size:
          type: integer
          format: uint64
    Pagination:
      type: object
      allOf:
        - $ref: '#/components/schemas/Page'
        - $ref: '#/components/schemas/Size'
    ComponentResource:
      type: object
      required:
        - componentTypeId
        - replica
        - cpu
        - memory
      properties:
        componentTypeId:
          type: string
        cpu:
          type: string
        memory:
          type: string
        replica:
          type: integer
    TenantResourceComponents:
      type: object
      properties:
        standalone:
          $ref: '#/components/schemas/ComponentResource'
        compute:
          $ref: '#/components/schemas/ComponentResource'
        compactor:
          $ref: '#/components/schemas/ComponentResource'
        frontend:
          $ref: '#/components/schemas/ComponentResource'
        meta:
          $ref: '#/components/schemas/ComponentResource'
        etcd:
          $ref: '#/components/schemas/ComponentResource'
    TenantResourceComputeCache:
      type: object
      required:
        - sizeGb
      properties:
        sizeGb:
          type: integer
    MetaStoreType:
      type: string
      enum:
        - etcd
        - postgresql
        - aws_rds
        - gcp_cloudsql
        - azr_postgres
        - sharing_pg
    MetaStoreEtcd:
      type: object
      required:
        - resource
        - sizeGb
      properties:
        resource:
          $ref: '#/components/schemas/ComponentResource'
        sizeGb:
          type: integer
    MetaStorePostgreSql:
      type: object
      required:
        - resource
        - sizeGb
      properties:
        resource:
          $ref: '#/components/schemas/ComponentResource'
        sizeGb:
          type: integer
    MetaStoreAwsRds:
      type: object
      required:
        - instanceClass
        - sizeGb
      properties:
        instanceClass:
          type: string
        sizeGb:
          type: integer
    MetaStoreGcpCloudSql:
      type: object
      required:
        - tier
        - sizeGb
      properties:
        tier:
          type: string
        sizeGb:
          type: integer
    MetaStoreAzrPostgres:
      type: object
      required:
        - sku
        - sizeGb
      properties:
        sku:
          type: string
        sizeGb:
          type: integer
    MetaStoreSharingPg:
      type: object
      required:
        - instanceId
      properties:
        instanceId:
          type: string
    TenantResourceMetaStore:
      type: object
      required:
        - type
        - rwu
      properties:
        type:
          $ref: '#/components/schemas/MetaStoreType'
        rwu:
          type: string
        etcd:
          $ref: '#/components/schemas/MetaStoreEtcd'
        postgresql:
          $ref: '#/components/schemas/MetaStorePostgreSql'
        aws_rds:
          $ref: '#/components/schemas/MetaStoreAwsRds'
        gcp_cloudsql:
          $ref: '#/components/schemas/MetaStoreGcpCloudSql'
        azr_postgres:
          $ref: '#/components/schemas/MetaStoreAzrPostgres'
        sharing_pg:
          $ref: '#/components/schemas/MetaStoreSharingPg'
    TenantResourceGroup:
      type: object
      required:
        - name
        - resource
        - computeCache
      properties:
        name:
          type: string
        resource:
          $ref: '#/components/schemas/ComponentResource'
        computeCache:
          $ref: '#/components/schemas/TenantResourceComputeCache'
    TenantResourceGroupArray:
      type: array
      items:
        $ref: '#/components/schemas/TenantResourceGroup'
    TenantResource:
      type: object
      required:
        - components
        - computeCache
      properties:
        components:
          $ref: '#/components/schemas/TenantResourceComponents'
        etcdVolumeSizeGiB:
          type: integer
        computeCache:
          $ref: '#/components/schemas/TenantResourceComputeCache'
        metaStore:
          $ref: '#/components/schemas/TenantResourceMetaStore'
        resourceGroups:
          $ref: '#/components/schemas/TenantResourceGroupArray'
    TierId:
      type: string
      enum:
        - Free
        - Invited
        - Developer-Free
        - Developer-Basic
        - Developer-Test
        - Standard
        - BYOC
        - Test
        - Benchmark
    TenantExtensionServerlessCompaction:
      type: object
      required:
        - enabled
        - maximumCompactionConcurrency
      properties:
        enabled:
          type: boolean
        maximumCompactionConcurrency:
          type: integer
    TenantExtensions:
      type: object
      properties:
        serverlessCompaction:
          $ref: '#/components/schemas/TenantExtensionServerlessCompaction'
    Tenant:
      type: object
      required:
        - id
        - userId
        - tenantName
        - region
        - status
        - tier
        - resources
        - createdAt
        - imageTag
        - latestImageTag
        - rw_config
        - updatedAt
        - health_status
        - nsId
        - orgId
        - etcd_config
        - usageType
      properties:
        id:
          type: integer
          format: uint64
        userId:
          type: integer
          format: uint64
        orgId:
          type: string
          format: uuid
        tenantName:
          type: string
        region:
          type: string
        resources:
          $ref: '#/components/schemas/TenantResource'
        status:
          type: string
          enum:
            - Creating
            - Running
            - Deleting
            - Failed
            - Stopped
            - Stopping
            - Starting
            - Expired
            - ConfigUpdating
            - Upgrading
            - Updating
            - Snapshotting
            - ExtensionCompactionEnabling
            - ExtensionCompactionDisabling
            - ExtensionServerlessBackfillEnabling
            - ExtensionServerlessBackfillUpdate
            - ExtensionServerlessBackfillDisabling
            - MetaMigrating
            - Restoring
            - Quiesced
            - ResourceGroupsUpdating
        tier:
          $ref: '#/components/schemas/TierId'
        usageType:
          type: string
          enum:
            - general
            - pipeline
        imageTag:
          type: string
          example: v0.1.12
        latestImageTag:
          type: string
          example: v0.1.12
        rw_config:
          type: string
        etcd_config:
          type: string
        health_status:
          type: string
          enum:
            - Unknown
            - Healthy
            - Unhealthy
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        nsId:
          type: string
          format: uuid
        clusterName:
          type: string
        upcomingSnapshotTime:
          type: string
          format: date-time
        extensions:
          $ref: '#/components/schemas/TenantExtensions'
    TenantArray:
      type: array
      items:
        $ref: '#/components/schemas/Tenant'
    TenantPagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: '#/components/schemas/Pagination'
        - type: object
          required:
            - tenants
          properties:
            tenants:
              $ref: '#/components/schemas/TenantArray'
    ComponentResourceRequest:
      type: object
      required:
        - componentTypeId
        - replica
      properties:
        componentTypeId:
          type: string
        replica:
          type: integer
    TenantResourceRequestComponents:
      type: object
      properties:
        standalone:
          $ref: '#/components/schemas/ComponentResourceRequest'
        compute:
          $ref: '#/components/schemas/ComponentResourceRequest'
        compactor:
          $ref: '#/components/schemas/ComponentResourceRequest'
        frontend:
          $ref: '#/components/schemas/ComponentResourceRequest'
        meta:
          $ref: '#/components/schemas/ComponentResourceRequest'
        etcd:
          $ref: '#/components/schemas/ComponentResourceRequest'
    TenantResourceRequestMetaStoreEtcd:
      type: object
      allOf:
        - $ref: '#/components/schemas/ComponentResourceRequest'
        - type: object
          required:
            - sizeGb
          properties:
            sizeGb:
              type: integer
    TenantResourceRequestMetaStorePostgreSql:
      type: object
      allOf:
        - $ref: '#/components/schemas/ComponentResourceRequest'
        - type: object
          required:
            - sizeGb
          properties:
            sizeGb:
              type: integer
    TenantResourceRequestMetaStore:
      type: object
      required:
        - type
      properties:
        type:
          $ref: '#/components/schemas/MetaStoreType'
        etcd:
          $ref: '#/components/schemas/TenantResourceRequestMetaStoreEtcd'
        postgresql:
          $ref: '#/components/schemas/TenantResourceRequestMetaStorePostgreSql'
    TenantResourceRequest:
      type: object
      required:
        - components
        - computeFileCacheSizeGiB
      properties:
        components:
          $ref: '#/components/schemas/TenantResourceRequestComponents'
        etcdVolumeSizeGiB:
          type: integer
        computeFileCacheSizeGiB:
          type: integer
        metaStore:
          $ref: '#/components/schemas/TenantResourceRequestMetaStore'
    TenantExtensionServerlessCompactionRequest:
      description: Serverless compaction request for tenant.
      type: object
      required:
        - maximumCompactionConcurrency
      properties:
        maximumCompactionConcurrency:
          type: integer
          minimum: 1
    TenantExtensionsRequest:
      description: Extensions request for tenant. Currently only serverless compaction is supported
      type: object
      properties:
        serverlessCompaction:
          $ref: '#/components/schemas/TenantExtensionServerlessCompactionRequest'
    TenantRequestRequestBody:
      type: object
      required:
        - tenantName
      properties:
        tenantName:
          type: string
        imageTag:
          type: string
          example: latest
        clusterName:
          type: string
        tier:
          $ref: '#/components/schemas/TierId'
        sku:
          type: string
        resources:
          $ref: '#/components/schemas/TenantResourceRequest'
        etcdConfig:
          type: string
        configId:
          type: string
          format: uuid
        rwConfig:
          description: if config ID is not provided, use this config. currently used in tf plugin
          type: string
        extensions:
          $ref: '#/components/schemas/TenantExtensionsRequest'
        usageType:
          type: string
          enum:
            - general
            - pipeline
          default: general
    AWSServingPrivateLinkInfo:
      type: object
      required:
        - host
        - port
        - serviceName
        - azs
      properties:
        host:
          type: string
        port:
          type: integer
        serviceName:
          type: string
        azs:
          type: array
          items:
            type: string
    Endpoint:
      type: object
      required:
        - id
        - nsId
        - host
        - port
        - database
        - options
        - internalHost
        - internalPort
      properties:
        id:
          type: integer
          format: int64
        nsId:
          type: string
          format: uuid
        host:
          type: string
        port:
          type: integer
        database:
          type: string
        options:
          type: string
        internalHost:
          type: string
        internalPort:
          type: integer
        awsServingPrivateLink:
          type: object
          $ref: '#/components/schemas/AWSServingPrivateLinkInfo'
    PostTenantResourcesRequestBody:
      type: object
      properties:
        meta:
          $ref: '#/components/schemas/ComponentResourceRequest'
        frontend:
          $ref: '#/components/schemas/ComponentResourceRequest'
        compute:
          $ref: '#/components/schemas/ComponentResourceRequest'
        compactor:
          $ref: '#/components/schemas/ComponentResourceRequest'
        standalone:
          $ref: '#/components/schemas/ComponentResourceRequest'
        extensions:
          $ref: '#/components/schemas/TenantExtensionsRequest'
    PostTenantUpdateVersionRequestBody:
      type: object
      properties:
        version:
          type: string
    ResourceGroupDetails:
      type: object
      allOf:
        - $ref: '#/components/schemas/TenantResourceGroup'
        - type: object
          required:
            - databaseCount
          properties:
            databaseCount:
              type: integer
    GetResourceGroupsResponseBody:
      type: object
      required:
        - resourceGroups
      properties:
        resourceGroups:
          type: array
          items:
            $ref: '#/components/schemas/ResourceGroupDetails'
    CreateResourceGroupsRequestBody:
      type: object
      required:
        - name
        - resource
      properties:
        name:
          type: string
        resource:
          $ref: '#/components/schemas/ComponentResourceRequest'
        fileCacheSizeGb:
          type: integer
    UpdateResourceGroupsRequestBody:
      type: object
      required:
        - resource
      properties:
        resource:
          $ref: '#/components/schemas/ComponentResourceRequest'
    Database:
      type: object
      required:
        - name
        - resourceGroup
      properties:
        name:
          type: string
        resourceGroup:
          type: string
    DatabasesPagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: '#/components/schemas/Pagination'
        - type: object
          required:
            - databases
          properties:
            databases:
              type: array
              items:
                $ref: '#/components/schemas/Database'
    CreateDatabaseRequestBody:
      type: object
      required:
        - name
        - resourceGroup
      properties:
        name:
          type: string
        resourceGroup:
          type: string
    SqlExecutionRequestBody:
      type: object
      required:
        - query
      properties:
        query:
          type: string
        timeout:
          type: integer
          description: timeout in ms
          default: 5000
    SqlQueryRequestBody:
      type: object
      required:
        - query
      properties:
        query:
          type: string
        timeout:
          type: integer
          description: timeout in ms
          default: 5000
        maxRowCount:
          type: integer
          default: 1000
    QueryColumn:
      type: object
      required:
        - name
        - dataType
      properties:
        name:
          type: string
        dataType:
          type: string
    QueryRow:
      type: array
      items: {}
    SqlQueryResponseBody:
      type: object
      required:
        - header
        - rows
        - executionTime
        - commandTag
      properties:
        header:
          type: array
          items:
            $ref: '#/components/schemas/QueryColumn'
        rows:
          type: array
          items:
            $ref: '#/components/schemas/QueryRow'
        executionTime:
          type: integer
        commandTag:
          type: string
    RelationInfo:
      type: object
      required:
        - relname
        - reltype
      properties:
        relname:
          type: string
        reltype:
          type: string
    TenantRelationsInfo:
      type: object
      required:
        - relations
      properties:
        relations:
          type: array
          items:
            $ref: '#/components/schemas/RelationInfo'
    DBUser:
      type: object
      required:
        - usesysid
        - username
        - usecreatedb
        - usesuper
        - usecreateuser
        - canlogin
      properties:
        usesysid:
          type: integer
          format: uint64
        username:
          type: string
        usecreatedb:
          type: boolean
        usesuper:
          type: boolean
        usecreateuser:
          type: boolean
        canlogin:
          type: boolean
    DBUserArray:
      type: array
      items:
        $ref: '#/components/schemas/DBUser'
    DBUsersPagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: '#/components/schemas/Pagination'
        - type: object
          required:
            - dbusers
          properties:
            dbusers:
              $ref: '#/components/schemas/DBUserArray'
    CreateDBUserRequestBody:
      type: object
      required:
        - username
        - password
        - superuser
        - createdb
      properties:
        username:
          type: string
        password:
          type: string
        superuser:
          type: boolean
        createdb:
          type: boolean
        createuser:
          type: boolean
    UpdateDBUserRequestBody:
      type: object
      required:
        - password
      properties:
        password:
          type: string
    PostPrivateLinkRequestBody:
      type: object
      required:
        - target
        - connectionName
      properties:
        target:
          type: string
        connectionName:
          type: string
    PostPrivateLinkResponseBody:
      type: object
      required:
        - id
        - connectionName
      properties:
        id:
          type: string
          format: uuid
        connectionName:
          type: string
    PrivateLink:
      type: object
      required:
        - id
        - tenantId
        - status
        - connectionState
        - connectionName
      properties:
        connectionName:
          type: string
        id:
          type: string
          format: uuid
        tenantId:
          type: integer
          format: int64
        status:
          type: string
          enum:
            - CREATING
            - CREATED
            - DELETING
            - ERROR
            - UNKNOWN
        connectionState:
          type: string
          enum:
            - STATUS_UNSPECIFIED
            - PENDING
            - ACCEPTED
            - REJECTED
            - CLOSED
        target:
          type: string
        endpoint:
          type: string
    ColumnDescArray:
      type: array
      items:
        $ref: '#/components/schemas/ColumnDesc'
    ColumnDesc:
      type: object
      required:
        - name
        - type
      properties:
        name:
          type: string
        type:
          type: string
        fields:
          $ref: '#/components/schemas/ColumnDescArray'
    Table:
      type: object
      required:
        - databaseName
        - definition
        - tableId
        - tableName
        - tableSchema
        - tableOwner
      properties:
        databaseName:
          type: string
        definition:
          type: string
        tableId:
          type: integer
          format: int64
        tableName:
          type: string
        tableSchema:
          type: string
        tableOwner:
          type: string
        createdAt:
          type: string
          format: date-time
        size:
          type: string
        columns:
          $ref: '#/components/schemas/ColumnDescArray'
    TableArray:
      type: array
      items:
        $ref: '#/components/schemas/Table'
    TablesPagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: '#/components/schemas/Pagination'
        - type: object
          required:
            - tables
          properties:
            tables:
              $ref: '#/components/schemas/TableArray'
    Connector:
      type: object
      properties:
        connectorType:
          type: string
        importantConfigs:
          type: object
          additionalProperties:
            type: string
        properties:
          type: object
          additionalProperties:
            type: string
    TableDetail:
      type: object
      required:
        - table
        - connector
      properties:
        table:
          $ref: '#/components/schemas/Table'
        connector:
          $ref: '#/components/schemas/Connector'
    RwDependency:
      type: object
      required:
        - name
        - type
        - id
        - schema
      properties:
        name:
          type: string
        type:
          type: string
        id:
          type: integer
          format: uint64
        schema:
          type: string
    RwDependencyArray:
      type: array
      items:
        $ref: '#/components/schemas/RwDependency'
    RwDependencyPagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: '#/components/schemas/Pagination'
        - type: object
          required:
            - dependencies
          properties:
            dependencies:
              $ref: '#/components/schemas/RwDependencyArray'
    RowCount:
      type: object
      required:
        - value
      properties:
        value:
          type: integer
          format: uint64
    Throughput:
      type: object
      required:
        - value
      description: unit in rows/s
      properties:
        value:
          type: number
          format: double
    DataLatency:
      type: object
      required:
        - value
      description: unit in second
      properties:
        value:
          type: number
          format: double
    LabelItem:
      type: object
      required:
        - key
        - value
      properties:
        key:
          type: string
        value:
          type: string
    MetricPoint:
      type: object
      required:
        - timestamp
        - value
      properties:
        timestamp:
          type: string
          format: date-time
        value:
          type: number
          format: double
    MetricItem:
      type: object
      required:
        - labels
        - values
      properties:
        labels:
          type: array
          items:
            $ref: '#/components/schemas/LabelItem'
        values:
          type: array
          items:
            $ref: '#/components/schemas/MetricPoint'
    Metrics:
      type: object
      required:
        - name
        - items
      properties:
        name:
          type: string
        items:
          type: array
          items:
            $ref: '#/components/schemas/MetricItem'
    MatView:
      type: object
      required:
        - databaseName
        - definition
        - matViewId
        - matViewName
        - matViewSchema
        - matViewOwner
        - fragmentIds
      properties:
        databaseName:
          type: string
        definition:
          type: string
        matViewId:
          type: integer
          format: int64
        matViewName:
          type: string
        matViewSchema:
          type: string
        matViewOwner:
          type: string
        fragmentIds:
          type: array
          items:
            type: integer
            format: int64
        columns:
          $ref: '#/components/schemas/ColumnDescArray'
        createdAt:
          type: string
          format: date-time
        size:
          type: string
    MatViewArray:
      type: array
      items:
        $ref: '#/components/schemas/MatView'
    MatViewsPagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: '#/components/schemas/Pagination'
        - type: object
          required:
            - matViews
          properties:
            matViews:
              $ref: '#/components/schemas/MatViewArray'
    DdlProgressStatus:
      type: string
      enum:
        - InProgress
        - Completed
    TenantDdlProgress:
      type: object
      required:
        - status
        - progress
        - initialized_at
      properties:
        status:
          $ref: '#/components/schemas/DdlProgressStatus'
        progress:
          type: string
        initialized_at:
          type: string
          format: date-time
    BackupSnapshotItem:
      type: object
      required:
        - id
        - created_at_unix_mills
        - status
      properties:
        id:
          type: string
          format: uuid
        created_at_unix_mills:
          type: integer
          format: int64
        meta_snapshot_id:
          type: integer
          format: int64
        status:
          type: string
        type:
          type: string
        rw_version:
          type: string
    BackupSnapshotsPagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: '#/components/schemas/Pagination'
        - type: object
          required:
            - items
          properties:
            items:
              type: array
              items:
                $ref: '#/components/schemas/BackupSnapshotItem'
    PostSnapshotResponseBody:
      type: object
      required:
        - workflowId
        - snapshotId
      properties:
        workflowId:
          type: string
          format: uuid
        snapshotId:
          type: string
          format: uuid
    PostTenantRestoreRequestBody:
      type: object
      description: configuration for new tenant to be created in restore command
      required:
        - newTenantName
      properties:
        newTenantName:
          type: string
    PostSourcesFetchSchemaRequestBody:
      type: object
      required:
        - format
        - location
        - url
      properties:
        format:
          type: string
          enum:
            - AVRO
            - PROTOBUF
            - JSON
        location:
          type: string
          enum:
            - WEB_LOCATION
            - SCHEMA_REGISTRY
            - S3
        url:
          type: string
        s3:
          type: object
          required:
            - region
          properties:
            region:
              type: string
            accessKeyId:
              type: string
            secretAccessKey:
              type: string
        schemaRegistry:
          type: object
          required:
            - topic
          properties:
            topic:
              type: string
            username:
              type: string
            password:
              type: string
        protobuf:
          type: object
          required:
            - message
          properties:
            message:
              type: string
    ColumnDescs:
      type: object
      required:
        - columns
      properties:
        columns:
          $ref: '#/components/schemas/ColumnDescArray'
    KafkaConfig:
      type: object
      required:
        - server
        - topic
      properties:
        server:
          type: string
        topic:
          type: string
        securityProtocol:
          type: string
          enum:
            - SASL_SSL
            - SSL
            - SASL_PLAINTEXT
        saslMechanisms:
          type: string
          enum:
            - OAUTHBEARER
            - GSSAPI
            - PLAIN
            - SCRAM-SHA-256
            - SCRAM-SHA-512
        saslUsername:
          type: string
        saslPassword:
          type: string
        caCertificate:
          type: string
    PostSourcesFetchKafkaSchemaRequestBody:
      type: object
      required:
        - kafkaConfig
      properties:
        kafkaConfig:
          $ref: '#/components/schemas/KafkaConfig'
    PostgresCdcConfig:
      type: object
      required:
        - hostname
        - port
        - username
        - password
        - database
      properties:
        hostname:
          type: string
        port:
          type: integer
        username:
          type: string
        password:
          type: string
        database:
          type: string
        sslMode:
          type: string
          enum:
            - disabled
            - preferred
            - required
            - verify-ca
            - verify-full
    PostSourcesPingRequestBody:
      type: object
      required:
        - type
        - config
      properties:
        type:
          type: string
          enum:
            - kafka
            - postgres-cdc
        config:
          oneOf:
            - $ref: '#/components/schemas/KafkaConfig'
            - $ref: '#/components/schemas/PostgresCdcConfig'
    SourceInfo:
      type: object
      required:
        - id
        - sourceName
        - isSchemaRegistry
        - hasPrimaryKey
        - definition
        - properties
        - owner
        - schema
      properties:
        id:
          type: integer
          format: int32
        sourceName:
          type: string
        connectorType:
          type: string
        topic:
          type: string
        endpoint:
          type: string
        scan.startup.mode:
          type: string
        scan.startup.timestamp_millis:
          type: string
        connection.name:
          type: string
        webLocation:
          type: string
        healthCheck:
          type: string
          enum:
            - Active
            - Failed
            - Unknown
        throughput:
          type: number
          format: double
        dataFormat:
          type: string
        isSchemaRegistry:
          type: boolean
          default: false
        hasPrimaryKey:
          type: boolean
          default: false
        definition:
          type: string
        properties:
          type: object
          additionalProperties:
            type: string
        createdAt:
          type: string
          format: date-time
        owner:
          type: string
        schema:
          type: string
    TenantSourcesInfo:
      type: object
      required:
        - sources
      properties:
        sources:
          type: array
          items:
            $ref: '#/components/schemas/SourceInfo'
    TenantSourceDetail:
      type: object
      required:
        - info
        - columns
        - importantConfigs
      properties:
        info:
          $ref: '#/components/schemas/SourceInfo'
        columns:
          $ref: '#/components/schemas/ColumnDescArray'
        importantConfigs:
          type: object
          additionalProperties:
            type: string
    SinkInfo:
      type: object
      required:
        - id
        - sinkName
        - definition
        - connectorType
        - owner
        - schema
      properties:
        id:
          type: integer
          format: int32
        sinkName:
          type: string
        connectorType:
          type: string
        endpoint:
          type: string
        type:
          type: string
        force_append_only:
          type: string
        primary_key:
          type: string
        healthCheck:
          type: string
          enum:
            - Active
            - Failed
          default: Active
        throughput:
          type: number
          format: double
        properties:
          type: object
          additionalProperties:
            type: string
        definition:
          type: string
        createdAt:
          type: string
          format: date-time
        owner:
          type: string
        schema:
          type: string
    TenantSinksInfo:
      type: object
      required:
        - sinks
      properties:
        sinks:
          type: array
          items:
            $ref: '#/components/schemas/SinkInfo'
    TenantSinkDetail:
      type: object
      required:
        - info
        - columns
        - importantConfigs
      properties:
        info:
          $ref: '#/components/schemas/SinkInfo'
        columns:
          $ref: '#/components/schemas/ColumnDescArray'
        importantConfigs:
          type: object
          additionalProperties:
            type: string
    SchemaInfo:
      type: object
      required:
        - catalog_name
        - schema_name
        - schema_owner
      properties:
        catalog_name:
          type: string
        schema_name:
          type: string
        schema_owner:
          type: string
    TenantSchemasInfo:
      type: object
      required:
        - schemas
      properties:
        schemas:
          type: array
          items:
            $ref: '#/components/schemas/SchemaInfo'
    ClusterStatus:
      type: string
      enum:
        - Uninitialized
        - Provisioned
        - Ready
        - Terminating
        - Deleted
        - PendingResourceDeletion
        - Updating
        - Failed
    ManagedCluster:
      required:
        - id
        - org
        - name
        - status
        - master_url
        - cluster_service_account
        - token
        - serving_type
        - settings
      properties:
        id:
          type: integer
          format: uint64
        org:
          type: string
          format: uuid
        name:
          type: string
        status:
          $ref: '#/components/schemas/ClusterStatus'
        master_url:
          type: string
        cluster_service_account:
          type: string
        token:
          type: string
        serving_type:
          type: string
        settings:
          type: object
          additionalProperties:
            type: string
    ManagedClusterArray:
      type: array
      items:
        $ref: '#/components/schemas/ManagedCluster'
    ManagedClustersPagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: '#/components/schemas/Pagination'
        - type: object
          required:
            - clusters
          properties:
            clusters:
              $ref: '#/components/schemas/ManagedClusterArray'
    PostByocClustersRequestBody:
      required:
        - name
        - settings
      properties:
        name:
          type: string
        version:
          type: string
        settings:
          type: object
          additionalProperties:
            type: string
    PutByocClusterRequestBody:
      required:
        - name
        - status
        - settings
      properties:
        name:
          type: string
        status:
          $ref: '#/components/schemas/ClusterStatus'
        settings:
          type: object
          additionalProperties:
            type: string
    PostByocClusterUpdateRequestBody:
      type: object
      properties:
        version:
          type: string
        customSettings:
          description: base64 encoded custom settings
          type: string
    Secret:
      type: object
      required:
        - id
        - name
        - owner
        - schema
      properties:
        id:
          type: integer
          format: uint64
        name:
          type: string
        owner:
          type: string
        schema:
          type: string
    SecretArray:
      type: array
      items:
        $ref: '#/components/schemas/Secret'
    SecretsPagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: '#/components/schemas/Pagination'
        - type: object
          required:
            - secrets
          properties:
            secrets:
              $ref: '#/components/schemas/SecretArray'
    CreateSecretRequestBody:
      type: object
      required:
        - name
        - value
      properties:
        name:
          type: string
        value:
          type: string
    RwSecretReferencesPagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: '#/components/schemas/Pagination'
        - type: object
          required:
            - references
          properties:
            references:
              $ref: '#/components/schemas/RwDependencyArray'
    IcebergCompaction:
      type: object
      required:
        - status
      properties:
        status:
          type: string
        config:
          type: string
        resources:
          $ref: '#/components/schemas/ComponentResource'
    PostTenantsTestAlertRequestBody:
      type: object
      required:
        - triggered
      properties:
        triggered:
          type: boolean
  responses:
    BadRequestResponse:
      description: 400 Bad Request
      content:
        application/json:
          schema:
            type: object
            required:
              - msg
            properties:
              msg:
                type: string
    AlreadyExistsResponse:
      description: 409 Bad Request
      content:
        application/json:
          schema:
            type: object
            required:
              - msg
            properties:
              msg:
                type: string
    NotFoundResponse:
      description: 404 Not Found
      content:
        application/json:
          schema:
            type: object
            required:
              - msg
            properties:
              msg:
                type: string
    DefaultResponse:
      description: Default responses returning msg
      content:
        application/json:
          schema:
            type: object
            required:
              - msg
            properties:
              msg:
                type: string
    FailedPreconditionResponse:
      description: 400 Failed Precondition
      content:
        application/json:
          schema:
            type: object
            required:
              - msg
            properties:
              msg:
                type: string
              code:
                type: string
    ForbiddenResponse:
      description: 403 Forbidden
      content:
        application/json:
          schema:
            type: object
            required:
              - msg
            properties:
              msg:
                type: string
    AvailabilityResponse:
      description: Availability response
      content:
        application/json:
          schema:
            type: object
            required:
              - available
              - msg
            properties:
              available:
                type: boolean
              msg:
                type: string
    PrometheusAPIResponse:
      description: Prometheus API response
      content:
        application/json:
          schema:
            type: object
            required:
              - status
              - data
            properties:
              status:
                type: string
              data:
                type: object
