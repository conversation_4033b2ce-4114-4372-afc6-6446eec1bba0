openapi: 3.0.3

info:
  title: v2
  version: 2.0-alpha
paths: {}
components:
  schemas:
    ##############################
    # Tenant
    ##############################
    TenantRequestRequestBody:
      type: object
      required: [tenantName]
      properties:
        tenantName:
          type: string
        imageTag:
          type: string
          example: latest
        clusterName:
          type: string
        tier:
          $ref: "mgmt_resource.yaml#/components/schemas/TierId"
        sku:
          type: string
        resources:
          $ref: "#/components/schemas/TenantResourceRequest"
        etcdConfig:
          type: string
        configId:
          type: string
          format: uuid
        rwConfig:
          description: "if config ID is not provided, use this config. currently used in tf plugin"
          type: string
        extensions:
          $ref: "#/components/schemas/TenantExtensionsRequest"
        usageType:
          type: string
          enum:
            - general
            - pipeline
          default: general
    TenantExtensionsRequest:
      description: "Extensions request for tenant. Currently only serverless compaction is supported"
      type: object
      properties:
        serverlessCompaction:
          $ref: "#/components/schemas/TenantExtensionServerlessCompactionRequest"
    TenantExtensionServerlessCompactionRequest:
      description: "Serverless compaction request for tenant."
      type: object
      required: [maximumCompactionConcurrency]
      properties:
        maximumCompactionConcurrency:
          type: integer
          minimum: 1
    TenantResourceRequest:
      type: object
      required: [components, computeFileCacheSizeGiB]
      properties:
        components:
          $ref: "#/components/schemas/TenantResourceRequestComponents"
        etcdVolumeSizeGiB:
          type: integer
        computeFileCacheSizeGiB:
          type: integer
        metaStore:
          $ref: "#/components/schemas/TenantResourceRequestMetaStore"

    TenantResourceRequestComponents:
      type: object
      properties:
        standalone:
          $ref: "#/components/schemas/ComponentResourceRequest"
        compute:
          $ref: "#/components/schemas/ComponentResourceRequest"
        compactor:
          $ref: "#/components/schemas/ComponentResourceRequest"
        frontend:
          $ref: "#/components/schemas/ComponentResourceRequest"
        meta:
          $ref: "#/components/schemas/ComponentResourceRequest"
        etcd:
          $ref: "#/components/schemas/ComponentResourceRequest"
    ComponentResourceRequest:
      type: object
      required: [componentTypeId, replica]
      properties:
        componentTypeId:
          type: string
        replica:
          type: integer

    TenantResourceRequestMetaStore:
      type: object
      required: [type]
      properties:
        type:
          $ref: "mgmt_resource.yaml#/components/schemas/MetaStoreType"
        etcd:
          $ref: "#/components/schemas/TenantResourceRequestMetaStoreEtcd"
        postgresql:
          $ref: "#/components/schemas/TenantResourceRequestMetaStorePostgreSql"

    TenantResourceRequestMetaStoreEtcd:
      type: object
      allOf:
        - $ref: "#/components/schemas/ComponentResourceRequest"
        - type: object
          required: [sizeGb]
          properties:
            sizeGb:
              type: integer
    TenantResourceRequestMetaStorePostgreSql:
      type: object
      allOf:
        - $ref: "#/components/schemas/ComponentResourceRequest"
        - type: object
          required: [sizeGb]
          properties:
            sizeGb:
              type: integer

    PostTenantResourcesRequestBody:
      type: object
      properties:
        meta:
          $ref: "#/components/schemas/ComponentResourceRequest"
        frontend:
          $ref: "#/components/schemas/ComponentResourceRequest"
        compute:
          $ref: "#/components/schemas/ComponentResourceRequest"
        compactor:
          $ref: "#/components/schemas/ComponentResourceRequest"
        standalone:
          $ref: "#/components/schemas/ComponentResourceRequest"
        extensions:
          $ref: "#/components/schemas/TenantExtensionsRequest"
    PostPrivateLinkRequestBody:
      type: object
      required: [target, connectionName]
      properties:
        target:
          type: string
        connectionName:
          type: string

    PostPrivateLinkResponseBody:
      type: object
      required:
        - id
        - connectionName
      properties:
        id:
          type: string
          format: uuid
        connectionName:
          type: string

    PostTenantUpdateVersionRequestBody:
      type: object
      properties:
        version:
          type: string

    PostTenantStatusRequestBody:
      type: object
      required: [target, previous]
      properties:
        target:
          type: string
        previous:
          type: string

    GetImageTagResponse:
      type: object
      required: [imageTag]
      properties:
        imageTag:
          type: string

    PostTenantRestoreRequestBody:
      type: object
      description: "configuration for new tenant to be created in restore command"
      required: [newTenantName]
      properties:
        newTenantName:
          type: string

    PostSnapshotResponseBody:
      type: object
      required:
        - workflowId
        - snapshotId
      properties:
        workflowId:
          type: string
          format: uuid
        snapshotId:
          type: string
          format: uuid
    ##############################
    # Resource Group
    ##############################
    GetResourceGroupsResponseBody:
      type: object
      required: [resourceGroups]
      properties:
        resourceGroups:
          type: array
          items:
            $ref: "mgmt_resource.yaml#/components/schemas/ResourceGroupDetails"

    ##############################
    # DBUser
    ##############################
    CreateDBUserRequestBody:
      type: object
      required: [username, password, superuser, createdb]
      properties:
        username:
          type: string
        password:
          type: string
        superuser:
          type: boolean
        createdb:
          type: boolean
        createuser:
          type: boolean
          # TODO(CLOUD-3565) make createuser required

    UpdateDBUserRequestBody:
      type: object
      required: [password]
      properties:
        password:
          type: string

    ##############################
    # Databases
    ##############################
    CreateDatabaseRequestBody:
      type: object
      required: [name, resourceGroup]
      properties:
        name:
          type: string
        resourceGroup:
          type: string

    ##############################
    # Resource Group
    ##############################
    CreateResourceGroupsRequestBody:
      type: object
      required: [name, resource]
      properties:
        name:
          type: string
        resource:
          $ref: "#/components/schemas/ComponentResourceRequest"
        fileCacheSizeGb:
          type: integer

    UpdateResourceGroupsRequestBody:
      type: object
      required: [resource]
      properties:
        resource:
          $ref: "#/components/schemas/ComponentResourceRequest"

    ##############################
    # Source
    ##############################
    PostSourcesPingRequestBody:
      type: object
      required: [type, config]
      properties:
        type:
          type: string
          enum:
            - kafka
            - postgres-cdc
        config:
          oneOf:
            - $ref: "mgmt_resource.yaml#/components/schemas/KafkaConfig"
            - $ref: "mgmt_resource.yaml#/components/schemas/PostgresCdcConfig"

    PostSourcesFetchSchemaRequestBody:
      type: object
      required: [format, location, url]
      properties:
        format:
          type: string
          enum: [AVRO, PROTOBUF, JSON]
        location:
          type: string
          enum: [WEB_LOCATION, SCHEMA_REGISTRY, S3]
        url:
          type: string
        s3:
          type: object
          required: [region]
          properties:
            region:
              type: string
            accessKeyId:
              type: string
            secretAccessKey:
              type: string
        schemaRegistry:
          type: object
          required: [topic]
          properties:
            topic:
              type: string
            username:
              type: string
            password:
              type: string
        protobuf:
          type: object
          required: [message]
          properties:
            message:
              type: string
    PostSourcesFetchKafkaSchemaRequestBody:
      type: object
      required: [kafkaConfig]
      properties:
        kafkaConfig:
          $ref: "mgmt_resource.yaml#/components/schemas/KafkaConfig"

    ##############################
    # Query
    ##############################
    SqlExecutionRequestBody:
      type: object
      required: [query]
      properties:
        query:
          type: string
        timeout:
          type: integer
          description: "timeout in ms"
          default: 5000
    SqlQueryRequestBody:
      type: object
      required: [query]
      properties:
        query:
          type: string
        timeout:
          type: integer
          description: "timeout in ms"
          default: 5000
        maxRowCount:
          type: integer
          default: 1000
    SqlQueryResponseBody:
      type: object
      required: [header, rows, executionTime, commandTag]
      properties:
        header:
          type: array
          items:
            $ref: "mgmt_resource.yaml#/components/schemas/QueryColumn"
        rows:
          type: array
          items:
            $ref: "mgmt_resource.yaml#/components/schemas/QueryRow"
        executionTime:
          type: integer
        commandTag:
          type: string

    ##############################
    # Managed Cluster
    ##############################
    PostByocClustersRequestBody:
      required: [name, settings]
      properties:
        name:
          type: string
        version:
          type: string
        settings: # map[string]string
          type: object
          additionalProperties:
            type: string

    PutByocClusterRequestBody:
      required: [name, status, settings]
      properties:
        name:
          type: string
        status:
          $ref: "mgmt_resource.yaml#/components/schemas/ClusterStatus"
        settings: # map[string]string
          type: object
          additionalProperties:
            type: string

    PostByocClusterUpdateRequestBody:
      type: object
      properties:
        version:
          type: string
        customSettings:
          description: "base64 encoded custom settings"
          type: string

    ##############################
    # Secret
    ##############################
    CreateSecretRequestBody:
      type: object
      required: [name, value]
      properties:
        name:
          type: string
        value:
          type: string

    ##############################
    # Alert
    ##############################
    PostTenantsTestAlertRequestBody:
      type: object
      required: [triggered]
      properties:
        triggered:
          type: boolean
