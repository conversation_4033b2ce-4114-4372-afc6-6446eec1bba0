REDOCLY_VERSION ?= 1.34.3
PRETTIER_VERSION ?= 3.6.2

MODULE_API_V2_MAKEFILE_PATH := $(abspath $(lastword $(MAKEFILE_LIST)))
MODULE_API_V2_ROOT ?= $(abspath $(dir $(MODULE_API_V2_MAKEFILE_PATH)))
MODULE_API_V2_PARENT_PATH ?= $(abspath $(dir $(MODULE_API_V2_ROOT)))

API_V2_SOURCE_DIR := $(MODULE_API_V2_ROOT)
API_V2_BUNDLE_DIR := $(API_V2_SOURCE_DIR)/bundle
API_V2_ROOTS := $(API_V2_SOURCE_DIR)/mgmt.yaml $(API_V2_SOURCE_DIR)/billing.yaml $(API_V2_SOURCE_DIR)/acc.yaml
API_V2_TARGETS := $(patsubst $(API_V2_SOURCE_DIR)/%,$(API_V2_SOURCE_DIR)/bundle/%,$(API_V2_ROOTS))
API_V2_SHARE_SOURCES = $(filter-out $(API_V2_ROOTS),$(wildcard $(API_V2_SOURCE_DIR)/*.yaml))

.PHONY: module/api/v2/build
module/api/v2/build: $(API_V2_TARGETS)

.PHONY: module/api/v2/clean
module/api/v2/clean:
	@echo "(api/v2) Cleaning API v2 bundles"
	@rm -rf $(API_V2_BUNDLE_DIR)

.PHONY: module/api/v2/fmt
module/api/v2/fmt:
	@docker run --rm -u $(id -u):$(id -g) -v "$(MODULE_API_V2_PARENT_PATH):/data" tmknom/prettier:$(PRETTIER_VERSION) --write '/data/v2/*.yaml'

$(API_V2_TARGETS): $(API_V2_SOURCE_DIR)/bundle/%.yaml: $(API_V2_SOURCE_DIR)/%.yaml $(API_V2_SHARE_SOURCES)
	@mkdir -p $(API_V2_BUNDLE_DIR)
	@echo "(api/v2) Building API v2 bundle for $<"
	@rm -f $@.tmp
	@echo "# Code generated by redocly/cli DO NOT EDIT." > $@.tmp
	@docker run \
		--rm \
		-v $(MODULE_API_V2_PARENT_PATH):/data \
		-v $(MODULE_API_V2_PARENT_PATH)/redocly-configs:/config \
		redocly/cli:$(REDOCLY_VERSION) \
		bundle "/data/$(<:$(MODULE_API_V2_PARENT_PATH)/%=%)" >> $@.tmp
	@mv $@.tmp $@
