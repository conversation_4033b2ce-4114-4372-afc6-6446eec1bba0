openapi: 3.0.3

info:
  title: v2
  version: 2.0-alpha
paths: {}
components:
  responses:
    DefaultResponse:
      description: "Default responses returning msg"
      content:
        application/json:
          schema:
            type: object
            required: [msg]
            properties:
              msg:
                type: string

    AvailabilityResponse:
      description: "Availability response"
      content:
        application/json:
          schema:
            type: object
            required: [available, msg]
            properties:
              available:
                type: boolean
              msg:
                type: string

    BadRequestResponse:
      description: "400 Bad Request"
      content:
        application/json:
          schema:
            type: object
            required: [msg]
            properties:
              msg:
                type: string

    FailedPreconditionResponse:
      description: "400 Failed Precondition"
      content:
        application/json:
          schema:
            type: object
            required: [msg]
            properties:
              msg:
                type: string
              code:
                type: string

    ForbiddenResponse:
      description: "403 Forbidden"
      content:
        application/json:
          schema:
            type: object
            required: [msg]
            properties:
              msg:
                type: string

    NotFoundResponse:
      description: "404 Not Found"
      content:
        application/json:
          schema:
            type: object
            required: [msg]
            properties:
              msg:
                type: string

    AlreadyExistsResponse:
      description: "409 Bad Request"
      content:
        application/json:
          schema:
            type: object
            required: [msg]
            properties:
              msg:
                type: string

    ResourceExhaustedResponse:
      description: "429 Resource Exhausted"
      content:
        application/json:
          schema:
            type: object
            required: [msg]
            properties:
              msg:
                type: string

    PrometheusAPIResponse:
      description: "Prometheus API response"
      content:
        application/json:
          schema:
            type: object
            required: [status, data]
            properties:
              status:
                type: string
              data:
                type: object
