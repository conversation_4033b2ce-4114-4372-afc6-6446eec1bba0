openapi: 3.0.3

info:
  title: v2
  version: 2.0-alpha
paths: {}
components:
  schemas:
    Pagination:
      type: object
      allOf:
        - $ref: "schema.yaml#/components/schemas/Page"
        - $ref: "schema.yaml#/components/schemas/Size"
    Page:
      type: object
      required: [limit, offset]
      properties:
        limit:
          type: integer
          format: uint64
        offset:
          type: integer
          format: uint64
    Size:
      type: object
      required: [size]
      properties:
        size:
          type: integer
          format: uint64
