locals {
  tf_cloud_organization = "risingwave-cloud"

  gke_project_id  = "rwcdev"
  env             = "dev"
  region          = "ap-southeast-1"
  assume_role_arn = "arn:aws:iam::023339134545:role/deploy"

  remote_eks_workspace                = "dev-apse1-internal-cluster"
  remote_config_center_workspace      = "dev-apse1-config-center"
  remote_config_center_init_workspace = "dev-apse1-config-center-init"
  remote_k8s_workspace                = "dev-apse1-internal-k8s"
  network_remote_workspace            = "dev-apse1-internal-network"
  remote_workspace                    = "dev-apse1-center-grafana"

  pg_major_version = "14"

  root_domain_name = "risingwave-cloud.xyz"

  byoc_gcp_control_plane_project_id = "rwcdev"
  byoc_gcp_telemetry_sa             = "<EMAIL>"

  byoc_azr_telemetry_app_id        = "d10c5e69-846a-40eb-b40d-f93ef9c9faca"
  byoc_azr_control_plane_tenant_id = "3dd90ad9-5a69-47e7-9cbf-0e29a5824574"

  admin_service_url_raw = "https://dev-admin-service-access.risingwave-cloud.xyz/api/v1"

  project     = "risingwave-cloud"
  environment = "dev"
  stack_tag   = "dev-apse1-center-grafana"
  version_tag = "20221207"
}

module "main" {
  source                            = "../../modules/env_base"
  tf_cloud_organization             = local.tf_cloud_organization
  env                               = local.env
  region                            = local.region
  assume_role_arn                   = local.assume_role_arn
  remote_internal_cluster_workspace = local.remote_eks_workspace
  config_center_init_workspace      = local.remote_config_center_init_workspace
  config_center_workspace           = local.remote_config_center_workspace
  remote_k8s_workspace              = local.remote_k8s_workspace
  network_remote_workspace          = local.network_remote_workspace
  remote_workspace                  = local.remote_workspace

  admin_service_url_raw = local.admin_service_url_raw

  pg_major_version = local.pg_major_version

  root_domain_name = local.root_domain_name

  ses_host            = var.ses_host
  ses_password        = var.ses_password
  ses_user            = var.ses_user
  alert_slack_webhook = var.alert_slack_webhook

  byoc_gcp_control_plane_project_id = local.byoc_gcp_control_plane_project_id
  byoc_gcp_telemetry_sa             = local.byoc_gcp_telemetry_sa

  byoc_azr_telemetry_app_id        = local.byoc_azr_telemetry_app_id
  byoc_azr_control_plane_tenant_id = local.byoc_azr_control_plane_tenant_id

  byoc_regions = []

  tenant_cluster_workspaces = [
    "dev-apse1-eks-a",
    "dev-gcp-asse1-gke-a",
    "dev-azr-inc-aks-a"
  ]

  tenant_k8s_workspaces = [
    "dev-apse1-eks-a-kubernetes",
    "dev-gcp-asse1-gke-a-k8s",
    "dev-azr-inc-aks-a-k8s"
  ]

  mgmt_cluster_workspaces = [
    "dev-aws-apso1-basic-cluster",
    "dev-apse1-mgmt",
    "dev-gcp-asse1-mgmt",
    "dev-azr-inc-mgmt"
  ]

  mgmt_k8s_workspaces = [
    "dev-aws-apso1-basic-cluster-k8s",
    "dev-apse1-mgmt-k8s",
    "dev-gcp-asse1-mgmt-k8s",
    "dev-azr-inc-mgmt-k8s",
  ]

  mgmt_db_workspaces = [
    "dev-gcp-asse1-mgmt-db",
    "dev-azr-inc-mgmt-db",
  ]

  account_cluster_workspaces = [
    "dev-apse1-acc"
  ]

  account_k8s_workspaces = [
    "dev-apse1-acc-k8s"
  ]

  account_db_workspaces = [
    "dev-apse1-acc-db"
  ]

  # port is defined in /internal_services_telemetry
  asdb_config = {
    enable       = true
    name         = "dev-apse1-acc-asdb",
    port         = 5432,
    workspace    = "dev-apse1-acc"
    db_workspace = "dev-apse1-acc-db"
  }

  bsdb_config = {
    enable       = true
    name         = "dev-apse1-acc-bsdb",
    port         = 5433,
    workspace    = "dev-apse1-acc"
    db_workspace = "dev-apse1-acc-db"
  }

  msdb_config = [
    {
      name           = "dev-apse1-mgmt-msdb",
      port           = 6432,
      cloud_provider = "aws"
      workspace      = "dev-apse1-mgmt",
      db_workspace   = "dev-apse1-mgmt",
    },
    {
      name           = "dev-gcp-asse1-mgmt-msdb",
      port           = 6532,
      cloud_provider = "gcp"
      workspace      = "dev-gcp-asse1-mgmt",
      db_workspace   = "dev-gcp-asse1-mgmt-db",
    },
    {
      name           = "dev-azr-inc-mgmt-msdb",
      port           = 6632,
      cloud_provider = "azr"
      workspace      = "dev-azr-inc-mgmt",
      db_workspace   = "dev-azr-inc-mgmt-db",
    }
  ]

  k8s_access_config = [
    {
      workspace     = "dev-apse1-acc",
      cluster_type  = "account",
      k8s_workspace = "dev-apse1-acc-k8s",
    },
    {
      workspace     = "dev-apse1-internal",
      cluster_type  = "internal",
      k8s_workspace = "dev-apse1-internal-telemetry",
    },
    {
      workspace      = "dev-apse1-eks-a",
      cluster_type   = "tenant",
      k8s_workspace  = "dev-apse1-eks-a-kubernetes",
      cloud_provider = "aws",
    },
    {
      workspace      = "dev-gcp-asse1-gke-a",
      cluster_type   = "tenant",
      k8s_workspace  = "dev-gcp-asse1-gke-a-k8s",
      cloud_provider = "gcp",
    },
    {
      workspace      = "dev-azr-inc-aks-a",
      cluster_type   = "tenant",
      k8s_workspace  = "dev-azr-inc-aks-a-k8s",
      cloud_provider = "azr",
    },
    {
      workspace      = "dev-apse1-mgmt",
      cluster_type   = "mgmt",
      k8s_workspace  = "dev-apse1-mgmt-k8s",
      cloud_provider = "aws",
    },
    {
      workspace      = "dev-gcp-asse1-mgmt",
      cluster_type   = "mgmt",
      k8s_workspace  = "dev-gcp-asse1-mgmt-k8s",
      cloud_provider = "gcp",
    },
    {
      workspace      = "dev-azr-inc-mgmt",
      cluster_type   = "mgmt",
      k8s_workspace  = "dev-azr-inc-mgmt-k8s",
      cloud_provider = "azr",
    },
    {
      workspace      = "dev-aws-apso1-mgmt",
      cluster_type   = "mgmt_central",
      mgmt_workspace = "dev-aws-apso1-basic-cluster",
      k8s_workspace  = "dev-aws-apso1-basic-cluster-k8s",
      cloud_provider = "aws",
    },
  ]

  internal_cluster_prefix = "dev-apse1"

  admin_service_url = "https://admin.dev.risingwave-cloud.xyz/swagger/"

  enable_cloudwatch_data_source = true

  gcm_data_source = {
    enable     = true,
    project_id = local.gke_project_id
  }

  tags = {
    Stack       = local.stack_tag
    VersionTag  = local.version_tag
    Project     = local.project
    Environment = local.environment
  }

  pagerduty_cloud_key  = var.pagerduty_cloud_key
  pagerduty_kernel_key = var.pagerduty_kernel_key
}
