locals {
  dashboard_folder_annotation = "grafana_folder"
  dashboard_label             = "grafana_dashboard"

  // region to datasource map,
  // e.g. {
  //   "ap-southeast-1": "dev-apse1-mgmt-msdb",
  //   "asia-southeast1": "dev-gcp-asse1-mgmt-msdb",
  //   "centralindia": "dev-azr-inc-mgmt-msdb"
  // }
  region_db_map = {
    for i, msdb_config in local.msdb_config : msdb_config.region => msdb_config.name
  }

  jsonnetSrc = [
    { name : "main", input = jsonencode({
      account = {
        workspace : var.account_cluster_workspaces[0],
        k8sDashboard = try("https://${local.k8s_dashboard[var.account_cluster_workspaces[0]].domain_name}/k8s/", "")
      },
      internal = {
        workspace : trimsuffix(var.remote_internal_cluster_workspace, "-cluster"),
        k8sDashboard = try("https://${local.k8s_dashboard[trimsuffix(var.remote_internal_cluster_workspace, "-cluster")].domain_name}/k8s/", "")
      },
      mgmt = {
        for workspace in var.mgmt_cluster_workspaces : try(local.central_basic_workspace_to_mgmt_workspace[workspace], workspace) => {
          workspace : try(local.central_basic_workspace_to_mgmt_workspace[workspace], workspace),
          k8sDashboard = try("https://${local.k8s_dashboard[try(local.central_basic_workspace_to_mgmt_workspace[workspace], workspace)].domain_name}/k8s/", "")
        }
      },
      tenant = {
        for workspace in var.tenant_cluster_workspaces : workspace => {
          workspace : workspace,
          k8sDashboard = try("https://${local.k8s_dashboard[workspace].domain_name}/k8s/", "")
        }
      },
      admin = {
        url = var.admin_service_url
      },
    }) },
    { name : "tenant", input : jsonencode({
      rootDomainName : var.root_domain_name
      adminServiceUrl : var.admin_service_url
    }) },
    { name : "tenants", input : "" },
    { name : "org", input : "" },
    { name : "orgs", input : "" },
    { name : "user", input : "" },
    { name : "users", input : "" },
    { name : "workflow", input : "" },
    { name : "workflows", input : "" },
    { name : "privatelinks", input : "" },
    { name : "cluster", input : "" },
    { name : "clusters", input : "" },

    { name : "rw_byoc_log_aws", input : "" },
    { name : "k8s_byoc_log_aws", input : "" },
    { name : "rw_byoc_log_gcp", input : "" },
    { name : "k8s_byoc_log_gcp", input : "" },
    { name : "rw_byoc_log_azr", input : "" },
    { name : "k8s_byoc_log_azr", input : "" },

    { name : "k8s_access", input : jsonencode({
      rootDomainName : var.root_domain_name
      workspaces : [for config in var.k8s_access_config : config.workspace]
    }) },
  ]
}

data "jsonnet_file" "admin-dashboard" {
  for_each = {
    for src in local.jsonnetSrc : src.name => src
  }
  source = "${path.module}/../../resources/jsonnet/dashboard/${each.key}.jsonnet"
  tla_code = each.value.input != "" ? {
    input = each.value.input
  } : {}
}

locals {
  dashboards = concat(
    [for src in local.jsonnetSrc : {
      name    = "admin-${replace(src.name, "_", "-")}"
      folder  = "Cloud Admin Dashboard"
      content = data.jsonnet_file.admin-dashboard[src.name].rendered
    }],
  )
}

resource "kubernetes_config_map" "dashboards" {
  for_each = { for index, dashboard in local.dashboards : dashboard.name => dashboard }

  metadata {
    name      = each.value.name
    namespace = var.namespace
    labels = {
      // sidecar will look for ConfigMap with `grafana_dashboard` label and mount them as dashboards
      // reference: ../../resources/dashboard/admin_tenants.json#sidecar
      "${local.dashboard_label}" = "1"
    }
    annotations = {
      // sidecar will put the dashboard.json under the folder defined in `grafana_folder` annotation
      "${local.dashboard_folder_annotation}" = each.value.folder
    }
  }

  data = {
    "${each.value.name}.json" = each.value.content
  }
}
