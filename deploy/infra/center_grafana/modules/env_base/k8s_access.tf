locals {
  central_basic_workspace_to_mgmt_workspace = {
    for config in var.k8s_access_config : config.mgmt_workspace => config.workspace if config.cluster_type == "mgmt_central"
  }
  k8s_access_namespace = "kubernetes-access"
  k8s_dashboard = merge(
    {
      for config in var.k8s_access_config : config.workspace => {
        workspace : config.workspace,
        k8s_workspace : config.k8s_workspace,
        token : data.tfe_outputs.remote_acc_k8s_output[config.k8s_workspace].values.account_k8s.k8sdashboard_pomerium_token,
        domain_name : "k8s.${config.workspace}.${var.root_domain_name}",
        gateway_dns : data.tfe_outputs.remote_acc_output[config.workspace].values.account_service.gateway_dns
      } if config.cluster_type == "account"
    },
    {
      for config in var.k8s_access_config : config.workspace => {
        workspace : config.workspace,
        k8s_workspace : config.k8s_workspace,
        token : data.tfe_outputs.remote_k8s_state.values.cluster_k8s_info.k8sdashboard_pomerium_token,
        domain_name : "k8s.${config.workspace}.${var.root_domain_name}",
        gateway_dns : data.tfe_outputs.remote_eks_state.values.cluster_info.gateway_dns
      } if config.cluster_type == "internal"
    },
    {
      for config in var.k8s_access_config : config.workspace => {
        workspace : config.workspace,
        k8s_workspace : config.k8s_workspace,
        token : data.tfe_outputs.remote_tenant_k8s_output[config.k8s_workspace].values.tenant_k8s.k8sdashboard_pomerium_token,
        domain_name : "k8s.${config.workspace}.${var.root_domain_name}",
        gateway_dns : data.tfe_outputs.remote_tenant_output[config.workspace].values.tenant_cluster.gateway_dns
      } if config.cluster_type == "tenant" && config.cloud_provider == "aws"
    },
    {
      for config in var.k8s_access_config : config.workspace => {
        workspace : config.workspace,
        k8s_workspace : config.k8s_workspace,
        token : data.tfe_outputs.remote_tenant_k8s_output[config.k8s_workspace].values.tenant_cluster_k8s.k8sdashboard_pomerium_token,
        domain_name : "k8s.${config.workspace}.${var.root_domain_name}",
        gateway_dns : data.tfe_outputs.remote_tenant_output[config.workspace].values.tenant_cluster.gateway_dns
      } if config.cluster_type == "tenant" && config.cloud_provider == "gcp"
    },
    {
      for config in var.k8s_access_config : config.workspace => {
        workspace : config.workspace,
        k8s_workspace : config.k8s_workspace,
        token : data.tfe_outputs.remote_tenant_k8s_output[config.k8s_workspace].values.tenant_cluster_k8s.k8sdashboard_pomerium_token,
        domain_name : "k8s.${config.workspace}.${var.root_domain_name}",
        gateway_dns : data.tfe_outputs.remote_tenant_output[config.workspace].values.tenant_cluster.gateway_dns
      } if config.cluster_type == "tenant" && config.cloud_provider == "azr"
    },
    {
      for config in var.k8s_access_config : config.workspace => {
        workspace : config.workspace,
        k8s_workspace : config.k8s_workspace,
        token : data.tfe_outputs.remote_tenant_k8s_output[config.k8s_workspace].values.tenant_cluster_k8s.k8sdashboard_pomerium_token,
        domain_name : "k8s.${config.workspace}.${var.root_domain_name}",
        gateway_dns : data.tfe_outputs.remote_tenant_output[config.workspace].values.tenant_cluster.gateway_dns
      } if config.cluster_type == "tenant" && config.cloud_provider == "azr"
    },
    {
      for config in var.k8s_access_config : config.workspace => {
        workspace : config.workspace,
        k8s_workspace : config.k8s_workspace,
        token : data.tfe_outputs.remote_mgmt_k8s_output[config.k8s_workspace].values.mgmt_k8s.k8sdashboard_pomerium_token,
        domain_name : "k8s.${config.workspace}.${var.root_domain_name}",
        gateway_dns : data.tfe_outputs.remote_mgmt_output[config.workspace].values.mgmt.gateway_dns
      } if config.cluster_type == "mgmt"
    },
    {
      for config in var.k8s_access_config : config.workspace => {
        workspace : config.workspace,
        k8s_workspace : config.k8s_workspace,
        token : data.tfe_outputs.remote_mgmt_k8s_output[config.k8s_workspace].values.basic_cluster_k8s.k8sdashboard_pomerium_token,
        domain_name : "k8s.${config.workspace}.${var.root_domain_name}",
        gateway_dns : data.tfe_outputs.remote_mgmt_output[config.mgmt_workspace].values.basic_cluster.gateway_dns
      } if config.cluster_type == "mgmt_central"
    }
  )
  k8s_apiserver = merge(
    {
      for config in var.k8s_access_config : config.workspace => {
        workspace : config.workspace,
        endpoint_type : "domain",
        endpoint : trimprefix(lower(nonsensitive(data.tfe_outputs.remote_acc_output[config.workspace].values.account_service.cluster_endpoint)), "https://")
      } if config.cluster_type == "account"
    },
    {
      for config in var.k8s_access_config : config.workspace => {
        workspace : config.workspace,
        endpoint_type : "domain",
        endpoint : trimprefix(lower(nonsensitive(local.cluster_endpoint)), "https://")
      } if config.cluster_type == "internal"
    },
    {
      for config in var.k8s_access_config : config.workspace => {
        workspace : config.workspace,
        endpoint_type : "domain",
        endpoint : trimprefix(lower(nonsensitive(data.tfe_outputs.remote_tenant_output[config.workspace].values.tenant_cluster.cluster_endpoint)), "https://")
      } if config.cluster_type == "tenant" && config.cloud_provider == "aws"
    },
    {
      for config in var.k8s_access_config : config.workspace => {
        workspace : config.workspace,
        endpoint_type : "ip",
        endpoint : nonsensitive(data.tfe_outputs.remote_tenant_output[config.workspace].values.tenant_cluster.cluster_endpoint)
      } if config.cluster_type == "tenant" && config.cloud_provider == "gcp"
    },
    {
      for config in var.k8s_access_config : config.workspace => {
        workspace : config.workspace,
        endpoint_type : "domain",
        endpoint : nonsensitive(data.tfe_outputs.remote_tenant_output[config.workspace].values.tenant_cluster.cluster_endpoint)
      } if config.cluster_type == "tenant" && config.cloud_provider == "azr"
    },
    {
      for config in var.k8s_access_config : config.workspace => {
        workspace : config.workspace,
        endpoint_type : "domain",
        endpoint : trimprefix(lower(nonsensitive(data.tfe_outputs.remote_mgmt_output[config.workspace].values.mgmt.cluster_endpoint)), "https://")
      } if config.cluster_type == "mgmt" && config.cloud_provider == "aws"
    },
    {
      for config in var.k8s_access_config : config.workspace => {
        workspace : config.workspace,
        endpoint_type : "ip",
        endpoint : nonsensitive(data.tfe_outputs.remote_mgmt_output[config.workspace].values.mgmt.cluster_endpoint)
      } if config.cluster_type == "mgmt" && config.cloud_provider == "gcp"
    },
    {
      for config in var.k8s_access_config : config.workspace => {
        workspace : config.workspace,
        endpoint_type : "domain",
        endpoint : nonsensitive(data.tfe_outputs.remote_mgmt_output[config.workspace].values.mgmt.cluster_endpoint)
      } if config.cluster_type == "mgmt" && config.cloud_provider == "azr"
    },
    {
      for config in var.k8s_access_config : config.workspace => {
        workspace : config.workspace,
        endpoint_type : "domain",
        endpoint : trimprefix(lower(nonsensitive(data.tfe_outputs.remote_mgmt_output[config.mgmt_workspace].values.basic_cluster.cluster_endpoint)), "https://")
      } if config.cluster_type == "mgmt_central" && config.cloud_provider == "aws"
    },
  )
}

resource "kubernetes_namespace" "kubernetes_dashboard" {
  metadata {
    name = local.k8s_access_namespace
  }
}

# dummy service account, for kubernetes_dashboard_token's "kubernetes.io/service-account.name" annotation
resource "kubernetes_service_account" "kubernetes_dashboard_empty" {
  depends_on = [kubernetes_namespace.kubernetes_dashboard]

  metadata {
    name      = "sa-empty"
    namespace = local.k8s_access_namespace
  }
}

resource "kubernetes_secret" "kubernetes_dashboard_token" {
  depends_on = [kubernetes_namespace.kubernetes_dashboard]
  for_each   = local.k8s_dashboard

  type = "kubernetes.io/service-account-token"
  metadata {
    name      = "${each.key}-secret"
    namespace = local.k8s_access_namespace
    annotations = {
      "kubernetes.io/service-account.name" : kubernetes_service_account.kubernetes_dashboard_empty.metadata[0].name
    }
  }
  binary_data = {
    token : base64encode(each.value.token)
  }
}

resource "kubernetes_service" "kubernetes_dashboard" {
  depends_on = [kubernetes_namespace.kubernetes_dashboard]
  for_each   = local.k8s_dashboard
  metadata {
    name      = "${each.key}-external"
    namespace = local.k8s_access_namespace
  }
  spec {
    type          = "ExternalName"
    external_name = each.value.gateway_dns
    port {
      protocol = "TCP"
      name     = "https"
      port     = 443
    }
  }
}

resource "kubernetes_service" "kubernetes_apiserver" {
  depends_on = [kubernetes_namespace.kubernetes_dashboard]
  for_each   = local.k8s_apiserver

  metadata {
    name      = "${each.key}-apiserver"
    namespace = local.k8s_access_namespace
  }
  spec {
    // ExternalName for domain endpoint, headless service for ip endpoint
    type          = each.value.endpoint_type == "domain" ? "ExternalName" : "ClusterIP"
    external_name = each.value.endpoint_type == "domain" ? each.value.endpoint : null
    cluster_ip    = each.value.endpoint_type == "ip" ? "None" : null
    port {
      protocol = "TCP"
      port     = 443
    }
  }
}

resource "kubernetes_endpoints_v1" "kubernetes_apiserver" {
  depends_on = [kubernetes_service.kubernetes_apiserver]
  for_each   = { for name, config in local.k8s_apiserver : name => config if config.endpoint_type == "ip" }

  metadata {
    name      = "${each.key}-apiserver"
    namespace = local.k8s_access_namespace
  }

  subset {
    address {
      ip = each.value.endpoint
    }
    port {
      port = 443
    }
  }
}

locals {
  k8s_dashboard_mtls_cert_name   = "kubernetes-dashboard-cert"
  k8s_dashboard_mtls_secret_name = "kubernetes-dashboard-mtls-secret"
  k8s_dashboard_mtls_dns_name    = "k8s.dashboard.${var.root_domain_name}"
}

resource "kubectl_manifest" "kubernetes_dashboard_certificate" {
  depends_on = [kubernetes_namespace.kubernetes_dashboard]
  yaml_body = templatefile(
    "${path.module}/../../resources/certificate.yaml",
    {
      "NAME"                = local.k8s_dashboard_mtls_cert_name
      "NAMESPACE"           = local.k8s_access_namespace
      "DNS_NAME"            = local.k8s_dashboard_mtls_dns_name
      "PRIVATE_ISSUER_NAME" = local.internal_cluster_private_issuer_name
      "SECRET_NAME"         = local.k8s_dashboard_mtls_secret_name
    }
  )
}

resource "kubectl_manifest" "kubernetes_dashboard_pomerium" {
  depends_on = [
    kubernetes_namespace.kubernetes_dashboard,
    kubectl_manifest.kubernetes_dashboard_certificate
  ]
  for_each = local.k8s_dashboard

  yaml_body = templatefile("${path.module}/../../resources/k8s_dashboard/pomerium.yaml", {
    WORKSPACE : each.key,
    NAMESPACE : local.k8s_access_namespace,
    PUBLIC_ISSUER_NAME : local.cluster_issuer_name,
    MTLS_SECRET_NAME : local.k8s_dashboard_mtls_secret_name,
    SERVICE_ACCOUNT_SECRET_NAME : kubernetes_secret.kubernetes_dashboard_token[each.key].metadata[0].name,
    SERVICE_NAME : kubernetes_service.kubernetes_dashboard[each.key].metadata[0].name,
    DOMAIN_NAME : each.value.domain_name,
  })
}

resource "kubectl_manifest" "kubernetes_apiserver_pomerium" {
  depends_on = [
    kubernetes_namespace.kubernetes_dashboard,
  ]
  for_each = local.k8s_apiserver

  yaml_body = templatefile("${path.module}/../../resources/k8s_apiserver/pomerium.yaml", {
    WORKSPACE : each.key,
    NAMESPACE : local.k8s_access_namespace,
    PUBLIC_ISSUER_NAME : local.cluster_issuer_name,
    SERVICE_ACCOUNT_SECRET_NAME : kubernetes_secret.kubernetes_dashboard_token[each.key].metadata[0].name,
    SERVICE_NAME : kubernetes_service.kubernetes_apiserver[each.key].metadata[0].name,
    DOMAIN_NAME : "k8s-api.${each.key}.${var.root_domain_name}",
  })
}
