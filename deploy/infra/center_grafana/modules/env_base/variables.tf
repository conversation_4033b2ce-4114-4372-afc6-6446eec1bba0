/** Base Vars **/
variable "region" {
  description = "Region code of this cloud environment. Please refer to https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/using-regions-availability-zones.html#local-zones-available"
  type        = string
}

variable "remote_workspace" {
  description = "The name of the remote workspace this module will be applied to"
  type        = string
}

variable "internal_cluster_prefix" {
  description = "The name of the internal cluster"
  type        = string
}

variable "assume_role_arn" {
  description = "ARN of the deply role AWS provider runs as."
  type        = string
}

variable "tf_cloud_organization" {
  description = "Name of Terraform Cloud Organization"
  type        = string
}

variable "config_center_init_workspace" {
  description = "Config center init remote workspace name. "
  type        = string
}

variable "tags" {
  description = "Tags to be applied to the this set of managed resources."
  type        = map(string)
  default     = {}
}

variable "env" {
  type = string

  validation {
    condition     = contains(["dev", "test", "rls", "prod"], var.env)
    error_message = "Allowed values for input_parameter are \"dev\", \"test\", \"rls\", or \"prod\"."
  }
}

variable "root_domain_name" {
  description = "The root domain name of the account service"
  type        = string
}

variable "config_center_workspace" {
  description = "Config center remote workspace name."
  type        = string
}

variable "remote_internal_cluster_workspace" {
  description = "Name of the remote internal cluster workspace."
  type        = string
}

variable "remote_k8s_workspace" {
  description = "The name of the remote workspace of the K8S resources in internal service cluster"
  type        = string
}

/** Grafana Vars **/
variable "namespace" {
  description = "The namespace of the grafana."
  type        = string
  default     = "grafana"
}

variable "tenant_cluster_workspaces" {
  type    = list(string)
  default = []
}

variable "tenant_k8s_workspaces" {
  type    = list(string)
  default = []
}

variable "mgmt_cluster_workspaces" {
  type    = list(string)
  default = []
}

variable "mgmt_k8s_workspaces" {
  type    = list(string)
  default = []
}

variable "mgmt_db_workspaces" {
  type    = list(string)
  default = []
}

variable "account_cluster_workspaces" {
  type    = list(string)
  default = []
}

variable "account_k8s_workspaces" {
  type    = list(string)
  default = []
}

variable "account_db_workspaces" {
  type    = list(string)
  default = []
}

variable "internal_cluster_workspaces" {
  type    = list(string)
  default = []
}

variable "asdb_config" {
  type = object({
    enable       = bool
    name         = optional(string, ""),
    port         = optional(number, 0),
    workspace    = optional(string, "")
    db_workspace = optional(string, "")
  })
  default = {
    enable = true
  }
}

variable "bsdb_config" {
  type = object({
    enable       = bool
    name         = optional(string, ""),
    port         = optional(number, 0),
    workspace    = optional(string, "")
    db_workspace = optional(string, "")
  })
  default = {
    enable = true
  }
}

variable "msdb_config" {
  type = list(object({
    name           = optional(string, ""),
    port           = optional(number, 0),
    cloud_provider = optional(string, ""),
    workspace      = optional(string, ""),
    db_workspace   = optional(string, "")
  }))
  default = []
}

variable "k8s_access_config" {
  type = list(object({
    workspace      = optional(string, ""),
    mgmt_workspace = optional(string, ""),
    k8s_workspace  = optional(string, ""),
    cluster_type   = optional(string, ""),
    cloud_provider = optional(string, ""),
  }))
  default = []
}

// AWS SES vars
variable "ses_host" {
  type = string
}

variable "ses_password" {
  type      = string
  sensitive = true
}

variable "ses_user" {
  type      = string
  sensitive = true
}

variable "alert_slack_webhook" {
  type      = string
  sensitive = true
}

// user group vars

## 76a00e5c-cb2e-4418-8829-89f94b83c4a1  -> Engineering Group
## 94d46317-111a-46af-a4b0-eab6b32e3f52  -> GrafanaAdmin Group
## More information can view here: https://portal.azure.com/#view/Microsoft_AAD_IAM/GroupsManagementMenuBlade/~/AllGroups
variable "admin_group_id" {
  type    = string
  default = "94d46317-111a-46af-a4b0-eab6b32e3f52"
}

variable "editor_group_id" {
  type    = string
  default = "76a00e5c-cb2e-4418-8829-89f94b83c4a1"
}

variable "network_remote_workspace" {
  description = "The name of the remote workspace of the regional network"
  type        = string
}

// postgreSQL part
variable "pg_major_version" {
  description = "PostgreSQL major version of the database"
  type        = string
}

// admin service part
variable "admin_service_url" {
  description = "Admin service url"
  type        = string
}

variable "admin_service_url_raw" {
  description = "Admin service url not protected by IAP"
  type        = string
}

variable "enable_cloudwatch_data_source" {
  type    = bool
  default = false
}

variable "enable_gcm_data_source" {
  type    = bool
  default = false
}

variable "gcm_data_source" {
  type = object({
    enable     = bool,
    project_id = optional(string, ""),
    region     = optional(string, ""),
  })
  default = {
    enable = false
  }
}

variable "alert_manager_fullname" {
  description = "The fullname of the alert-manager."
  type        = string
  default     = "risingwave-alert-manager"
}

variable "alert_manager_namespace" {
  description = "The namespace of the alert-manager."
  type        = string
  default     = "alert-manager"
}

variable "pagerduty_cloud_key" {
  type      = string
  sensitive = true
  default   = ""
}

variable "pagerduty_kernel_key" {
  type      = string
  sensitive = true
  default   = ""
}

variable "byoc_gcp_control_plane_project_id" {
  type = string
}

variable "byoc_gcp_telemetry_sa" {
  type = string
}

variable "byoc_azr_control_plane_tenant_id" {
  type = string
}

variable "byoc_azr_telemetry_app_id" {
  type = string
}

variable "byoc_regions" {
  type = list(string)
}
