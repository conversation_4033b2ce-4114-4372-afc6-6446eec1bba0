locals {
  tf_cloud_organization = "risingwave-cloud"

  env             = "prod"
  region          = "us-east-2"
  assume_role_arn = "arn:aws:iam::600598779918:role/deploy"

  remote_eks_workspace                = "prod-useast2-internal-cluster"
  remote_config_center_workspace      = "prod-useast2-config-center"
  remote_config_center_init_workspace = "prod-useast2-config-center-init"
  remote_k8s_workspace                = "prod-useast2-internal-k8s"
  network_remote_workspace            = "prod-useast2-internal-network"
  remote_workspace                    = "prod-useast2-center-grafana"

  pg_major_version = "14"

  root_domain_name = "risingwave.cloud"

  byoc_gcp_control_plane_project_id = "rwcprod"
  byoc_gcp_telemetry_sa             = "<EMAIL>"

  byoc_azr_telemetry_app_id        = "e24f8099-85da-4594-9611-6bc06aaacd69"
  byoc_azr_control_plane_tenant_id = "3dd90ad9-5a69-47e7-9cbf-0e29a5824574"

  admin_service_url_raw = "https://prod-admin-service-access.risingwave.cloud/api/v1"

  project     = "risingwave-cloud"
  environment = "prod"
  stack_tag   = "prod-useast2-center-grafana"
  version_tag = "20221207"
}

module "main" {
  source                            = "../../modules/env_base"
  tf_cloud_organization             = local.tf_cloud_organization
  env                               = local.env
  region                            = local.region
  assume_role_arn                   = local.assume_role_arn
  remote_internal_cluster_workspace = local.remote_eks_workspace
  config_center_init_workspace      = local.remote_config_center_init_workspace
  config_center_workspace           = local.remote_config_center_workspace
  remote_k8s_workspace              = local.remote_k8s_workspace
  network_remote_workspace          = local.network_remote_workspace
  remote_workspace                  = local.remote_workspace

  admin_service_url_raw = local.admin_service_url_raw

  pg_major_version = local.pg_major_version

  root_domain_name = local.root_domain_name

  ses_host            = var.ses_host
  ses_password        = var.ses_password
  ses_user            = var.ses_user
  alert_slack_webhook = var.alert_slack_webhook

  byoc_gcp_control_plane_project_id = local.byoc_gcp_control_plane_project_id
  byoc_gcp_telemetry_sa             = local.byoc_gcp_telemetry_sa

  byoc_azr_telemetry_app_id        = local.byoc_azr_telemetry_app_id
  byoc_azr_control_plane_tenant_id = local.byoc_azr_control_plane_tenant_id

  byoc_regions = [
    "us-east-1",
    "us-east-2",
    "us-west-2",
    "eu-west-1",
    "eu-west-2",
    "eu-north-1",
    "ap-southeast-1",
    "europe-west3",
    "europe-north1",
    "southeastasia"
  ]

  tenant_cluster_workspaces = [
    "canary-useast2-eks-a",
    "prod-aws-euwe2-eks-a",
    "prod-aws-euno1-eks-a",
    "prod-aws-usea1-eks-a",
    "prod-aws-uswe2-eks-a",
    "prod-aws-apse1-eks-a",
    "prod-aws-euwe1-eks-a",
    "prod-gcp-euwe3-gke-a",
  ]

  tenant_k8s_workspaces = [
    "canary-useast2-eks-a-kubernetes",
    "prod-aws-euwe2-eks-a-kubernetes",
    "prod-aws-euno1-eks-a-kubernetes",
    "prod-aws-usea1-eks-a-kubernetes",
    "prod-aws-uswe2-eks-a-kubernetes",
    "prod-aws-apse1-eks-a-kubernetes",
    "prod-aws-euwe1-eks-a-kubernetes",
    "prod-gcp-euwe3-gke-a-k8s",
  ]

  mgmt_cluster_workspaces = [
    "prod-aws-usea2-basic-cluster",
    "canary-useast2-mgmt",
    "prod-aws-euwe2-mgmt",
    "prod-aws-euno1-mgmt",
    "prod-aws-usea1-mgmt",
    "prod-aws-uswe2-mgmt",
    "prod-aws-apse1-mgmt",
    "prod-aws-euwe1-mgmt",
    "prod-gcp-euwe3-mgmt",
    "prod-gcp-euno1-mgmt",
    "prod-azr-asse-mgmt",
  ]

  mgmt_k8s_workspaces = [
    "prod-aws-usea2-basic-cluster-k8s",
    "canary-useast2-mgmt-k8s",
    "prod-aws-euwe2-mgmt-k8s",
    "prod-aws-euno1-mgmt-k8s",
    "prod-aws-usea1-mgmt-k8s",
    "prod-aws-uswe2-mgmt-k8s",
    "prod-aws-apse1-mgmt-k8s",
    "prod-aws-euwe1-mgmt-k8s",
    "prod-gcp-euwe3-mgmt-k8s",
    "prod-gcp-euno1-mgmt-k8s",
    "prod-azr-asse-mgmt-k8s",
  ]

  mgmt_db_workspaces = [
    "prod-gcp-euwe3-mgmt-db",
    "prod-gcp-euno1-mgmt-db",
    "prod-azr-asse-mgmt-db",
  ]

  account_cluster_workspaces = [
    "canary-useast2-acc"
  ]

  account_k8s_workspaces = [
    "canary-useast2-acc-k8s"
  ]

  account_db_workspaces = [
    "canary-useast2-acc-db"
  ]

  # port is defined in /internal_services_telemetry
  asdb_config = {
    enable       = true
    name         = "canary-useast2-acc-asdb",
    port         = 5432,
    workspace    = "canary-useast2-acc"
    db_workspace = "canary-useast2-acc-db"
  }

  bsdb_config = {
    enable       = true
    name         = "canary-useast2-acc-bsdb",
    port         = 5433,
    workspace    = "canary-useast2-acc"
    db_workspace = "canary-useast2-acc-db"
  }

  msdb_config = [
    // aws
    {
      name           = "canary-useast2-mgmt-msdb",
      port           = 6433,
      cloud_provider = "aws"
      workspace      = "canary-useast2-mgmt"
      db_workspace   = "canary-useast2-mgmt"
    },
    {
      name           = "prod-aws-euwe2-mgmt-msdb",
      port           = 6434,
      cloud_provider = "aws"
      workspace      = "prod-aws-euwe2-mgmt"
      db_workspace   = "prod-aws-euwe2-mgmt"
    },
    {
      name           = "prod-aws-euno1-mgmt-msdb",
      port           = 6435,
      cloud_provider = "aws"
      workspace      = "prod-aws-euno1-mgmt"
      db_workspace   = "prod-aws-euno1-mgmt"
    },
    {
      name           = "prod-aws-usea1-mgmt-msdb",
      port           = 6436,
      cloud_provider = "aws"
      workspace      = "prod-aws-usea1-mgmt"
      db_workspace   = "prod-aws-usea1-mgmt"
    },
    {
      name           = "prod-aws-uswe2-mgmt-msdb",
      port           = 6437,
      cloud_provider = "aws"
      workspace      = "prod-aws-uswe2-mgmt"
      db_workspace   = "prod-aws-uswe2-mgmt"
    },
    {
      name           = "prod-aws-apse1-mgmt-msdb",
      port           = 6438,
      cloud_provider = "aws"
      workspace      = "prod-aws-apse1-mgmt"
      db_workspace   = "prod-aws-apse1-mgmt"
    },
    {
      name           = "prod-aws-euwe1-mgmt-msdb",
      port           = 6432,
      cloud_provider = "aws"
      workspace      = "prod-aws-euwe1-mgmt"
      db_workspace   = "prod-aws-euwe1-mgmt"
    },
    // gcp
    {
      name           = "prod-gcp-euwe3-mgmt-msdb",
      port           = 6536,
      cloud_provider = "gcp"
      workspace      = "prod-gcp-euwe3-mgmt"
      db_workspace   = "prod-gcp-euwe3-mgmt-db"
    },
    {
      name           = "prod-gcp-euno1-mgmt-msdb",
      port           = 6532,
      cloud_provider = "gcp"
      workspace      = "prod-gcp-euno1-mgmt"
      db_workspace   = "prod-gcp-euno1-mgmt-db"
    },
    {
      name           = "prod-azr-asse-mgmt-msdb",
      port           = 6632,
      cloud_provider = "azr"
      workspace      = "prod-azr-asse-mgmt"
      db_workspace   = "prod-azr-asse-mgmt-db"
    },
  ]

  k8s_access_config = [
    {
      workspace     = "canary-useast2-acc",
      cluster_type  = "account",
      k8s_workspace = "canary-useast2-acc-k8s",
    },
    {
      workspace     = "prod-useast2-internal",
      cluster_type  = "internal",
      k8s_workspace = "prod-useast2-internal-telemetry",
    },
    {
      workspace      = "canary-useast2-eks-a",
      cluster_type   = "tenant",
      k8s_workspace  = "canary-useast2-eks-a-kubernetes",
      cloud_provider = "aws",
    },
    {
      workspace      = "prod-gcp-euwe3-gke-a",
      cluster_type   = "tenant",
      k8s_workspace  = "prod-gcp-euwe3-gke-a-k8s",
      cloud_provider = "gcp",
    },
    {
      workspace      = "prod-aws-euwe2-eks-a",
      cluster_type   = "tenant",
      k8s_workspace  = "prod-aws-euwe2-eks-a-kubernetes",
      cloud_provider = "aws",
    },
    {
      workspace      = "prod-aws-euno1-eks-a",
      cluster_type   = "tenant",
      k8s_workspace  = "prod-aws-euno1-eks-a-kubernetes",
      cloud_provider = "aws",
    },
    {
      workspace      = "prod-aws-usea1-eks-a",
      cluster_type   = "tenant",
      k8s_workspace  = "prod-aws-usea1-eks-a-kubernetes",
      cloud_provider = "aws",
    },
    {
      workspace      = "prod-aws-uswe2-eks-a",
      cluster_type   = "tenant",
      k8s_workspace  = "prod-aws-uswe2-eks-a-kubernetes",
      cloud_provider = "aws",
    },
    {
      workspace      = "prod-aws-apse1-eks-a",
      cluster_type   = "tenant",
      k8s_workspace  = "prod-aws-apse1-eks-a-kubernetes",
      cloud_provider = "aws",
    },
    {
      workspace      = "prod-aws-euwe1-eks-a",
      cluster_type   = "tenant",
      k8s_workspace  = "prod-aws-euwe1-eks-a-kubernetes",
      cloud_provider = "aws",
    },
    {
      workspace      = "canary-useast2-mgmt",
      cluster_type   = "mgmt",
      k8s_workspace  = "canary-useast2-mgmt-k8s",
      cloud_provider = "aws",
    },
    {
      workspace      = "prod-gcp-euwe3-mgmt",
      cluster_type   = "mgmt",
      k8s_workspace  = "prod-gcp-euwe3-mgmt-k8s",
      cloud_provider = "gcp",
    },
    {
      workspace      = "prod-gcp-euno1-mgmt",
      cluster_type   = "mgmt",
      k8s_workspace  = "prod-gcp-euno1-mgmt-k8s",
      cloud_provider = "gcp",
    },
    {
      workspace      = "prod-aws-euwe2-mgmt",
      cluster_type   = "mgmt",
      k8s_workspace  = "prod-aws-euwe2-mgmt-k8s",
      cloud_provider = "aws",
    },
    {
      workspace      = "prod-aws-euno1-mgmt",
      cluster_type   = "mgmt",
      k8s_workspace  = "prod-aws-euno1-mgmt-k8s",
      cloud_provider = "aws",
    },
    {
      workspace      = "prod-aws-usea1-mgmt",
      cluster_type   = "mgmt",
      k8s_workspace  = "prod-aws-usea1-mgmt-k8s",
      cloud_provider = "aws",
    },
    {
      workspace      = "prod-aws-uswe2-mgmt",
      cluster_type   = "mgmt",
      k8s_workspace  = "prod-aws-uswe2-mgmt-k8s",
      cloud_provider = "aws",
    },
    {
      workspace      = "prod-aws-apse1-mgmt",
      cluster_type   = "mgmt",
      k8s_workspace  = "prod-aws-apse1-mgmt-k8s",
      cloud_provider = "aws",
    },
    {
      workspace      = "prod-aws-euwe1-mgmt",
      cluster_type   = "mgmt",
      k8s_workspace  = "prod-aws-euwe1-mgmt-k8s",
      cloud_provider = "aws",
    },
    {
      workspace      = "prod-azr-asse-mgmt",
      cluster_type   = "mgmt",
      k8s_workspace  = "prod-azr-asse-mgmt-k8s",
      cloud_provider = "azr",
    },
    {
      workspace      = "prod-aws-usea2-mgmt",
      cluster_type   = "mgmt_central",
      mgmt_workspace = "prod-aws-usea2-basic-cluster",
      k8s_workspace  = "prod-aws-usea2-basic-cluster-k8s",
      cloud_provider = "aws",
    },
  ]

  internal_cluster_prefix = "prod-useast2"

  admin_service_url = "https://admin.prod.risingwave.cloud/swagger/"

  tags = {
    Stack       = local.stack_tag
    VersionTag  = local.version_tag
    Project     = local.project
    Environment = local.environment
  }

  pagerduty_cloud_key  = var.pagerduty_cloud_key
  pagerduty_kernel_key = var.pagerduty_kernel_key
}
