locals {
  tf_cloud_organization = "risingwave-cloud"

  env             = "test"
  region          = "us-east-2"
  assume_role_arn = "arn:aws:iam::023339134545:role/deploy"

  remote_eks_workspace                = "test-useast2-internal-cluster"
  remote_config_center_workspace      = "test-useast2-config-center"
  remote_config_center_init_workspace = "test-useast2-config-center-init"
  remote_k8s_workspace                = "test-useast2-internal-k8s"
  network_remote_workspace            = "test-useast2-internal-network"
  remote_workspace                    = "test-useast2-center-grafana"

  pg_major_version = "14"

  root_domain_name = "risingwave-cloud.xyz"

  byoc_gcp_control_plane_project_id = "rwctest"
  byoc_gcp_telemetry_sa             = "<EMAIL>"

  byoc_azr_telemetry_app_id        = "d10c5e69-846a-40eb-b40d-f93ef9c9faca"
  byoc_azr_control_plane_tenant_id = "3dd90ad9-5a69-47e7-9cbf-0e29a5824574"

  admin_service_url_raw = "https://test-admin-service-access.risingwave-cloud.xyz/api/v1"

  project     = "risingwave-cloud"
  environment = "test"
  stack_tag   = "test-useast2-center-grafana"
  version_tag = "20221207"
}

module "main" {
  source                            = "../../modules/env_base"
  tf_cloud_organization             = local.tf_cloud_organization
  env                               = local.env
  region                            = local.region
  assume_role_arn                   = local.assume_role_arn
  remote_internal_cluster_workspace = local.remote_eks_workspace
  config_center_init_workspace      = local.remote_config_center_init_workspace
  config_center_workspace           = local.remote_config_center_workspace
  remote_k8s_workspace              = local.remote_k8s_workspace
  network_remote_workspace          = local.network_remote_workspace
  remote_workspace                  = local.remote_workspace

  admin_service_url_raw = local.admin_service_url_raw

  pg_major_version = local.pg_major_version

  root_domain_name = local.root_domain_name

  ses_host            = var.ses_host
  ses_password        = var.ses_password
  ses_user            = var.ses_user
  alert_slack_webhook = var.alert_slack_webhook

  byoc_gcp_control_plane_project_id = local.byoc_gcp_control_plane_project_id
  byoc_gcp_telemetry_sa             = local.byoc_gcp_telemetry_sa

  byoc_azr_telemetry_app_id        = local.byoc_azr_telemetry_app_id
  byoc_azr_control_plane_tenant_id = local.byoc_azr_control_plane_tenant_id

  byoc_regions = []

  tenant_cluster_workspaces = [
    "test-useast1-eks-a",
    "test-gcp-usce1-gke-a",
    "test-azr-use2-aks-a",
  ]

  tenant_k8s_workspaces = [
    "test-useast1-eks-a-kubernetes",
    "test-gcp-usce1-gke-a-k8s",
    "test-azr-use2-aks-a-k8s",
  ]

  mgmt_cluster_workspaces = [
    "test-aws-apso1-basic-cluster",
    "test-useast1-mgmt",
    "test-gcp-usce1-mgmt",
    "test-azr-use2-mgmt",
  ]

  mgmt_k8s_workspaces = [
    "test-aws-apso1-basic-cluster-k8s",
    "test-useast1-mgmt-k8s",
    "test-gcp-usce1-mgmt-k8s",
    "test-azr-use2-mgmt-k8s",
  ]

  mgmt_db_workspaces = [
    "test-gcp-usce1-mgmt-db",
    "test-azr-use2-mgmt-db",
  ]

  account_cluster_workspaces = [
    "test-useast2-acc"
  ]

  account_k8s_workspaces = [
    "test-useast2-acc-k8s"
  ]

  account_db_workspaces = [
    "test-useast2-acc-db"
  ]

  # port is defined in /internal_services_telemetry
  asdb_config = {
    enable       = true
    name         = "test-useast2-acc-asdb",
    port         = 5432,
    workspace    = "test-useast2-acc"
    db_workspace = "test-useast2-acc-db"
  }

  bsdb_config = {
    enable       = true
    name         = "test-useast2-acc-bsdb",
    port         = 5433,
    workspace    = "test-useast2-acc"
    db_workspace = "test-useast2-acc-db"
  }

  msdb_config = [
    {
      name           = "test-useast1-mgmt-msdb",
      port           = 6432,
      cloud_provider = "aws"
      workspace      = "test-useast1-mgmt",
      db_workspace   = "test-useast1-mgmt",
    },
    {
      name           = "test-gcp-usce1-mgmt-msdb",
      port           = 6532,
      cloud_provider = "gcp"
      workspace      = "test-gcp-usce1-mgmt",
      db_workspace   = "test-gcp-usce1-mgmt-db",
    },
    {
      name           = "test-azr-use2-mgmt-msdb",
      port           = 6633,
      cloud_provider = "azr"
      workspace      = "test-azr-use2-mgmt",
      db_workspace   = "test-azr-use2-mgmt-db",
    },
  ]

  k8s_access_config = [
    {
      workspace     = "test-useast2-acc",
      cluster_type  = "account",
      k8s_workspace = "test-useast2-acc-k8s",
    },
    {
      workspace     = "test-useast2-internal",
      cluster_type  = "internal",
      k8s_workspace = "test-useast2-internal-telemetry",
    },
    {
      workspace      = "test-useast1-eks-a",
      cluster_type   = "tenant",
      k8s_workspace  = "test-useast1-eks-a-kubernetes",
      cloud_provider = "aws",
    },
    {
      workspace      = "test-gcp-usce1-gke-a",
      cluster_type   = "tenant",
      k8s_workspace  = "test-gcp-usce1-gke-a-k8s",
      cloud_provider = "gcp",
    },
    {
      workspace      = "test-azr-use2-aks-a",
      cluster_type   = "tenant",
      k8s_workspace  = "test-azr-use2-aks-a-k8s",
      cloud_provider = "azr",
    },
    {
      workspace      = "test-useast1-mgmt",
      cluster_type   = "mgmt",
      k8s_workspace  = "test-useast1-mgmt-k8s",
      cloud_provider = "aws",
    },
    {
      workspace      = "test-gcp-usce1-mgmt",
      cluster_type   = "mgmt",
      k8s_workspace  = "test-gcp-usce1-mgmt-k8s",
      cloud_provider = "gcp",
    },
    {
      workspace      = "test-azr-use2-mgmt",
      cluster_type   = "mgmt",
      k8s_workspace  = "test-azr-use2-mgmt-k8s",
      cloud_provider = "azr",
    },
    {
      workspace      = "test-aws-apso1-mgmt",
      cluster_type   = "mgmt_central",
      mgmt_workspace = "test-aws-apso1-basic-cluster",
      k8s_workspace  = "test-aws-apso1-basic-cluster-k8s",
      cloud_provider = "aws",
    },
  ]

  internal_cluster_prefix = "test-useast2"

  enable_cloudwatch_data_source = true

  admin_service_url = "https://admin.test.risingwave-cloud.xyz/swagger/"

  tags = {
    Stack       = local.stack_tag
    VersionTag  = local.version_tag
    Project     = local.project
    Environment = local.environment
  }
}
