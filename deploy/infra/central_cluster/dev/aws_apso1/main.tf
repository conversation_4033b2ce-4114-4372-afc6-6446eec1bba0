locals {
  tf_cloud_organization = "risingwave-cloud"

  env                            = "dev"
  region                         = "ap-south-1"
  remote_network_workspace       = "dev-aws-apso1-mgmt-network"
  remote_config_center_workspace = "dev-aws-apso1-config-center"
  remote_central_db_workspace    = "dev-aws-apso1-central-db"
  remote_eks_workspace           = "dev-aws-apso1-basic-cluster"
  remote_k8s_workspace           = "dev-aws-apso1-basic-cluster-k8s"
  assume_role_arn                = "arn:aws:iam::533267327614:role/deploy"
  cluster_domain_name            = "risingwave-cloud.xyz"

  pomerium_replica = 1

  project     = "risingwave-cloud"
  environment = "dev"
  stack_tag   = "dev-aws-apso1-internal"
  version_tag = "20221207"
}

module "main" {
  source = "../../modules/aws/env_base"

  env                            = local.env
  region                         = local.region
  remote_network_workspace       = local.remote_network_workspace
  remote_config_center_workspace = local.remote_config_center_workspace
  remote_central_db_workspace    = local.remote_central_db_workspace
  remote_eks_workspace           = local.remote_eks_workspace
  remote_k8s_workspace           = local.remote_k8s_workspace
  tf_cloud_organization          = local.tf_cloud_organization
  assume_role_arn                = local.assume_role_arn
  cluster_domain_name            = local.cluster_domain_name

  pomerium_replica  = local.pomerium_replica
  idp_tenant_id     = var.azure_ad_tenant_id
  idp_client_id     = var.azure_ad_client_id
  idp_client_secret = var.azure_ad_client_secret

  tags = {
    Stack       = local.stack_tag
    VersionTag  = local.version_tag
    Project     = local.project
    Environment = local.environment
  }
}
