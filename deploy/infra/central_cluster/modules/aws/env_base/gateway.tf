resource "kubernetes_namespace_v1" "gateway" {
  metadata {
    name = "gateway"
  }
}

module "gateway" {
  source = "../../../../shared_telemetry_modules/k8s/nginx_gateway"

  namespace             = kubernetes_namespace_v1.gateway.metadata.0.name
  vpc_cidr              = local.mgmt_vpc.vpc_cidr_block
  dns_name              = local.gateway_dns
  private_issuer_name   = local.private_issuer_name
  nginx_version         = module.shared_resource_versions.nginx
  nginx_sidecar_version = module.shared_resource_versions.nginx_sidecar

  ports = concat(
    [
      {
        port             = 443,
        external         = true,
        target_group_arn = nonsensitive(local.gateway_target_group_arn)
      },
    ],
    [
      for p in var.gateway_internal_ports : {
        port     = p
        external = false
      }
    ],
  )

  tags = var.tags
}

module "gateway_http" {
  source = "../../../../shared_telemetry_modules/k8s/nginx_gateway_route_default_http"

  namespace = kubernetes_namespace_v1.gateway.metadata.0.name

  telemetry_forwarding = {
    enable = false
    # TODO enable telemetry forwarding once workloads are set.
    # vm_write_url  = module.victoria_metrics.write_url
    # vm_read_url   = module.victoria_metrics.read_only_url
    # vm_alert_url  = module.victoria_metrics.alert_url
    # loki_url      = "http://${module.loki_scalable.internal_endpoint}"
    # pyroscope_url = local.pyroscope_enabled ? module.pyroscope[0].pyroscope_url : ""
  }
}
