module "iap" {
  source              = "../../iap"
  iap_dns             = "authenticate.internal.${var.env}.${var.cluster_domain_name}"
  iap_db_username     = local.admin_db_username
  iap_db_password     = local.admin_db_password
  iap_db_address      = local.admin_db_address
  cluster_issuer_name = local.cluster_issuer_name
  pomerium_version    = module.resource_versions.pomerium
  pomerium_replica    = var.pomerium_replica
  idp_tenant_id       = var.idp_tenant_id
  idp_client_id       = var.idp_client_id
  idp_client_secret   = var.idp_client_secret
}
