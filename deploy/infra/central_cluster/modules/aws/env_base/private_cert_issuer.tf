locals {
  private_issuer_approle_prefix = "private_issuer_${replace(local.cluster_name, "-", "_")}"
  private_issuer_name           = "management-svc-issuer"

}

module "private_issuer_approle" {
  source               = "../../../../shared_modules/vault/vault_approle_auth"
  secret_path          = "${module.vault_init.config_center_init.pki_intermediate_path}/*"
  resource_prefix      = local.private_issuer_approle_prefix
  capabilities         = ["create", "read", "update", "delete", "list"]
  approle_backend_path = module.vault_init.config_center_init.approle_auth_path
}

module "private_issuer" {
  source            = "../../../../shared_modules/k8s/private_cert_issuer"
  vault_url         = local.vault_url
  auth_path         = module.vault_init.config_center_init.approle_auth_path
  issuer_name       = local.private_issuer_name
  issuer_namespace  = "cert-manager"
  pki_path          = module.vault_init.config_center_init.pki_intermediate_path
  pki_role_name     = module.vault_init.config_center_init.pki_role_name
  approle_role_id   = module.private_issuer_approle.approle_role_id
  approle_secret_id = module.private_issuer_approle.approle_secret_id
}
