provider "aws" {
  region = var.region
  assume_role {
    role_arn = var.assume_role_arn
  }
}

provider "aws" {
  alias  = "vault"
  region = local.root_token_secret_region
  assume_role {
    role_arn = var.assume_role_arn
  }
}

data "aws_secretsmanager_secret_version" "vault_root_token" {
  secret_id     = local.root_token_secret_arn
  version_stage = "AWSCURRENT"
  provider      = aws.vault
}

locals {
  raw_root_token = jsondecode(data.aws_secretsmanager_secret_version.vault_root_token.secret_string)[local.root_token_secret_key]
  root_token     = jsondecode(base64decode(local.raw_root_token))["root_token"]
}
provider "vault" {
  address = local.vault_url
  token   = local.root_token
  # https://github.com/hashicorp/terraform-provider-vault/issues/922
  max_lease_ttl_seconds = 315360000 # 10 years
}

data "aws_eks_cluster_auth" "eks" {
  name = local.cluster_name
}

provider "kubernetes" {
  host                   = local.cluster_endpoint
  cluster_ca_certificate = base64decode(local.cluster_certificate_authority_data)
  token                  = data.aws_eks_cluster_auth.eks.token
}

provider "kubectl" {
  host                   = local.cluster_endpoint
  cluster_ca_certificate = base64decode(local.cluster_certificate_authority_data)
  load_config_file       = false
  token                  = data.aws_eks_cluster_auth.eks.token
}

locals {
  # non-default context name to protect from using wrong kubeconfig
  kubeconfig_context = "_terraform-kustomization-${local.cluster_name}_"

  kubeconfig = {
    apiVersion = "v1"
    clusters = [
      {
        name = local.kubeconfig_context
        cluster = {
          certificate-authority-data = local.cluster_certificate_authority_data
          server                     = local.cluster_endpoint
        }
      }
    ]
    users = [
      {
        name = local.kubeconfig_context
        user = {
          token = data.aws_eks_cluster_auth.eks.token
        }
      }
    ]
    contexts = [
      {
        name = local.kubeconfig_context
        context = {
          cluster = local.kubeconfig_context
          user    = local.kubeconfig_context
        }
      }
    ]
  }
}

provider "kustomization" {
  kubeconfig_raw = yamlencode(local.kubeconfig)
  context        = local.kubeconfig_context
}
