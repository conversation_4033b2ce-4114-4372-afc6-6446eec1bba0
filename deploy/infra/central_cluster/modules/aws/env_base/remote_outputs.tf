data "tfe_outputs" "remote_network_state" {
  organization = var.tf_cloud_organization
  workspace    = var.remote_network_workspace
}

locals {
  mgmt_vpc = data.tfe_outputs.remote_network_state.values.mgmt_vpc
}

data "tfe_outputs" "remote_config_center_state" {
  organization = var.tf_cloud_organization
  workspace    = var.remote_config_center_workspace
}

locals {
  vault_url                = data.tfe_outputs.remote_config_center_state.values.config_center.vault_url
  root_token_secret_region = data.tfe_outputs.remote_config_center_state.values.config_center.root_token_secret_region
  root_token_secret_arn    = data.tfe_outputs.remote_config_center_state.values.config_center.root_token_secret_arn
  root_token_secret_key    = data.tfe_outputs.remote_config_center_state.values.config_center.root_token_secret_key
}

data "tfe_outputs" "remote_central_db_state" {
  organization = var.tf_cloud_organization
  workspace    = var.remote_central_db_workspace
}

locals {
  admin_db_address  = data.tfe_outputs.remote_central_db_state.values.central_db.admin_db_address
  admin_db_username = data.tfe_outputs.remote_central_db_state.values.central_db.admin_db_username
  admin_db_password = data.tfe_outputs.remote_central_db_state.values.central_db.admin_db_password
}

data "tfe_outputs" "remote_k8s_state" {
  organization = var.tf_cloud_organization
  workspace    = var.remote_k8s_workspace
}

locals {
  cluster_issuer_name = data.tfe_outputs.remote_k8s_state.values.basic_cluster_k8s.cluster_issuer_name
}

data "tfe_outputs" "remote_eks_state" {
  organization = var.tf_cloud_organization
  workspace    = var.remote_eks_workspace
}

locals {
  cluster_endpoint                   = data.tfe_outputs.remote_eks_state.values.basic_cluster.cluster_endpoint
  cluster_certificate_authority_data = data.tfe_outputs.remote_eks_state.values.basic_cluster.cluster_certificate_authority_data
  cluster_name                       = data.tfe_outputs.remote_eks_state.values.basic_cluster.cluster_id
  eks_cluster_region                 = data.tfe_outputs.remote_eks_state.values.basic_cluster.cluster_region
  cluster_oidc_provider_arn          = data.tfe_outputs.remote_eks_state.values.basic_cluster.cluster_oidc_provider_arn
  gateway_dns                        = data.tfe_outputs.remote_eks_state.values.basic_cluster.gateway_dns
  gateway_target_group_arn           = data.tfe_outputs.remote_eks_state.values.basic_cluster.gateway_target_group_arn
}
