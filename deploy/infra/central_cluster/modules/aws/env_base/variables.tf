variable "tf_cloud_organization" {
  type        = string
  description = "Name of the tf cloud organization."
}

variable "env" {
  type = string

  validation {
    condition     = contains(["dev", "test", "rls", "canary", "prod"], var.env)
    error_message = "Allowed values for input_parameter are \"dev\", \"test\", \"rls\", \"canary\", or \"prod\"."
  }
}

variable "region" {
  description = "Region of the current cloud environment."
  type        = string
}

variable "remote_network_workspace" {
  type        = string
  description = "Name of the remote config center workspace."
}

variable "remote_config_center_workspace" {
  type        = string
  description = "Name of the remote config center workspace."
}

variable "remote_central_db_workspace" {
  type        = string
  description = "Name of the remote config center workspace."
}

variable "remote_k8s_workspace" {
  type = string
}

variable "remote_eks_workspace" {
  type = string
}

variable "assume_role_arn" {
  type        = string
  description = "The ARN of the AWS assume role for initiazlizing AWS provider."
}

variable "cluster_domain_name" {
  description = "Domain name of the cluster"
  type        = string
}

variable "pomerium_replica" {
  description = "replica of Pomeroum deployment"
  type        = number
}

variable "idp_tenant_id" {
  description = "tenant id of the Azure IDP app"
  type        = string
}

variable "idp_client_id" {
  description = "client id of the Azure IDP app"
  type        = string
}

variable "idp_client_secret" {
  description = "client secret of the Azure IDP app"
  type        = string
}

variable "gateway_internal_ports" {
  type        = list(number)
  description = "The internal ports for gateway to listen"
  default     = []
}

variable "tags" {
  description = "Tags to be applied to the this set of managed resources."
  type        = map(string)
  default     = {}
}
