terraform {
  required_providers {
    vault = {
      source  = "hashicorp/vault"
      version = "5.1.0"
    }
    aws = {
      source  = "hashicorp/aws"
      version = "6.7.0"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "2.38.0"
    }
    kubectl = {
      source  = "gavinbunney/kubectl"
      version = "1.19.0"
    }
    kustomization = {
      source  = "kbst/kustomization"
      version = "0.9.6"
    }
  }
}
