locals {
  iap_namespace  = "pomerium"
  iap_deployment = "pomerium"

  iap_dns_name            = var.iap_dns
  iap_cert_name           = "pomerium-proxy-tls"
  iap_storage_secret_name = "pomerium-storage"
  idp_secret_name         = "idp"
  iap_db_database_name    = "iap"

  cert_yaml_data = templatefile(
    "${path.module}/template/cert.yaml.tftpl",
    {
      "cluster_issuer_name" = var.cluster_issuer_name
      "iap_namespace"       = local.iap_namespace
      "iap_dns_name"        = local.iap_dns_name
      "iap_cert_name"       = local.iap_cert_name
    }
  )

  pomerium_settings_yaml_data = templatefile(
    "${path.module}/template/pomerium.yaml.tftpl",
    {
      "iap_namespace"           = local.iap_namespace
      "iap_dns_name"            = local.iap_dns_name
      "iap_cert_name"           = local.iap_cert_name
      "idp_secret_name"         = local.idp_secret_name
      "idp_tenant_id"           = var.idp_tenant_id
      "iap_storage_secret_name" = local.iap_storage_secret_name
    }
  )
}

resource "kubernetes_secret_v1" "idp_secrets" {
  metadata {
    name      = local.idp_secret_name
    namespace = local.iap_namespace
  }

  data = {
    client_id     = var.idp_client_id
    client_secret = var.idp_client_secret
  }

  type       = "Opaque"
  depends_on = [kustomization_resource.pomerium_crd]
}

resource "kubectl_manifest" "iap_certificate" {
  yaml_body  = local.cert_yaml_data
  depends_on = [kustomization_resource.pomerium_crd]
}

data "kustomization_overlay" "pomerium_crd" {
  resources = [
    "https://raw.githubusercontent.com/pomerium/ingress-controller/${var.pomerium_version}/deployment.yaml",
  ]

  patches {
    target {
      kind      = "Deployment"
      name      = "pomerium"
      namespace = "pomerium"
    }
    patch = <<-EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: pomerium
  namespace: pomerium
spec:
  replicas: ${var.pomerium_replica}
EOF
  }

  patches {
    target {
      kind      = "Service"
      name      = "pomerium-proxy"
      namespace = "pomerium"
    }
    patch = <<-EOF
apiVersion: v1
kind: Service
metadata:
  name: pomerium-proxy
  namespace: pomerium
  annotations:
    external-dns.alpha.kubernetes.io/hostname: '${local.iap_dns_name}'
    service.beta.kubernetes.io/aws-load-balancer-type: external
    service.beta.kubernetes.io/aws-load-balancer-nlb-target-type: ip
    service.beta.kubernetes.io/aws-load-balancer-scheme: internet-facing
EOF

  }

}

resource "kustomization_resource" "pomerium_crd" {
  for_each = data.kustomization_overlay.pomerium_crd.ids
  manifest = data.kustomization_overlay.pomerium_crd.manifests[each.value]
}

resource "kubernetes_secret_v1" "iap_storage" {
  metadata {
    name      = local.iap_storage_secret_name
    namespace = local.iap_namespace
  }

  data = {
    connection = "postgresql://${var.iap_db_username}:${var.iap_db_password}@${var.iap_db_address}/${local.iap_db_database_name}"
  }

  type = "Opaque"

  depends_on = [kustomization_resource.pomerium_crd]
}

resource "kubectl_manifest" "pomerium_settings" {
  yaml_body = local.pomerium_settings_yaml_data
  depends_on = [
    kubernetes_secret_v1.idp_secrets,
    kubernetes_secret_v1.iap_storage,
    kubectl_manifest.iap_certificate,
    kustomization_resource.pomerium_crd
  ]
}
