apiVersion: ingress.pomerium.io/v1
kind: Pomerium
metadata:
  name: global
spec:
  secrets: ${iap_namespace}/bootstrap
  authenticate:
    url: https://${iap_dns_name}
  identityProvider:
    provider: azure
    url: 'https://login.microsoftonline.com/${idp_tenant_id}/v2.0'
    secret: ${iap_namespace}/${idp_secret_name}
  certificates:
    - ${iap_namespace}/${iap_cert_name}
  storage:
    postgres:
      secret: ${iap_namespace}/${iap_storage_secret_name}
