variable "iap_dns" {
  type = string
}

variable "iap_db_username" {
  type = string
}

variable "iap_db_password" {
  type = string
}

variable "iap_db_address" {
  type = string
}

variable "cluster_issuer_name" {
  type = string
}

variable "pomerium_version" {
  type = string
}

variable "pomerium_replica" {
  description = "replica of Pomeroum deployment"
  type        = number
}

variable "idp_tenant_id" {
  description = "tenant id of the Azure IDP app"
  type        = string
}

variable "idp_client_id" {
  description = "client id of the Azure IDP app"
  type        = string
}

variable "idp_client_secret" {
  description = "client secret of the Azure IDP app"
  type        = string
}
