variable "env" {
  type = string

  validation {
    condition     = contains(["dev", "test", "rls", "prod"], var.env)
    error_message = "Allowed values for input_parameter are \"dev\", \"test\", \"rls\", \"canary\", or \"prod\"."
  }
}

// https://github.com/pomerium/pomerium/releases
output "pomerium" {
  value = lookup({ dev = "v0.29.4", test = "v0.29.4", rls = "v0.29.4", prod = "v0.29.4" }, var.env)
}
