# Example: how to enable a kv secret engine under path test/ and put some data.
# For more vault resources, see: https://registry.terraform.io/providers/hashicorp/vault/latest/docs/resources/kv_secret
# resource "vault_mount" "test" {
#   path        = "test"
#   type        = "kv"
#   options     = { version = "1" }
#   description = "Test kv secret engine mount"
# }

# resource "vault_kv_secret" "secret" {
#   path = "${vault_mount.test}/secret"
#   data_json = jsonencode(
#   {
#     zip = "zap",
#     foo = "bar"
#   }
#   )
# }

locals {
  pki_root_path         = "rwc_pki"
  pki_intermediate_path = "rwc_pki_int"
  approle_auth_path     = "rwc_approle"
  common_kv_path        = "rwc_kv_common"
  root_cert_ttl         = "315360000" # 10 years.
  intermediate_cert_ttl = "157680000" # 5 years.
  cert_ttl              = "31536000"  # 1 year.
  key_size              = 4096
  pki_common_name       = "rwc.internal.svc"
  pki_role_name         = "rwc_private_issuer_role"
}

# Enable AppRole Auth Method
resource "vault_auth_backend" "approle" {
  type = "approle"
  path = local.approle_auth_path
}

# Enable common kv secret engine
resource "vault_mount" "common_kv" {
  path        = local.common_kv_path
  type        = "kv-v2"
  options     = { version = "2" }
  description = "Common kv secret engine."
}

# Enable pki secret engine
# NOTE: do not update pki related resources in-place. destroy and create to update.
# TODO: move intermediate related resources to shared_modules.
# https://registry.terraform.io/providers/hashicorp/vault/latest/docs/resources/pki_secret_backend_intermediate_set_signed
resource "vault_mount" "pki_root" {
  path                      = local.pki_root_path
  type                      = "pki"
  default_lease_ttl_seconds = local.root_cert_ttl
  max_lease_ttl_seconds     = local.root_cert_ttl
  description               = "RisingWave pki secret engine root path"
}

resource "vault_mount" "pki_intermediate" {
  path                      = local.pki_intermediate_path
  type                      = "pki"
  description               = "RisingWave pki secret engine intermediate path"
  default_lease_ttl_seconds = local.intermediate_cert_ttl
  max_lease_ttl_seconds     = local.intermediate_cert_ttl
}

# Generate root cert.
# NOTE: We may need to handle root cert expiring manually. Here are some references.
# - https://registry.terraform.io/providers/hashicorp/vault/latest/docs/resources/pki_secret_backend_root_cert
# - https://developer.hashicorp.com/vault/tutorials/secrets-management/pki-engine#step-7-rotate-root-ca
# - https://www.reddit.com/r/devops/comments/elhtwj/what_to_do_about_vault_pki_ca_expiring/
resource "vault_pki_secret_backend_root_cert" "pki_root_cert" {
  backend              = vault_mount.pki_root.path
  type                 = "internal"
  common_name          = "Root CA"
  ttl                  = local.root_cert_ttl
  format               = "pem"
  private_key_format   = "der"
  key_type             = "rsa"
  key_bits             = local.key_size
  exclude_cn_from_sans = true
  organization         = "RisingWave Labs Internal"
}


# Create intermediate CA cert request
resource "vault_pki_secret_backend_intermediate_cert_request" "intermediate_cert_request" {
  backend     = vault_mount.pki_intermediate.path
  type        = "internal"
  common_name = local.pki_common_name
}

# Sign intermediate
resource "vault_pki_secret_backend_root_sign_intermediate" "sign_intermediate" {
  backend              = vault_mount.pki_root.path
  csr                  = vault_pki_secret_backend_intermediate_cert_request.intermediate_cert_request.csr
  common_name          = "RWC Intermediate CA"
  exclude_cn_from_sans = true
  organization         = "RisingWave Inc"
  ou                   = "RisingWave OU"
  ttl                  = local.intermediate_cert_ttl
}

# Set intermediate signed
resource "vault_pki_secret_backend_intermediate_set_signed" "set_signed" {
  backend     = vault_mount.pki_intermediate.path
  certificate = "${vault_pki_secret_backend_root_sign_intermediate.sign_intermediate.certificate}\n${vault_pki_secret_backend_root_cert.pki_root_cert.certificate}"
}

# Create a role
resource "vault_pki_secret_backend_role" "role" {
  backend       = vault_mount.pki_intermediate.path
  name          = local.pki_role_name
  ttl           = local.cert_ttl
  max_ttl       = local.cert_ttl
  allow_ip_sans = true
  key_type      = "rsa"
  key_bits      = local.key_size
  allowed_domains = [
    local.pki_common_name,   # for the side start TLS connection
    var.cluster_domain_name, # for public accessible URL
    "svc.cluster.local",     # for inter-cluster communication
  ]
  allow_subdomains = true
}

resource "vault_policy" "metrics_policy" {
  name   = "self_metrics_policy"
  policy = <<EOF
path "sys/metrics" {
  capabilities = ["read", "list"]
}
EOF
}

# TODO: https://linear.app/risingwave-labs/issue/CLOUD-1174/alert-when-some-tokens-are-about-to-expire
resource "vault_token" "metrics_token" {
  # NOTE: underscore is not allowed in display_name
  display_name = "self-metrics-token"
  policies     = [vault_policy.metrics_policy.name]
  ttl          = "87600h" # 10 years
}
