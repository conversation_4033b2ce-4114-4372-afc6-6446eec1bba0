module "config_center_resource_versions" {
  source = "../resource_versions"
  env    = var.env
}


module "vault" {
  source = "../vault_server"

  aws_region                = var.region
  eks_cluster_name          = local.eks_cluster_name
  eks_cluster_region        = local.eks_cluster_region
  workspace_name            = var.workspace_name
  cluster_oidc_provider_arn = local.cluster_oidc_provider_arn
  vpc                       = local.vpc
  zone_id                   = var.route53_zone_id
  cluster_domain_name       = var.cluster_domain_name
  lb_arn                    = local.gateway_lb_arn
  lb_dns                    = local.gateway_lb_dns
  lb_zone_id                = local.gateway_lb_zone_id

  vault_version = module.config_center_resource_versions.vault

  dynamodb_arn      = local.dynamodb_arn
  dynamodb_name     = local.dynamodb_name
  dynamodb_region   = local.dynamodb_region
  aws_secret_arn    = local.aws_secret_arn
  aws_secret_region = local.aws_secret_region
  aws_kms_key_id    = local.kms_key_id
  aws_kms_region    = local.kms_region
  aws_kms_arn       = local.kms_arn
  assume_role_arn   = var.assume_role_arn

  cluster_issuer_name = local.cluster_issuer_name
  vault_ha_replicas   = var.vault_ha_replicas
  # NOTE: use "is_restore = true" to restore the Vault server from existing DynamoDB without vault initialization.
  is_restore = var.is_restore

  enable_alerting = var.enable_alerting

  tags = var.tags

  providers = {
    aws.network = aws.network
  }

  legacy_dns = var.legacy_dns
}
