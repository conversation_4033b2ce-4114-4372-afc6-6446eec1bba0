data "tfe_outputs" "remote_eks_state" {
  organization = var.tf_cloud_organization
  workspace    = var.remote_eks_workspace
}

data "tfe_outputs" "remote_db_state" {
  organization = var.tf_cloud_organization
  workspace    = var.remote_db_workspace
}

data "tfe_outputs" "remote_state_network" {
  organization = var.tf_cloud_organization
  workspace    = var.remote_network_workspace
}

data "tfe_outputs" "remote_k8s_state" {
  organization = var.tf_cloud_organization
  workspace    = var.remote_k8s_workspace
}

locals {
  cluster_endpoint                   = data.tfe_outputs.remote_eks_state.values.basic_cluster.cluster_endpoint
  cluster_certificate_authority_data = data.tfe_outputs.remote_eks_state.values.basic_cluster.cluster_certificate_authority_data
  eks_cluster_name                   = data.tfe_outputs.remote_eks_state.values.basic_cluster.cluster_id
  eks_cluster_region                 = data.tfe_outputs.remote_eks_state.values.basic_cluster.cluster_region
  cluster_oidc_provider_arn          = data.tfe_outputs.remote_eks_state.values.basic_cluster.cluster_oidc_provider_arn
  gateway_lb_arn                     = data.tfe_outputs.remote_eks_state.values.basic_cluster.gateway_lb_arn
  gateway_lb_dns                     = data.tfe_outputs.remote_eks_state.values.basic_cluster.gateway_lb_dns
  gateway_lb_zone_id                 = data.tfe_outputs.remote_eks_state.values.basic_cluster.gateway_lb_zone_id

  cluster_issuer_name = data.tfe_outputs.remote_k8s_state.values.basic_cluster_k8s.cluster_issuer_name

  central_db = data.tfe_outputs.remote_db_state.values.central_db

  dynamodb_arn      = local.central_db.config_center_db_arn
  dynamodb_name     = local.central_db.config_center_db_name
  dynamodb_region   = local.central_db.config_center_db_region
  aws_secret_arn    = local.central_db.config_center_secret_arn
  aws_secret_region = local.central_db.config_center_secret_region
  kms_key_id        = local.central_db.config_center_kms_key_id
  kms_region        = local.central_db.config_center_kms_region
  kms_arn           = local.central_db.config_center_kms_arn

  vpc = data.tfe_outputs.remote_state_network.values.mgmt_vpc
}
