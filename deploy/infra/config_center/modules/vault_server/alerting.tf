module "alerting" {
  count = var.enable_alerting ? 1 : 0

  source = "../../../shared_telemetry_modules/alerting/aws/modules"

  region     = var.aws_region
  sns_prefix = "vault"

  nlb_alerting_specs = [
    {
      name = join("/", slice(split("/", var.lb_arn), 1, length(split("/", var.lb_arn))))
      target_group_names = [
        aws_lb_target_group.vault_lb_target_group.name
      ]
    }
  ]

  tags = var.tags
}
