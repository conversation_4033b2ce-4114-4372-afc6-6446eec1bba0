resource "aws_lb_listener" "vault_lb_listener" {
  load_balancer_arn = var.lb_arn
  port              = local.vault_service_port
  protocol          = "TCP"
  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.vault_lb_target_group.arn
  }

  tags = var.tags
}

resource "aws_lb_target_group" "vault_lb_target_group" {
  name        = "${var.workspace_name}-vault"
  port        = local.vault_service_port
  protocol    = "TCP"
  target_type = "ip"
  vpc_id      = var.vpc.vpc_id
  health_check {
    enabled  = true
    port     = local.vault_service_port
    protocol = "HTTPS"
    path     = "/v1/sys/health"
    matcher  = "200-299,429"
  }

  tags = var.tags
}

resource "aws_route53_record" "vault_dns_record" {
  zone_id = var.zone_id
  name    = local.vault_dns_name
  type    = "A"
  alias {
    name                   = var.lb_dns
    zone_id                = var.lb_zone_id
    evaluate_target_health = true
  }

  provider = aws.network
}

resource "aws_route53_record" "vault_dns_record_legacy" {
  zone_id = var.zone_id
  name    = var.legacy_dns
  type    = "A"
  alias {
    name                   = var.lb_dns
    zone_id                = var.lb_zone_id
    evaluate_target_health = true
  }

  provider = aws.network
}
