variable "workspace_name" {
  description = "Terraform workspace name"
  type        = string
}

variable "assume_role_arn" {
  description = "ARN of the deploy role AWS provider runs as."
  type        = string
}

variable "cluster_oidc_provider_arn" {
  description = "The ARN of the OIDC Provider in the cluster."
  type        = string
}

# DynamoDB information.
variable "dynamodb_arn" {
  description = "The ARN of the DynamoDB table. It is required by Vault helm chart."
  type        = string
}

variable "dynamodb_region" {
  description = "The Region of the DynamoDB table. It is required by Vault helm chart."
  type        = string
}

variable "dynamodb_name" {
  description = "The TableName of the DynamoDB table. It is required by Vault helm chart."
  type        = string
}

variable "aws_secret_arn" {
  description = "The ARN of the AWS secret manager secret. It is used for storing vault root token."
  type        = string
}

variable "aws_secret_region" {
  description = "The Region of the AWS secret manager secret. It is used for storing vault root token."
  type        = string
}

variable "aws_kms_region" {
  description = "The Region of the AWS KMS. It is used for auto unsealing."
  type        = string
}

variable "aws_kms_key_id" {
  description = "The Key ID of the AWS KMS. It is used for auto unsealing."
  type        = string
}

variable "aws_kms_arn" {
  description = "The ARN of the AWS KMS. It is used for auto unsealing."
  type        = string
}

# Vault Helm configuration.
variable "vault_chart_repo" {
  description = "The repo of the helm chart."
  type        = string
  default     = "https://helm.releases.hashicorp.com"
}

variable "vault_chart_name" {
  description = "The chart name of the helm chart."
  type        = string
  default     = "vault"
}

variable "vault_release_name" {
  description = "The release name of the helm chart."
  type        = string
  default     = "vault"
}

variable "vault_namespace" {
  description = "The namespace running vault under."
  type        = string
  default     = "vault"
}

variable "vault_version" {
  description = "The version of the vault chart."
  type        = string
  default     = "0.23.0"
}

variable "vault_ha_replicas" {
  description = "The number of vault server replicas. WARNING: If it is larger than number of k8s nodes, auto scaling will be triggered."
}

variable "tags" {
  description = "Tags to be applied to the this set of managed resources."
  type        = map(string)
  default     = {}
}

variable "aws_region" {
  description = "Region of the current cloud environment."
  type        = string
}

variable "eks_cluster_name" {
  description = "Name of the target eks cluster to deploy vault."
  type        = string
}

variable "eks_cluster_region" {
  description = "Region of the target eks cluster to deploy vault."
  type        = string
}

variable "cluster_domain_name" {
  description = "Domain name of the cluster"
  type        = string
}

variable "cluster_issuer_name" {
  description = "Name of the cert-manager ClusterIssuer."
  type        = string
}


variable "vpc" {
  description = "Internal cluster's VPC."
}

variable "zone_id" {
  description = "Route53 zone id"
  type        = string
}

variable "lb_arn" {
  description = "ARN of the pre-provisioned LB"
  type        = string
}

variable "lb_dns" {
  description = "DNS of the pre-provisioned LB"
  type        = string
}

variable "lb_zone_id" {
  description = "Zone ID of the pre-provisioned LB"
  type        = string
}

variable "is_restore" {
  description = "If true, the module won't do vault init but restores vault from existing DynamoDB and SecretManager"
  type        = bool
  default     = false
}

variable "enable_alerting" {
  type        = bool
  default     = false
  description = "If true, enable AWS alerting on the CloudWatch"
}

variable "legacy_dns" {
  type    = string
  default = ""
}
