# Create Namespace and Service Account
resource "kubernetes_namespace_v1" "vault_namespace" {
  metadata {
    name = var.vault_namespace
  }
}

resource "kubernetes_service_account_v1" "vault_service_account" {
  metadata {
    name      = local.service_account_name
    namespace = var.vault_namespace
    labels = {
      "app.kubernetes.io/name" = local.service_account_name
    }
    annotations = {
      "eks.amazonaws.com/role-arn" = module.vault_role.iam_role_arn
    }
  }

  depends_on = [
    kubernetes_namespace_v1.vault_namespace
  ]
}

# Create Certificate for Vault TLS.
resource "kubectl_manifest" "vault_certificate" {
  yaml_body = local.cert_yaml_data
}

# Create Target group binding
resource "kubectl_manifest" "vault_tgb" {
  yaml_body = local.tgb_yaml_data
  depends_on = [
    helm_release.vault,
    aws_lb_listener.vault_lb_listener,
    aws_lb_target_group.vault_lb_target_group,
    aws_route53_record.vault_dns_record,
  ]
}

resource "helm_release" "vault" {
  name             = var.vault_release_name
  namespace        = var.vault_namespace
  create_namespace = false
  repository       = var.vault_chart_repo
  chart            = var.vault_chart_name
  version          = var.vault_version
  timeout          = 1200

  # Ref: - https://developer.hashicorp.com/vault/docs/platform/k8s/helm/configuration
  set {
    name  = "global.tlsDisable"
    value = "false"
  }
  set {
    name  = "injector.enabled"
    value = "false"
  }
  set {
    name  = "server.updateStrategyType"
    value = "RollingUpdate"
  }
  set {
    name  = "server.service.enabled"
    value = "true"
  }
  set {
    name  = "server.serviceAccount.name"
    value = local.service_account_name
  }
  set {
    name  = "server.serviceAccount.create"
    value = "false"
  }
  set {
    name  = "server.service.type"
    value = "ClusterIP"
  }
  set {
    name  = "server.ha.enabled"
    value = "true"
  }
  set {
    name  = "server.ha.replicas"
    value = var.vault_ha_replicas
  }
  set {
    name  = "server.extraEnvironmentVars.VAULT_TLSCERT"
    value = "/vault/userconfig/vault-tls/tls.crt"
  }
  set {
    name  = "server.extraEnvironmentVars.VAULT_TLSKEY"
    value = "/vault/userconfig/vault-tls/tls.key"
  }
  set {
    name  = "server.extraVolumes[0].type"
    value = "secret"
  }
  set {
    name  = "server.extraVolumes[0].name"
    value = "vault-tls"
  }
  set {
    name = "server.ha.config"
    # FYI: https://developer.hashicorp.com/vault/docs/configuration
    # NOTE: `max_lease_ttl` was set to 87600h (10 years). See: https://github.com/hashicorp/terraform-provider-vault/issues/1512#issuecomment-**********
    # NOTE: remember to restart the statefulset manually if you update this value. See: https://github.com/hashicorp/vault-helm/issues/748
    value = <<EOF
disable_mlock = true
service_registration "kubernetes" {}
ui = false
default_lease_ttl = "720h"
max_lease_ttl = "87600h"
listener "tcp" {
  tls_disable = 0
  address = "[::]:${local.vault_service_port}"
  cluster_address = "[::]:${local.vault_cluster_port}"
  tls_cert_file = "/vault/userconfig/vault-tls/tls.crt"
  tls_key_file  = "/vault/userconfig/vault-tls/tls.key"
}
storage "dynamodb" {
  ha_enabled = "true"
  region = "${var.dynamodb_region}"
  table = "${var.dynamodb_name}"
}
seal "awskms" {
  kms_key_id = "${var.aws_kms_key_id}"
  region = "${var.aws_kms_region}"
}
telemetry {
  disable_hostname = true
  prometheus_retention_time = "12h"
}
EOF
  }
  set {
    name  = "server.statefulSet.annotations.secret\\.reloader\\.stakater\\.com/reload"
    value = "vault-tls"
  }

  depends_on = [
    kubernetes_namespace_v1.vault_namespace,
    kubernetes_service_account_v1.vault_service_account,
    kubectl_manifest.vault_certificate,
    aws_lb_listener.vault_lb_listener,
    aws_lb_target_group.vault_lb_target_group,
    aws_route53_record.vault_dns_record,
  ]
}

# NOTE: we use this null_resource to initialize vault.
# The tirggers is set to run the local-exec every time.
# If the vault has been initialized and the meta data has been stored in DynamoDB, KMS and SecretManager,
# set the var.is_restore = true, so that the  `init_vault.sh` won't be executed.
# Ref: https://developer.hashicorp.com/vault/docs/platform/k8s/helm/run
resource "null_resource" "vault_init_sh" {
  provisioner "local-exec" {
    # TODO: consider dynamically decide whether to run it instead of manually filp this flag.
    # when is_restore=true, do nothing.
    command = var.is_restore ? "echo 'Restoring Vault instance, skip the operator init operation...'" : local.vault_init_command

    # NOTE: use "non-sensitive" to view logs for debug.
    # https://github.com/hashicorp/terraform/issues/27154
    environment = {
      EKS_CLUSTER_NAME   = nonsensitive(var.eks_cluster_name)
      EKS_CLUSTER_REGION = nonsensitive(var.eks_cluster_region)
      SECRET_NAME        = nonsensitive(var.aws_secret_arn)
      SECRET_REGION      = var.aws_region
      VAULT_NAMESPACE    = var.vault_namespace
      VAULT_LEADER_POD   = local.vault_leader_pod
      ASSUME_ROLE_ARN    = var.assume_role_arn
    }
    interpreter = ["/bin/bash", "-c"]
    working_dir = path.module
  }

  # run everytime.
  triggers = {
    build_number = timestamp()
  }

  depends_on = [
    helm_release.vault,
  ]
}
