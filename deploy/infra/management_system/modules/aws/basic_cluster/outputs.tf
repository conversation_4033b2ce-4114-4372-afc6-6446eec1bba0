output "basic_cluster" {
  sensitive = true
  value = {
    // The endpoint of the mgmt cluster.
    cluster_endpoint = module.eks.cluster_endpoint

    cluster_certificate_authority_data = module.eks.cluster_certificate_authority_data

    cluster_oidc_provider_arn = module.eks.oidc_provider_arn

    cluster_id = module.eks.cluster_name

    cluster_node_security_group_id = module.eks.node_security_group_id

    cluster_managed_node_groups = module.eks.eks_managed_node_groups

    cluster_region = var.region

    ################################################################################
    # ALB
    ################################################################################
    gateway_lb_arn = aws_lb.gateway.arn
    // The ARN of gateway target group
    gateway_target_group_arn = aws_lb_target_group.target.arn

    gateway_target_group_name = aws_lb_target_group.target.name

    // The DNS of gateway
    gateway_dns = local.gateway_dns

    gateway_lb_dns = aws_lb.gateway.dns_name

    gateway_lb_zone_id = aws_lb.gateway.zone_id

    ################################################################################
    # Security Group
    ################################################################################
    aws_kms_key_eks_arn = aws_kms_key.eks.arn

    ################################################################################
    # EBS
    ################################################################################
    ebs_encryption_key_arn = module.ebs_kms_key.key_arn

    ################################################################################
    # VPC
    ################################################################################
    vpc_cidr = local.mgmt_vpc.vpc_cidr_block
  }
}
