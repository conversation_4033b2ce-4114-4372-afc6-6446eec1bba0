groups:
- name: Alert Example
  labels:
    description: "test alert"
    category: test
    severity: warning
  rules:
  - alert: ExampleAlertForTesting
    annotations:
      description: "test alert from tenant {{ $labels.tenant_ns_id }}"
      summary: "test alert"
      pod: "{{ $labels.pod }}"
      cloud: "{{ $labels.cloud }}"
      region: "{{ $labels.region }}"
    expr: |
      sum by (cloud, region, tenant_ns_id) (
        mgmt_tenant_test_alert
        AND on(tenant_ns_id, pod)
        (
          mgmt_tenant_test_alert_last_monitored_at_timestamp == topk(1, mgmt_tenant_test_alert_last_monitored_at_timestamp) by (tenant_ns_id)
        )
      ) > 0
    for: 0m
    labels:
      group: cloud
      namespace: "{{ $labels.namespace }}"
