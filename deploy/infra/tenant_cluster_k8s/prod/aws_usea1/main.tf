/**********************************
 * TERRAFORM CLOUD CONFIGURATIONS *
 **********************************/
locals {
  region = "us-east-1"
  // Workspace name is used to construct resource names. The acutually workspace
  // name is too long for some resource names. For example, length of iam role
  // name is 64 max. So here:
  // s/canary-useast2-eks-a-kubenetes/canary-useast2-eks-a-k8s
  workspace_name            = "prod-aws-usea1-eks-a-k8s"
  remote_eks_workspace      = "prod-aws-usea1-eks-a"
  remote_mgmt_workspace     = "prod-aws-usea1-mgmt"
  remote_mgmt_k8s_workspace = "prod-aws-usea1-mgmt-k8s"
}



module "rwc_prod" {
  source                    = "../../modules/aws/env_base_prod"
  region                    = local.region
  remote_eks_workspace      = local.remote_eks_workspace
  remote_mgmt_workspace     = local.remote_mgmt_workspace
  remote_mgmt_k8s_workspace = local.remote_mgmt_k8s_workspace

  workspace_name = local.workspace_name

  ## https://grafana.prod.risingwave.cloud/d/e7273789-c14e-4b59-a7c9-8694f388ed62/mimir-resource-dashboard?orgId=1&var-datasource=*****************&var-ingester=mimir-ingester-2&from=now-6h&to=now
  mimir_resources = {
    ingester = {
      request_memory = 4
      request_cpu    = 0.5
      limit_memory   = 4
      limit_cpu      = 1
    }
    distributor = {
      request_memory = 0.5
      request_cpu    = 0.25
      limit_memory   = 0.5
      limit_cpu      = 0.5
    }
  }

  mimir_ingester_replica    = 3
  mimir_distributor_replica = 3

  harness_api_key    = var.harness_api_key
  harness_account_id = var.harness_account_id
  harness_project_id = var.harness_project_id
  harness_org_id     = var.harness_org_id

  enable_grafana_cloud       = var.grafana_cloud_workspace_id != ""
  grafana_cloud_password     = var.grafana_cloud_password
  grafana_cloud_workspace_id = var.grafana_cloud_workspace_id
  grafana_cloud_username     = var.grafana_cloud_username

  opentelemetry_datadog_config = [
    {
      risingwave_namespace    = "rwc-g1j1bvfrsaeo4a8a25cr1u4gel-data-universe-prod"
      risingwave_cluster_name = "data-universe-prod"
      datadog_key             = var.huntclub_datadog_api_key
      env                     = "prod"
    }
  ]
}
