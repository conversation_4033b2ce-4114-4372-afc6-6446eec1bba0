locals {
  env_folder  = var.env == "rls" ? "release" : var.env == "canary" ? "prod" : var.env
  aws_account = var.env == "prod" || var.env == "canary" ? "rwcprod" : "rwc${var.env}"
  env_tag     = var.env == "canary" ? "prod" : var.env

  path_prefix = var.env == "dev" ? "" : "deploy/infra/"

  env_to_aws_credential_var_set = {
    dev    = "AWS credential ************(rwcdev)/deploy"
    test   = "AWS credential ************(rwctest)/deploy"
    rls    = "AWS credential ************(rwcrls)/deploy"
    canary = "AWS credential ************(rwcprod)/deploy"
    prod   = "AWS credential ************(rwcprod)/deploy"
  }
}

module "this" {
  source = "../workspace"

  workspace_name = "${var.env}-aws-${var.region_code}-central-cluster"
  project_name   = "central_cluster"
  env            = local.env_tag
  vcs_apply      = var.env != "dev"

  working_directory = "${local.path_prefix}central_cluster/${local.env_folder}/aws_${var.region_code}"
  trigger_patterns = [
    "${local.path_prefix}shared_modules/**/*",
    "${local.path_prefix}central_cluster/modules/**/*",
    "${local.path_prefix}central_cluster/${local.env_folder}/aws_${var.region_code}/*",
  ]
  variable_sets = [
    local.env_to_aws_credential_var_set[var.env],
    "azure-ad-pomerium-no-expiration",
  ]
}
