# ##################################################
#
# In cluster Authentication to the API server
#
# ##################################################
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{.Values.cloudagent.saName}}
  namespace: {{.Values.cloudagent.namespace}}
rules:
- apiGroups:
  - 'crossplane.io'
  - 'risingwave.risingwavelabs.com'
  - 'monitoring.coreos.com'
  - 'acid.zalan.do' # postgresql
  - 'batch'
  - 'rbac.authorization.k8s.io'
  - 'networking.k8s.io'
  - 'policy' # need poddisruptionbudgets.policy in helm releases
  - 'apps' # need statefulsets.apps in helm releases
  - '' # core API
  {{- if .Values.envIsAWS }}
  - 'services.k8s.aws'
  - 'ec2.services.k8s.aws'
  - 'iam.services.k8s.aws'
  - 'rds.services.k8s.aws'
  - 'vpcresources.k8s.aws' # securitygrouppolicies
  {{- end }}
  {{- if .Values.envIsGCP }}
  - 'iamserviceaccounts.iam.cnrm.cloud.google.com'
  - 'iampolicies.iam.cnrm.cloud.google.com'
  - 'iam.cnrm.cloud.google.com'
  - 'compute.cnrm.cloud.google.com'
  - 'sql.cnrm.cloud.google.com'
  {{- end }}
  {{- if .Values.envIsAZR }}
  - 'managedidentity.azure.com'
  - 'authorization.azure.com'
  - 'network.azure.com'
  - 'userassignedidentities.managedidentity.azure.com'
  - 'federatedidentitycredentials.managedidentity.azure.com'
  - 'roleassignments.authorization.azure.com'
  - 'privateendpoints.network.azure.com'
  - 'dbforpostgresql.azure.com'
  {{- end }}
  resources: ["*"]
  verbs: ["*"]
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{.Values.cloudagent.saName}}
  namespace: {{.Values.cloudagent.namespace}}
  annotations:
  {{- if .Values.envIsAWS }}
    eks.amazonaws.com/role-arn: {{.Values.cloudagent.awsIamRoleArn}}
  {{- end }}
  {{- if .Values.envIsGCP }}
    iam.gke.io/gcp-service-account: {{.Values.cloudagent.gcpSa}}
  {{- end }}
  {{- if .Values.envIsAZR }}
    azure.workload.identity/client-id: {{.Values.cloudagent.azrClientId}}
  {{- end }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{.Values.cloudagent.saName}}-role-binding
subjects:
- namespace: {{.Values.cloudagent.namespace}}
  kind: ServiceAccount
  name: {{.Values.cloudagent.saName}}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{.Values.cloudagent.saName}}
---
apiVersion: v1
kind: Namespace
metadata:
  name: {{.Values.cloudagent.namespace}}
  labels:
    name: {{.Values.cloudagent.namespace}}
  {{- if .Values.envIsAWS }}
    elbv2.k8s.aws/pod-readiness-gate-inject: enabled
  {{- end }}
---
# ##################################################
#
# TLS Certificate
#
# ##################################################
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: {{.Values.cloudagent.certName}}
  namespace: {{.Values.cloudagent.namespace}}
spec:
  secretName: {{.Values.cloudagent.certSecretName}}
  duration: 2160h # 90d
  renewBefore: 720h # 30d
  subject:
    organizations:
      - Risingwave
  commonName: "{{.Values.cloudagent.dns}}"
  isCA: false
  privateKey:
    algorithm: RSA
    encoding: PKCS1
    size: 4096
  dnsNames:
    - "{{.Values.cloudagent.dns}}"
  issuerRef:
    name: {{.Values.privateIssuerName}}
    kind: ClusterIssuer
---
# ##################################################
#
# Application Service
#
# ##################################################
apiVersion: v1
kind: ConfigMap
metadata:
  name: cloudagent-cluster-config-path
  namespace: {{.Values.cloudagent.namespace}}
data:
  # A self-issued certificate has tls.crt, tls.key, and ca.crt.
  # https://cert-manager.io/docs/concepts/certificate/
  #
  # We're using Vault as the issuer. So the ca.crt in all clusters
  # are in the same chain of trust. So we can put the ca.crt in the
  # clinet_ca_path here, to validate if the client certificate is
  # issued by our system.
  config.textproto: |
    k8s_config {
      cluster_id: "{{.Values.cloudagentCfg.clusterId}}"
      endpoint: "{{.Values.cloudagentCfg.k8sEndpiint}}"
      ca_certificate_base64: "{{.Values.cloudagentCfg.k8sCACert}}"
      in_cluster_auth {}
      allow_helm_charts: ["https://charts.bitnami.com/bitnami/etcd-*", "https://risingwavelabs.github.io/risingwave-extensions/charts/risingwave-extensions-*", "https://risingwavelabs.github.io/risingwave-extensions/charts/risingwave-serverless-compaction-*", "https://risingwavelabs.github.io/serverless-backfill-controller/charts/serverless-backfill-controller-*", "https://risingwavelabs.github.io/risingwave-extensions/risingwave-extensions-*"]
      task_config {
        image: "{{.Values.cloudagentCfg.taskImage}}"
        service_account: "{{.Values.cloudagent.saName}}"
        namespace: "{{.Values.cloudagent.namespace}}"
        pull_policy: PULL_ALWAYS
        {{- if .Values.affinityFlag}}
        tolerations {
          key: "machine-workload-type"
          operator: TOLERATION_OPERATOR_EQUAL
          value: "non-system-workload"
          effect: TAINT_EFFECT_NO_SCHEDULE
        }
        affinity {
          node_affinity: {
            required_during_scheduling_ignored_during_execution: {
              node_selector_terms: {
                match_expressions: {
                  key: "machine-workload-type"
                  operator: NODE_SELECTOR_OPERATOR_IN
                  values: "non-system-workload"
                }
              }
            }
          }
        }
        {{- end }}
        {{- if .Values.envIsAZR}}
        labels {
          key: "azure.workload.identity/use"
          value: "true"
        }
        {{- end }}
      }
    }
    {{- if .Values.envIsAWS }}
    aws_config {
      account_id: "{{.Values.cloudagentCfg.awsAccountId}}"
      region: "{{.Values.cloudagentCfg.awsRegion}}"
      oidc_provider: "{{.Values.cloudagentCfg.awsOidcProvider}}"
      vpc_id: "{{.Values.cloudagentCfg.awsVpcId}}"
      eks_web_identity {}
    }
    {{- end }}
    {{- if .Values.envIsGCP }}
    gcp_config {
      project_id: "{{.Values.cloudagentCfg.gcpProjectId}}"
      region: "{{.Values.cloudagentCfg.gcpRegion}}"
      tenant_vpc: "{{.Values.cloudagentCfg.gcpTenantVpc}}"
      gke_web_identity {}
    }
    {{- end }}
    {{- if .Values.envIsAZR }}
    azr_config {
      subscription_id: "{{.Values.cloudagentCfg.azrSubscription}}"
      location: "{{.Values.cloudagentCfg.azrLocation}}"
      resource_group: "{{.Values.cloudagentCfg.azrResourceGroup}}"
      oidc_issuer: "{{.Values.cloudagentCfg.azrOidcIssuer}}"
      aks_workload_identity {}
    }
    {{- end }}
    tls_config {
      cert_path: "{{.Values.cloudagent.certsDirectory}}/tls.crt"
      key_path: "{{.Values.cloudagent.certsDirectory}}/tls.key"
      client_ca_path: "{{.Values.cloudagent.certsDirectory}}/ca.crt"
    }
    telemetry_config {
      local_prometheus_config {
        url: "http://mimir-nginx.mimir.svc"
      }
    }
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{.Values.cloudagent.name}}
  namespace: {{.Values.cloudagent.namespace}}
  labels:
    app: {{.Values.cloudagent.name}}
  annotations:
    secret.reloader.stakater.com/reload: "{{.Values.cloudagent.certSecretName}}"
    configmap.reloader.stakater.com/reload: "{{.Values.cloudagent.svcConfigMapName}}"
spec:
  replicas: {{.Values.cloudagent.replicas}}
  selector:
    matchLabels:
      app: {{.Values.cloudagent.name}}
  minReadySeconds: 60
  template:
    metadata:
      labels:
        app: {{.Values.cloudagent.name}}
        {{- if .Values.envIsAZR }}
        azure.workload.identity/use: "true"
        {{- end }}
    spec:
{{- if .Values.affinityFlag}}
      tolerations:
      - key: "machine-workload-type"
        operator: "Equal"
        value: "non-system-workload"
        effect: "NoSchedule"
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: machine-workload-type
                operator: In
                values:
                - non-system-workload
{{- end}}
      serviceAccountName: {{.Values.cloudagent.saName}}
      containers:
      - name: cloudagent
        resources:
          limits:
            cpu: {{.Values.cloudagent.resourcesLimitCPU}}
            memory: {{.Values.cloudagent.resourcesLimitMemory}}
          requests:
            cpu: {{.Values.cloudagent.resourcesRequestCPU}}
            memory: {{.Values.cloudagent.resourcesRequestMemory}}
        command: ["/app/cloudagent"]
        args:
        - "-config_path"
        - "/app/config/config.textproto"
        - "-port"
        - "{{.Values.cloudagent.port}}"
        - "-zpageport"
        - "{{.Values.cloudagent.zpagePort}}"
        image: {{.Values.cloudagent.image}}
        imagePullPolicy: Always
        ports:
        - name: {{.Values.cloudagent.containerPortName}}
          containerPort: {{.Values.cloudagent.port}}
        - name: {{.Values.cloudagent.containerZpagePortName}}
          containerPort: {{.Values.cloudagent.zpagePort}}
        volumeMounts:
        - name: cloudagent-cluster-config-volume
          mountPath: /app/config
          readOnly: true
        - name: cert-vol
          readOnly: true
          mountPath: {{.Values.cloudagent.certsDirectory}}
        lifecycle:
          preStop:
            exec:
              command: ["sleep","90"]
      volumes:
      - name: cloudagent-cluster-config-volume
        configMap:
          name: cloudagent-cluster-config-path
      - name: cert-vol
        secret:
          secretName: {{.Values.cloudagent.certSecretName}}
      terminationGracePeriodSeconds: 120
# GCP/AZR service is defined in Terraform in order to provision Private Service
# Connect:
# https://github.com/risingwavelabs/risingwave-cloud/blob/main/deploy/infra/tenant_cluster_k8s/modules/gcp/cloudagent_manifest/service.yaml
# https://github.com/risingwavelabs/risingwave-cloud/blob/main/deploy/infra/tenant_cluster_k8s/modules/azr/env_base/agent.tf
{{- if .Values.envIsAWS }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{.Values.cloudagent.name}}
  namespace: {{.Values.cloudagent.namespace}}
  labels:
    app: {{.Values.cloudagent.name}}
spec:
  selector:
    app: {{.Values.cloudagent.name}}
  ports:
  - name: {{.Values.cloudagent.containerPortName}}
    port: {{.Values.cloudagent.port}}
    targetPort: {{.Values.cloudagent.port}}
    protocol: TCP
  - name: {{.Values.cloudagent.containerZpagePortName}}
    port: {{.Values.cloudagent.zpagePort}}
    targetPort: {{.Values.cloudagent.zpagePort}}
    protocol: TCP
  type: ClusterIP
---
apiVersion: elbv2.k8s.aws/v1beta1
kind: TargetGroupBinding
metadata:
  name: {{.Values.cloudagent.name}}
  namespace: {{.Values.cloudagent.namespace}}
spec:
  serviceRef:
    name: {{.Values.cloudagent.name}}
    port: {{.Values.cloudagent.containerPortName}}
  targetGroupARN: {{.Values.cloudagent.targetGroupARN}}
  networking:
    ingress:
    - from:
      - ipBlock:
          cidr: {{.Values.cloudagent.targetGroupCidr}}
      ports:
      - protocol: TCP
        port: {{.Values.cloudagent.port}}
---
apiVersion: elbv2.k8s.aws/v1beta1
kind: TargetGroupBinding
metadata:
  name: {{.Values.cloudagent.name}}-zpage
  namespace: {{.Values.cloudagent.namespace}}
spec:
  serviceRef:
    name: {{.Values.cloudagent.name}}
    port: {{.Values.cloudagent.containerZpagePortName}}
  targetGroupARN: {{.Values.cloudagent.zpageTargetGroupARN}}
  networking:
    ingress:
    - from:
      - ipBlock:
          cidr: {{.Values.cloudagent.targetGroupCidr}}
      ports:
      - protocol: TCP
        port: {{.Values.cloudagent.zpagePort}}
{{- end }}
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{.Values.cloudagent.name}}
  namespace: {{.Values.cloudagent.namespace}}
  labels:
    name: {{.Values.cloudagent.name}}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{.Values.cloudagent.name}}
  minReplicas: 1
  maxReplicas: 3
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 80
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
