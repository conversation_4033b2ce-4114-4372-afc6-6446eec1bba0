# Common values used by workflow engine and api service.
namespace: app
refreshInterval: 15s
mgmtKVPath: <+variable[<+stage.variables.cluster_name> + "_mgmt_kv_path"]>
dbCredentialPath: <+variable[<+stage.variables.cluster_name> + "_db_credential_path"]>
secretStoreName: <+variable[<+stage.variables.cluster_name> + "_secret_store_name"]>
appTokensPath: <+variable[<+stage.variables.cluster_name> + "_app_tokens_path"]>

mgmt:
  accountUrl: <+variable[<+stage.variables.cluster_name> + "_account_service_url"]>
  accountAdminUrl: <+variable[<+stage.variables.cluster_name> + "_account_service_admin_url"]>
  risingwavePassword: "<+variable[<+stage.variables.cluster_name> + "_risingwave_password"]>"
  # Values are defined in Terraform:
  # https://github.com/risingwavelabs/risingwave-cloud/blob/main/deploy/infra/management_system_k8s/modules/aws/env_base/storage_class.tf
  # https://github.com/risingwavelabs/risingwave-cloud/blob/main/deploy/infra/tenant_cluster_k8s/modules/gcp/env_base/storageclass.tf
  # TODO(CLOUD-1490): move these settings to MSDB cluster settings or CLoud
  # agent config as it's essentially a per tenant cluster setting
  metaStorageClass: "risingwave"

serviceConfig:
  name: service-config
  namespace: app

podManagementConfig:
  name: pod-management-config
  namespace: app

resourceDef:
  name: resource-def-configmap
  namespace: app

# Values used by MTLS
privateIssuerName: "management-svc-issuer"

# Let's Encrypt
rwletsencryptname: <+<+infra.tags.platform>.equals("azr")?"rw-letsencrypt-issuer":"">

# Environment info
envIsPROD: <+<+env.name>.endsWith("PROD")>
envIsCANARY: <+<+env.name>.endsWith("PROD") && <+infra.tags.is_canary> != null && <+infra.tags.is_canary>.equals("true")>
envIsRLS: <+<+env.name>.endsWith("RLS")>
envIsTEST: <+<+env.name>.endsWith("TEST")>
envIsDEV: <+<+env.name>.endsWith("DEV")>

# Infrastructure info
region: <+variable[<+stage.variables.cluster_name> + "_region"]>
infraIsGCP: <+<+infra.tags.platform>.equals("gcp")>
infraIsAWS: <+<+infra.tags.platform>.equals("aws")>
infraIsAZR: <+<+infra.tags.platform>.equals("azr")>

zones: "<+variable[<+stage.variables.cluster_name>+"_zones"]>"
awsAccountId: "<+<+infra.tags.platform>.equals("aws")?<+variable[<+stage.variables.cluster_name>+"_account_id"]>:"">"
awsBYOCSubnetIDs: <+<+infra.tags.platform>.equals("aws")?"<+variable[<+stage.variables.cluster_name> + "_byoc_subnets"]>":"">
awsBYOCSecurityGroupID: <+<+infra.tags.platform>.equals("aws")?"<+variable[<+stage.variables.cluster_name> + "_byoc_endpoint_sg"]>":"">
awsVPCID: <+<+infra.tags.platform>.equals("aws")?"<+variable[<+stage.variables.cluster_name> + "_vpc_id"]>":"">

gcpProjectId: <+<+infra.tags.platform>.equals("gcp")?"<+variable[<+stage.variables.cluster_name>+"_project_id"]>":"">
gcpVPCSelfLink: <+<+infra.tags.platform>.equals("gcp")?"<+variable[<+stage.variables.cluster_name> + "_vpc_self_link"]>":"">
gcpBYOCSubnetSelfLink: <+<+infra.tags.platform>.equals("gcp")?"<+variable[<+stage.variables.cluster_name> + "_byoc_subnet_self_link"]>":"">
gcpBYOCTelemetryGroup: <+<+env.name>.endsWith("PROD")? "<EMAIL>" : "<EMAIL>">


azrSubscriptionId: <+<+infra.tags.platform>.equals("azr")?"<+variable[<+stage.variables.cluster_name>+"_subscription_id"]>":"">
azrTenantId: <+<+infra.tags.platform>.equals("azr")?"<+variable[<+stage.variables.cluster_name>+"_tenant_id"]>":"">
azrBYOCSubnetId: <+<+infra.tags.platform>.equals("azr")?"<+variable[<+stage.variables.cluster_name>+"_byoc_privatelink_subnet"]>":"">
azrBYOCResourceGroup: <+<+infra.tags.platform>.equals("azr")?"<+variable[<+stage.variables.cluster_name>+"_mgmt_byoc_resource_group"]>":"">
azrBYOCControlPlaneSecurityGroupId: <+<+infra.tags.platform>.equals("azr")?"<+variable[<+stage.variables.cluster_name>+"_byoc_control_plane_security_group"]>":"">
azrBYOCTelemetrySecurityGroupId: <+<+infra.tags.platform>.equals("azr")?"<+variable[<+stage.variables.cluster_name>+"_byoc_telemetry_security_group"]>":"">
azrDiagStorageAccount: <+<+infra.tags.platform>.equals("azr")?"<+variable[<+stage.variables.cluster_name>+"_diag_storage_acc"]>":"">

mgmtResourceGroup: <+<+infra.tags.platform>.equals("azr")?"<+variable[<+stage.variables.cluster_name> + "_mgmt_resource_group"]>":"">

# Helm ectd chart version
etcdChart: https://charts.bitnami.com/bitnami/etcd-9.0.4.tgz

# flag that control affinity and toleration feature
affinityFlag: true

# byoc
byocClusterVersion: "v2025.32.0"

# RW Diagnosis Report
diagBucket: "<+variable[<+stage.variables.cluster_name>+"_diag_bucket"]>"

# risingwave extensions
extensions:
  serverlesscompaction:
    ChartTag: "risingwave-serverless-compaction-0.2.5.tgz"

  serverlessbackfill:
    ChartUrl: "https://risingwavelabs.github.io/serverless-backfill-controller/charts/serverless-backfill-controller"

    ChartTag: "2.0.9"
    EnabledByDefault: false
    SetSbcAddr: true

    service:
      name: "serverless-backfill-controller"
      port: 1298


features:
  auditLog: <+<+env.name>.endsWith("DEV") || <+env.name>.endsWith("PROD")>
  autoBackup: true
  snapshotGCEnableDelete: true
  metaMigration: <+<+env.name>.endsWith("DEV") || <+env.name>.endsWith("TEST") || <+env.name>.endsWith("RLS")>
  configSet: true
  serverlessBackfillExtension:  <+<+env.name>.endsWith("DEV") || <+env.name>.endsWith("TEST") || <+env.name>.endsWith("RLS")>
  disallowCrossNamespaceRWEgress: true
  tierEnforcement: true
  supportVariousCPUMemoryRatio: true

cloudAgentMaxGRPCMsgSize: "33554432"

msdbConnection:
  maxConns: 10
  queryTimeoutMs: 1000
  updateTimeoutMs: 2000
  deleteTimeoutMs: 2000
  upsertTimeoutMs: 2000
  createTimeoutMs: 2000
  transactionTimeoutMs: 2000

PgMetaStoreAvailableVersions: <+(<+env.name>.endsWith("DEV") || <+env.name>.endsWith("TEST") || <+env.name>.endsWith("RLS")) ? "2.0.0" : "2.0.5">


AutoBackupMaximumDeletableSnapshots: 3
AutoBackupIntervalMinutes: 30
PeriodicalBackupRetentionMinstType: 0
OperationalBackupRetentionMins: 4320 # 3 days

