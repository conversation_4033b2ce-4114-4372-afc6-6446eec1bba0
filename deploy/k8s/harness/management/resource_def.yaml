apiVersion: v1
kind: ConfigMap
metadata:
  name: {{.Values.resourceDef.name}}
  namespace: {{.Values.resourceDef.namespace}}
data:
  resourcedef.prod.yaml: |
    Tiers:
      - Id: Developer-Free
        ServerlessCompactionOptions:
          Allowed: false
        AllowComputeFileCache: false
        MaximumComputeFileCacheGB: 0
        ExpirationMechanism: usage
        DefaultRetentionPeriod: 0
        DefaultValidityPeriod: 7
        AvailableStandaloneTypeList:
          - Id: "dev-1c4g"
            MaximumReplica: 0
        AvailableMetaStore:
          Etcd:
            TypeList:
              - Id: "dev-etcd"
                MaximumReplica: 1
            MaximumSizeGB: 20
          Prefer: etcd
      - Id: Developer-Basic
        ServerlessCompactionOptions:
          Allowed: false
        AllowComputeFileCache: false
        MaximumComputeFileCacheGB: 0
        {{- if or .Values.envIsDEV .Values.envIsTEST .Values.envIsRLS}}
        ExpirationMechanism: date
        DefaultRetentionPeriod: 3
        DefaultValidityPeriod: 1
        {{- else }}
        ExpirationMechanism: none
        DefaultRetentionPeriod: 0
        DefaultValidityPeriod: 0
        {{- end}}
        AvailableStandaloneTypeList:
          - Id: "dev-2c8g"
            MaximumReplica: 1
        AvailableMetaStore:
          Etcd:
            TypeList:
              - Id: "dev-etcd"
                MaximumReplica: 1
            MaximumSizeGB: 20
        {{- if or .Values.envIsDEV .Values.envIsTEST .Values.envIsRLS}}
          PostgreSql:
            TypeList:
              - Id: "dev-etcd"
                MaximumReplica: 1
            MaximumSizeGB: 20
          SharingPg:
          Prefer: sharing_pg
        {{- else }}
          Prefer: etcd
        {{- end}}
      {{- if or .Values.envIsDEV .Values.envIsTEST .Values.envIsRLS}}
      - Id: Benchmark
        ServerlessCompactionOptions:
          Allowed: true
        AllowComputeFileCache: false
        MaximumComputeFileCacheGB: 0
        ExpirationMechanism: date
        DefaultRetentionPeriod: 1
        DefaultValidityPeriod: 0
        AvailableComputeTypeList:
          - Id: "p-4c16g"
            MaximumReplica: 8
        AvailableCompactorTypeList:
          - Id: "p-4c16g"
            MaximumReplica: 8
        AvailableFrontendTypeList:
          - Id: "p-2c8g"
            MaximumReplica: 8
        AvailableMetaTypeList:
          - Id: "p-2c8g"
            MaximumReplica: 8
        AvailableMetaStore:
          Prefer: sharing_pg
          Etcd:
            TypeList:
              - Id: "p-2c8g"
                MaximumReplica: 1
            MaximumSizeGB: 50
          PostgreSql:
            TypeList:
              - Id: "p-2c8g"
                MaximumReplica: 1
            MaximumSizeGB: 20
          SharingPg:
      {{- end}}
      {{- if or .Values.envIsDEV .Values.envIsTEST .Values.envIsRLS}}
      - Id: Test
        ServerlessCompactionOptions:
          Allowed: true
        AllowComputeFileCache: false
        MaximumComputeFileCacheGB: 0
        ExpirationMechanism: date
        DefaultRetentionPeriod: 1
        DefaultValidityPeriod: 2
        AvailableComputeTypeList:
          - Id: "t-cn"
            MaximumReplica: 2
            MaximumTrialReplica: 1
        AvailableCompactorTypeList:
          - Id: "t-cp"
            MaximumReplica: 1
            MaximumTrialReplica: 1
        AvailableFrontendTypeList:
          - Id: "t-ft"
            MaximumReplica: 1
            MaximumTrialReplica: 1
        AvailableMetaTypeList:
          - Id: "t-mt"
            MaximumReplica: 1
            MaximumTrialReplica: 1
        AvailableMetaStore:
          Prefer: sharing_pg
          Etcd:
            TypeList:
              - Id: "t-et"
                MaximumReplica: 1
            MaximumSizeGB: 1
          PostgreSql:
            TypeList:
              - Id: "t-pg"
                MaximumReplica: 1
            MaximumSizeGB: 1
          SharingPg:
      {{- end}}
      - Id: Invited
        ServerlessCompactionOptions:
          Allowed: true
        AllowComputeFileCache: true
        MaximumComputeFileCacheGB: 50
        {{- if or .Values.envIsDEV .Values.envIsTEST .Values.envIsRLS}}
        ExpirationMechanism: date
        DefaultRetentionPeriod: 3
        DefaultValidityPeriod: 1
        {{- else }}
        ExpirationMechanism: none
        DefaultRetentionPeriod: 0
        DefaultValidityPeriod: 0
        {{- end}}
        AvailableStandaloneTypeList:
          - Id: "p-2c8g"
            MaximumReplica: 1
          - Id: "p-3c12g"
            MaximumReplica: 1
          - Id: "p-4c16g"
            MaximumReplica: 1
          - Id: "p-6c24g"
            MaximumReplica: 1
          - Id: "p-8c32g"
            MaximumReplica: 1
          - Id: "p-14c56g"
            MaximumReplica: 1
          - Id: "p-16c64g"
            MaximumReplica: 1
          - Id: "p-28c112g"
            MaximumReplica: 1
          - Id: "p-30c120g"
            MaximumReplica: 1
          - Id: "p-32c128g"
            MaximumReplica: 1
          - Id: "p-48c192g"
            MaximumReplica: 1
        AvailableComputeTypeList:
          - Id: "p-0.5c2g"
            MaximumReplica: 10
          - Id: "p-1c4g"
            MaximumReplica: 10
          - Id: "p-1.5c6g"
            MaximumReplica: 10
          - Id: "p-2c8g"
            MaximumReplica: 10
          - Id: "p-3c12g"
            MaximumReplica: 10
          - Id: "p-4c16g"
            MaximumReplica: 10
          - Id: "p-5c20g"
            MaximumReplica: 10
          - Id: "p-6c24g"
            MaximumReplica: 10
          - Id: "p-7c28g"
            MaximumReplica: 10
          - Id: "p-8c32g"
            MaximumReplica: 10
          - Id: "p-10c40g"
            MaximumReplica: 10
          - Id: "p-12c48g"
            MaximumReplica: 10
          - Id: "p-14c56g"
            MaximumReplica: 10
          - Id: "p-16c64g"
            MaximumReplica: 10
          - Id: "p-20c80g"
            MaximumReplica: 10
          - Id: "p-24c96g"
            MaximumReplica: 10
          - Id: "p-32c128g"
            MaximumReplica: 10
          - Id: "p-48c192g"
            MaximumReplica: 10
          - Id: "p-64c256g"
            MaximumReplica: 10
        AvailableCompactorTypeList:
          - Id: "p-0.5c2g"
            MaximumReplica: 10
          - Id: "p-1c4g"
            MaximumReplica: 10
          - Id: "p-2c8g"
            MaximumReplica: 10
          - Id: "p-4c16g"
            MaximumReplica: 10
          - Id: "p-6c24g"
            MaximumReplica: 10
          - Id: "p-8c32g"
            MaximumReplica: 10
          - Id: "p-10c40g"
            MaximumReplica: 10
          - Id: "p-12c48g"
            MaximumReplica: 10
          - Id: "p-16c64g"
            MaximumReplica: 10
          - Id: "p-24c96g"
            MaximumReplica: 10
          - Id: "p-32c128g"
            MaximumReplica: 10
          - Id: "p-48c192g"
            MaximumReplica: 10
          - Id: "p-64c256g"
            MaximumReplica: 10
        AvailableFrontendTypeList:
          - Id: "p-0.5c2g"
            MaximumReplica: 10
          - Id: "p-1c4g"
            MaximumReplica: 10
          - Id: "p-1.5c6g"
            MaximumReplica: 10
          - Id: "p-2c8g"
            MaximumReplica: 10
          - Id: "p-3c12g"
            MaximumReplica: 10
          - Id: "p-4c16g"
            MaximumReplica: 10
          - Id: "p-5c20g"
            MaximumReplica: 10
          - Id: "p-6c24g"
            MaximumReplica: 10
          - Id: "p-7c28g"
            MaximumReplica: 10
          - Id: "p-8c32g"
            MaximumReplica: 10
          - Id: "p-10c40g"
            MaximumReplica: 10
          - Id: "p-12c48g"
            MaximumReplica: 10
          - Id: "p-14c56g"
            MaximumReplica: 10
          - Id: "p-16c64g"
            MaximumReplica: 10
          - Id: "p-20c80g"
            MaximumReplica: 10
          - Id: "p-24c96g"
            MaximumReplica: 10
          - Id: "p-32c128g"
            MaximumReplica: 10
          - Id: "p-36c144g"
            MaximumReplica: 10
          - Id: "p-48c192g"
            MaximumReplica: 10
          - Id: "p-64c256g"
            MaximumReplica: 10
        AvailableMetaTypeList:
          - Id: "p-0.5c2g"
            MaximumReplica: 3
          - Id: "p-1c4g"
            MaximumReplica: 3
          - Id: "p-1.5c6g"
            MaximumReplica: 3
          - Id: "p-2c8g"
            MaximumReplica: 3
          - Id: "p-3c12g"
            MaximumReplica: 3
          - Id: "p-4c16g"
            MaximumReplica: 3
          - Id: "p-5c20g"
            MaximumReplica: 3
          - Id: "p-6c24g"
            MaximumReplica: 3
          - Id: "p-7c28g"
            MaximumReplica: 3
          - Id: "p-8c32g"
            MaximumReplica: 3
          - Id: "p-16c64g"
            MaximumReplica: 3
          - Id: "p-10c40g"
            MaximumReplica: 3
          - Id: "p-12c48g"
            MaximumReplica: 3
        AvailableMetaStore:
          {{- if .Values.infraIsAWS }}
          Prefer: aws_rds
          {{- else if .Values.infraIsGCP }}
          Prefer: gcp_cloudsql
          {{- else if .Values.infraIsAZR }}
          Prefer: azr_postgres
          {{- else }}
          Prefer: etcd
          {{- end }}
          {{- if .Values.infraIsAWS }}
          AwsRds:
            MachineTypeList:
              - Name: "db.t4g.medium"
                Cpu: "1"
              - Name: "db.t4g.large"
                Cpu: "2"
              - Name: "db.t4g.xlarge"
                Cpu: "4"
            MaximumSizeGB: 20
          {{- end}}
          {{- if .Values.infraIsGCP }}
          GcpCloudSql:
            MachineTypeList:
              - Name: "db-custom-1-3840"
                Cpu: "1"
              - Name: "db-custom-2-7680"
                Cpu: "2"
              - Name: "db-custom-4-15360"
                Cpu: "4"
            MaximumSizeGB: 20
          {{- end}}
          {{- if .Values.infraIsAZR }}
          AzrPostgres:
            MachineTypeList:
              - Name: "Standard_B2s"
                Cpu: "1"
              - Name: "Standard_B2ms"
                Cpu: "2"
              - Name: "Standard_B4ms"
                Cpu: "4"
            MaximumSizeGB: 32
          {{- end}}
          Etcd:
            TypeList:
              - Id: "p-0.5c2g"
                MaximumReplica: 3
              - Id: "p-1c4g"
                MaximumReplica: 3
              - Id: "p-1.5c6g"
                MaximumReplica: 3
              - Id: "p-2c8g"
                MaximumReplica: 3
              - Id: "p-3c12g"
                MaximumReplica: 3
              - Id: "p-4c16g"
                MaximumReplica: 3
              - Id: "p-5c20g"
                MaximumReplica: 3
              - Id: "p-6c24g"
                MaximumReplica: 3
              - Id: "p-7c28g"
                MaximumReplica: 3
              - Id: "p-8c32g"
                MaximumReplica: 3
            MaximumSizeGB: 16
      - Id: BYOC
        ServerlessCompactionOptions:
          Allowed: true
        AllowComputeFileCache: true
        MaximumComputeFileCacheGB: 50
        {{- if or .Values.envIsDEV .Values.envIsTEST .Values.envIsRLS}}
        ExpirationMechanism: date
        DefaultRetentionPeriod: 3
        DefaultValidityPeriod: 1
        {{- else }}
        ExpirationMechanism: none
        DefaultRetentionPeriod: 0
        DefaultValidityPeriod: 0
        {{- end}}
        AvailableStandaloneTypeList:
          - Id: "p-2c8g"
            MaximumReplica: 1
          - Id: "p-3c12g"
            MaximumReplica: 1
          - Id: "p-4c16g"
            MaximumReplica: 1
          - Id: "p-6c24g"
            MaximumReplica: 1
          - Id: "p-8c32g"
            MaximumReplica: 1
          - Id: "p-14c56g"
            MaximumReplica: 1
          - Id: "p-16c64g"
            MaximumReplica: 1
          - Id: "p-28c112g"
            MaximumReplica: 1
          - Id: "p-30c120g"
            MaximumReplica: 1
          - Id: "p-32c128g"
            MaximumReplica: 1
          - Id: "p-48c192g"
            MaximumReplica: 1
        AvailableComputeTypeList:
          - Id: "p-0.5c2g"
            MaximumReplica: 15
          - Id: "p-1c4g"
            MaximumReplica: 15
          - Id: "p-1.5c6g"
            MaximumReplica: 15
          - Id: "p-2c8g"
            MaximumReplica: 15
          - Id: "p-3c12g"
            MaximumReplica: 15
          - Id: "p-4c16g"
            MaximumReplica: 15
          - Id: "p-5c20g"
            MaximumReplica: 15
          - Id: "p-6c24g"
            MaximumReplica: 15
          - Id: "p-7c28g"
            MaximumReplica: 15
          - Id: "p-8c32g"
            MaximumReplica: 15
          - Id: "p-10c40g"
            MaximumReplica: 15
          - Id: "p-12c48g"
            MaximumReplica: 15
          - Id: "p-14c56g"
            MaximumReplica: 15
          - Id: "p-16c64g"
            MaximumReplica: 15
          - Id: "p-20c80g"
            MaximumReplica: 15
          - Id: "p-24c96g"
            MaximumReplica: 15
          - Id: "p-32c128g"
            MaximumReplica: 15
          - Id: "p-48c192g"
            MaximumReplica: 15
          - Id: "p-64c256g"
            MaximumReplica: 15
          - Id: "p-0.5c4g"
            MaximumReplica: 15
          - Id: "p-1c8g"
            MaximumReplica: 15
          - Id: "p-1.5c12g"
            MaximumReplica: 15
          - Id: "p-2c16g"
            MaximumReplica: 15
          - Id: "p-3c24g"
            MaximumReplica: 15
          - Id: "p-4c32g"
            MaximumReplica: 15
          - Id: "p-5c40g"
            MaximumReplica: 15
          - Id: "p-6c48g"
            MaximumReplica: 15
          - Id: "p-7c56g"
            MaximumReplica: 15
          - Id: "p-8c64g"
            MaximumReplica: 15
          - Id: "p-10c80g"
            MaximumReplica: 15
          - Id: "p-12c96g"
            MaximumReplica: 15
          - Id: "p-14c112g"
            MaximumReplica: 15
          - Id: "p-16c128g"
            MaximumReplica: 15
          - Id: "p-20c160g"
            MaximumReplica: 15
          - Id: "p-24c192g"
            MaximumReplica: 15
          - Id: "p-32c256g"
            MaximumReplica: 15
          - Id: "p-48c384g"
            MaximumReplica: 15
          - Id: "p-64c512g"
            MaximumReplica: 15
        AvailableCompactorTypeList:
          - Id: "p-0.5c2g"
            MaximumReplica: 15
          - Id: "p-1c4g"
            MaximumReplica: 15
          - Id: "p-2c8g"
            MaximumReplica: 15
          - Id: "p-4c16g"
            MaximumReplica: 15
          - Id: "p-6c24g"
            MaximumReplica: 15
          - Id: "p-8c32g"
            MaximumReplica: 15
          - Id: "p-10c40g"
            MaximumReplica: 15
          - Id: "p-12c48g"
            MaximumReplica: 15
          - Id: "p-16c64g"
            MaximumReplica: 15
          - Id: "p-24c96g"
            MaximumReplica: 15
          - Id: "p-32c128g"
            MaximumReplica: 15
          - Id: "p-48c192g"
            MaximumReplica: 15
          - Id: "p-64c256g"
            MaximumReplica: 15
        AvailableFrontendTypeList:
          - Id: "p-0.5c2g"
            MaximumReplica: 10
          - Id: "p-1c4g"
            MaximumReplica: 10
          - Id: "p-1.5c6g"
            MaximumReplica: 10
          - Id: "p-2c8g"
            MaximumReplica: 10
          - Id: "p-3c12g"
            MaximumReplica: 10
          - Id: "p-4c16g"
            MaximumReplica: 10
          - Id: "p-5c20g"
            MaximumReplica: 10
          - Id: "p-6c24g"
            MaximumReplica: 10
          - Id: "p-7c28g"
            MaximumReplica: 10
          - Id: "p-8c32g"
            MaximumReplica: 10
          - Id: "p-10c40g"
            MaximumReplica: 10
          - Id: "p-12c48g"
            MaximumReplica: 10
          - Id: "p-14c56g"
            MaximumReplica: 10
          - Id: "p-16c64g"
            MaximumReplica: 10
          - Id: "p-20c80g"
            MaximumReplica: 10
          - Id: "p-24c96g"
            MaximumReplica: 10
          - Id: "p-32c128g"
            MaximumReplica: 10
          - Id: "p-36c144g"
            MaximumReplica: 10
          - Id: "p-48c192g"
            MaximumReplica: 10
          - Id: "p-64c256g"
            MaximumReplica: 10
        AvailableMetaTypeList:
          - Id: "p-0.5c2g"
            MaximumReplica: 3
          - Id: "p-1c4g"
            MaximumReplica: 3
          - Id: "p-1.5c6g"
            MaximumReplica: 3
          - Id: "p-2c8g"
            MaximumReplica: 3
          - Id: "p-3c12g"
            MaximumReplica: 3
          - Id: "p-4c16g"
            MaximumReplica: 3
          - Id: "p-5c20g"
            MaximumReplica: 3
          - Id: "p-6c24g"
            MaximumReplica: 3
          - Id: "p-7c28g"
            MaximumReplica: 3
          - Id: "p-8c32g"
            MaximumReplica: 3
          - Id: "p-16c64g"
            MaximumReplica: 3
          - Id: "p-10c40g"
            MaximumReplica: 3
          - Id: "p-12c48g"
            MaximumReplica: 3
        AvailableMetaStore:
          {{- if and .Values.infraIsAWS }}
          Prefer: aws_rds
          {{- else if .Values.infraIsGCP }}
          Prefer: gcp_cloudsql
          {{- else if .Values.infraIsAZR }}
          Prefer: azr_postgres
          {{- else }}
          Prefer: etcd
          {{- end }}
          {{- if .Values.infraIsAWS }}
          AwsRds:
            MachineTypeList:
              - Name: "db.t4g.medium"
                Cpu: "1"
              - Name: "db.t4g.large"
                Cpu: "2"
              - Name: "db.t4g.xlarge"
                Cpu: "4"
            MaximumSizeGB: 20
          {{- else if .Values.infraIsGCP }}
          GcpCloudSql:
            MachineTypeList:
              - Name: "db-custom-1-3840"
                Cpu: "1"
              - Name: "db-custom-2-7680"
                Cpu: "2"
              - Name: "db-custom-4-15360"
                Cpu: "4"
            MaximumSizeGB: 20
          {{- else if .Values.infraIsAZR }}
          AzrPostgres:
            MachineTypeList:
              - Name: "Standard_B2s"
                Cpu: "1"
              - Name: "Standard_B2ms"
                Cpu: "2"
              - Name: "Standard_B4ms"
                Cpu: "4"
            MaximumSizeGB: 32
          {{- end}}
          Etcd:
            TypeList:
              - Id: "p-0.5c2g"
                MaximumReplica: 3
              - Id: "p-1c4g"
                MaximumReplica: 3
              - Id: "p-1.5c6g"
                MaximumReplica: 3
              - Id: "p-2c8g"
                MaximumReplica: 3
              - Id: "p-3c12g"
                MaximumReplica: 3
              - Id: "p-4c16g"
                MaximumReplica: 3
              - Id: "p-5c20g"
                MaximumReplica: 3
              - Id: "p-6c24g"
                MaximumReplica: 3
              - Id: "p-7c28g"
                MaximumReplica: 3
              - Id: "p-8c32g"
                MaximumReplica: 3
            MaximumSizeGB: 16
      - Id: Standard
        AllowComputeFileCache: true
        {{- if not .Values.features.tierEnforcement}}
        MaximumComputeFileCacheGB: 50
        ServerlessCompactionOptions:
          Allowed: true
        {{- else }}
        MaximumComputeFileCacheGB: 0
        ServerlessCompactionOptions:
          Allowed: false
        {{- end}}
        {{- if or .Values.envIsDEV .Values.envIsTEST .Values.envIsRLS}}
        ExpirationMechanism: date
        DefaultRetentionPeriod: 3
        DefaultValidityPeriod: 1
        {{- else }}
        ExpirationMechanism: none
        DefaultRetentionPeriod: 0
        DefaultValidityPeriod: 0
        {{- end}}
        AvailableStandaloneTypeList:
          - Id: "p-1.5c6g"
            MaximumReplica: 1
            MaximumTrialReplica: 1
          - Id: "p-2c8g"
            MaximumReplica: 1
            MaximumTrialReplica: 1
          - Id: "p-3c12g"
            MaximumReplica: 1
          - Id: "p-4c16g"
            MaximumReplica: 1
          - Id: "p-6c24g"
            MaximumReplica: 1
          - Id: "p-8c32g"
            MaximumReplica: 1
          - Id: "p-14c56g"
            MaximumReplica: 1
          - Id: "p-16c64g"
            MaximumReplica: 1
          - Id: "p-24c96g"
            MaximumReplica: 1
          - Id: "p-28c112g"
            MaximumReplica: 1
          - Id: "p-30c120g"
            MaximumReplica: 1
          - Id: "p-32c128g"
            MaximumReplica: 1
          - Id: "p-44c176g"
            MaximumReplica: 1
          - Id: "p-48c192g"
            MaximumReplica: 1
          - Id: "p-56c224g"
            MaximumReplica: 1
          - Id: "p-64c256g"
            MaximumReplica: 1
        {{- if not .Values.features.tierEnforcement}}
        AvailableComputeTypeList:
          - Id: "p-0.5c2g"
            MaximumReplica: 10
          - Id: "p-1c4g"
            MaximumReplica: 10
            MaximumTrialReplica: 1
          - Id: "p-1.5c6g"
            MaximumReplica: 10
          - Id: "p-2c8g"
            MaximumReplica: 10
          - Id: "p-3c12g"
            MaximumReplica: 10
          - Id: "p-4c16g"
            MaximumReplica: 10
          - Id: "p-5c20g"
            MaximumReplica: 10
          - Id: "p-6c24g"
            MaximumReplica: 10
          - Id: "p-7c28g"
            MaximumReplica: 10
          - Id: "p-8c32g"
            MaximumReplica: 10
          - Id: "p-10c40g"
            MaximumReplica: 10
          - Id: "p-12c48g"
            MaximumReplica: 10
          - Id: "p-14c56g"
            MaximumReplica: 10
          - Id: "p-16c64g"
            MaximumReplica: 10
          - Id: "p-20c80g"
            MaximumReplica: 10
          - Id: "p-24c96g"
            MaximumReplica: 10
          - Id: "p-32c128g"
            MaximumReplica: 10
          - Id: "p-48c192g"
            MaximumReplica: 10
          - Id: "p-64c256g"
            MaximumReplica: 10
        AvailableCompactorTypeList:
          - Id: "p-0.5c2g"
            MaximumReplica: 10
            MaximumTrialReplica: 1
          - Id: "p-1c4g"
            MaximumReplica: 10
          - Id: "p-2c8g"
            MaximumReplica: 10
          - Id: "p-4c16g"
            MaximumReplica: 10
          - Id: "p-6c24g"
            MaximumReplica: 10
          - Id: "p-8c32g"
            MaximumReplica: 10
          - Id: "p-10c40g"
            MaximumReplica: 10
          - Id: "p-12c48g"
            MaximumReplica: 10
          - Id: "p-16c64g"
            MaximumReplica: 10
          - Id: "p-24c96g"
            MaximumReplica: 10
          - Id: "p-32c128g"
            MaximumReplica: 10
          - Id: "p-48c192g"
            MaximumReplica: 10
          - Id: "p-64c256g"
            MaximumReplica: 10
        AvailableFrontendTypeList:
          - Id: "p-0.5c2g"
            MaximumReplica: 5
            MaximumTrialReplica: 1
          - Id: "p-1c4g"
            MaximumReplica: 5
          - Id: "p-1.5c6g"
            MaximumReplica: 5
          - Id: "p-2c8g"
            MaximumReplica: 5
          - Id: "p-3c12g"
            MaximumReplica: 5
          - Id: "p-4c16g"
            MaximumReplica: 5
          - Id: "p-5c20g"
            MaximumReplica: 5
          - Id: "p-6c24g"
            MaximumReplica: 5
          - Id: "p-7c28g"
            MaximumReplica: 5
          - Id: "p-8c32g"
            MaximumReplica: 5
          - Id: "p-10c40g"
            MaximumReplica: 5
          - Id: "p-12c48g"
            MaximumReplica: 5
          - Id: "p-14c56g"
            MaximumReplica: 5
          - Id: "p-16c64g"
            MaximumReplica: 5
          - Id: "p-20c80g"
            MaximumReplica: 5
          - Id: "p-24c96g"
            MaximumReplica: 5
          - Id: "p-32c128g"
            MaximumReplica: 5
          - Id: "p-36c144g"
            MaximumReplica: 10
          - Id: "p-48c192g"
            MaximumReplica: 5
          - Id: "p-64c256g"
            MaximumReplica: 5
        AvailableMetaTypeList:
          - Id: "p-0.5c2g"
            MaximumReplica: 3
            MaximumTrialReplica: 1
          - Id: "p-1c4g"
            MaximumReplica: 3
          - Id: "p-1.5c6g"
            MaximumReplica: 3
          - Id: "p-2c8g"
            MaximumReplica: 3
          - Id: "p-3c12g"
            MaximumReplica: 3
          - Id: "p-4c16g"
            MaximumReplica: 3
          - Id: "p-5c20g"
            MaximumReplica: 3
          - Id: "p-6c24g"
            MaximumReplica: 3
          - Id: "p-7c28g"
            MaximumReplica: 3
          - Id: "p-8c32g"
            MaximumReplica: 3
          - Id: "p-16c64g"
            MaximumReplica: 3
          - Id: "p-10c40g"
            MaximumReplica: 3
          - Id: "p-12c48g"
            MaximumReplica: 3
        {{- end}}
        AvailableMetaStore:
          Prefer: sharing_pg
          SharingPg:
          {{- if and .Values.infraIsAWS (or .Values.envIsDEV .Values.envIsTEST .Values.envIsRLS) }}
          AwsRds:
            MachineTypeList:
              - Name: "db.t4g.medium"
                Cpu: "1"
              - Name: "db.t4g.large"
                Cpu: "2"
              - Name: "db.t4g.xlarge"
                Cpu: "4"
            MaximumSizeGB: 20
          {{- else if and .Values.infraIsGCP (or .Values.envIsDEV .Values.envIsTEST .Values.envIsRLS) }}
          GcpCloudSql:
            MachineTypeList:
              - Name: "db-custom-1-3840"
                Cpu: "1"
              - Name: "db-custom-2-7680"
                Cpu: "2"
              - Name: "db-custom-4-15360"
                Cpu: "4"
            MaximumSizeGB: 20
          {{- else if and .Values.infraIsAZR (or .Values.envIsDEV .Values.envIsTEST .Values.envIsRLS) }}
          AzrPostgres:
            MachineTypeList:
              - Name: "Standard_B2s"
                Cpu: "1"
              - Name: "Standard_B2ms"
                Cpu: "2"
              - Name: "Standard_B4ms"
                Cpu: "4"
            MaximumSizeGB: 32
          {{- end}}
          Etcd:
            TypeList:
              - Id: "p-0.5c2g"
                MaximumReplica: 3
              - Id: "p-1c4g"
                MaximumReplica: 3
              - Id: "p-1.5c6g"
                MaximumReplica: 3
              - Id: "p-2c8g"
                MaximumReplica: 3
              - Id: "p-3c12g"
                MaximumReplica: 3
              - Id: "p-4c16g"
                MaximumReplica: 3
              - Id: "p-5c20g"
                MaximumReplica: 3
              - Id: "p-6c24g"
                MaximumReplica: 3
              - Id: "p-7c28g"
                MaximumReplica: 3
              - Id: "p-8c32g"
                MaximumReplica: 3
            MaximumSizeGB: 16
    ComponentTypes:
      - Id: "dev-1c4g"
        CpuRequest: "100m"
        CpuLimit: "1"
        MemoryRequest: "1Gi"
        MemoryLimit: "4Gi"
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      - Id: "dev-2c8g"
        CpuRequest: "1"
        CpuLimit: "2"
        MemoryRequest: "4Gi"
        MemoryLimit: "8Gi"
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      - Id: "dev-etcd"
        CpuRequest: "100m"
        CpuLimit: "500m"
        MemoryRequest: "0"
        MemoryLimit: "512Mi"
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      - Id: "f-1c1g"
        CpuRequest: "200m"
        CpuLimit: "1"
        MemoryRequest: 256Mi
        MemoryLimit: 1Gi
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      - Id: "f-1c2g"
        CpuRequest: "200m"
        CpuLimit: "1"
        MemoryRequest: 512Mi
        MemoryLimit: 2Gi
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      {{- if or .Values.envIsDEV .Values.envIsTEST .Values.envIsRLS}}
      - Id: "t-ft"
        CpuRequest: 100m
        CpuLimit: 200m
        MemoryRequest: 256Mi
        MemoryLimit: 1Gi
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      - Id: "t-cn"
        CpuRequest: 200m
        CpuLimit: "1"
        MemoryRequest: 256Mi
        MemoryLimit: 1Gi
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      - Id: "t-cp"
        CpuRequest: 100m
        CpuLimit: 200m
        MemoryRequest: 256Mi
        MemoryLimit: 256Mi
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      - Id: "t-mt"
        CpuRequest: 100m
        CpuLimit: 200m
        MemoryRequest: 256Mi
        MemoryLimit: 1Gi
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      - Id: "t-et"
        CpuRequest: 100m
        CpuLimit: 200m
        MemoryRequest: 128Mi
        MemoryLimit: 128Mi
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      - Id: "t-pg"
        CpuRequest: 200m
        CpuLimit: 500m
        MemoryRequest: 256Mi
        MemoryLimit: 512Mi
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      {{- end}}
      - Id: "p-0.5c2g"
        CpuRequest: "0.5"
        CpuLimit: "0.5"
        MemoryRequest: 2Gi
        MemoryLimit: 2Gi
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      - Id: "p-1c4g"
        CpuRequest: "1"
        CpuLimit: "1"
        MemoryRequest: 4Gi
        MemoryLimit: 4Gi
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      - Id: "p-1.5c6g"
        CpuRequest: "1.5"
        CpuLimit: "1.5"
        MemoryRequest: 6Gi
        MemoryLimit: 6Gi
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      - Id: "p-2c8g"
        CpuRequest: "2"
        CpuLimit: "2"
        MemoryRequest: 8Gi
        MemoryLimit: 8Gi
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      - Id: "p-3c12g"
        CpuRequest: "3"
        CpuLimit: "3"
        {{- if .Values.infraIsAZR }}
        MemoryRequest: 10.1875Gi
        MemoryLimit: 10.1875Gi
        {{- else }}
        MemoryRequest: 12Gi
        MemoryLimit: 12Gi
        {{- end}}
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      - Id: "p-4c16g"
        CpuRequest: "4"
        CpuLimit: "4"
        MemoryRequest: 16Gi
        MemoryLimit: 16Gi
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      - Id: "p-5c20g"
        CpuRequest: "5"
        CpuLimit: "5"
        MemoryRequest: 20Gi
        MemoryLimit: 20Gi
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      - Id: "p-6c24g"
        CpuRequest: "6"
        CpuLimit: "6"
        MemoryRequest: 24Gi
        MemoryLimit: 24Gi
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      - Id: "p-7c28g"
        CpuRequest: "7"
        CpuLimit: "7"
        {{- if .Values.infraIsAZR }}
        MemoryRequest: 24.875Gi
        MemoryLimit: 24.875Gi
        {{- else }}
        MemoryRequest: 28Gi
        MemoryLimit: 28Gi
        {{- end}}
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      - Id: "p-8c32g"
        CpuRequest: "7"
        CpuLimit: "8"
        {{- if .Values.infraIsAZR }}
        MemoryRequest: 24.875Gi
        MemoryLimit: 24.875Gi
        {{- else }}
        MemoryRequest: 30Gi
        MemoryLimit: 30Gi
        {{- end}}
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      - Id: "p-10c40g"
        CpuRequest: "9"
        CpuLimit: "10"
        MemoryRequest: 38Gi
        MemoryLimit: 38Gi
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      - Id: "p-12c48g"
        CpuRequest: "11"
        CpuLimit: "12"
        MemoryRequest: 45Gi
        MemoryLimit: 45Gi
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      - Id: "p-14c56g"
        CpuRequest: "13"
        CpuLimit: "14"
        MemoryRequest: 52Gi
        MemoryLimit: 52Gi
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      - Id: "p-16c64g"
        CpuRequest: "15"
        CpuLimit: "16"
        {{- if .Values.infraIsAZR }}
        MemoryRequest: 56Gi
        MemoryLimit: 56Gi
        {{- else }}
        MemoryRequest: 60Gi
        MemoryLimit: 60Gi
        {{- end}}
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      - Id: "p-20c80g"
        CpuRequest: "20"
        CpuLimit: "20"
        MemoryRequest: 80Gi
        MemoryLimit: 80Gi
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      - Id: "p-24c96g"
        CpuRequest: "23"
        CpuLimit: "24"
        MemoryRequest: 92Gi
        MemoryLimit: 92Gi
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      - Id: "p-28c112g"
        CpuRequest: "27"
        CpuLimit: "28"
        MemoryRequest: 108Gi
        MemoryLimit: 108Gi
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      - Id: "p-30c120g"
        CpuRequest: "28"
        CpuLimit: "30"
        MemoryRequest: 112Gi
        MemoryLimit: 112Gi
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      - Id: "p-32c128g"
        CpuRequest: "30"
        CpuLimit: "32"
        MemoryRequest: 118Gi
        MemoryLimit: 118Gi
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      - Id: "p-36c144g"
        CpuRequest: "35"
        CpuLimit: "36"
        MemoryRequest: 144Gi
        MemoryLimit: 144Gi
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      - Id: "p-44c176g"
        CpuRequest: "40"
        CpuLimit: "44"
        MemoryRequest: 164Gi
        MemoryLimit: 164Gi
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      - Id: "p-48c192g"
        CpuRequest: "44"
        CpuLimit: "48"
        MemoryRequest: 177Gi
        MemoryLimit: 177Gi
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      - Id: "p-56c224g"
        CpuRequest: "50"
        CpuLimit: "56"
        MemoryRequest: 204Gi
        MemoryLimit: 204Gi
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      - Id: "p-64c256g"
        CpuRequest: "58"
        CpuLimit: "64"
        MemoryRequest: 236Gi
        MemoryLimit: 236Gi
        NodeSelectorCPUMemoryRatioValue: "1c4m"
      - Id: "p-0.5c4g"
        CpuRequest: "0.5"
        CpuLimit: "0.5"
        MemoryRequest: 4Gi
        MemoryLimit: 4Gi
        NodeSelectorCPUMemoryRatioValue: "1c8m"
      - Id: "p-1c8g"
        CpuRequest: "1"
        CpuLimit: "1"
        MemoryRequest: 8Gi
        MemoryLimit: 8Gi
        NodeSelectorCPUMemoryRatioValue: "1c8m"
      - Id: "p-1.5c12g"
        CpuRequest: "1.5"
        CpuLimit: "1.5"
        MemoryRequest: 12Gi
        MemoryLimit: 12Gi
        NodeSelectorCPUMemoryRatioValue: "1c8m"
      - Id: "p-2c16g"
        CpuRequest: "2"
        CpuLimit: "2"
        MemoryRequest: 16Gi
        MemoryLimit: 16Gi
        NodeSelectorCPUMemoryRatioValue: "1c8m"
      - Id: "p-3c24g"
        CpuRequest: "3"
        CpuLimit: "3"
        {{- if .Values.infraIsAZR }}
        MemoryRequest: 22.1875Gi
        MemoryLimit: 22.1875Gi
        {{- else }}
        MemoryRequest: 24Gi
        MemoryLimit: 24Gi
        {{- end}}
        NodeSelectorCPUMemoryRatioValue: "1c8m"
      - Id: "p-4c32g"
        CpuRequest: "4"
        CpuLimit: "4"
        MemoryRequest: 32Gi
        MemoryLimit: 32Gi
        NodeSelectorCPUMemoryRatioValue: "1c8m"
      - Id: "p-5c40g"
        CpuRequest: "5"
        CpuLimit: "5"
        MemoryRequest: 40Gi
        MemoryLimit: 40Gi
        NodeSelectorCPUMemoryRatioValue: "1c8m"
      - Id: "p-6c48g"
        CpuRequest: "6"
        CpuLimit: "6"
        MemoryRequest: 48Gi
        MemoryLimit: 48Gi
        NodeSelectorCPUMemoryRatioValue: "1c8m"
      - Id: "p-7c56g"
        CpuRequest: "7"
        CpuLimit: "7"
        {{- if .Values.infraIsAZR }}
        MemoryRequest: 52.875Gi
        MemoryLimit: 52.875Gi
        {{- else }}
        MemoryRequest: 56Gi
        MemoryLimit: 56Gi
        {{- end}}
        NodeSelectorCPUMemoryRatioValue: "1c8m"
      - Id: "p-8c64g"
        CpuRequest: "7"
        CpuLimit: "8"
        {{- if .Values.infraIsAZR }}
        MemoryRequest: 56.875Gi
        MemoryLimit: 56.875Gi
        {{- else }}
        MemoryRequest: 62Gi
        MemoryLimit: 62Gi
        {{- end}}
        NodeSelectorCPUMemoryRatioValue: "1c8m"
      - Id: "p-10c80g"
        CpuRequest: "9"
        CpuLimit: "10"
        MemoryRequest: 78Gi
        MemoryLimit: 78Gi
        NodeSelectorCPUMemoryRatioValue: "1c8m"
      - Id: "p-12c96g"
        CpuRequest: "11"
        CpuLimit: "12"
        MemoryRequest: 93Gi
        MemoryLimit: 93Gi
        NodeSelectorCPUMemoryRatioValue: "1c8m"
      - Id: "p-14c112g"
        CpuRequest: "13"
        CpuLimit: "14"
        MemoryRequest: 108Gi
        MemoryLimit: 108Gi
        NodeSelectorCPUMemoryRatioValue: "1c8m"
      - Id: "p-16c128g"
        CpuRequest: "15"
        CpuLimit: "16"
        {{- if .Values.infraIsAZR }}
        MemoryRequest: 120Gi
        MemoryLimit: 120Gi
        {{- else }}
        MemoryRequest: 124Gi
        MemoryLimit: 124Gi
        {{- end}}
        NodeSelectorCPUMemoryRatioValue: "1c8m"
      - Id: "p-20c160g"
        CpuRequest: "20"
        CpuLimit: "20"
        MemoryRequest: 160Gi
        MemoryLimit: 160Gi
        NodeSelectorCPUMemoryRatioValue: "1c8m"
      - Id: "p-24c192g"
        CpuRequest: "23"
        CpuLimit: "24"
        MemoryRequest: 188Gi
        MemoryLimit: 188Gi
        NodeSelectorCPUMemoryRatioValue: "1c8m"
      - Id: "p-28c224g"
        CpuRequest: "27"
        CpuLimit: "28"
        MemoryRequest: 220Gi
        MemoryLimit: 220Gi
        NodeSelectorCPUMemoryRatioValue: "1c8m"
      - Id: "p-30c240g"
        CpuRequest: "28"
        CpuLimit: "30"
        MemoryRequest: 232Gi
        MemoryLimit: 232Gi
        NodeSelectorCPUMemoryRatioValue: "1c8m"
      - Id: "p-32c256g"
        CpuRequest: "30"
        CpuLimit: "32"
        MemoryRequest: 236Gi
        MemoryLimit: 236Gi
        NodeSelectorCPUMemoryRatioValue: "1c8m"
      - Id: "p-36c288g"
        CpuRequest: "35"
        CpuLimit: "36"
        MemoryRequest: 288Gi
        MemoryLimit: 288Gi
        NodeSelectorCPUMemoryRatioValue: "1c8m"
      - Id: "p-44c352g"
        CpuRequest: "40"
        CpuLimit: "44"
        MemoryRequest: 340Gi
        MemoryLimit: 340Gi
        NodeSelectorCPUMemoryRatioValue: "1c8m"
      - Id: "p-48c384g"
        CpuRequest: "44"
        CpuLimit: "48"
        MemoryRequest: 369Gi
        MemoryLimit: 369Gi
        NodeSelectorCPUMemoryRatioValue: "1c8m"
      - Id: "p-56c448g"
        CpuRequest: "50"
        CpuLimit: "56"
        MemoryRequest: 428Gi
        MemoryLimit: 428Gi
        NodeSelectorCPUMemoryRatioValue: "1c8m"
      - Id: "p-64c512g"
        CpuRequest: "58"
        CpuLimit: "64"
        MemoryRequest: 492Gi
        MemoryLimit: 492Gi
        NodeSelectorCPUMemoryRatioValue: "1c8m"
