// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package server

import (
	"github.com/google/wire"
	"github.com/risingwavelabs/risingwave-cloud/internal/account"
	"github.com/risingwavelabs/risingwave-cloud/internal/admin/controller"
	"github.com/risingwavelabs/risingwave-cloud/internal/agent/provider"
	"github.com/risingwavelabs/risingwave-cloud/internal/apiservice/controllers/controllerv1"
	"github.com/risingwavelabs/risingwave-cloud/internal/apiservice/controllers/controllerv2"
	"github.com/risingwavelabs/risingwave-cloud/internal/apiservice/services"
	"github.com/risingwavelabs/risingwave-cloud/internal/config"
	"github.com/risingwavelabs/risingwave-cloud/internal/infra/logging"
	"github.com/risingwavelabs/risingwave-cloud/internal/infra/metastore"
	"github.com/risingwavelabs/risingwave-cloud/internal/infra/metrics"
	"github.com/risingwavelabs/risingwave-cloud/internal/infra/network"
	"github.com/risingwavelabs/risingwave-cloud/internal/infra/storage"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/cluster"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/extensions"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/rwconfig"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/rwcrd"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/rwwait"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/rwworkload"
	"github.com/risingwavelabs/risingwave-cloud/internal/managedcluster/byoc"
	"github.com/risingwavelabs/risingwave-cloud/internal/managedcluster/cloud"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/modeltx"
	"github.com/risingwavelabs/risingwave-cloud/internal/postgres"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/configset"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/diagnosis"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/rwdb"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/rwresource"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/rwsync"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenant/metastoremanager"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/alert"
	byoc2 "github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/byoc"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/computecache"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/configupdate"
	delete2 "github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/delete"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/etcd"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/expire"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/extensions/backfill"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/extensions/compaction"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/extensions/iceberg"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/gc"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/imagetag"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/metabackup"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/metamigration"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/metaupgrade"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/monitor"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/privatelink"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/provision"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/resourcegroup"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/stopstart"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/update"
	"github.com/risingwavelabs/risingwave-cloud/shared/ginx"
)

// Injectors from wire.go:

// Injector. Wires up providers. We declare the API server initialization, and wire writes the function body.
func InitializeApiService() (*AllComponents, error) {
	string2 := LoadVersion()
	debugController := ginx.NewDebugController(string2)
	pool := model.GetConnectionPool()
	modelModel := model.NewModelPool(pool)
	txProvider := modeltx.NewPoolTxProvider(pool)
	registry := workflow.NewRegistry()
	repository := workflow.NewRepository(registry, modelModel, txProvider)
	cloudAgentProviderInterface := provider.NewCloudAgentProvider(modelModel)
	sensitiveFields, err := rwdb.LoadKernelSensitiveFields()
	if err != nil {
		return nil, err
	}
	risingWaveClientInterface := rwdb.NewRisingWaveClient(modelModel, sensitiveFields)
	rwSynchronizer := rwsync.New(modelModel, txProvider)
	agentConfig, err := account.LoadAgentConfig()
	if err != nil {
		return nil, err
	}
	agent := account.NewAgent(agentConfig)
	backupWorkflowTriggerInterface := metabackup.NewMetaBackupWorkflowService(repository, cloudAgentProviderInterface, modelModel, txProvider, risingWaveClientInterface, agent)
	deleteWorkflowTriggerInterface := metabackup.NewDeleteSnapshotWorkflowService(repository, cloudAgentProviderInterface, modelModel, txProvider, risingWaveClientInterface)
	autoBackupWorkflowTriggerInterface := metabackup.NewAutoMetaBackupWorkflowService(repository, modelModel, backupWorkflowTriggerInterface, deleteWorkflowTriggerInterface)
	rwDiagAgentFactoryInterface := diagnosis.NewRwDiagAgentFactory()
	monitorWorkflowService := monitor.NewMonitorWorkflowService(repository, modelModel, risingWaveClientInterface, rwSynchronizer, autoBackupWorkflowTriggerInterface, cloudAgentProviderInterface, rwDiagAgentFactoryInterface)
	iKubernetesClusterProvider := cluster.NewKubernetesClusterProvider(modelModel, txProvider, agent)
	factory := network.NewFactory(modelModel)
	factoryInterface := storage.NewFactory(modelModel)
	serverlessCompaction := extensions.NewServerlessCompaction(cloudAgentProviderInterface, factoryInterface)
	deleteTenantWorkflowService := delete2.NewDeleteTenantWorkflowService(repository, iKubernetesClusterProvider, agent, modelModel, txProvider, cloudAgentProviderInterface, factory, factoryInterface, serverlessCompaction)
	metricsFactory := metrics.NewFactory(modelModel)
	expireTenantWorkflowService := expire.NewExpireTenantWorkflowService(repository, iKubernetesClusterProvider, deleteTenantWorkflowService, agent, metricsFactory, serverlessCompaction, modelModel, txProvider, factoryInterface)
	waiter := rwwait.NewWaiter()
	risingWaveWorkloadManager := rwworkload.NewRisingWaveWorkloadManager(waiter)
	stopStartTenantWorkflowService := stopstart.NewStopStartTenantWorkflowService(repository, iKubernetesClusterProvider, cloudAgentProviderInterface, risingWaveWorkloadManager, agent, expireTenantWorkflowService, modelModel, txProvider, risingWaveClientInterface, serverlessCompaction, factoryInterface)
	metastoreFactory := metastore.NewFactory(modelModel, agent)
	iProvisionTenantWorkflowService := provision.NewProvisionTenantWorkflowService(repository, monitorWorkflowService, expireTenantWorkflowService, stopStartTenantWorkflowService, iKubernetesClusterProvider, agent, risingWaveClientInterface, risingWaveWorkloadManager, serverlessCompaction, modelModel, txProvider, cloudAgentProviderInterface, factoryInterface, metastoreFactory)
	configsetRepository := configset.NewRepository(modelModel, txProvider)
	rwconfigFactory := rwconfig.NewFactory(factoryInterface, configsetRepository)
	rwBuilder := rwcrd.NewRwBuilder(modelModel)
	updateComputeCacheWorkflowService := computecache.NewUpdateComputeCacheWorkflowService(repository, rwconfigFactory, waiter, rwBuilder, configsetRepository, modelModel, txProvider, cloudAgentProviderInterface)
	tenantServiceInterface := services.NewTenantService(modelModel, txProvider, repository, cloudAgentProviderInterface, iProvisionTenantWorkflowService, deleteTenantWorkflowService, updateComputeCacheWorkflowService)
	rwUserService := services.NewRwUserService(modelModel, risingWaveClientInterface, rwSynchronizer)
	matViewService := services.NewMatViewService(risingWaveClientInterface, modelModel)
	metricsServiceInterface := services.NewMetricsService(metricsFactory, cloudAgentProviderInterface)
	connectorConfigsList, err := services.LoadConnectorConfigsList()
	if err != nil {
		return nil, err
	}
	sourceService := services.NewSourceService(risingWaveClientInterface, metricsServiceInterface, connectorConfigsList, cloudAgentProviderInterface)
	sinkService := services.NewSinkService(risingWaveClientInterface, metricsServiceInterface, connectorConfigsList)
	inPlaceRestoreWorkflowTriggerInterface := metabackup.NewInPlaceRestoreWorkflowService(repository, cloudAgentProviderInterface, txProvider, risingWaveClientInterface, risingWaveWorkloadManager, iKubernetesClusterProvider, metastoreFactory, factoryInterface, modelModel)
	metaBackupService := services.NewMetaBackupService(modelModel, backupWorkflowTriggerInterface, deleteWorkflowTriggerInterface, inPlaceRestoreWorkflowTriggerInterface)
	iProvisionPrivateLinkWorkflowService := privatelink.NewProvisionPrivateLinkWorkflowService(repository, modelModel, cloudAgentProviderInterface, factory, agent, txProvider)
	iDeletePrivateLinkWorkflowService := privatelink.NewDeletePrivateLinkWorkflowService(repository, modelModel, cloudAgentProviderInterface, factory, agent, txProvider)
	privateLinkService := services.NewPrivateLinkService(cloudAgentProviderInterface, iProvisionPrivateLinkWorkflowService, iDeletePrivateLinkWorkflowService, factory, modelModel)
	loggingFactory := logging.NewFactory(modelModel)
	logServiceInterface := services.NewLogService(loggingFactory)
	updateWorkflowService := configupdate.NewUpdateWorkflowService(repository, risingWaveWorkloadManager, agent, modelModel, txProvider, serverlessCompaction, cloudAgentProviderInterface, rwconfigFactory, configsetRepository, rwBuilder, factoryInterface)
	resourcesDef := config.GetResourceDef()
	manager := metastoremanager.NewManager(modelModel, resourcesDef)
	metaMigrationWorkflowService := metamigration.NewMetaMigrationWorkflowService(repository, modelModel, txProvider, cloudAgentProviderInterface, risingWaveWorkloadManager, metastoreFactory, manager)
	imageTagUpdateWorkflowService := imagetag.NewImageTagUpdateWorkflowService(repository, cloudAgentProviderInterface, waiter, metaMigrationWorkflowService, backupWorkflowTriggerInterface, modelModel, txProvider, risingWaveClientInterface, serverlessCompaction, factoryInterface)
	configUpdateWorkflowService := etcd.NewConfigUpdateWorkflowService(repository, waiter, cloudAgentProviderInterface, agent, modelModel, txProvider)
	updateTenantResourcesService := update.NewTenantResourcesService(repository, agent, risingWaveWorkloadManager, rwconfigFactory, cloudAgentProviderInterface, waiter, configsetRepository, serverlessCompaction, modelModel, txProvider, factoryInterface)
	tenantOperationService := services.NewTenantOperationService(configsetRepository, stopStartTenantWorkflowService, updateWorkflowService, imageTagUpdateWorkflowService, configUpdateWorkflowService, updateTenantResourcesService)
	enableWorkflowService := compaction.NewEnableWorkflowService(repository, agent, modelModel, txProvider, serverlessCompaction, cloudAgentProviderInterface, factoryInterface)
	disableWorkflowService := compaction.NewDisableWorkflowService(repository, agent, modelModel, txProvider, serverlessCompaction, cloudAgentProviderInterface, factoryInterface)
	compactionUpdateWorkflowService := compaction.NewUpdateWorkflowService(repository, agent, modelModel, txProvider, serverlessCompaction, cloudAgentProviderInterface, factoryInterface)
	backfillEnableWorkflowService := backfill.NewEnableWorkflowService(repository, modelModel, txProvider, cloudAgentProviderInterface, risingWaveClientInterface)
	backfillUpdateWorkflowService := backfill.NewUpdateWorkflowService(repository, agent, modelModel, txProvider, cloudAgentProviderInterface, risingWaveClientInterface)
	backfillDisableWorkflowService := backfill.NewDisableWorkflowService(repository, modelModel, txProvider, cloudAgentProviderInterface, risingWaveClientInterface)
	compactionWorkflowService := iceberg.NewCompactionWorkflowService(repository, modelModel, txProvider, cloudAgentProviderInterface, waiter)
	tenantExtensionService := services.NewTenantExtensionService(modelModel, txProvider, enableWorkflowService, disableWorkflowService, compactionUpdateWorkflowService, backfillEnableWorkflowService, backfillUpdateWorkflowService, backfillDisableWorkflowService, compactionWorkflowService, repository)
	clusterRepository := byoc.NewByocRepository(modelModel, txProvider)
	prepareBYOCClusterWorkflowService := byoc2.NewPrepareBYOCClusterWorkflowService(repository, clusterRepository, modelModel)
	terminateBYOCClusterWorkflowService := byoc2.NewTerminateBYOCClusterWorkflowService(repository, clusterRepository, modelModel)
	postUpdateBYOCClusterWorkflowService := byoc2.NewPostUpdateBYOCClusterWorkflowService(repository, clusterRepository, modelModel)
	updateBYOCWorkflowService := byoc2.NewUpdateBYOCWorkflowService(repository, clusterRepository, cloudAgentProviderInterface, postUpdateBYOCClusterWorkflowService)
	rwRelationProvider := rwresource.NewRwRelationProvider(risingWaveClientInterface, modelModel)
	userController := controllerv1.NewUserController(tenantServiceInterface, rwUserService, matViewService, sourceService, sinkService, metaBackupService, privateLinkService, logServiceInterface, metricsServiceInterface, tenantOperationService, tenantExtensionService, prepareBYOCClusterWorkflowService, terminateBYOCClusterWorkflowService, updateBYOCWorkflowService, postUpdateBYOCClusterWorkflowService, modelModel, manager, rwRelationProvider, clusterRepository, agent, cloudAgentProviderInterface, repository)
	serverV1ApiServiceControllers := v1ApiServiceControllers{
		DebugController: debugController,
		UserController:  userController,
	}
	rwDatabaseService := services.NewRwDatabaseService(modelModel, risingWaveClientInterface, rwSynchronizer)
	tableService := services.NewTableService(risingWaveClientInterface, modelModel)
	secretService := services.NewSecretService(risingWaveClientInterface)
	resourceGroupWorkflowService := resourcegroup.NewResourceGroupWorkflowService(repository, modelModel, txProvider, cloudAgentProviderInterface, waiter, rwconfigFactory, rwBuilder, configsetRepository)
	resourceGroupService := services.NewResourceGroupService(resourceGroupWorkflowService, risingWaveClientInterface, modelModel)
	rwDependencyProvider := rwresource.NewRwDependencyProvider(risingWaveClientInterface, modelModel)
	controllerv2UserController := controllerv2.NewUserController(tenantServiceInterface, tenantOperationService, tenantExtensionService, privateLinkService, metaBackupService, rwUserService, rwDatabaseService, matViewService, tableService, sourceService, sinkService, secretService, resourceGroupService, metricsServiceInterface, rwRelationProvider, rwDependencyProvider, manager, agent, modelModel, cloudAgentProviderInterface, clusterRepository, prepareBYOCClusterWorkflowService, terminateBYOCClusterWorkflowService, updateBYOCWorkflowService, postUpdateBYOCClusterWorkflowService, risingWaveClientInterface)
	serverV2ApiServiceControllers := v2ApiServiceControllers{
		UserController: controllerv2UserController,
	}
	authSecret, err := LoadAuthSecret()
	if err != nil {
		return nil, err
	}
	authenticator := LoadAuthenticator(agent)
	authorizer := LoadAuthorizer(agent)
	tierInfoProvider := LoadTierInfoProvider(agent)
	userServer, err := NewAPIServiceServer(serverV1ApiServiceControllers, serverV2ApiServiceControllers, authSecret, authenticator, authorizer, tierInfoProvider, tenantServiceInterface)
	if err != nil {
		return nil, err
	}
	metaUpgradeWorkflowService := metaupgrade.NewMetaUpgradeWorkflowService(repository, modelModel, txProvider, cloudAgentProviderInterface, risingWaveWorkloadManager, metastoreFactory)
	cloudClusterRepository := cloud.NewCloudRepository(modelModel, txProvider)
	adminController := controller.NewAdminController(debugController, tenantServiceInterface, tenantOperationService, tenantExtensionService, privateLinkService, metaBackupService, resourceGroupService, agent, expireTenantWorkflowService, stopStartTenantWorkflowService, metaMigrationWorkflowService, metaUpgradeWorkflowService, manager, updateBYOCWorkflowService, modelModel, txProvider, repository, cloudClusterRepository, clusterRepository, metricsFactory, cloudAgentProviderInterface, rwDiagAgentFactoryInterface)
	adminServer, err := NewAdminServiceServer(adminController)
	if err != nil {
		return nil, err
	}
	garbageCollectionWorkflowService := gc.NewGarbageCollectionWorkflowService(repository, iKubernetesClusterProvider, agent, cloudAgentProviderInterface, modelModel, txProvider, risingWaveClientInterface)
	notificationsProcessWorkflowService := alert.NewNotificationsProcessWorkflowService(repository, agent, modelModel, txProvider)
	allWorkflows := &AllWorkflows{
		IProvisionTenantWorkflowService:        iProvisionTenantWorkflowService,
		IProvisionPrivateLinkWorkflowService:   iProvisionPrivateLinkWorkflowService,
		IDeletePrivateLinkWorkflowService:      iDeletePrivateLinkWorkflowService,
		DeleteTenantWorkflowService:            deleteTenantWorkflowService,
		StopStartTenantWorkflowService:         stopStartTenantWorkflowService,
		MonitorWorkflowService:                 monitorWorkflowService,
		ConfigUpdateWorkflowService:            updateWorkflowService,
		UpdateComputeCacheWorkflowService:      updateComputeCacheWorkflowService,
		ImageTagUpdateWorkflowService:          imageTagUpdateWorkflowService,
		ExpireTenantWorkflowService:            expireTenantWorkflowService,
		EtcdUpdateWorkflowService:              configUpdateWorkflowService,
		UpdateTenantResourcesService:           updateTenantResourcesService,
		PrepareBYOCClusterWorkflowService:      prepareBYOCClusterWorkflowService,
		TerminateBYOCClusterWorkflowService:    terminateBYOCClusterWorkflowService,
		PostUpdateBYOCClusterWorkflowService:   postUpdateBYOCClusterWorkflowService,
		UpdateBYOCWorkflowService:              updateBYOCWorkflowService,
		GarbageCollectionWorkflowService:       garbageCollectionWorkflowService,
		DeleteWorkflowTriggerInterface:         deleteWorkflowTriggerInterface,
		BackupWorkflowTriggerInterface:         backupWorkflowTriggerInterface,
		AutoBackupWorkflowTriggerInterface:     autoBackupWorkflowTriggerInterface,
		InPlaceRestoreWorkflowTriggerInterface: inPlaceRestoreWorkflowTriggerInterface,
		CompactionEnableWorkflowService:        enableWorkflowService,
		DisableWorkflowService:                 disableWorkflowService,
		UpdateWorkflowService:                  compactionUpdateWorkflowService,
		BackfillEnableWorkflowService:          backfillEnableWorkflowService,
		BackfillUpdateWorkflowService:          backfillUpdateWorkflowService,
		BackfillDisableWorkflowService:         backfillDisableWorkflowService,
		MetaMigrationWorkflowService:           metaMigrationWorkflowService,
		MetaUpgradeWorkflowService:             metaUpgradeWorkflowService,
		ResourceGroupWorkflowService:           resourceGroupWorkflowService,
		NotificationsProcessWorkflowService:    notificationsProcessWorkflowService,
	}
	allComponents := &AllComponents{
		ApiServer:    userServer,
		AdminServer:  adminServer,
		AllWorkflows: allWorkflows,
	}
	return allComponents, nil
}

// wire.go:

var accountSet = wire.NewSet(account.NewAgent, account.LoadAgentConfig)

var authSet = wire.NewSet(
	LoadAuthSecret,
	accountSet,
	LoadAuthenticator,
	LoadAuthorizer,
	LoadTierInfoProvider,
)

var debugControllerSet = wire.NewSet(
	LoadVersion, ginx.NewDebugController,
)

var v1ControllerSet = wire.NewSet(
	debugControllerSet, controllerv1.NewUserController,
)

var v2ControllerSet = wire.NewSet(controllerv2.NewUserController)

var adminControllerSet = wire.NewSet(controller.NewAdminController)

var serviceSet = wire.NewSet(
	authSet, services.NewRwUserService, services.NewRwDatabaseService, services.NewMatViewService, services.NewMetricsService, services.NewTenantService, services.NewTenantOperationService, services.NewTenantExtensionService, services.NewPrivateLinkService, services.NewMetaBackupService, services.NewTableService, services.NewSourceService, services.NewSinkService, services.NewSecretService, services.NewLogService, services.NewResourceGroupService, rwsync.New, rwresource.NewRwRelationProvider, rwresource.NewRwDependencyProvider, metastoremanager.NewManager, config.GetResourceDef,
)

var infraSet = wire.NewSet(postgres.NewProvider, provider.NewCloudAgentProvider, network.NewFactory, metrics.NewFactory, logging.NewFactory, metastore.NewFactory, diagnosis.NewRwDiagAgentFactory, rwcrd.NewRwBuilder, rwwait.NewWaiter, rwworkload.NewRisingWaveWorkloadManager, rwdb.LoadKernelSensitiveFields, services.LoadConnectorConfigsList, rwconfig.NewFactory, storage.NewFactory)

var modelSet = wire.NewSet(model.GetConnectionPool, model.NewModelPool, modeltx.NewPoolTxProvider, byoc.NewByocRepository, cloud.NewCloudRepository, configset.NewRepository)

var workflowSet = wire.NewSet(workflow.NewRepository, workflow.NewRegistry)

type BackfillEnableWorkflowService = *backfill.EnableWorkflowService

type BackfillDisableWorkflowService = *backfill.DisableWorkflowService

type CompactionEnableWorkflowService = *compaction.EnableWorkflowService

type BackfillUpdateWorkflowService = *backfill.UpdateWorkflowService

type ConfigUpdateWorkflowService = configupdate.UpdateWorkflowService

type EtcdUpdateWorkflowService = *etcd.ConfigUpdateWorkflowService

// AllWorkflows is a util struct that contains all available workflows.
type AllWorkflows struct {
	provision.IProvisionTenantWorkflowService
	privatelink.IProvisionPrivateLinkWorkflowService
	privatelink.IDeletePrivateLinkWorkflowService

	*delete2.DeleteTenantWorkflowService
	*stopstart.StopStartTenantWorkflowService
	*monitor.MonitorWorkflowService
	ConfigUpdateWorkflowService
	*computecache.UpdateComputeCacheWorkflowService
	*imagetag.ImageTagUpdateWorkflowService
	*expire.ExpireTenantWorkflowService
	EtcdUpdateWorkflowService
	*update.UpdateTenantResourcesService
	*byoc2.PrepareBYOCClusterWorkflowService
	*byoc2.TerminateBYOCClusterWorkflowService
	*byoc2.PostUpdateBYOCClusterWorkflowService
	*byoc2.UpdateBYOCWorkflowService
	*gc.GarbageCollectionWorkflowService
	metabackup.DeleteWorkflowTriggerInterface
	metabackup.BackupWorkflowTriggerInterface
	metabackup.AutoBackupWorkflowTriggerInterface
	metabackup.InPlaceRestoreWorkflowTriggerInterface

	CompactionEnableWorkflowService
	*compaction.DisableWorkflowService
	*compaction.UpdateWorkflowService
	BackfillEnableWorkflowService
	BackfillUpdateWorkflowService
	BackfillDisableWorkflowService
	metamigration.MetaMigrationWorkflowService
	metaupgrade.MetaUpgradeWorkflowService
	resourcegroup.ResourceGroupWorkflowService

	*alert.NotificationsProcessWorkflowService
}

var workflowImplSet = wire.NewSet(
	workflowSet, provision.NewProvisionTenantWorkflowService, privatelink.NewProvisionPrivateLinkWorkflowService, privatelink.NewDeletePrivateLinkWorkflowService, delete2.NewDeleteTenantWorkflowService, stopstart.NewStopStartTenantWorkflowService, monitor.NewMonitorWorkflowService, configupdate.NewUpdateWorkflowService, imagetag.NewImageTagUpdateWorkflowService, expire.NewExpireTenantWorkflowService, etcd.NewConfigUpdateWorkflowService, cluster.NewKubernetesClusterProvider, update.NewTenantResourcesService, byoc2.NewPrepareBYOCClusterWorkflowService, byoc2.NewTerminateBYOCClusterWorkflowService, byoc2.NewPostUpdateBYOCClusterWorkflowService, byoc2.NewUpdateBYOCWorkflowService, gc.NewGarbageCollectionWorkflowService, metabackup.NewMetaBackupWorkflowService, metabackup.NewDeleteSnapshotWorkflowService, metabackup.NewAutoMetaBackupWorkflowService, metabackup.NewInPlaceRestoreWorkflowService, compaction.NewDisableWorkflowService, compaction.NewUpdateWorkflowService, compaction.NewEnableWorkflowService, iceberg.NewCompactionWorkflowService, metamigration.NewMetaMigrationWorkflowService, metaupgrade.NewMetaUpgradeWorkflowService, backfill.NewEnableWorkflowService, backfill.NewUpdateWorkflowService, backfill.NewDisableWorkflowService, computecache.NewUpdateComputeCacheWorkflowService, extensions.NewServerlessCompaction, extensions.NewServerlessBackfill, resourcegroup.NewResourceGroupWorkflowService, alert.NewNotificationsProcessWorkflowService, rwdb.NewRisingWaveClient,
)

type AllComponents struct {
	ApiServer    *UserServer
	AdminServer  *AdminServer
	AllWorkflows *AllWorkflows
}

func fenceWire() error {
	panic("not reach here")
}
