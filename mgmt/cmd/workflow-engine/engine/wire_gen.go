// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package engine

import (
	"github.com/google/wire"
	"github.com/risingwavelabs/risingwave-cloud/internal/account"
	"github.com/risingwavelabs/risingwave-cloud/internal/agent/provider"
	"github.com/risingwavelabs/risingwave-cloud/internal/config"
	"github.com/risingwavelabs/risingwave-cloud/internal/infra/metastore"
	"github.com/risingwavelabs/risingwave-cloud/internal/infra/metrics"
	"github.com/risingwavelabs/risingwave-cloud/internal/infra/network"
	"github.com/risingwavelabs/risingwave-cloud/internal/infra/storage"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/cluster"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/extensions"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/rwconfig"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/rwcrd"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/rwwait"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/rwworkload"
	"github.com/risingwavelabs/risingwave-cloud/internal/managedcluster/byoc"
	"github.com/risingwavelabs/risingwave-cloud/internal/managedcluster/cloud"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/modeltx"
	"github.com/risingwavelabs/risingwave-cloud/internal/postgres"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/configset"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/diagnosis"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/rwdb"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/rwsync"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenant/metastoremanager"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/alert"
	byoc2 "github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/byoc"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/computecache"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/configupdate"
	delete2 "github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/delete"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/etcd"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/expire"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/extensions/backfill"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/extensions/compaction"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/extensions/iceberg"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/gc"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/imagetag"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/metabackup"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/metamigration"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/metaupgrade"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/monitor"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/privatelink"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/provision"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/resourcegroup"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/stopstart"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/update"
)

// Injectors from wire.go:

func InitializeEngine() (*Engine, error) {
	engineConfig := LoadEngineConfig()
	registry := workflow.NewRegistry()
	pool := model.GetConnectionPool()
	modelModel := model.NewModelPool(pool)
	txProvider := modeltx.NewPoolTxProvider(pool)
	repository := workflow.NewRepository(registry, modelModel, txProvider)
	sensitiveFields, err := rwdb.LoadKernelSensitiveFields()
	if err != nil {
		return nil, err
	}
	risingWaveClientInterface := rwdb.NewRisingWaveClient(modelModel, sensitiveFields)
	rwSynchronizer := rwsync.New(modelModel, txProvider)
	cloudAgentProviderInterface := provider.NewCloudAgentProvider(modelModel)
	agentConfig, err := account.LoadAgentConfig()
	if err != nil {
		return nil, err
	}
	agent := account.NewAgent(agentConfig)
	backupWorkflowTriggerInterface := metabackup.NewMetaBackupWorkflowService(repository, cloudAgentProviderInterface, modelModel, txProvider, risingWaveClientInterface, agent)
	deleteWorkflowTriggerInterface := metabackup.NewDeleteSnapshotWorkflowService(repository, cloudAgentProviderInterface, modelModel, txProvider, risingWaveClientInterface)
	autoBackupWorkflowTriggerInterface := metabackup.NewAutoMetaBackupWorkflowService(repository, modelModel, backupWorkflowTriggerInterface, deleteWorkflowTriggerInterface)
	rwDiagAgentFactoryInterface := diagnosis.NewRwDiagAgentFactory()
	monitorWorkflowService := monitor.NewMonitorWorkflowService(repository, modelModel, risingWaveClientInterface, rwSynchronizer, autoBackupWorkflowTriggerInterface, cloudAgentProviderInterface, rwDiagAgentFactoryInterface)
	iKubernetesClusterProvider := cluster.NewKubernetesClusterProvider(modelModel, txProvider, agent)
	factory := network.NewFactory(modelModel)
	factoryInterface := storage.NewFactory(modelModel)
	serverlessCompaction := extensions.NewServerlessCompaction(cloudAgentProviderInterface, factoryInterface)
	deleteTenantWorkflowService := delete2.NewDeleteTenantWorkflowService(repository, iKubernetesClusterProvider, agent, modelModel, txProvider, cloudAgentProviderInterface, factory, factoryInterface, serverlessCompaction)
	metricsFactory := metrics.NewFactory(modelModel)
	expireTenantWorkflowService := expire.NewExpireTenantWorkflowService(repository, iKubernetesClusterProvider, deleteTenantWorkflowService, agent, metricsFactory, serverlessCompaction, modelModel, txProvider, factoryInterface)
	waiter := rwwait.NewWaiter()
	risingWaveWorkloadManager := rwworkload.NewRisingWaveWorkloadManager(waiter)
	stopStartTenantWorkflowService := stopstart.NewStopStartTenantWorkflowService(repository, iKubernetesClusterProvider, cloudAgentProviderInterface, risingWaveWorkloadManager, agent, expireTenantWorkflowService, modelModel, txProvider, risingWaveClientInterface, serverlessCompaction, factoryInterface)
	metastoreFactory := metastore.NewFactory(modelModel, agent)
	iProvisionTenantWorkflowService := provision.NewProvisionTenantWorkflowService(repository, monitorWorkflowService, expireTenantWorkflowService, stopStartTenantWorkflowService, iKubernetesClusterProvider, agent, risingWaveClientInterface, risingWaveWorkloadManager, serverlessCompaction, modelModel, txProvider, cloudAgentProviderInterface, factoryInterface, metastoreFactory)
	iProvisionPrivateLinkWorkflowService := privatelink.NewProvisionPrivateLinkWorkflowService(repository, modelModel, cloudAgentProviderInterface, factory, agent, txProvider)
	iDeletePrivateLinkWorkflowService := privatelink.NewDeletePrivateLinkWorkflowService(repository, modelModel, cloudAgentProviderInterface, factory, agent, txProvider)
	configsetRepository := configset.NewRepository(modelModel, txProvider)
	rwconfigFactory := rwconfig.NewFactory(factoryInterface, configsetRepository)
	rwBuilder := rwcrd.NewRwBuilder(modelModel)
	updateWorkflowService := configupdate.NewUpdateWorkflowService(repository, risingWaveWorkloadManager, agent, modelModel, txProvider, serverlessCompaction, cloudAgentProviderInterface, rwconfigFactory, configsetRepository, rwBuilder, factoryInterface)
	resourcesDef := config.GetResourceDef()
	manager := metastoremanager.NewManager(modelModel, resourcesDef)
	metaMigrationWorkflowService := metamigration.NewMetaMigrationWorkflowService(repository, modelModel, txProvider, cloudAgentProviderInterface, risingWaveWorkloadManager, metastoreFactory, manager)
	imageTagUpdateWorkflowService := imagetag.NewImageTagUpdateWorkflowService(repository, cloudAgentProviderInterface, waiter, metaMigrationWorkflowService, backupWorkflowTriggerInterface, modelModel, txProvider, risingWaveClientInterface, serverlessCompaction, factoryInterface)
	configUpdateWorkflowService := etcd.NewConfigUpdateWorkflowService(repository, waiter, cloudAgentProviderInterface, agent, modelModel, txProvider)
	updateTenantResourcesService := update.NewTenantResourcesService(repository, agent, risingWaveWorkloadManager, rwconfigFactory, cloudAgentProviderInterface, waiter, configsetRepository, serverlessCompaction, modelModel, txProvider, factoryInterface)
	clusterRepository := byoc.NewByocRepository(modelModel, txProvider)
	prepareBYOCClusterWorkflowService := byoc2.NewPrepareBYOCClusterWorkflowService(repository, clusterRepository, modelModel)
	terminateBYOCClusterWorkflowService := byoc2.NewTerminateBYOCClusterWorkflowService(repository, clusterRepository, modelModel)
	postUpdateBYOCClusterWorkflowService := byoc2.NewPostUpdateBYOCClusterWorkflowService(repository, clusterRepository, modelModel)
	updateBYOCWorkflowService := byoc2.NewUpdateBYOCWorkflowService(repository, clusterRepository, cloudAgentProviderInterface, postUpdateBYOCClusterWorkflowService)
	garbageCollectionWorkflowService := gc.NewGarbageCollectionWorkflowService(repository, iKubernetesClusterProvider, agent, cloudAgentProviderInterface, modelModel, txProvider, risingWaveClientInterface)
	inPlaceRestoreWorkflowTriggerInterface := metabackup.NewInPlaceRestoreWorkflowService(repository, cloudAgentProviderInterface, txProvider, risingWaveClientInterface, risingWaveWorkloadManager, iKubernetesClusterProvider, metastoreFactory, factoryInterface, modelModel)
	enableWorkflowService := compaction.NewEnableWorkflowService(repository, agent, modelModel, txProvider, serverlessCompaction, cloudAgentProviderInterface, factoryInterface)
	disableWorkflowService := compaction.NewDisableWorkflowService(repository, agent, modelModel, txProvider, serverlessCompaction, cloudAgentProviderInterface, factoryInterface)
	compactionUpdateWorkflowService := compaction.NewUpdateWorkflowService(repository, agent, modelModel, txProvider, serverlessCompaction, cloudAgentProviderInterface, factoryInterface)
	backfillUpdateWorkflowService := backfill.NewUpdateWorkflowService(repository, agent, modelModel, txProvider, cloudAgentProviderInterface, risingWaveClientInterface)
	metaUpgradeWorkflowService := metaupgrade.NewMetaUpgradeWorkflowService(repository, modelModel, txProvider, cloudAgentProviderInterface, risingWaveWorkloadManager, metastoreFactory)
	backfillEnableWorkflowService := backfill.NewEnableWorkflowService(repository, modelModel, txProvider, cloudAgentProviderInterface, risingWaveClientInterface)
	backfillDisableWorkflowService := backfill.NewDisableWorkflowService(repository, modelModel, txProvider, cloudAgentProviderInterface, risingWaveClientInterface)
	updateComputeCacheWorkflowService := computecache.NewUpdateComputeCacheWorkflowService(repository, rwconfigFactory, waiter, rwBuilder, configsetRepository, modelModel, txProvider, cloudAgentProviderInterface)
	resourceGroupWorkflowService := resourcegroup.NewResourceGroupWorkflowService(repository, modelModel, txProvider, cloudAgentProviderInterface, waiter, rwconfigFactory, rwBuilder, configsetRepository)
	notificationsProcessWorkflowService := alert.NewNotificationsProcessWorkflowService(repository, agent, modelModel, txProvider)
	compactionWorkflowService := iceberg.NewCompactionWorkflowService(repository, modelModel, txProvider, cloudAgentProviderInterface, waiter)
	allWorkflows := &AllWorkflows{
		IProvisionTenantWorkflowService:        iProvisionTenantWorkflowService,
		IProvisionPrivateLinkWorkflowService:   iProvisionPrivateLinkWorkflowService,
		IDeletePrivateLinkWorkflowService:      iDeletePrivateLinkWorkflowService,
		DeleteTenantWorkflowService:            deleteTenantWorkflowService,
		StopStartTenantWorkflowService:         stopStartTenantWorkflowService,
		MonitorWorkflowService:                 monitorWorkflowService,
		ConfigUpdateWorkflowService:            updateWorkflowService,
		ImageTagUpdateWorkflowService:          imageTagUpdateWorkflowService,
		ExpireTenantWorkflowService:            expireTenantWorkflowService,
		EtcdUpdateWorkflowService:              configUpdateWorkflowService,
		UpdateTenantResourcesService:           updateTenantResourcesService,
		PrepareBYOCClusterWorkflowService:      prepareBYOCClusterWorkflowService,
		TerminateBYOCClusterWorkflowService:    terminateBYOCClusterWorkflowService,
		PostUpdateBYOCClusterWorkflowService:   postUpdateBYOCClusterWorkflowService,
		UpdateBYOCWorkflowService:              updateBYOCWorkflowService,
		GarbageCollectionWorkflowService:       garbageCollectionWorkflowService,
		DeleteWorkflowTriggerInterface:         deleteWorkflowTriggerInterface,
		BackupWorkflowTriggerInterface:         backupWorkflowTriggerInterface,
		AutoBackupWorkflowTriggerInterface:     autoBackupWorkflowTriggerInterface,
		InPlaceRestoreWorkflowTriggerInterface: inPlaceRestoreWorkflowTriggerInterface,
		CompactionEnableWorkflowService:        enableWorkflowService,
		CompactionDisableWorkflowService:       disableWorkflowService,
		UpdateWorkflowService:                  compactionUpdateWorkflowService,
		BackfillUpdateWorkflowService:          backfillUpdateWorkflowService,
		MetaMigrationWorkflowService:           metaMigrationWorkflowService,
		MetaUpgradeWorkflowService:             metaUpgradeWorkflowService,
		BackfillEnableWorkflowService:          backfillEnableWorkflowService,
		BackfillDisableWorkflowService:         backfillDisableWorkflowService,
		UpdateComputeCacheWorkflowService:      updateComputeCacheWorkflowService,
		ResourceGroupWorkflowService:           resourceGroupWorkflowService,
		NotificationsProcessWorkflowService:    notificationsProcessWorkflowService,
		CompactionWorkflowService:              compactionWorkflowService,
	}
	engine := NewEngine(engineConfig, repository, modelModel, txProvider, allWorkflows)
	return engine, nil
}

func InitializeAllComponents() (*AllComponents, error) {
	agentConfig, err := account.LoadAgentConfig()
	if err != nil {
		return nil, err
	}
	agent := account.NewAgent(agentConfig)
	pool := model.GetConnectionPool()
	modelModel := model.NewModelPool(pool)
	txProvider := modeltx.NewPoolTxProvider(pool)
	registry := workflow.NewRegistry()
	repository := workflow.NewRepository(registry, modelModel, txProvider)
	sensitiveFields, err := rwdb.LoadKernelSensitiveFields()
	if err != nil {
		return nil, err
	}
	risingWaveClientInterface := rwdb.NewRisingWaveClient(modelModel, sensitiveFields)
	rwSynchronizer := rwsync.New(modelModel, txProvider)
	cloudAgentProviderInterface := provider.NewCloudAgentProvider(modelModel)
	backupWorkflowTriggerInterface := metabackup.NewMetaBackupWorkflowService(repository, cloudAgentProviderInterface, modelModel, txProvider, risingWaveClientInterface, agent)
	deleteWorkflowTriggerInterface := metabackup.NewDeleteSnapshotWorkflowService(repository, cloudAgentProviderInterface, modelModel, txProvider, risingWaveClientInterface)
	autoBackupWorkflowTriggerInterface := metabackup.NewAutoMetaBackupWorkflowService(repository, modelModel, backupWorkflowTriggerInterface, deleteWorkflowTriggerInterface)
	rwDiagAgentFactoryInterface := diagnosis.NewRwDiagAgentFactory()
	monitorWorkflowService := monitor.NewMonitorWorkflowService(repository, modelModel, risingWaveClientInterface, rwSynchronizer, autoBackupWorkflowTriggerInterface, cloudAgentProviderInterface, rwDiagAgentFactoryInterface)
	iKubernetesClusterProvider := cluster.NewKubernetesClusterProvider(modelModel, txProvider, agent)
	factory := network.NewFactory(modelModel)
	factoryInterface := storage.NewFactory(modelModel)
	serverlessCompaction := extensions.NewServerlessCompaction(cloudAgentProviderInterface, factoryInterface)
	deleteTenantWorkflowService := delete2.NewDeleteTenantWorkflowService(repository, iKubernetesClusterProvider, agent, modelModel, txProvider, cloudAgentProviderInterface, factory, factoryInterface, serverlessCompaction)
	metricsFactory := metrics.NewFactory(modelModel)
	expireTenantWorkflowService := expire.NewExpireTenantWorkflowService(repository, iKubernetesClusterProvider, deleteTenantWorkflowService, agent, metricsFactory, serverlessCompaction, modelModel, txProvider, factoryInterface)
	waiter := rwwait.NewWaiter()
	risingWaveWorkloadManager := rwworkload.NewRisingWaveWorkloadManager(waiter)
	stopStartTenantWorkflowService := stopstart.NewStopStartTenantWorkflowService(repository, iKubernetesClusterProvider, cloudAgentProviderInterface, risingWaveWorkloadManager, agent, expireTenantWorkflowService, modelModel, txProvider, risingWaveClientInterface, serverlessCompaction, factoryInterface)
	metastoreFactory := metastore.NewFactory(modelModel, agent)
	iProvisionTenantWorkflowService := provision.NewProvisionTenantWorkflowService(repository, monitorWorkflowService, expireTenantWorkflowService, stopStartTenantWorkflowService, iKubernetesClusterProvider, agent, risingWaveClientInterface, risingWaveWorkloadManager, serverlessCompaction, modelModel, txProvider, cloudAgentProviderInterface, factoryInterface, metastoreFactory)
	iProvisionPrivateLinkWorkflowService := privatelink.NewProvisionPrivateLinkWorkflowService(repository, modelModel, cloudAgentProviderInterface, factory, agent, txProvider)
	iDeletePrivateLinkWorkflowService := privatelink.NewDeletePrivateLinkWorkflowService(repository, modelModel, cloudAgentProviderInterface, factory, agent, txProvider)
	configsetRepository := configset.NewRepository(modelModel, txProvider)
	rwconfigFactory := rwconfig.NewFactory(factoryInterface, configsetRepository)
	rwBuilder := rwcrd.NewRwBuilder(modelModel)
	updateWorkflowService := configupdate.NewUpdateWorkflowService(repository, risingWaveWorkloadManager, agent, modelModel, txProvider, serverlessCompaction, cloudAgentProviderInterface, rwconfigFactory, configsetRepository, rwBuilder, factoryInterface)
	resourcesDef := config.GetResourceDef()
	manager := metastoremanager.NewManager(modelModel, resourcesDef)
	metaMigrationWorkflowService := metamigration.NewMetaMigrationWorkflowService(repository, modelModel, txProvider, cloudAgentProviderInterface, risingWaveWorkloadManager, metastoreFactory, manager)
	imageTagUpdateWorkflowService := imagetag.NewImageTagUpdateWorkflowService(repository, cloudAgentProviderInterface, waiter, metaMigrationWorkflowService, backupWorkflowTriggerInterface, modelModel, txProvider, risingWaveClientInterface, serverlessCompaction, factoryInterface)
	configUpdateWorkflowService := etcd.NewConfigUpdateWorkflowService(repository, waiter, cloudAgentProviderInterface, agent, modelModel, txProvider)
	updateTenantResourcesService := update.NewTenantResourcesService(repository, agent, risingWaveWorkloadManager, rwconfigFactory, cloudAgentProviderInterface, waiter, configsetRepository, serverlessCompaction, modelModel, txProvider, factoryInterface)
	clusterRepository := byoc.NewByocRepository(modelModel, txProvider)
	prepareBYOCClusterWorkflowService := byoc2.NewPrepareBYOCClusterWorkflowService(repository, clusterRepository, modelModel)
	terminateBYOCClusterWorkflowService := byoc2.NewTerminateBYOCClusterWorkflowService(repository, clusterRepository, modelModel)
	postUpdateBYOCClusterWorkflowService := byoc2.NewPostUpdateBYOCClusterWorkflowService(repository, clusterRepository, modelModel)
	updateBYOCWorkflowService := byoc2.NewUpdateBYOCWorkflowService(repository, clusterRepository, cloudAgentProviderInterface, postUpdateBYOCClusterWorkflowService)
	garbageCollectionWorkflowService := gc.NewGarbageCollectionWorkflowService(repository, iKubernetesClusterProvider, agent, cloudAgentProviderInterface, modelModel, txProvider, risingWaveClientInterface)
	inPlaceRestoreWorkflowTriggerInterface := metabackup.NewInPlaceRestoreWorkflowService(repository, cloudAgentProviderInterface, txProvider, risingWaveClientInterface, risingWaveWorkloadManager, iKubernetesClusterProvider, metastoreFactory, factoryInterface, modelModel)
	enableWorkflowService := compaction.NewEnableWorkflowService(repository, agent, modelModel, txProvider, serverlessCompaction, cloudAgentProviderInterface, factoryInterface)
	disableWorkflowService := compaction.NewDisableWorkflowService(repository, agent, modelModel, txProvider, serverlessCompaction, cloudAgentProviderInterface, factoryInterface)
	compactionUpdateWorkflowService := compaction.NewUpdateWorkflowService(repository, agent, modelModel, txProvider, serverlessCompaction, cloudAgentProviderInterface, factoryInterface)
	backfillUpdateWorkflowService := backfill.NewUpdateWorkflowService(repository, agent, modelModel, txProvider, cloudAgentProviderInterface, risingWaveClientInterface)
	metaUpgradeWorkflowService := metaupgrade.NewMetaUpgradeWorkflowService(repository, modelModel, txProvider, cloudAgentProviderInterface, risingWaveWorkloadManager, metastoreFactory)
	backfillEnableWorkflowService := backfill.NewEnableWorkflowService(repository, modelModel, txProvider, cloudAgentProviderInterface, risingWaveClientInterface)
	backfillDisableWorkflowService := backfill.NewDisableWorkflowService(repository, modelModel, txProvider, cloudAgentProviderInterface, risingWaveClientInterface)
	updateComputeCacheWorkflowService := computecache.NewUpdateComputeCacheWorkflowService(repository, rwconfigFactory, waiter, rwBuilder, configsetRepository, modelModel, txProvider, cloudAgentProviderInterface)
	resourceGroupWorkflowService := resourcegroup.NewResourceGroupWorkflowService(repository, modelModel, txProvider, cloudAgentProviderInterface, waiter, rwconfigFactory, rwBuilder, configsetRepository)
	notificationsProcessWorkflowService := alert.NewNotificationsProcessWorkflowService(repository, agent, modelModel, txProvider)
	compactionWorkflowService := iceberg.NewCompactionWorkflowService(repository, modelModel, txProvider, cloudAgentProviderInterface, waiter)
	allWorkflows := &AllWorkflows{
		IProvisionTenantWorkflowService:        iProvisionTenantWorkflowService,
		IProvisionPrivateLinkWorkflowService:   iProvisionPrivateLinkWorkflowService,
		IDeletePrivateLinkWorkflowService:      iDeletePrivateLinkWorkflowService,
		DeleteTenantWorkflowService:            deleteTenantWorkflowService,
		StopStartTenantWorkflowService:         stopStartTenantWorkflowService,
		MonitorWorkflowService:                 monitorWorkflowService,
		ConfigUpdateWorkflowService:            updateWorkflowService,
		ImageTagUpdateWorkflowService:          imageTagUpdateWorkflowService,
		ExpireTenantWorkflowService:            expireTenantWorkflowService,
		EtcdUpdateWorkflowService:              configUpdateWorkflowService,
		UpdateTenantResourcesService:           updateTenantResourcesService,
		PrepareBYOCClusterWorkflowService:      prepareBYOCClusterWorkflowService,
		TerminateBYOCClusterWorkflowService:    terminateBYOCClusterWorkflowService,
		PostUpdateBYOCClusterWorkflowService:   postUpdateBYOCClusterWorkflowService,
		UpdateBYOCWorkflowService:              updateBYOCWorkflowService,
		GarbageCollectionWorkflowService:       garbageCollectionWorkflowService,
		DeleteWorkflowTriggerInterface:         deleteWorkflowTriggerInterface,
		BackupWorkflowTriggerInterface:         backupWorkflowTriggerInterface,
		AutoBackupWorkflowTriggerInterface:     autoBackupWorkflowTriggerInterface,
		InPlaceRestoreWorkflowTriggerInterface: inPlaceRestoreWorkflowTriggerInterface,
		CompactionEnableWorkflowService:        enableWorkflowService,
		CompactionDisableWorkflowService:       disableWorkflowService,
		UpdateWorkflowService:                  compactionUpdateWorkflowService,
		BackfillUpdateWorkflowService:          backfillUpdateWorkflowService,
		MetaMigrationWorkflowService:           metaMigrationWorkflowService,
		MetaUpgradeWorkflowService:             metaUpgradeWorkflowService,
		BackfillEnableWorkflowService:          backfillEnableWorkflowService,
		BackfillDisableWorkflowService:         backfillDisableWorkflowService,
		UpdateComputeCacheWorkflowService:      updateComputeCacheWorkflowService,
		ResourceGroupWorkflowService:           resourceGroupWorkflowService,
		NotificationsProcessWorkflowService:    notificationsProcessWorkflowService,
		CompactionWorkflowService:              compactionWorkflowService,
	}
	allComponents := &AllComponents{
		Agent:        agent,
		Model:        modelModel,
		TxProvider:   txProvider,
		Repository:   repository,
		AllWorkflows: allWorkflows,
	}
	return allComponents, nil
}

// wire.go:

var accountSet = wire.NewSet(account.NewAgent, account.LoadAgentConfig)

var modelSet = wire.NewSet(model.GetConnectionPool, model.NewModelPool, modeltx.NewPoolTxProvider, byoc.NewByocRepository, cloud.NewCloudRepository, configset.NewRepository)

var workflowSet = wire.NewSet(workflow.NewRepository, workflow.NewRegistry)

var infraSet = wire.NewSet(postgres.NewProvider, provider.NewCloudAgentProvider, network.NewFactory, metrics.NewFactory, metastore.NewFactory, diagnosis.NewRwDiagAgentFactory, rwcrd.NewRwBuilder, rwwait.NewWaiter, rwworkload.NewRisingWaveWorkloadManager, metastoremanager.NewManager, config.GetResourceDef, rwdb.LoadKernelSensitiveFields, rwconfig.NewFactory, storage.NewFactory)

type BackfillEnableWorkflowService = *backfill.EnableWorkflowService

type BackfillUpdateWorkflowService = *backfill.UpdateWorkflowService

type CompactionEnableWorkflowService = *compaction.EnableWorkflowService

type BackfillDisableWorkflowService = *backfill.DisableWorkflowService

type CompactionDisableWorkflowService = *compaction.DisableWorkflowService

type ConfigUpdateWorkflowService = configupdate.UpdateWorkflowService

type EtcdUpdateWorkflowService = *etcd.ConfigUpdateWorkflowService

type AllWorkflows struct {
	provision.IProvisionTenantWorkflowService
	privatelink.IProvisionPrivateLinkWorkflowService
	privatelink.IDeletePrivateLinkWorkflowService

	*delete2.DeleteTenantWorkflowService
	*stopstart.StopStartTenantWorkflowService
	*monitor.MonitorWorkflowService
	ConfigUpdateWorkflowService
	*imagetag.ImageTagUpdateWorkflowService
	*expire.ExpireTenantWorkflowService
	EtcdUpdateWorkflowService
	*update.UpdateTenantResourcesService
	*byoc2.PrepareBYOCClusterWorkflowService
	*byoc2.TerminateBYOCClusterWorkflowService
	*byoc2.PostUpdateBYOCClusterWorkflowService
	*byoc2.UpdateBYOCWorkflowService
	*gc.GarbageCollectionWorkflowService
	metabackup.DeleteWorkflowTriggerInterface
	metabackup.BackupWorkflowTriggerInterface
	metabackup.AutoBackupWorkflowTriggerInterface
	metabackup.InPlaceRestoreWorkflowTriggerInterface

	CompactionEnableWorkflowService
	CompactionDisableWorkflowService
	*compaction.UpdateWorkflowService
	BackfillUpdateWorkflowService
	metamigration.MetaMigrationWorkflowService
	metaupgrade.MetaUpgradeWorkflowService

	BackfillEnableWorkflowService
	BackfillDisableWorkflowService
	*computecache.UpdateComputeCacheWorkflowService
	resourcegroup.ResourceGroupWorkflowService

	*alert.NotificationsProcessWorkflowService
	*iceberg.CompactionWorkflowService
}

var workflowImplSet = wire.NewSet(
	workflowSet,
	infraSet, provision.NewProvisionTenantWorkflowService, privatelink.NewProvisionPrivateLinkWorkflowService, privatelink.NewDeletePrivateLinkWorkflowService, delete2.NewDeleteTenantWorkflowService, stopstart.NewStopStartTenantWorkflowService, monitor.NewMonitorWorkflowService, configupdate.NewUpdateWorkflowService, imagetag.NewImageTagUpdateWorkflowService, expire.NewExpireTenantWorkflowService, etcd.NewConfigUpdateWorkflowService, update.NewTenantResourcesService, byoc2.NewPrepareBYOCClusterWorkflowService, byoc2.NewTerminateBYOCClusterWorkflowService, byoc2.NewPostUpdateBYOCClusterWorkflowService, byoc2.NewUpdateBYOCWorkflowService, gc.NewGarbageCollectionWorkflowService, metabackup.NewMetaBackupWorkflowService, metabackup.NewDeleteSnapshotWorkflowService, metabackup.NewAutoMetaBackupWorkflowService, metabackup.NewInPlaceRestoreWorkflowService, compaction.NewEnableWorkflowService, compaction.NewDisableWorkflowService, compaction.NewUpdateWorkflowService, iceberg.NewCompactionWorkflowService, metamigration.NewMetaMigrationWorkflowService, metaupgrade.NewMetaUpgradeWorkflowService, backfill.NewEnableWorkflowService, backfill.NewUpdateWorkflowService, backfill.NewDisableWorkflowService, computecache.NewUpdateComputeCacheWorkflowService, resourcegroup.NewResourceGroupWorkflowService, alert.NewNotificationsProcessWorkflowService, accountSet, extensions.NewServerlessCompaction, cluster.NewKubernetesClusterProvider, rwdb.NewRisingWaveClient, rwsync.New,
)

type AllComponents struct {
	account.Agent

	*model.Model
	modeltx.TxProvider

	*workflow.Repository
	*AllWorkflows
}

func fenceWire() error {
	panic("not reach here")
}
