//go:build !ut

package internal

import (
	"encoding/json"
	"testing"

	"github.com/jinzhu/copier"
	"github.com/stretchr/testify/require"
)

type A struct {
	Array *[]string `json:"array,omitempty"`
}

type B struct {
	Array *[]string `json:"array,omitempty"`
}

func TestA(t *testing.T) {
	a := &A{Array: nil}
	b := &B{Array: &[]string{}}

	err := copier.Copy(b, a)
	require.NoError(t, err)

	jsonA, err := json.Marshal(a)
	require.NoError(t, err)
	jsonB, err := json.<PERSON>(b)
	require.NoError(t, err)

	require.JSONEq(t, string(jsonA), string(jsonB))
}
