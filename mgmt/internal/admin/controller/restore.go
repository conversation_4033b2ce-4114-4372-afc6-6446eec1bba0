package controller

import (
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"github.com/risingwavelabs/eris"
	"github.com/risingwavelabs/risingwave-cloud/internal/config"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/version"
	"github.com/risingwavelabs/risingwave-cloud/shared/ginx"
	"github.com/risingwavelabs/risingwave-cloud/shared/namespace"
	"github.com/risingwavelabs/risingwave-cloud/shared/spec/mgmt_admin"
)

func (controller *AdminController) PostTenantTenantIdBackupSnapshotIdRestore(c *gin.Context, tenantID uint64, snapshotID uuid.UUID) {
	var req mgmt_admin.PostTenantRestoreRequestBody
	if err := c.BindJSON(&req); err != nil {
		ginx.InvalidArgumentError(c, err)
		return
	}

	oldTenant, err := controller.tenantService.GetTenantByID(c, tenantID)
	if err != nil {
		if eris.GetCode(err) == eris.CodeNotFound {
			ginx.NotFoundError(c, err)
			return
		}
		ginx.InternalError(c, err)
		return
	}

	supported, err := version.IsRestoreSupported(oldTenant.ImageTag)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}
	if !supported {
		ginx.FailedPreconditionf(c, "version %s not supported for restore", oldTenant.ImageTag)
		return
	}

	newTenantName := req.NewTenantName
	if !namespace.IsValidResourceName(newTenantName) {
		ginx.InvalidArgument(c, "The cluster name must only contain letters, numbers, and hyphens, with a length of no more than 32 characters.")
		return
	}
	check, err := controller.accountAgent.CheckTenantAvailability(c, oldTenant.OrgID, 0, oldTenant.TierID, newTenantName, config.Conf.Region)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}
	if !check.Available {
		ginx.FailedPrecondition(c, check.Msg)
		return
	}

	extSpecs, err := controller.tenantExtensionService.GetSpecTenantExtensionsByTenantID(c, oldTenant.ID)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	rtn, err := controller.tenantService.RestoreTenant(c, oldTenant, newTenantName, snapshotID, extSpecs)
	if err != nil {
		//nolint:exhaustive
		switch eris.GetCode(err) {
		case eris.CodeInvalidArgument:
			ginx.InvalidArgumentError(c, err)
		case eris.CodeNotFound:
			ginx.NotFoundError(c, err)
		case eris.CodeAlreadyExists:
			ginx.AlreadyExistsError(c, err)
		case eris.CodeFailedPrecondition:
			ginx.FailedPreconditionError(c, err)
		default:
			ginx.InternalError(c, err)
		}
		return
	}
	c.JSON(202, &rtn)
}

func (controller *AdminController) PostTenantTenantIdBackupSnapshotIdInPlaceRestore(c *gin.Context, tenantID uint64, snapshotID uuid.UUID) {
	workflowID, err := controller.metaBackupService.InPlaceRestore(c, tenantID, snapshotID)
	if err != nil {
		if eris.GetCode(err) == eris.CodeFailedPrecondition {
			ginx.FailedPreconditionError(c, err)
			return
		} else if eris.GetCode(err) == eris.CodeNotFound {
			ginx.NotFoundError(c, err)
			return
		}
		ginx.AdminInternalError(c, err)
		return
	}
	c.JSON(202, mgmt_admin.WorkflowIdResponseBody{WorkflowId: *workflowID})
}
