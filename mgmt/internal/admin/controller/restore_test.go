package controller

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"

	"github.com/risingwavelabs/eris"

	account_mock "github.com/risingwavelabs/risingwave-cloud/internal/account/mock"
	"github.com/risingwavelabs/risingwave-cloud/internal/apiservice/dto"
	mock_service "github.com/risingwavelabs/risingwave-cloud/internal/apiservice/services/mock"
	"github.com/risingwavelabs/risingwave-cloud/internal/config"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	mockmodel "github.com/risingwavelabs/risingwave-cloud/internal/model/mock"
	"github.com/risingwavelabs/risingwave-cloud/shared/spec/acc_admin"
	"github.com/risingwavelabs/risingwave-cloud/shared/spec/mgmt_admin"
	"github.com/risingwavelabs/risingwave-cloud/shared/tenant/tier"
)

func TestRestore(t *testing.T) {
	type testCase struct {
		name       string
		setupMocks func(
			c *gin.Context,
			orgID, snapshotID uuid.UUID,
			newTenantName string,
			mockQuerier *mockmodel.MockQuerier,
			mockTenantSvc *mock_service.MockTenantServiceInterface,
			mockMetaBackupSvc *mock_service.MockMetaBackupService,
			mockAccountAgent *account_mock.MockAgent,
			mockTenantExtensionSvc *mock_service.MockTenantExtensionService,

		) *AdminController
		expectedStatus int
		newTenantName  string
	}

	orgID := uuid.Must(uuid.NewRandom())
	snapshotID := uuid.Must(uuid.NewRandom())

	testCases := []testCase{
		{
			name: "successful restore",
			setupMocks: func(
				c *gin.Context,
				orgID,
				snapshotID uuid.UUID,
				newTenantName string,
				mockQuerier *mockmodel.MockQuerier,
				mockTenantSvc *mock_service.MockTenantServiceInterface,
				mockMetaBackupSvc *mock_service.MockMetaBackupService,
				mockAccountAgent *account_mock.MockAgent,
				mockTenantExtensionSvc *mock_service.MockTenantExtensionService,
			) *AdminController {
				oldTenant := model.Tenant{
					ID:       1,
					TierID:   string(tier.Invited),
					ImageTag: "v2.3.0",
					OrgID:    orgID,
				}

				config.Conf.Region = "ap-southeast-1"
				mockTenantSvc.EXPECT().GetTenantByID(c, uint64(1)).Return(oldTenant, nil)
				mockAccountAgent.EXPECT().
					CheckTenantAvailability(c, orgID, uint64(0), oldTenant.TierID, newTenantName, config.Conf.Region).
					Return(&acc_admin.AvailabilityResponse{Available: true}, nil)
				mockTenantExtensionSvc.EXPECT().GetSpecTenantExtensionsByTenantID(c, oldTenant.ID).Return(nil, nil)
				mockTenantSvc.EXPECT().RestoreTenant(c, oldTenant, newTenantName, snapshotID, nil).Return(&dto.Tenant{}, nil)

				controller := &AdminController{
					tenantService:          mockTenantSvc,
					tenantExtensionService: mockTenantExtensionSvc,
					metaBackupService:      mockMetaBackupSvc,
					accountAgent:           mockAccountAgent,
					model:                  model.NewModel(mockQuerier),
				}

				return controller
			},
			expectedStatus: http.StatusAccepted,
		},
		{
			name: "failed precondition - invalid new tenant name",
			setupMocks: func(
				c *gin.Context,
				orgID,
				_ uuid.UUID,
				_ string,
				mockQuerier *mockmodel.MockQuerier,
				mockTenantSvc *mock_service.MockTenantServiceInterface,
				mockMetaBackupSvc *mock_service.MockMetaBackupService,
				mockAccountAgent *account_mock.MockAgent,
				mockTenantExtensionSvc *mock_service.MockTenantExtensionService,
			) *AdminController {
				oldTenant := model.Tenant{
					ID:       1,
					TierID:   string(tier.Invited),
					ImageTag: "v2.3.0",
					OrgID:    orgID,
				}

				config.Conf.Region = "ap-southeast-1"
				mockTenantSvc.EXPECT().GetTenantByID(c, uint64(1)).Return(oldTenant, nil)
				controller := &AdminController{
					tenantService:          mockTenantSvc,
					tenantExtensionService: mockTenantExtensionSvc,
					metaBackupService:      mockMetaBackupSvc,
					accountAgent:           mockAccountAgent,
					model:                  model.NewModel(mockQuerier),
				}
				return controller
			},
			expectedStatus: http.StatusBadRequest,
			newTenantName:  "&&&",
		},
		{
			name: "failed precondition - version not supported",
			setupMocks: func(
				c *gin.Context,
				orgID,
				_ uuid.UUID,
				_ string,
				mockQuerier *mockmodel.MockQuerier,
				mockTenantSvc *mock_service.MockTenantServiceInterface,
				mockMetaBackupSvc *mock_service.MockMetaBackupService,
				mockAccountAgent *account_mock.MockAgent,
				mockTenantExtensionSvc *mock_service.MockTenantExtensionService,
			) *AdminController {
				oldTenant := model.Tenant{
					ID:       1,
					TierID:   string(tier.Invited),
					ImageTag: "v2.2.0",
					OrgID:    orgID,
				}

				config.Conf.Region = "ap-southeast-1"
				mockTenantSvc.EXPECT().GetTenantByID(c, uint64(1)).Return(oldTenant, nil)
				controller := &AdminController{
					tenantService:          mockTenantSvc,
					tenantExtensionService: mockTenantExtensionSvc,
					metaBackupService:      mockMetaBackupSvc,
					accountAgent:           mockAccountAgent,
					model:                  model.NewModel(mockQuerier),
				}
				return controller
			},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "failed precondition - name unavailable",
			setupMocks: func(
				c *gin.Context,
				orgID,
				_ uuid.UUID,
				newTenantName string,
				mockQuerier *mockmodel.MockQuerier,
				mockTenantSvc *mock_service.MockTenantServiceInterface,
				mockMetaBackupSvc *mock_service.MockMetaBackupService,
				mockAccountAgent *account_mock.MockAgent,
				mockTenantExtensionSvc *mock_service.MockTenantExtensionService,
			) *AdminController {
				oldTenant := model.Tenant{
					ID:       1,
					TierID:   string(tier.Invited),
					ImageTag: "v2.3.0",
					OrgID:    orgID,
				}

				config.Conf.Region = "ap-southeast-1"
				mockTenantSvc.EXPECT().GetTenantByID(c, uint64(1)).Return(oldTenant, nil)
				mockAccountAgent.EXPECT().
					CheckTenantAvailability(c, orgID, uint64(0), oldTenant.TierID, newTenantName, config.Conf.Region).
					Return(&acc_admin.AvailabilityResponse{Available: false}, nil)
				controller := &AdminController{
					tenantService:          mockTenantSvc,
					tenantExtensionService: mockTenantExtensionSvc,
					metaBackupService:      mockMetaBackupSvc,
					accountAgent:           mockAccountAgent,
					model:                  model.NewModel(mockQuerier),
				}
				return controller
			},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "internal service error - failed to get extension specs",
			setupMocks: func(
				c *gin.Context,
				orgID,
				_ uuid.UUID,
				newTenantName string,
				mockQuerier *mockmodel.MockQuerier,
				mockTenantSvc *mock_service.MockTenantServiceInterface,
				mockMetaBackupSvc *mock_service.MockMetaBackupService,
				mockAccountAgent *account_mock.MockAgent,
				mockTenantExtensionSvc *mock_service.MockTenantExtensionService,
			) *AdminController {
				oldTenant := model.Tenant{
					ID:       1,
					TierID:   string(tier.Invited),
					ImageTag: "v2.3.0",
					OrgID:    orgID,
				}

				config.Conf.Region = "ap-southeast-1"
				mockTenantSvc.EXPECT().GetTenantByID(c, uint64(1)).Return(oldTenant, nil)
				mockAccountAgent.EXPECT().
					CheckTenantAvailability(c, orgID, uint64(0), oldTenant.TierID, newTenantName, config.Conf.Region).
					Return(&acc_admin.AvailabilityResponse{Available: true}, nil)
				mockTenantExtensionSvc.EXPECT().GetSpecTenantExtensionsByTenantID(c, oldTenant.ID).Return(nil, eris.New("failed to get specs"))

				controller := &AdminController{
					tenantService:          mockTenantSvc,
					tenantExtensionService: mockTenantExtensionSvc,
					metaBackupService:      mockMetaBackupSvc,
					accountAgent:           mockAccountAgent,
					model:                  model.NewModel(mockQuerier),
				}

				return controller
			},
			expectedStatus: http.StatusInternalServerError,
		},
		{
			name: "unsuccessful restore - invalid arg",
			setupMocks: func(
				c *gin.Context,
				orgID,
				snapshotID uuid.UUID,
				newTenantName string,
				mockQuerier *mockmodel.MockQuerier,
				mockTenantSvc *mock_service.MockTenantServiceInterface,
				mockMetaBackupSvc *mock_service.MockMetaBackupService,
				mockAccountAgent *account_mock.MockAgent,
				mockTenantExtensionSvc *mock_service.MockTenantExtensionService,
			) *AdminController {
				oldTenant := model.Tenant{
					ID:       1,
					TierID:   string(tier.Invited),
					ImageTag: "v2.3.0",
					OrgID:    orgID,
				}

				config.Conf.Region = "ap-southeast-1"
				mockTenantSvc.EXPECT().GetTenantByID(c, uint64(1)).Return(oldTenant, nil)
				mockAccountAgent.EXPECT().
					CheckTenantAvailability(c, orgID, uint64(0), oldTenant.TierID, newTenantName, config.Conf.Region).
					Return(&acc_admin.AvailabilityResponse{Available: true}, nil)
				mockTenantExtensionSvc.EXPECT().GetSpecTenantExtensionsByTenantID(c, oldTenant.ID).Return(nil, nil)
				mockTenantSvc.EXPECT().RestoreTenant(c, oldTenant, newTenantName, snapshotID, nil).Return(nil, eris.New("").WithCode(eris.CodeInvalidArgument))

				controller := &AdminController{
					tenantService:          mockTenantSvc,
					tenantExtensionService: mockTenantExtensionSvc,
					metaBackupService:      mockMetaBackupSvc,
					accountAgent:           mockAccountAgent,
					model:                  model.NewModel(mockQuerier),
				}

				return controller
			},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "unsuccessful restore - aleady exists",
			setupMocks: func(
				c *gin.Context,
				orgID,
				snapshotID uuid.UUID,
				newTenantName string,
				mockQuerier *mockmodel.MockQuerier,
				mockTenantSvc *mock_service.MockTenantServiceInterface,
				mockMetaBackupSvc *mock_service.MockMetaBackupService,
				mockAccountAgent *account_mock.MockAgent,
				mockTenantExtensionSvc *mock_service.MockTenantExtensionService,
			) *AdminController {
				oldTenant := model.Tenant{
					ID:       1,
					TierID:   string(tier.Invited),
					ImageTag: "v2.3.0",
					OrgID:    orgID,
				}

				config.Conf.Region = "ap-southeast-1"
				mockTenantSvc.EXPECT().GetTenantByID(c, uint64(1)).Return(oldTenant, nil)
				mockAccountAgent.EXPECT().
					CheckTenantAvailability(c, orgID, uint64(0), oldTenant.TierID, newTenantName, config.Conf.Region).
					Return(&acc_admin.AvailabilityResponse{Available: true}, nil)
				mockTenantExtensionSvc.EXPECT().GetSpecTenantExtensionsByTenantID(c, oldTenant.ID).Return(nil, nil)
				mockTenantSvc.EXPECT().RestoreTenant(c, oldTenant, newTenantName, snapshotID, nil).Return(nil, eris.New("").WithCode(eris.CodeAlreadyExists))

				controller := &AdminController{
					tenantService:          mockTenantSvc,
					tenantExtensionService: mockTenantExtensionSvc,
					metaBackupService:      mockMetaBackupSvc,
					accountAgent:           mockAccountAgent,
					model:                  model.NewModel(mockQuerier),
				}

				return controller
			},
			expectedStatus: http.StatusConflict,
		},
		{
			name: "unsuccessful restore - aleady exists",
			setupMocks: func(
				c *gin.Context,
				orgID,
				snapshotID uuid.UUID,
				newTenantName string,
				mockQuerier *mockmodel.MockQuerier,
				mockTenantSvc *mock_service.MockTenantServiceInterface,
				mockMetaBackupSvc *mock_service.MockMetaBackupService,
				mockAccountAgent *account_mock.MockAgent,
				mockTenantExtensionSvc *mock_service.MockTenantExtensionService,
			) *AdminController {
				oldTenant := model.Tenant{
					ID:       1,
					TierID:   string(tier.Invited),
					ImageTag: "v2.3.0",
					OrgID:    orgID,
				}

				config.Conf.Region = "ap-southeast-1"
				mockTenantSvc.EXPECT().GetTenantByID(c, uint64(1)).Return(oldTenant, nil)
				mockAccountAgent.EXPECT().
					CheckTenantAvailability(c, orgID, uint64(0), oldTenant.TierID, newTenantName, config.Conf.Region).
					Return(&acc_admin.AvailabilityResponse{Available: true}, nil)
				mockTenantExtensionSvc.EXPECT().GetSpecTenantExtensionsByTenantID(c, oldTenant.ID).Return(nil, nil)
				mockTenantSvc.EXPECT().RestoreTenant(c, oldTenant, newTenantName, snapshotID, nil).Return(nil, eris.New("").WithCode(eris.CodeNotFound))

				controller := &AdminController{
					tenantService:          mockTenantSvc,
					tenantExtensionService: mockTenantExtensionSvc,
					metaBackupService:      mockMetaBackupSvc,
					accountAgent:           mockAccountAgent,
					model:                  model.NewModel(mockQuerier),
				}

				return controller
			},
			expectedStatus: http.StatusNotFound,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			newTenantName := "testrestore"
			if tc.newTenantName != "" {
				newTenantName = tc.newTenantName
			}
			req := mgmt_admin.PostTenantRestoreRequestBody{
				NewTenantName: newTenantName,
			}
			// body, err := json.Marshal(req)
			// require.NoError(t, err)

			rec := httptest.NewRecorder()
			c, _ := buildTestContext(rec, req)

			mockQuerier := mockmodel.NewMockQuerier(ctrl)
			mockTenantSvc := mock_service.NewMockTenantServiceInterface(ctrl)
			mockTenantExtSvc := mock_service.NewMockTenantExtensionService(ctrl)
			mockMetaBackupSvc := mock_service.NewMockMetaBackupService(ctrl)
			mockAccountAgent := account_mock.NewMockAgent(ctrl)
			controller := tc.setupMocks(c, orgID, snapshotID, newTenantName, mockQuerier, mockTenantSvc, mockMetaBackupSvc, mockAccountAgent, mockTenantExtSvc)
			controller.PostTenantTenantIdBackupSnapshotIdRestore(c, uint64(1), snapshotID)
			assert.Equal(t, tc.expectedStatus, rec.Code)
		})
	}
}

func TestInPlaceRestore(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	workflowID := uuid.New()

	testCases := []struct {
		name                        string
		rtnErr                      error
		rtnWorkflowID               *uuid.UUID
		googleAPIStandardStatusCode int
	}{
		{name: "normal case", googleAPIStandardStatusCode: 202, rtnErr: nil, rtnWorkflowID: &workflowID},
		{name: "invalid argument - snapshot does not exist", googleAPIStandardStatusCode: 404, rtnErr: eris.New("").WithCode(eris.CodeNotFound), rtnWorkflowID: nil},
		{name: "invalid argument - tenant in wrong state", googleAPIStandardStatusCode: 400, rtnErr: eris.New("").WithCode(eris.CodeFailedPrecondition), rtnWorkflowID: nil},
		{name: "internal serivce error", googleAPIStandardStatusCode: 500, rtnErr: eris.New("").WithCode(eris.CodeInternal), rtnWorkflowID: nil},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			t.Logf("test case: %s, expected googleAPIStandardStatusCode: %d", testCase.name, testCase.googleAPIStandardStatusCode)

			metaBackupSvc := mock_service.NewMockMetaBackupService(ctrl)
			rec := httptest.NewRecorder()

			controller := &AdminController{
				metaBackupService: metaBackupSvc,
			}

			tenantID := uint64(1)
			snapshotID := uuid.Must(uuid.NewRandom())
			ctx, _ := gin.CreateTestContext(rec)

			metaBackupSvc.
				EXPECT().
				InPlaceRestore(gomock.Any(), tenantID, snapshotID).
				Return(testCase.rtnWorkflowID, testCase.rtnErr)

			controller.PostTenantTenantIdBackupSnapshotIdInPlaceRestore(ctx, tenantID, snapshotID)

			assert.Equal(t, testCase.googleAPIStandardStatusCode, rec.Code)
			var resp mgmt_admin.WorkflowIdResponseBody
			err := json.Unmarshal(rec.Body.Bytes(), &resp)
			require.NoError(t, err)
			if testCase.rtnWorkflowID != nil {
				assert.Equal(t, *testCase.rtnWorkflowID, resp.WorkflowId)
			}
		})
	}
}
