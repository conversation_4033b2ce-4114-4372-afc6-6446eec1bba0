package controller

import (
	"net/http"

	"github.com/BurntSushi/toml"
	"github.com/gin-gonic/gin"
	"github.com/risingwavelabs/eris"
	"k8s.io/utils/ptr"

	apierr "github.com/risingwavelabs/risingwave-cloud/internal/apiservice/errors"
	"github.com/risingwavelabs/risingwave-cloud/internal/apiservice/services"
	"github.com/risingwavelabs/risingwave-cloud/internal/config"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/extensions/compaction"
	"github.com/risingwavelabs/risingwave-cloud/shared/ginx"
	"github.com/risingwavelabs/risingwave-cloud/shared/spec/mgmt_admin"
)

func (controller *AdminController) PostTenantTenantIdExtensionsCompactionEnable(c *gin.Context, tenantID uint64) {
	tenant, err := controller.tenantService.GetTenantByID(c, tenantID)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err, tenantID)
		return
	}

	if tenant.Resources.IsStandalone() {
		ginx.FailedPreconditionf(c, "extension compaction isn't supported for standalone deployment")
		return
	}

	runningWorkflow, err := controller.workflowRepo.GetRunningWorkflowOfTenant(c, compaction.EnableWorkflowType, tenantID)
	if err != nil {
		ginx.AdminInternalError(c, err)
		return
	}

	if runningWorkflow != nil {
		ginx.FailedPreconditionf(c, "Illegal status, already exist running workflow")
		return
	}

	if tenant.Status != model.TenantStatusRunning {
		ginx.FailedPreconditionf(c, "Illegal status, tenant should be Running")
		return
	}

	// check extension status
	extension, err := controller.tenantExtensionService.GetTenantExtensionByTenantIDAndResourceType(c, tenantID, model.ExtensionsResourceTypeCompaction)

	// If extension not exist, will create it in enable workflow
	if err != nil && !eris.Is(err, apierr.ErrTenantExtensionNotExist) {
		ginx.AdminInternalError(c, err)
		return
	}

	// check extension status whether can enable
	if err == nil && services.CanNotEnableExtension(extension) {
		ginx.FailedPreconditionf(c, "Illegal status: %s, cannot enable extensions compaction", extension.Status)
		return
	}

	workflowID, err := controller.tenantExtensionService.EnableExtensionsCompaction(c, tenantID)
	if err != nil {
		ginx.AdminInternalError(c, err)
		return
	}

	c.JSON(202, mgmt_admin.WorkflowIdResponseBody{
		WorkflowId: workflowID,
	})
}

func (controller *AdminController) PostTenantTenantIdExtensionsCompactionDisable(c *gin.Context, tenantID uint64) {
	tenant, err := controller.tenantService.GetTenantByID(c, tenantID)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err, tenantID)
		return
	}

	if tenant.Resources.IsStandalone() {
		ginx.FailedPreconditionf(c, "extension compaction isn't supported for standalone deployment")
		return
	}

	runningWorkflow, err := controller.workflowRepo.GetRunningWorkflowOfTenant(c, compaction.DisableWorkflowType, tenantID)
	if err != nil {
		ginx.AdminInternalError(c, err)
		return
	}

	if runningWorkflow != nil {
		ginx.FailedPreconditionf(c, "Illegal status, already exist running workflow")
		return
	}

	if tenant.Status != model.TenantStatusRunning {
		ginx.FailedPreconditionf(c, "Illegal status, tenant should be Running")
		return
	}

	extension, err := controller.tenantExtensionService.GetTenantExtensionByTenantIDAndResourceType(c, tenantID, model.ExtensionsResourceTypeCompaction)
	if err != nil {
		ginx.AdminInternalError(c, err)
		return
	}
	if extension.Status != model.ExtensionStatusRunning {
		ginx.FailedPreconditionf(c, "Illegal status, cannot disable extensions compaction")
		return
	}

	workflowID, err := controller.tenantExtensionService.DisableExtensionsCompaction(c, tenantID)
	if err != nil {
		ginx.AdminInternalError(c, err)
		return
	}

	c.JSON(202, mgmt_admin.WorkflowIdResponseBody{
		WorkflowId: workflowID,
	})
}

func (controller *AdminController) PostTenantTenantIdExtensionsCompactionUpdate(c *gin.Context, tenantID uint64) {
	tenant, err := controller.tenantService.GetTenantByID(c, tenantID)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err, tenantID)
		return
	}

	var req mgmt_admin.PostTenantTenantIdExtensionsCompactionUpdateJSONRequestBody
	if err := c.BindJSON(&req); err != nil {
		ginx.InvalidArgumentError(c, err)
		return
	}

	if tenant.Resources.IsStandalone() {
		ginx.FailedPreconditionf(c, "extension compaction isn't supported for standalone deployment")
		return
	}

	if tenant.Status != model.TenantStatusRunning {
		ginx.FailedPreconditionf(c, "Illegal status, tenant should be Running")
		return
	}

	runningWorkflow, err := controller.workflowRepo.GetRunningWorkflowOfTenant(c, compaction.DisableWorkflowType, tenantID)
	if err != nil {
		ginx.AdminInternalError(c, err)
		return
	}

	if runningWorkflow != nil {
		ginx.FailedPreconditionf(c, "Illegal status, already exist running workflow")
		return
	}

	// check extension status
	extension, err := controller.tenantExtensionService.GetTenantExtensionByTenantIDAndResourceType(c, tenantID, model.ExtensionsResourceTypeCompaction)
	if err != nil {
		ginx.AdminInternalError(c, err)
		return
	}
	if extension.Status != model.ExtensionStatusRunning {
		ginx.FailedPreconditionf(c, "Illegal status, cannot update extensions compaction")
		return
	}

	// TODO: update the request body.
	option := tenantext.ServerlessCompactionParams{
		MaxConcurrency: ptr.To(req.Scaler.MaxReplicas),
	}

	workflowID, err := controller.tenantExtensionService.UpdateExtensionsCompaction(c, tenantID, option)
	if err != nil {
		ginx.AdminInternalError(c, err)
		return
	}

	c.JSON(202, mgmt_admin.WorkflowIdResponseBody{
		WorkflowId: workflowID,
	})
}

func (controller *AdminController) GetTenantTenantIdExtensionsCompactionParameters(c *gin.Context, tenantID uint64) {
	tenant, err := controller.tenantService.GetTenantByID(c, tenantID)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err, tenantID)
		return
	}

	if tenant.Resources.IsStandalone() {
		ginx.FailedPreconditionf(c, "extension compaction isn't supported for standalone deployment")
		return
	}

	runningWorkflow, err := controller.workflowRepo.GetRunningWorkflowOfTenant(c, compaction.DisableWorkflowType, tenantID)
	if err != nil {
		ginx.AdminInternalError(c, err)
		return
	}

	if runningWorkflow != nil {
		ginx.FailedPreconditionf(c, "Illegal status, already exist running workflow")
		return
	}

	// check extension status
	extension, err := controller.tenantExtensionService.GetTenantExtensionByTenantIDAndResourceType(c, tenantID, model.ExtensionsResourceTypeCompaction)
	if err != nil && eris.Is(err, apierr.ErrTenantExtensionNotExist) {
		ginx.FailedPreconditionf(c, "tenant extension serverless compaction is not exist")
		return
	}
	if err != nil {
		ginx.AdminInternalError(c, err)
		return
	}
	ret := config.NewRisingWaveExtensionsCompactionWithRWConfig()
	err = ret.UnmarshalJSON([]byte(*extension.Config))
	if err != nil {
		ginx.AdminInternalError(c, err)
		return
	}

	c.JSON(200, mgmt_admin.GetTenantExtensionCompactionParametersResponseBody{
		Compactor: &mgmt_admin.PostTenantExtensionCompactionCompactorRequestBody{
			CpuLimit:      ret.Compactor.CPULimit,
			CpuRequest:    ret.Compactor.CPURequest,
			MemoryLimit:   ret.Compactor.MemoryLimit,
			MemoryRequest: ret.Compactor.MemoryRequest,
		},
		Scaler: &mgmt_admin.PostTenantExtensionCompactionScalerRequestBody{
			CollectInterval:               int(ret.Scaler.CollectInterval),
			CoolDownPeriod:                int(ret.Scaler.CoolDownPeriod),
			DesiredReplicas:               int(ret.Scaler.DesiredReplicas),
			MaxReplicas:                   int(ret.Scaler.MaxReplicas),
			MinReplicas:                   int(ret.Scaler.MinReplicas),
			PollingInterval:               int(ret.Scaler.PollingInterval),
			ScaleDownToNRequiredTimes:     int(ret.Scaler.ScaleDownToNRequiredTimes),
			ScaleDownToZeroRequiredTimes:  int(ret.Scaler.ScaleDownToZeroRequiredTimes),
			DefaultParallelism:            int(ret.Scaler.DefaultParallelism),
			ExpirationExpireTime:          int(ret.Scaler.ExpirationExpireTime),
			DefaultCapacityReservedBuffer: int(ret.Scaler.DefaultCapacityReservedBuffer),
		},
	})
}

func (controller *AdminController) GetTenantTenantIdExtensionsCompactionStatus(c *gin.Context, tenantID uint64) {
	tenant, err := controller.tenantService.GetTenantByID(c, tenantID)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err, tenantID)
		return
	}

	if tenant.Resources.IsStandalone() {
		ginx.FailedPreconditionf(c, "extension compaction isn't supported for standalone deployment")
		return
	}

	extension, err := controller.tenantExtensionService.GetTenantExtensionByTenantIDAndResourceType(c, tenantID, model.ExtensionsResourceTypeCompaction)
	if err != nil && !eris.Is(err, apierr.ErrTenantExtensionNotExist) {
		ginx.AdminInternalError(c, err)
		return
	}

	if eris.Is(err, apierr.ErrTenantExtensionNotExist) {
		ginx.NotFoundError(c, err)
		return
	}

	c.JSON(200, mgmt_admin.GetTenantExtensionCompactionStatusResponseBody{
		Status: extension.Status,
	})
}

func (controller *AdminController) PostTenantTenantIdExtensionsCompactionStatus(c *gin.Context, tenantID uint64) {
	var req mgmt_admin.PostTenantExtensionCompactionStatusRequestBody
	if err := c.BindJSON(&req); err != nil {
		ginx.InvalidArgumentError(c, err)
		return
	}
	if !controller.checkExtensionCompactionStatus(req.Previous) || !controller.checkExtensionCompactionStatus(req.Target) {
		ginx.FailedPreconditionf(c, "Illegal status: %s, cannot update extensions compaction status", req)
		return
	}

	tenant, err := controller.tenantService.GetTenantByID(c, tenantID)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err, tenantID)
		return
	}

	if tenant.Resources.IsStandalone() {
		ginx.FailedPreconditionf(c, "extension compaction isn't supported for standalone deployment")
		return
	}

	// check extension exist
	extension, err := controller.tenantExtensionService.GetTenantExtensionByTenantIDAndResourceType(c, tenantID, model.ExtensionsResourceTypeCompaction)
	if err != nil && !eris.Is(err, apierr.ErrTenantExtensionNotExist) {
		ginx.AdminInternalError(c, err)
		return
	}

	if eris.Is(err, apierr.ErrTenantExtensionNotExist) {
		ginx.NotFoundError(c, err)
		return
	}

	if extension.Status != req.Previous {
		ginx.FailedPreconditionf(c, "Illegal status, extension status: %s doesn't match with previous status: %s", extension.Status, req.Previous)
		return
	}
	// Update extension status
	err = controller.tenantExtensionService.CompareAndUpdateTenantExtensionStatusByTenantIDAndResourceTypeAndStatuses(c, tenantID, model.ExtensionsResourceTypeCompaction, req.Target, []string{req.Previous})
	if err != nil {
		ginx.AdminInternalError(c, err)
		return
	}

	// Get updated extension status
	extension, err = controller.tenantExtensionService.GetTenantExtensionByTenantIDAndResourceType(c, tenantID, model.ExtensionsResourceTypeCompaction)
	if err != nil && !eris.Is(err, apierr.ErrTenantExtensionNotExist) {
		ginx.AdminInternalError(c, err)
		return
	}

	if eris.Is(err, apierr.ErrTenantExtensionNotExist) {
		ginx.NotFoundError(c, err)
		return
	}

	if extension.Status != req.Target {
		ginx.FailedPreconditionf(c, "Illegal status, updated extension status: %s doesn't match with target status: %s", extension.Status, req.Target)
		return
	}

	c.JSON(200, mgmt_admin.GetTenantExtensionCompactionStatusResponseBody{
		Status: extension.Status,
	})
}

func (controller *AdminController) checkExtensionCompactionStatus(status string) bool {
	if status != model.ExtensionStatusEnabling && status != model.ExtensionStatusDisabling && status != model.ExtensionStatusRunning && status != model.ExtensionStatusUpdating && status != model.ExtensionStatusDisabled && status != model.ExtensionStatusFailed {
		return false
	}
	return true
}

func (controller *AdminController) PostTenantTenantIdExtensionsServerlessbackfillEnable(c *gin.Context, tenantID uint64) {
	if err := controller.tenantExtensionService.ValidateExtensionServerlessBackfillCanEnable(c, tenantID); err != nil {
		code := eris.GetCode(err)
		if code == eris.CodeFailedPrecondition {
			ginx.FailedPreconditionError(c, err)
			return
		}
		if code == eris.CodeNotFound {
			ginx.NotFoundError(c, err)
			return
		}
		ginx.InternalError(c, err)
		return
	}

	workflowID, err := controller.tenantExtensionService.EnableExtensionsServerlessBackfill(c, tenantID)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	c.JSON(202, mgmt_admin.WorkflowIdResponseBody{
		WorkflowId: workflowID,
	})
}

func (controller *AdminController) PostTenantTenantIdExtensionsServerlessbackfillDisable(c *gin.Context, tenantID uint64) {
	if err := controller.tenantExtensionService.ValidateExtensionServerlessBackfillCanDisable(c, tenantID); err != nil {
		code := eris.GetCode(err)
		if code == eris.CodeFailedPrecondition {
			ginx.FailedPreconditionError(c, err)
			return
		}
		if code == eris.CodeNotFound {
			ginx.NotFoundError(c, err)
			return
		}
		ginx.InternalError(c, err)
		return
	}

	workflowID, err := controller.tenantExtensionService.DisableExtensionsServerlessBackfill(c, tenantID)
	if err != nil {
		ginx.AdminInternalError(c, err)
		return
	}

	c.JSON(202, mgmt_admin.WorkflowIdResponseBody{
		WorkflowId: workflowID,
	})
}

func (controller *AdminController) PostTenantTenantIdExtensionsServerlessbackfillVersion(c *gin.Context, tenantID uint64) {
	raw, err := ginx.RequestBody(c)
	if err != nil {
		ginx.AdminInternalError(c, eris.Wrap(err, "failed to to read version from request body"))
		return
	}
	if err := controller.tenantExtensionService.ValidateExtensionServerlessBackfillCanPostVersion(c, tenantID); err != nil {
		switch eris.GetCode(err) {
		case eris.CodeFailedPrecondition:
			ginx.FailedPreconditionError(c, err)
		case eris.CodeNotFound:
			ginx.NotFoundError(c, err)
		case eris.CodeCanceled, eris.CodeUnknown, eris.CodeInvalidArgument, eris.CodeDeadlineExceeded, eris.CodeAlreadyExists, eris.CodePermissionDenied, eris.CodeResourceExhausted, eris.CodeAborted, eris.CodeOutOfRange, eris.CodeUnimplemented, eris.CodeInternal, eris.CodeUnavailable, eris.CodeDataLoss, eris.CodeUnauthenticated:
			ginx.InternalError(c, err)
		}
		return
	}

	workflowID, err := controller.tenantExtensionService.UpdateExtensionsServerlessBackfill(c, tenantID, string(raw))
	if err != nil {
		ginx.AdminInternalError(c, err)
		return
	}

	c.JSON(202, mgmt_admin.WorkflowIdResponseBody{
		WorkflowId: workflowID,
	})
}

func (controller *AdminController) GetTenantsTenantIdExtensionsIcebergCompaction(c *gin.Context, tenantID uint64) {
	tenant, err := controller.tenantService.GetTenantByID(c, tenantID)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err, tenantID)
		return
	}

	if tenant.Resources.IsStandalone() {
		ginx.FailedPreconditionf(c, "iceberg compaction is not supported in standalone mode")
		return
	}

	icebergCompaction, err := controller.tenantExtensionService.GetExtensionIcebergCompaction(c, tenant.ID)
	if err != nil {
		if eris.Is(err, apierr.ErrTenantExtensionNotExist) {
			ginx.NotFoundf(c, "iceberg compaction not found")
			return
		}
		ginx.InternalError(c, err)
		return
	}

	c.JSON(http.StatusOK, icebergCompaction)
}

func (controller *AdminController) PostTenantsTenantIdExtensionsIcebergCompaction(c *gin.Context, tenantID uint64) {
	tenant, err := controller.tenantService.GetTenantByID(c, tenantID)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err, tenantID)
		return
	}

	if tenant.Resources.IsStandalone() {
		ginx.FailedPreconditionf(c, "iceberg compaction is not supported in standalone mode")
		return
	}

	if tenant.Status != model.TenantStatusRunning {
		ginx.FailedPreconditionf(c, "tenant %d is not running", tenant.ID)
		return
	}

	// Check if the iceberg compaction isn't enabled.
	tenantExt, err := controller.tenantExtensionService.GetTenantExtensionByTenantIDAndResourceType(c,
		tenant.ID, model.ExtensionsResourceTypeIcebergCompaction)
	if err != nil && !eris.Is(err, apierr.ErrTenantExtensionNotExist) {
		ginx.InternalError(c, err)
		return
	}

	if tenantExt != model.TenantExtensionNil && tenantExt.Status != model.ExtensionStatusDisabled {
		ginx.FailedPreconditionf(c, "iceberg compaction is in unexpected status: %s", tenantExt.Status)
		return
	}

	// opts will be nil if the request body is empty.
	var req mgmt_admin.PostTenantsTenantIdExtensionsIcebergCompactionJSONRequestBody
	if err := c.BindJSON(&req); err != nil {
		ginx.InvalidArgumentf(c, "invalid request body: %s", err.Error())
		return
	}

	// Validation
	if req.Resources == nil {
		ginx.InvalidArgumentf(c, "invalid request body: %s", "resources is required")
		return
	}
	if req.Resources.Replica <= 0 {
		ginx.InvalidArgumentf(c, "invalid request body: %s", "replica must be greater than 0")
		return
	}
	if req.Resources.ComponentTypeId == "" {
		ginx.InvalidArgumentf(c, "invalid request body: %s", "component type id is required")
		return
	}
	if config.GetResourceDef().GetComponentTypeByID(req.Resources.ComponentTypeId) == nil {
		ginx.InvalidArgumentf(c, "invalid request body: %s", "component type id is invalid")
		return
	}
	if req.Config != nil {
		var tmp any
		if _, err := toml.Decode(*req.Config, &tmp); err != nil {
			ginx.InvalidArgumentf(c, "invalid request body: %s", "failed to decode config")
			return
		}
	}

	// Start the workflow.
	workflowID, err := controller.tenantExtensionService.EnableExtensionIcebergCompaction(c,
		tenant.ID,
		&services.IcebergCompactionOptions{
			Config:          ptr.Deref(req.Config, ""),
			Replicas:        req.Resources.Replica,
			ComponentTypeID: req.Resources.ComponentTypeId,
		})
	if err != nil {
		if eris.GetCode(err) == eris.CodeFailedPrecondition {
			ginx.FailedPrecondition(c, err.Error())
			return
		}
		ginx.InternalError(c, err)
		return
	}

	c.JSON(202, mgmt_admin.WorkflowIdResponseBody{
		WorkflowId: workflowID,
	})
}

func (controller *AdminController) PutTenantsTenantIdExtensionsIcebergCompaction(c *gin.Context, tenantID uint64) {
	tenant, err := controller.tenantService.GetTenantByID(c, tenantID)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err, tenantID)
		return
	}

	if tenant.Resources.IsStandalone() {
		ginx.FailedPreconditionf(c, "iceberg compaction is not supported in standalone mode")
		return
	}

	if tenant.Status != model.TenantStatusRunning {
		ginx.FailedPreconditionf(c, "tenant %d is not running", tenant.ID)
		return
	}

	var req mgmt_admin.PutTenantsTenantIdExtensionsIcebergCompactionJSONRequestBody
	if err := c.BindJSON(&req); err != nil {
		ginx.InvalidArgumentf(c, "invalid request body: %s", err.Error())
		return
	}

	// Validation
	if req.Resources == nil {
		ginx.InvalidArgumentf(c, "invalid request body: %s", "resources is required")
		return
	}
	if req.Resources.Replica < 0 {
		ginx.InvalidArgumentf(c, "invalid request body: %s", "replica must be greater than or equal to 0")
		return
	}
	if req.Resources.ComponentTypeId == "" {
		ginx.InvalidArgumentf(c, "invalid request body: %s", "component type id is required")
		return
	}
	if config.GetResourceDef().GetComponentTypeByID(req.Resources.ComponentTypeId) == nil {
		ginx.InvalidArgumentf(c, "invalid request body: %s", "component type id is invalid")
		return
	}
	if req.Config != nil {
		var tmp any
		if _, err := toml.Decode(*req.Config, &tmp); err != nil {
			ginx.InvalidArgumentf(c, "invalid request body: %s", "failed to decode config")
			return
		}
	}

	// Check if the iceberg compaction is enabled.
	tenantExt, err := controller.tenantExtensionService.GetTenantExtensionByTenantIDAndResourceType(c,
		tenant.ID, model.ExtensionsResourceTypeIcebergCompaction)
	if err != nil {
		if eris.Is(err, apierr.ErrTenantExtensionNotExist) {
			ginx.NotFoundf(c, "iceberg compaction not found")
			return
		}
		ginx.InternalError(c, err)
		return
	}
	if tenantExt.Status != model.ExtensionStatusRunning {
		ginx.FailedPreconditionf(c, "iceberg compaction is not running")
		return
	}

	// Start the workflow.
	workflowID, err := controller.tenantExtensionService.UpdateExtensionIcebergCompaction(c, tenant.ID, services.IcebergCompactionOptions{
		Config:          ptr.Deref(req.Config, ""),
		Replicas:        req.Resources.Replica,
		ComponentTypeID: req.Resources.ComponentTypeId,
	})
	if err != nil {
		if eris.GetCode(err) == eris.CodeFailedPrecondition {
			ginx.FailedPrecondition(c, err.Error())
			return
		}
		ginx.InternalError(c, err)
		return
	}

	c.JSON(202, mgmt_admin.WorkflowIdResponseBody{
		WorkflowId: workflowID,
	})
}

func (controller *AdminController) DeleteTenantsTenantIdExtensionsIcebergCompaction(c *gin.Context, tenantID uint64) {
	tenant, err := controller.tenantService.GetTenantByID(c, tenantID)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err, tenantID)
		return
	}

	if tenant.Resources.IsStandalone() {
		ginx.FailedPreconditionf(c, "iceberg compaction is not supported in standalone mode")
		return
	}

	if tenant.Status != model.TenantStatusRunning {
		ginx.FailedPreconditionf(c, "tenant %d is not running", tenant.ID)
		return
	}

	// Check if the iceberg compaction is enabled.
	tenantExt, err := controller.tenantExtensionService.GetTenantExtensionByTenantIDAndResourceType(c,
		tenant.ID, model.ExtensionsResourceTypeIcebergCompaction)
	if err != nil {
		if eris.Is(err, apierr.ErrTenantExtensionNotExist) {
			ginx.NotFoundf(c, "iceberg compaction not found")
			return
		}
		ginx.InternalError(c, err)
		return
	}
	if tenantExt.Status != model.ExtensionStatusRunning {
		ginx.FailedPreconditionf(c, "iceberg compaction is not running")
		return
	}

	// Start the workflow.
	workflowID, err := controller.tenantExtensionService.DisableExtensionIcebergCompaction(c, tenant.ID)
	if err != nil {
		if eris.GetCode(err) == eris.CodeFailedPrecondition {
			ginx.FailedPrecondition(c, err.Error())
			return
		}
		ginx.InternalError(c, err)
		return
	}

	c.JSON(202, mgmt_admin.WorkflowIdResponseBody{
		WorkflowId: workflowID,
	})
}
