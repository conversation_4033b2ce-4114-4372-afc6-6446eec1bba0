package agent

import (
	"context"
	"regexp"
	"strconv"
	"time"

	"github.com/risingwavelabs/eris"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	pbtimestamp "google.golang.org/protobuf/types/known/timestamppb"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	pbupdate "github.com/risingwavelabs/cloudagent/pbgen/common/resource/update"
	pbrw "github.com/risingwavelabs/cloudagent/pbgen/common/rw"
	pbk8ssvc "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	pbrwcsvc "github.com/risingwavelabs/cloudagent/pbgen/services/rwc"

	"github.com/risingwavelabs/risingwave-cloud/internal/model"
)

func (agent *CloudAgent) CreateRisingWave(ctx context.Context, req *pbk8ssvc.CreateRisingWaveRequest) error {
	res, err := agent.k8s.CreateRisingWave(ctx, req)
	if err != nil {
		return eris.Wrapf(err, "failed to create rw object %v", req.GetResourceMeta())
	}
	if res.GetStatus().GetCode() != pbcreation.StatusCode_SCHEDULED &&
		res.GetStatus().GetCode() != pbcreation.StatusCode_ALREADY_EXISTS {
		return eris.Errorf("expect scheduled status, unexpected creation status %s", res.GetStatus().GetCode())
	}
	return nil
}

func (agent *CloudAgent) DeleteRisingWave(ctx context.Context, name, namespace string) error {
	res, err := agent.k8s.DeleteRisingWave(ctx, &pbk8ssvc.DeleteRisingWaveRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        name,
			Namespace: namespace,
		},
	})
	if err != nil {
		return eris.Wrapf(err, "failed to create rw object name: %v, namespace: %v", name, namespace)
	}
	if res.GetStatus().GetCode() != pbdeletion.StatusCode_SCHEDULED &&
		res.GetStatus().GetCode() != pbdeletion.StatusCode_NOT_FOUND {
		return eris.Errorf("expect scheduled status, unexpected deletion status %s", res.GetStatus().GetCode())
	}
	return nil
}

func (agent *CloudAgent) GetRisingWave(ctx context.Context, name, namespace string) (*pbk8ssvc.GetRisingWaveResponse, error) {
	res, err := agent.k8s.GetRisingWave(ctx, &pbk8ssvc.GetRisingWaveRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        name,
			Namespace: namespace,
		},
	})
	if err != nil {
		return nil, eris.Wrapf(err, "failed to retrieve rw, name: %v, ns: %v", name, namespace)
	}
	return res, nil
}

func (agent *CloudAgent) UpdateRisingWaveImage(ctx context.Context, name, namespace, imageTag string) error {
	_, err := agent.k8s.UpdateRisingWaveImage(ctx, &pbk8ssvc.UpdateRisingWaveImageRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        name,
			Namespace: namespace,
		},
		ImageTag: imageTag,
	})
	if err != nil {
		return eris.Wrapf(err, "failed to update rw image, name: %v, ns: %v, image: %v", name, namespace, imageTag)
	}
	return nil
}

// ScaleRisingWaveOneOf scales the RisingWave instance in standalone or in cluster mode.
// May be used in the future to switch between cluster and standalone mode.
// https://linear.app/risingwave-labs/issue/CLOUD-2212/[devtiercontrolplane]-support-scaling-from-dev-tier
func (agent *CloudAgent) ScaleRisingWaveOneOf(ctx context.Context, req *pbk8ssvc.ScaleRisingWaveRequestOneOf) error {
	_, err := agent.k8s.ScaleRisingWaveOneOf(ctx, req)
	if err != nil {
		return eris.Wrapf(err, "failed to scale rw, req: %v", req.GetResourceMeta())
	}
	return nil
}

func (agent *CloudAgent) StopRisingWave(ctx context.Context, name, namespace string) error {
	_, err := agent.k8s.StopRisingWave(ctx, &pbk8ssvc.StopRisingWaveRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        name,
			Namespace: namespace,
		},
	})
	if err != nil {
		return eris.Wrapf(err, "failed to stop rw, name: %v, ns: %v", name, namespace)
	}
	return nil
}

func (agent *CloudAgent) StartRisingWave(ctx context.Context, name, namespace string, overrides []*pbk8ssvc.RisingWaveReplicaOverride) error {
	_, err := agent.k8s.StartRisingWave(ctx, &pbk8ssvc.StartRisingWaveRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        name,
			Namespace: namespace,
		},
		Overrides: overrides,
	})
	if err != nil {
		return eris.Wrapf(err, "failed to start rw, name: %v, ns: %v", name, namespace)
	}
	return nil
}

func (agent *CloudAgent) UpdateRisingWaveComponents(ctx context.Context, req *pbk8ssvc.UpdateRisingWaveComponentsRequest) error {
	_, err := agent.k8s.UpdateRisingWaveComponents(ctx, req)
	if err != nil {
		return eris.Wrapf(err, "failed to update rw components, req: %v", req)
	}
	return nil
}

func (agent *CloudAgent) UpdateRisingWaveMetaStore(ctx context.Context, req *pbk8ssvc.UpdateRisingWaveMetaStoreRequest) error {
	_, err := agent.k8s.UpdateRisingWaveMetaStore(ctx, req)
	if err != nil {
		return eris.Wrapf(err, "failed to update rw meta store, req: %v", req)
	}
	return nil
}

func getSnapshotIDByFilteringMetaBackupLog(content string) (int64, error) {
	r := regexp.MustCompile("backup job succeeded: job ([0-9]*)")
	snippet := r.FindStringSubmatch(content)
	if len(snippet) == 0 {
		return 0, eris.Errorf("failed to find backup job id: no matches, content: CONTENT START~%s~END OF CONTENT", content)
	}
	if len(snippet) < 2 {
		return 0, eris.Errorf("unexpected behavior of FindStringSubMatch, getting: %v, content: %s", snippet, content)
	}
	raw := snippet[1]
	id, err := strconv.ParseInt(raw, 10, 64)
	if err != nil {
		return 0, eris.Wrapf(err, "failed to parse id: %s", raw)
	}
	return id, nil
}

func (agent *CloudAgent) MetaBackup(ctx context.Context, req *pbrwcsvc.MetaNodeBackupRequest, tenantID uint64) (int64, error) {
	res, err := startRetryableTaskAndWait(
		ctx,
		agent,
		req,
		CloudResourceCreateOption{
			cloudResourceType: TenantResourceType,
			tenantID:          tenantID,
			resourceType:      model.CloudResourceMetaBackupTask,
		},
		agent.rwc.MetaNodeBackup,
		PollingMetaBackupTimeout,
		PollingMetaBackupInterval)
	if err != nil {
		return 0, eris.Wrapf(err, "failed to run meta backup task, req: %v", req)
	}
	// TODO: use a more stable approach. this is a workaround to extract the snapshot ID.
	id, err := getSnapshotIDByFilteringMetaBackupLog(res.GetStatus().GetMessage())
	if err != nil {
		return 0, eris.Wrapf(err, "failed to extract snapshot ID from task log: %s", res.GetStatus().GetMessage())
	}
	return id, nil
}

func (agent *CloudAgent) CleanupMetaBackupTask(ctx context.Context, taskResourceID string) error {
	return agent.cleanupTaskAndWait(
		ctx,
		taskResourceID,
		"",
		CloudResourceDeleteOption{
			cloudResourceType: TenantResourceType,
			resourceType:      model.CloudResourceMetaBackupTask,
		},
	)
}

func (agent *CloudAgent) MetaRestore(ctx context.Context, req *pbrwcsvc.RestoreMetaRequest, tenantID uint64) error {
	_, err := startRetryableTaskAndWait(
		ctx,
		agent,
		req,
		CloudResourceCreateOption{
			cloudResourceType: TenantResourceType,
			tenantID:          tenantID,
			resourceType:      model.CloudResourceMetaRestoreTask,
		},
		agent.rwc.RestoreMeta,
		PollingMetaRestoreTimeout,
		PollingMetaRestoreInterval)
	if err != nil {
		return eris.Wrapf(err, "failed to run meta restore task, req: %v", req)
	}
	return nil
}

func (agent *CloudAgent) CleanupMetaRestoreTask(ctx context.Context, taskResourceID string) error {
	return agent.cleanupTaskAndWait(
		ctx,
		taskResourceID,
		"",
		CloudResourceDeleteOption{
			cloudResourceType: TenantResourceType,
			resourceType:      model.CloudResourceMetaRestoreTask,
		},
	)
}

func (agent *CloudAgent) MetaMigrationAndAwait(ctx context.Context, req *pbrwcsvc.MetaMigrationRequest, tenantID uint64) error {
	_, err := startRetryableTaskAndWait(
		ctx,
		agent,
		req,
		CloudResourceCreateOption{
			cloudResourceType: TenantResourceType,
			tenantID:          tenantID,
			resourceType:      model.CloudResourceMetaMigrationTask,
		},
		agent.rwc.MetaMigration,
		PollingMetaMigrationTimeout,
		PollingMetaMigrationInterval)
	if err != nil {
		return eris.Wrapf(err, "failed to run meta migration task, req: %v", req)
	}
	return nil
}

func (agent *CloudAgent) CleanupMetaMigrationTask(ctx context.Context, taskResourceID string, taskNamespace string) error {
	return agent.cleanupTaskAndWait(
		ctx,
		taskResourceID,
		taskNamespace,
		CloudResourceDeleteOption{
			cloudResourceType: TenantResourceType,
			resourceType:      model.CloudResourceMetaMigrationTask,
		},
	)
}

func (agent *CloudAgent) UpdateRisingWaveLicenseKey(ctx context.Context, name, namespace, secretName string) error {
	_, err := agent.k8s.UpdateRisingWaveLicenseKey(ctx, &pbk8ssvc.UpdateRisingWaveLicenseKeyRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        name,
			Namespace: namespace,
		},
		SecretName: secretName,
	})
	if err != nil {
		return eris.Wrapf(err, "failed to update rw license key, name: %v, ns: %v, secret name: %v", name, namespace, secretName)
	}
	return nil
}

func (agent *CloudAgent) UpdateRisingWaveSecretStore(ctx context.Context, name, namespace, secretName, secretKey string) error {
	_, err := agent.k8s.UpdateRisingWaveSecretStore(ctx, &pbk8ssvc.UpdateRisingWaveSecretStoreRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        name,
			Namespace: namespace,
		},
		SecretName: secretName,
		SecretKey:  secretKey,
	})
	if err != nil {
		return eris.Wrapf(err, "failed to update rw secret store, name: %v, ns: %v, secret name: %v, secret key: %v", name, namespace, secretName, secretKey)
	}
	return nil
}

func (agent *CloudAgent) CreateRisingWaveNodeGroup(ctx context.Context, name, namespace string, component pbrw.ComponentType, nodeGroup *pbrw.NodeGroupSpec) error {
	res, err := agent.k8s.CreateRisingWaveNodeGroup(ctx, &pbk8ssvc.CreateRisingWaveNodeGroupRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        name,
			Namespace: namespace,
		},
		Component: component,
		NodeGroup: nodeGroup,
	})
	if err != nil {
		return eris.Wrapf(err, "failed to create rw node group for %s/%s, node group: %s/%s", namespace, name, component, nodeGroup.GetName())
	}
	if res.GetStatus().GetCode() != pbcreation.StatusCode_CREATED &&
		res.GetStatus().GetCode() != pbcreation.StatusCode_ALREADY_EXISTS {
		return eris.Errorf("unexpected creation status %s", res.GetStatus().GetCode())
	}
	return nil
}

func (agent *CloudAgent) UpdateRisingWaveNodeGroup(ctx context.Context, name, namespace string, component pbrw.ComponentType, nodeGroup *pbrw.NodeGroupSpec) error {
	res, err := agent.k8s.UpdateRisingWaveNodeGroup(ctx, &pbk8ssvc.UpdateRisingWaveNodeGroupRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        name,
			Namespace: namespace,
		},
		Component: component,
		NodeGroup: nodeGroup,
	})
	if err != nil {
		return eris.Wrapf(err, "failed to update rw node group for %s/%s, node group: %s/%s", namespace, name, component, nodeGroup.GetName())
	}
	if res.GetStatus().GetCode() != pbupdate.StatusCode_UPDATED &&
		res.GetStatus().GetCode() != pbupdate.StatusCode_ALREADY_EXISTS {
		return eris.Errorf("unexpected update status %s", res.GetStatus().GetCode())
	}
	return nil
}

func (agent *CloudAgent) DeleteRisingWaveNodeGroup(ctx context.Context, name, namespace string, component pbrw.ComponentType, nodeGroup string) error {
	res, err := agent.k8s.DeleteRisingWaveNodeGroup(ctx, &pbk8ssvc.DeleteRisingWaveNodeGroupRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        name,
			Namespace: namespace,
		},
		Component:     component,
		NodeGroupName: nodeGroup,
	})
	if err != nil {
		return eris.Wrapf(err, "failed to delete rw node group for %s/%s, node group: %s/%s", namespace, name, component, nodeGroup)
	}
	if res.GetStatus().GetCode() != pbdeletion.StatusCode_DELETED &&
		res.GetStatus().GetCode() != pbdeletion.StatusCode_NOT_FOUND {
		return eris.Errorf("unexpected deletion status %s", res.GetStatus().GetCode())
	}
	return nil
}

func (agent *CloudAgent) UpdateRisingWaveNodeGroupConfiguration(ctx context.Context, name, namespace string, component pbrw.ComponentType, nodeGroup string, config *pbrw.NodeConfig) error {
	res, err := agent.k8s.UpdateRisingWaveNodeGroupConfiguration(ctx, &pbk8ssvc.UpdateRisingWaveNodeGroupConfigurationRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        name,
			Namespace: namespace,
		},
		Component:  component,
		NodeGroup:  nodeGroup,
		NodeConfig: config,
	})
	if err != nil {
		grpcErr, ok := status.FromError(err)
		if ok && grpcErr.Code() == codes.Aborted {
			return eris.WithCode(eris.Wrapf(err, "conflict to update risingwave"), eris.CodeAborted)
		}
		return eris.Wrapf(err, "failed to delete rw node group for %s/%s, node group: %s/%s", namespace, name, component, nodeGroup)
	}
	if res.GetStatus().GetCode() != pbupdate.StatusCode_UPDATED {
		return eris.Errorf("unexpected update status %s", res.GetStatus().GetCode())
	}
	return nil
}

func (agent *CloudAgent) UpdateRisingWaveNodeGroupRestartAt(ctx context.Context, name, namespace string, component pbrw.ComponentType, nodeGroup string, restartAt *time.Time) error {
	var pbRestartAt *pbtimestamp.Timestamp
	if restartAt != nil {
		pbRestartAt = pbtimestamp.New(*restartAt)
	}

	res, err := agent.k8s.UpdateRisingWaveNodeGroupRestartAt(ctx, &pbk8ssvc.UpdateRisingWaveNodeGroupRestartAtRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        name,
			Namespace: namespace,
		},
		Component: component,
		NodeGroup: nodeGroup,
		RestartAt: pbRestartAt,
	})
	if err != nil {
		grpcErr, ok := status.FromError(err)
		if ok && grpcErr.Code() == codes.Aborted {
			return eris.WithCode(eris.Wrapf(err, "conflict to update risingwave"), eris.CodeAborted)
		}
		return eris.Wrapf(err, "failed to delete rw node group for %s/%s, node group: %s/%s", namespace, name, component, nodeGroup)
	}
	if res.GetStatus().GetCode() != pbupdate.StatusCode_UPDATED {
		return eris.Errorf("unexpected update status %s", res.GetStatus().GetCode())
	}
	return nil
}
