package controllerv1

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/jinzhu/copier"
	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/risingwave-cloud/internal/apiservice/dto"
	"github.com/risingwavelabs/risingwave-cloud/internal/apiservice/services"
	"github.com/risingwavelabs/risingwave-cloud/internal/config"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/version"
	"github.com/risingwavelabs/risingwave-cloud/shared/ginx"
	ginauth "github.com/risingwavelabs/risingwave-cloud/shared/ginx/auth"
	"github.com/risingwavelabs/risingwave-cloud/shared/namespace"
	"github.com/risingwavelabs/risingwave-cloud/shared/paging"
	"github.com/risingwavelabs/risingwave-cloud/shared/ptr"
	"github.com/risingwavelabs/risingwave-cloud/shared/spec/mgmt_admin"
	spec "github.com/risingwavelabs/risingwave-cloud/shared/spec/mgmt_spec"
)

// Delete one of the meta store snapshot by its id.
func (controller *UserController) DeleteTenantTenantIdBackupSnapshotId(c *gin.Context, tenantID uint64, snapshotID uuid.UUID) {
	// Get user info.
	userInfo, err := ginauth.GetUserInfo(c)
	if err != nil {
		ginx.InternalError(c, eris.Wrap(err, "unable to get user info: "))
		return
	}
	// Get tenant record.
	_, err = controller.tenantService.GetOrgTenantByID(c, userInfo.OrgID, tenantID)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err, tenantID)
		return
	}

	id, err := controller.metaBackupService.StartDeleteSnapshot(c, tenantID, snapshotID)
	if err != nil {
		if eris.Is(err, services.ErrSnapshotCreating) {
			ginx.FailedPreconditionf(c, "snapshot is creating")
			return
		}
		if eris.Is(err, services.ErrSnapshotDeleting) {
			c.JSON(http.StatusAccepted, mgmt_admin.OptionalWorkflowIdResponseBody{Msg: ptr.Ptr("snapshot deletion is already in progress")})
			return
		}
		ginx.InternalError(c, err)
		return
	}
	c.JSON(http.StatusAccepted, mgmt_admin.OptionalWorkflowIdResponseBody{WorkflowId: &id})
}

// Get all meta store snapshots.
func (controller *UserController) GetTenantTenantIdBackup(c *gin.Context, tenantID uint64, params spec.GetTenantTenantIdBackupParams) {
	// Get user info.
	userInfo, err := ginauth.GetUserInfo(c)
	if err != nil {
		ginx.InternalError(c, eris.Wrap(err, "unable to get user info: "))
		return
	}
	// Get tenant record.
	_, err = controller.tenantService.GetOrgTenantByID(c, userInfo.OrgID, tenantID)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err, tenantID)
		return
	}

	pager := paging.NewPager(20, 100)
	page := pager.Paging(params.Offset, params.Limit)

	res, err := controller.metaBackupService.GetAllMetaSnapshots(c, tenantID, int32(page.Offset), int32(page.Limit))
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	cnt, err := controller.metaBackupService.GetAllMetaSnapshotsCount(c, tenantID)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	resSpec, err := toBackupSnapshotsSpec(res)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	rtn := spec.BackupSnapshotsSizePage{
		Limit:  page.Limit,
		Offset: page.Offset,
		Items:  resSpec,
		Size:   cnt,
	}

	c.JSON(http.StatusOK, rtn)
}

// Start a meta backup to take a snapshot of the meta store.
func (controller *UserController) PostTenantTenantIdBackup(c *gin.Context, tenantID uint64) {
	// Get user info.
	userInfo, err := ginauth.GetUserInfo(c)
	if err != nil {
		ginx.InternalError(c, eris.Wrap(err, "unable to get user info: "))
		return
	}
	// Get tenant record.
	_, err = controller.tenantService.GetOrgTenantByID(c, userInfo.OrgID, tenantID)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err, tenantID)
		return
	}
	workflowID, snapshotID, err := controller.metaBackupService.StartMetaBackup(c, tenantID)
	if eris.GetCode(err) == eris.CodeFailedPrecondition {
		ginx.FailedPreconditionError(c, err)
		return
	}
	if err != nil {
		ginx.InternalError(c, err)
		return
	}
	ginx.Accepted(c, spec.PostSnapshotResponseBody{WorkflowId: *workflowID, SnapshotId: *snapshotID}, "snapshot creation is in progress")
}

// Restore the meta store to the snapshot.
func (controller *UserController) PostTenantTenantIdBackupSnapshotIdRestore(c *gin.Context, tenantID uint64, snapshotId uuid.UUID) {
	authInfo, err := ginauth.GetAuthInfo(c)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	var req mgmt_admin.PostTenantRestoreRequestBody
	if err = c.BindJSON(&req); err != nil {
		ginx.InvalidArgumentError(c, err)
		return
	}

	// old tenant validation
	oldTenant, err := controller.tenantService.GetOrgTenantByID(c, authInfo.OrgID, tenantID)
	if err != nil {
		if eris.GetCode(err) == eris.CodeNotFound {
			ginx.NotFoundError(c, err)
			return
		}
		ginx.InternalError(c, err)
		return
	}

	supported, err := version.IsRestoreSupported(oldTenant.ImageTag)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}
	if !supported {
		ginx.FailedPreconditionf(c, "version %s not supported for restore", oldTenant.ImageTag)
		return
	}

	newTenantName := req.NewTenantName
	if !namespace.IsValidResourceName(newTenantName) {
		ginx.InvalidArgument(c, "The cluster name must only contain letters, numbers, and hyphens, with a length of no more than 32 characters.")
		return
	}
	check, err := controller.accountAgent.CheckTenantAvailability(c, authInfo.OrgID, 0, oldTenant.TierID, newTenantName, config.Conf.Region)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}
	if !check.Available {
		ginx.FailedPrecondition(c, check.Msg)
		return
	}

	extSpecs, err := controller.tenantExtensionService.GetSpecTenantExtensionsByTenantID(c, oldTenant.ID)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	rtn, err := controller.tenantService.RestoreTenant(c, oldTenant, newTenantName, snapshotId, extSpecs)
	if err != nil {
		//nolint:exhaustive
		switch eris.GetCode(err) {
		case eris.CodeInvalidArgument:
			ginx.InvalidArgumentError(c, err)
		case eris.CodeNotFound:
			ginx.NotFoundError(c, err)
		case eris.CodeAlreadyExists:
			ginx.AlreadyExistsError(c, err)
		case eris.CodeFailedPrecondition:
			ginx.FailedPreconditionError(c, err)
		default:
			ginx.InternalError(c, err)
		}
		return
	}
	c.JSON(202, &rtn)
}

func (controller *UserController) PostTenantsTenantIdUnquiesce(c *gin.Context, tenantID uint64) {
	authInfo, err := ginauth.GetAuthInfo(c)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	tenant, err := controller.tenantService.GetOrgTenantByID(c, authInfo.OrgID, tenantID)
	if err != nil {
		if eris.GetCode(err) == eris.CodeNotFound {
			ginx.NotFoundError(c, err)
			return
		}
		ginx.InternalError(c, err)
		return
	}

	err = controller.tenantOperationService.StartInstance(c, tenant.ID)
	if err != nil {
		ginx.InternalError(c, err)
	}

	ginx.Accepted(c, nil, "unquiescing tenant")
}

func toBackupSnapshotsSpec(backupSnapshots []dto.BackupSnapshotItem) ([]spec.BackupSnapshotItem, error) {
	if backupSnapshots == nil {
		return nil, nil
	}
	backupSnapshotsSpec := make([]spec.BackupSnapshotItem, len(backupSnapshots))
	err := copier.Copy(&backupSnapshotsSpec, &backupSnapshots)
	if err != nil {
		return nil, err
	}
	return backupSnapshotsSpec, nil
}
