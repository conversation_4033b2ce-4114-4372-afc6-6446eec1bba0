package controllerv1

import (
	"bytes"
	"encoding/json"
	"net/http"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"

	"github.com/risingwavelabs/eris"
	account_mock "github.com/risingwavelabs/risingwave-cloud/internal/account/mock"
	"github.com/risingwavelabs/risingwave-cloud/internal/apiservice/dto"
	"github.com/risingwavelabs/risingwave-cloud/internal/apiservice/services"
	mock_service "github.com/risingwavelabs/risingwave-cloud/internal/apiservice/services/mock"
	"github.com/risingwavelabs/risingwave-cloud/internal/config"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	mockmodel "github.com/risingwavelabs/risingwave-cloud/internal/model/mock"
	"github.com/risingwavelabs/risingwave-cloud/shared/ptr"
	"github.com/risingwavelabs/risingwave-cloud/shared/spec/acc_admin"
	"github.com/risingwavelabs/risingwave-cloud/shared/spec/mgmt_admin"
	spec "github.com/risingwavelabs/risingwave-cloud/shared/spec/mgmt_spec"
	"github.com/risingwavelabs/risingwave-cloud/shared/tenant/tier"
)

func TestDeleteTenantTenantIdBackupSnapshotId(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	var (
		orgID             = uuid.Must(uuid.NewRandom())
		snapshotID        = uuid.Must(uuid.NewRandom())
		workflowID        = uuid.Must(uuid.NewRandom())
		tenantID   uint64 = 1
	)

	type testCase struct {
		err    error
		status int
	}

	var tests = []testCase{
		{
			err:    nil,
			status: http.StatusAccepted,
		},
		{
			err:    services.ErrSnapshotCreating,
			status: http.StatusBadRequest,
		},
		{
			err:    services.ErrSnapshotDeleting,
			status: http.StatusAccepted,
		},
	}

	for _, test := range tests {
		svc := mock_service.NewMockTenantServiceInterface(ctrl)
		metaBackupSvc := mock_service.NewMockMetaBackupService(ctrl)

		controller := &UserController{
			tenantService:     svc,
			metaBackupService: metaBackupSvc,
		}

		c, _ := newTestContext(orgID)

		svc.
			EXPECT().
			GetOrgTenantByID(c, orgID, tenantID).
			Return(model.Tenant{}, nil)

		metaBackupSvc.EXPECT().
			StartDeleteSnapshot(c, tenantID, snapshotID).
			Return(workflowID, test.err)

		controller.DeleteTenantTenantIdBackupSnapshotId(c, tenantID, snapshotID)
		assert.Equal(t, test.status, c.Writer.Status())
	}
}

func TestGetTenantTenantIdBackup(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	var (
		orgID           = uuid.Must(uuid.NewRandom())
		tenantID uint64 = 1
		offset          = ptr.Ptr[uint64](0)
		limit           = ptr.Ptr[uint64](2)
		size     uint64 = 3
		items           = []dto.BackupSnapshotItem{
			{}, {},
		}
		itemsSpec = []spec.BackupSnapshotItem{
			{}, {},
		}
	)

	svc := mock_service.NewMockTenantServiceInterface(ctrl)
	metaBackupSvc := mock_service.NewMockMetaBackupService(ctrl)
	c, r := newTestContext(orgID)

	controller := &UserController{
		tenantService:     svc,
		metaBackupService: metaBackupSvc,
	}

	svc.
		EXPECT().
		GetOrgTenantByID(c, orgID, tenantID).
		Return(model.Tenant{}, nil)

	metaBackupSvc.
		EXPECT().
		GetAllMetaSnapshots(c, tenantID, int32(*offset), int32(*limit)).
		Return(items, nil)

	metaBackupSvc.
		EXPECT().
		GetAllMetaSnapshotsCount(c, tenantID).
		Return(size, nil)

	controller.GetTenantTenantIdBackup(c, tenantID, spec.GetTenantTenantIdBackupParams{
		Offset: offset,
		Limit:  limit,
	})
	assert.Equal(t, http.StatusOK, c.Writer.Status())

	assert.Equal(t, toJSON(t, spec.BackupSnapshotsSizePage{
		Limit:  *limit,
		Offset: *offset,
		Items:  itemsSpec,
		Size:   size,
	}), r.Body.String())
}

func TestPostTenantTenantIdBackup(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	var (
		orgID                   = uuid.Must(uuid.NewRandom())
		tenantID         uint64 = 1
		backupWorkflowID        = uuid.Must(uuid.NewRandom())
		snapshotID              = uuid.Must(uuid.NewRandom())
	)

	type testCase struct {
		metabackupResErr   error
		expectedStatusCode int
	}

	testCases := []testCase{
		{
			metabackupResErr:   eris.New("tenant still snapshotting").WithCode(eris.CodeFailedPrecondition),
			expectedStatusCode: http.StatusBadRequest,
		},
		{
			metabackupResErr:   eris.New("something went wrong"),
			expectedStatusCode: http.StatusInternalServerError,
		},
		{
			expectedStatusCode: http.StatusAccepted,
		},
	}

	for _, tc := range testCases {
		svc := mock_service.NewMockTenantServiceInterface(ctrl)
		metaBackupSvc := mock_service.NewMockMetaBackupService(ctrl)

		controller := &UserController{
			tenantService:     svc,
			metaBackupService: metaBackupSvc,
		}

		c, _ := newTestContext(orgID)

		svc.
			EXPECT().
			GetOrgTenantByID(c, orgID, tenantID).
			Return(model.Tenant{}, nil)

		if tc.metabackupResErr != nil {
			metaBackupSvc.EXPECT().
				StartMetaBackup(c, tenantID).
				Return(nil, nil, tc.metabackupResErr)
		} else {
			metaBackupSvc.EXPECT().
				StartMetaBackup(c, tenantID).
				Return(&backupWorkflowID, &snapshotID, nil)
		}

		controller.PostTenantTenantIdBackup(c, tenantID)
		assert.Equal(t, tc.expectedStatusCode, c.Writer.Status())
	}
}

func TestPostTenantTenantIdBackupSnapshotIdRestore(t *testing.T) {
	type testCase struct {
		name       string
		setupMocks func(
			c *gin.Context,
			orgID, snapshotID uuid.UUID,
			newTenantName string,
			mockQuerier *mockmodel.MockQuerier,
			mockTenantSvc *mock_service.MockTenantServiceInterface,
			mockMetaBackupSvc *mock_service.MockMetaBackupService,
			mockAccountAgent *account_mock.MockAgent,
			mockTenantExtensionSvc *mock_service.MockTenantExtensionService,

		) *UserController
		expectedStatus int
		newTenantName  string
	}

	orgID := uuid.Must(uuid.NewRandom())
	snapshotID := uuid.Must(uuid.NewRandom())
	oldTenantID := uint64(1)

	testCases := []testCase{
		{
			name: "successful restore",
			setupMocks: func(
				c *gin.Context,
				orgID,
				snapshotID uuid.UUID,
				newTenantName string,
				mockQuerier *mockmodel.MockQuerier,
				mockTenantSvc *mock_service.MockTenantServiceInterface,
				mockMetaBackupSvc *mock_service.MockMetaBackupService,
				mockAccountAgent *account_mock.MockAgent,
				mockTenantExtensionSvc *mock_service.MockTenantExtensionService,
			) *UserController {
				oldTenant := model.Tenant{
					ID:       1,
					TierID:   string(tier.Invited),
					ImageTag: "v2.3.0",
				}

				config.Conf.Region = "ap-southeast-1"
				mockTenantSvc.EXPECT().GetOrgTenantByID(c, orgID, oldTenantID).Return(oldTenant, nil)
				mockAccountAgent.EXPECT().
					CheckTenantAvailability(c, orgID, uint64(0), oldTenant.TierID, newTenantName, config.Conf.Region).
					Return(&acc_admin.AvailabilityResponse{Available: true}, nil)
				mockTenantExtensionSvc.EXPECT().GetSpecTenantExtensionsByTenantID(c, oldTenant.ID).Return(nil, nil)
				mockTenantSvc.EXPECT().RestoreTenant(c, oldTenant, newTenantName, snapshotID, nil).Return(&dto.Tenant{}, nil)

				controller := &UserController{
					tenantService:          mockTenantSvc,
					tenantExtensionService: mockTenantExtensionSvc,
					metaBackupService:      mockMetaBackupSvc,
					accountAgent:           mockAccountAgent,
					model:                  model.NewModel(mockQuerier),
				}

				return controller
			},
			expectedStatus: http.StatusAccepted,
		},
		{
			name: "failed precondition - version not supported",
			setupMocks: func(
				c *gin.Context,
				orgID,
				_ uuid.UUID,
				_ string,
				mockQuerier *mockmodel.MockQuerier,
				mockTenantSvc *mock_service.MockTenantServiceInterface,
				mockMetaBackupSvc *mock_service.MockMetaBackupService,
				mockAccountAgent *account_mock.MockAgent,
				mockTenantExtensionSvc *mock_service.MockTenantExtensionService,
			) *UserController {
				oldTenant := model.Tenant{
					ID:       1,
					TierID:   string(tier.Invited),
					ImageTag: "v2.2.9",
				}

				config.Conf.Region = "ap-southeast-1"
				mockTenantSvc.EXPECT().GetOrgTenantByID(c, orgID, oldTenantID).Return(oldTenant, nil)
				controller := &UserController{
					tenantService:          mockTenantSvc,
					tenantExtensionService: mockTenantExtensionSvc,
					metaBackupService:      mockMetaBackupSvc,
					accountAgent:           mockAccountAgent,
					model:                  model.NewModel(mockQuerier),
				}
				return controller
			},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "failed precondition - invalid new tenant name",
			setupMocks: func(
				c *gin.Context,
				orgID,
				_ uuid.UUID,
				_ string,
				mockQuerier *mockmodel.MockQuerier,
				mockTenantSvc *mock_service.MockTenantServiceInterface,
				mockMetaBackupSvc *mock_service.MockMetaBackupService,
				mockAccountAgent *account_mock.MockAgent,
				mockTenantExtensionSvc *mock_service.MockTenantExtensionService,
			) *UserController {
				oldTenant := model.Tenant{
					ID:       1,
					TierID:   string(tier.Invited),
					ImageTag: "v2.3.0",
				}

				config.Conf.Region = "ap-southeast-1"
				mockTenantSvc.EXPECT().GetOrgTenantByID(c, orgID, oldTenantID).Return(oldTenant, nil)
				controller := &UserController{
					tenantService:          mockTenantSvc,
					tenantExtensionService: mockTenantExtensionSvc,
					metaBackupService:      mockMetaBackupSvc,
					accountAgent:           mockAccountAgent,
					model:                  model.NewModel(mockQuerier),
				}
				return controller
			},
			expectedStatus: http.StatusBadRequest,
			newTenantName:  "&&&",
		},
		{
			name: "failed precondition - name unavailable",
			setupMocks: func(
				c *gin.Context,
				orgID,
				_ uuid.UUID,
				newTenantName string,
				mockQuerier *mockmodel.MockQuerier,
				mockTenantSvc *mock_service.MockTenantServiceInterface,
				mockMetaBackupSvc *mock_service.MockMetaBackupService,
				mockAccountAgent *account_mock.MockAgent,
				mockTenantExtensionSvc *mock_service.MockTenantExtensionService,
			) *UserController {
				oldTenant := model.Tenant{
					ID:       1,
					TierID:   string(tier.Invited),
					ImageTag: "v2.3.0",
				}

				config.Conf.Region = "ap-southeast-1"
				mockTenantSvc.EXPECT().GetOrgTenantByID(c, orgID, oldTenantID).Return(oldTenant, nil)
				mockAccountAgent.EXPECT().
					CheckTenantAvailability(c, orgID, uint64(0), oldTenant.TierID, newTenantName, config.Conf.Region).
					Return(&acc_admin.AvailabilityResponse{Available: false}, nil)
				controller := &UserController{
					tenantService:          mockTenantSvc,
					tenantExtensionService: mockTenantExtensionSvc,
					metaBackupService:      mockMetaBackupSvc,
					accountAgent:           mockAccountAgent,
					model:                  model.NewModel(mockQuerier),
				}
				return controller
			},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "internal service error - failed to get extension specs",
			setupMocks: func(
				c *gin.Context,
				orgID,
				_ uuid.UUID,
				newTenantName string,
				mockQuerier *mockmodel.MockQuerier,
				mockTenantSvc *mock_service.MockTenantServiceInterface,
				mockMetaBackupSvc *mock_service.MockMetaBackupService,
				mockAccountAgent *account_mock.MockAgent,
				mockTenantExtensionSvc *mock_service.MockTenantExtensionService,
			) *UserController {
				oldTenant := model.Tenant{
					ID:       1,
					TierID:   string(tier.Invited),
					ImageTag: "v2.3.0",
				}

				config.Conf.Region = "ap-southeast-1"
				mockTenantSvc.EXPECT().GetOrgTenantByID(c, orgID, oldTenantID).Return(oldTenant, nil)
				mockAccountAgent.EXPECT().
					CheckTenantAvailability(c, orgID, uint64(0), oldTenant.TierID, newTenantName, config.Conf.Region).
					Return(&acc_admin.AvailabilityResponse{Available: true}, nil)
				mockTenantExtensionSvc.EXPECT().GetSpecTenantExtensionsByTenantID(c, oldTenant.ID).Return(nil, eris.New("failed to get specs"))

				controller := &UserController{
					tenantService:          mockTenantSvc,
					tenantExtensionService: mockTenantExtensionSvc,
					metaBackupService:      mockMetaBackupSvc,
					accountAgent:           mockAccountAgent,
					model:                  model.NewModel(mockQuerier),
				}

				return controller
			},
			expectedStatus: http.StatusInternalServerError,
		},
		{
			name: "unsuccessful restore - invalid arg",
			setupMocks: func(
				c *gin.Context,
				orgID,
				snapshotID uuid.UUID,
				newTenantName string,
				mockQuerier *mockmodel.MockQuerier,
				mockTenantSvc *mock_service.MockTenantServiceInterface,
				mockMetaBackupSvc *mock_service.MockMetaBackupService,
				mockAccountAgent *account_mock.MockAgent,
				mockTenantExtensionSvc *mock_service.MockTenantExtensionService,
			) *UserController {
				oldTenant := model.Tenant{
					ID:       1,
					TierID:   string(tier.Invited),
					ImageTag: "v2.3.0",
				}

				config.Conf.Region = "ap-southeast-1"
				mockTenantSvc.EXPECT().GetOrgTenantByID(c, orgID, oldTenantID).Return(oldTenant, nil)
				mockAccountAgent.EXPECT().
					CheckTenantAvailability(c, orgID, uint64(0), oldTenant.TierID, newTenantName, config.Conf.Region).
					Return(&acc_admin.AvailabilityResponse{Available: true}, nil)
				mockTenantExtensionSvc.EXPECT().GetSpecTenantExtensionsByTenantID(c, oldTenant.ID).Return(nil, nil)
				mockTenantSvc.EXPECT().RestoreTenant(c, oldTenant, newTenantName, snapshotID, nil).Return(nil, eris.New("").WithCode(eris.CodeInvalidArgument))

				controller := &UserController{
					tenantService:          mockTenantSvc,
					tenantExtensionService: mockTenantExtensionSvc,
					metaBackupService:      mockMetaBackupSvc,
					accountAgent:           mockAccountAgent,
					model:                  model.NewModel(mockQuerier),
				}

				return controller
			},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "unsuccessful restore - aleady exists",
			setupMocks: func(
				c *gin.Context,
				orgID,
				snapshotID uuid.UUID,
				newTenantName string,
				mockQuerier *mockmodel.MockQuerier,
				mockTenantSvc *mock_service.MockTenantServiceInterface,
				mockMetaBackupSvc *mock_service.MockMetaBackupService,
				mockAccountAgent *account_mock.MockAgent,
				mockTenantExtensionSvc *mock_service.MockTenantExtensionService,
			) *UserController {
				oldTenant := model.Tenant{
					ID:       1,
					TierID:   string(tier.Invited),
					ImageTag: "v2.3.0",
				}

				config.Conf.Region = "ap-southeast-1"
				mockTenantSvc.EXPECT().GetOrgTenantByID(c, orgID, oldTenantID).Return(oldTenant, nil)
				mockAccountAgent.EXPECT().
					CheckTenantAvailability(c, orgID, uint64(0), oldTenant.TierID, newTenantName, config.Conf.Region).
					Return(&acc_admin.AvailabilityResponse{Available: true}, nil)
				mockTenantExtensionSvc.EXPECT().GetSpecTenantExtensionsByTenantID(c, oldTenant.ID).Return(nil, nil)
				mockTenantSvc.EXPECT().RestoreTenant(c, oldTenant, newTenantName, snapshotID, nil).Return(nil, eris.New("").WithCode(eris.CodeAlreadyExists))

				controller := &UserController{
					tenantService:          mockTenantSvc,
					tenantExtensionService: mockTenantExtensionSvc,
					metaBackupService:      mockMetaBackupSvc,
					accountAgent:           mockAccountAgent,
					model:                  model.NewModel(mockQuerier),
				}

				return controller
			},
			expectedStatus: http.StatusConflict,
		},
		{
			name: "unsuccessful restore - aleady exists",
			setupMocks: func(
				c *gin.Context,
				orgID,
				snapshotID uuid.UUID,
				newTenantName string,
				mockQuerier *mockmodel.MockQuerier,
				mockTenantSvc *mock_service.MockTenantServiceInterface,
				mockMetaBackupSvc *mock_service.MockMetaBackupService,
				mockAccountAgent *account_mock.MockAgent,
				mockTenantExtensionSvc *mock_service.MockTenantExtensionService,
			) *UserController {
				oldTenant := model.Tenant{
					ID:       1,
					TierID:   string(tier.Invited),
					ImageTag: "v2.3.0",
				}

				config.Conf.Region = "ap-southeast-1"
				mockTenantSvc.EXPECT().GetOrgTenantByID(c, orgID, oldTenantID).Return(oldTenant, nil)
				mockAccountAgent.EXPECT().
					CheckTenantAvailability(c, orgID, uint64(0), oldTenant.TierID, newTenantName, config.Conf.Region).
					Return(&acc_admin.AvailabilityResponse{Available: true}, nil)
				mockTenantExtensionSvc.EXPECT().GetSpecTenantExtensionsByTenantID(c, oldTenant.ID).Return(nil, nil)
				mockTenantSvc.EXPECT().RestoreTenant(c, oldTenant, newTenantName, snapshotID, nil).Return(nil, eris.New("").WithCode(eris.CodeNotFound))

				controller := &UserController{
					tenantService:          mockTenantSvc,
					tenantExtensionService: mockTenantExtensionSvc,
					metaBackupService:      mockMetaBackupSvc,
					accountAgent:           mockAccountAgent,
					model:                  model.NewModel(mockQuerier),
				}

				return controller
			},
			expectedStatus: http.StatusNotFound,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			c, _ := newTestContext(orgID, uuid.Nil)
			newTenantName := "testrestore"
			if tc.newTenantName != "" {
				newTenantName = tc.newTenantName
			}
			req := mgmt_admin.PostTenantRestoreRequestBody{
				NewTenantName: newTenantName,
			}
			body, err := json.Marshal(req)
			require.NoError(t, err)
			c.Request, err = http.NewRequest(http.MethodPost, "http://localhost/api/v2/tenants/restore", bytes.NewReader(body))
			require.NoError(t, err)

			mockQuerier := mockmodel.NewMockQuerier(ctrl)
			mockTenantSvc := mock_service.NewMockTenantServiceInterface(ctrl)
			mockTenantExtensionSvc := mock_service.NewMockTenantExtensionService(ctrl)
			mockMetaBackupSvc := mock_service.NewMockMetaBackupService(ctrl)
			mockAccountAgent := account_mock.NewMockAgent(ctrl)
			controller := tc.setupMocks(c, orgID, snapshotID, newTenantName, mockQuerier, mockTenantSvc, mockMetaBackupSvc, mockAccountAgent, mockTenantExtensionSvc)
			controller.PostTenantTenantIdBackupSnapshotIdRestore(c, oldTenantID, snapshotID)
			assert.Equal(t, tc.expectedStatus, c.Writer.Status())
		})
	}
}

func TestUnquiesceTenant(t *testing.T) {
	var (
		orgID           = uuid.Must(uuid.NewRandom())
		tenantID uint64 = 1
	)

	type testCase struct {
		name   string
		err    error
		status int
	}

	var tests = []testCase{
		{
			name:   "normal case",
			err:    nil,
			status: http.StatusAccepted,
		},
		{
			name:   "random error",
			err:    eris.New(""),
			status: http.StatusInternalServerError,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			svc := mock_service.NewMockTenantServiceInterface(ctrl)
			tenantOperationService := mock_service.NewMockTenantOperationService(ctrl)

			controller := &UserController{
				tenantService:          svc,
				tenantOperationService: tenantOperationService,
			}

			c, _ := newTestContext(orgID, uuid.Nil)

			svc.
				EXPECT().
				GetOrgTenantByID(c, orgID, tenantID).
				Return(model.Tenant{
					ID: tenantID,
				}, nil)

			tenantOperationService.EXPECT().
				StartInstance(c, tenantID).
				Return(test.err)

			controller.PostTenantsTenantIdUnquiesce(c, tenantID)
			assert.Equal(t, test.status, c.Writer.Status())
		})
	}
}
