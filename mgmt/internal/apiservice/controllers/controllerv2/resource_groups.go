package controllerv2

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/jinzhu/copier"
	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenant/resourcereq"
	"github.com/risingwavelabs/risingwave-cloud/shared/ginx"
	ginauth "github.com/risingwavelabs/risingwave-cloud/shared/ginx/auth"
	"github.com/risingwavelabs/risingwave-cloud/shared/spec/mgmt_spec_v2"
)

func (controller *UserController) GetTenantsNsIdResourceGroups(c *gin.Context, nsId uuid.UUID) {
	authInfo, err := ginauth.GetAuthInfo(c)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}
	tenant, err := controller.tenantService.GetOrgTenantByNsID(c, authInfo.OrgID, nsId)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err)
		return
	}

	details, err := controller.resourceGroupService.GetResourceGroupDetails(c, tenant)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}
	detailsSpec := []mgmt_spec_v2.ResourceGroupDetails{}
	err = copier.Copy(&detailsSpec, details)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	c.JSON(http.StatusOK, mgmt_spec_v2.GetResourceGroupsResponseBody{
		ResourceGroups: detailsSpec,
	})
}

func (controller *UserController) PostTenantsNsIdResourceGroups(c *gin.Context, nsId uuid.UUID) {
	authInfo, err := ginauth.GetAuthInfo(c)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	var req resourcereq.TenantResourceGroupRequest
	if err = c.BindJSON(&req); err != nil {
		ginx.InvalidArgumentError(c, err)
		return
	}

	tenant, err := controller.tenantService.GetOrgTenantByNsID(c, authInfo.OrgID, nsId)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err)
		return
	}
	if tenant.Status != model.TenantStatusRunning {
		ginx.FailedPrecondition(c, "Cluster is not running")
		return
	}

	_, err = controller.resourceGroupService.CreateResourceGroup(c, tenant, req)
	if err != nil && eris.GetCode(err) == eris.CodeInvalidArgument {
		ginx.InvalidArgumentError(c, err)
		return
	}
	if err != nil && eris.GetCode(err) == eris.CodeFailedPrecondition {
		ginx.FailedPreconditionError(c, err)
		return
	}
	if err != nil && eris.GetCode(err) == eris.CodeAlreadyExists {
		ginx.AlreadyExistsError(c, err)
		return
	}
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	ginx.Accepted(c, nil, "creating resource group")
}

func (controller *UserController) PostTenantsNsIdResourceGroupsResourceGroup(c *gin.Context, nsId uuid.UUID, resourceGroup string) {
	authInfo, err := ginauth.GetAuthInfo(c)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	var req mgmt_spec_v2.UpdateResourceGroupsRequestBody
	if err = c.BindJSON(&req); err != nil {
		ginx.InvalidArgumentError(c, err)
		return
	}

	tenant, err := controller.tenantService.GetOrgTenantByNsID(c, authInfo.OrgID, nsId)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err)
		return
	}
	if tenant.Status != model.TenantStatusRunning {
		ginx.FailedPrecondition(c, "Cluster is not running")
		return
	}

	request := resourcereq.TenantResourceGroupRequest{
		Name: resourceGroup,
		Resource: resourcereq.ComponentResourceRequest{
			ComponentTypeID: req.Resource.ComponentTypeId,
			Replica:         req.Resource.Replica,
		},
		FileCacheSizeGb: 0, // Don't support update disk cache in this API
	}
	_, err = controller.resourceGroupService.UpdateResourceGroup(c, tenant, request)
	if err != nil && eris.GetCode(err) == eris.CodeInvalidArgument {
		ginx.InvalidArgumentError(c, err)
		return
	}
	if err != nil && eris.GetCode(err) == eris.CodeNotFound {
		ginx.NotFoundError(c, err)
		return
	}
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	ginx.Accepted(c, nil, "updating resource group")
}

func (controller *UserController) DeleteTenantsNsIdResourceGroupsResourceGroup(c *gin.Context, nsId uuid.UUID, resourceGroup string) {
	authInfo, err := ginauth.GetAuthInfo(c)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	tenant, err := controller.tenantService.GetOrgTenantByNsID(c, authInfo.OrgID, nsId)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err)
		return
	}
	if tenant.Status != model.TenantStatusRunning {
		ginx.FailedPrecondition(c, "Cluster is not running")
		return
	}

	_, err = controller.resourceGroupService.DeleteResourceGroup(c, tenant, resourceGroup)
	if err != nil && eris.GetCode(err) == eris.CodeFailedPrecondition {
		ginx.FailedPreconditionError(c, err)
		return
	}
	if err != nil && eris.GetCode(err) == eris.CodeNotFound {
		ginx.NotFoundError(c, err)
		return
	}
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	ginx.Accepted(c, nil, "deleting resource group")
}
