package controllerv2

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/risingwavelabs/eris"
	"go.uber.org/zap"

	"github.com/risingwavelabs/risingwave-cloud/internal/apiservice/errors"
	"github.com/risingwavelabs/risingwave-cloud/internal/config"
	"github.com/risingwavelabs/risingwave-cloud/internal/managedcluster/byoc"
	clustersettings "github.com/risingwavelabs/risingwave-cloud/internal/managedcluster/settings"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/rwsecret"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/version"
	metastoredef "github.com/risingwavelabs/risingwave-cloud/internal/tenant/metastore"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenant/metastoremanager"
	resource "github.com/risingwavelabs/risingwave-cloud/internal/tenant/resourcereq"
	"github.com/risingwavelabs/risingwave-cloud/shared/ginx"
	ginauth "github.com/risingwavelabs/risingwave-cloud/shared/ginx/auth"
	"github.com/risingwavelabs/risingwave-cloud/shared/logger"
	"github.com/risingwavelabs/risingwave-cloud/shared/ptr"
	spec "github.com/risingwavelabs/risingwave-cloud/shared/spec/mgmt_spec_v2"
)

func (controller *UserController) PostTenantsNsIdStart(c *gin.Context, nsId uuid.UUID) {
	authInfo, err := ginauth.GetAuthInfo(c)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	tenant, err := controller.tenantService.GetOrgTenantByNsID(c, authInfo.OrgID, nsId)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err)
		return
	}
	if tenant.Status != model.TenantStatusStopped && tenant.Status != model.TenantStatusExpired {
		ginx.FailedPreconditionf(c, "illegal state: cluster should be stopped or expired")
		return
	}

	err = controller.tenantOperationService.StartInstance(c, tenant.ID)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	ginx.Accepted(c, nil, "starting cluster instance")
}

func (controller *UserController) PostTenantsNsIdStop(c *gin.Context, nsId uuid.UUID) {
	authInfo, err := ginauth.GetAuthInfo(c)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	tenant, err := controller.tenantService.GetOrgTenantByNsID(c, authInfo.OrgID, nsId)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err)
		return
	}
	if tenant.Status != model.TenantStatusRunning {
		ginx.FailedPreconditionf(c, "failed to stop the cluster when it is already stopped or in creation")
		return
	}

	err = controller.tenantOperationService.StopInstance(c, tenant.ID)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	ginx.Accepted(c, nil, "stopping cluster instance")
}

func (controller *UserController) PostTenantsNsIdRestart(c *gin.Context, nsId uuid.UUID) {
	authInfo, err := ginauth.GetAuthInfo(c)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	tenant, err := controller.tenantService.GetOrgTenantByNsID(c, authInfo.OrgID, nsId)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err)
		return
	}
	if tenant.Status != model.TenantStatusRunning {
		ginx.FailedPreconditionf(c, "illegal state: cluster should be running")
		return
	}

	err = controller.tenantOperationService.StartInstance(c, tenant.ID)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	ginx.Accepted(c, nil, "starting cluster instance")
}

func (controller *UserController) PostTenantsNsIdUpdateResource(c *gin.Context, nsId uuid.UUID) {
	var req spec.PostTenantResourcesRequestBody
	if err := c.BindJSON(&req); err != nil {
		ginx.InvalidArgumentError(c, err)
		return
	}

	if req.Meta == nil && req.Frontend == nil && req.Compactor == nil && req.Compute == nil && req.Standalone == nil {
		ginx.InvalidArgumentf(c, "at least one resource must be provided")
		return
	}

	// Get user info.
	authInfo, err := ginauth.GetAuthInfo(c)
	if err != nil {
		ginx.InternalError(c, eris.Wrap(err, "unable to get user info: "))
		return
	}

	tenant, err := controller.tenantService.GetOrgTenantByNsID(c, authInfo.OrgID, nsId)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err)
		return
	}

	if tenant.Status == model.TenantStatusUpdating {
		ginx.FailedPreconditionf(c, "cluster is already updating")
		return
	}
	if tenant.Status != model.TenantStatusRunning {
		ginx.FailedPreconditionf(c, "cluster is not running")
		return
	}

	reqResource, err := toTenantResourceRequestComponents(&req)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}
	// check resource request
	validation, err := resource.ParseResourceUpdate(tenant.TierID, *reqResource)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}
	if !validation.IsValid {
		ginx.InvalidArgument(c, validation.Message)
		return
	}

	// reject extension update request when this is a standalone tenant
	if req.Extensions != nil && tenant.Resources.IsStandalone() {
		ginx.FailedPreconditionf(c, "Illegal status, cannot update extension resources for standalone tenant")
		return
	}

	extension, err := controller.tenantExtensionService.GetTenantExtensionByTenantIDAndResourceType(c, tenant.ID, model.ExtensionsResourceTypeCompaction)
	if err != nil && !eris.Is(err, errors.ErrTenantExtensionNotExist) {
		ginx.AdminInternalError(c, err)
		return
	}
	compactionOptions, err := validation.WillUpdateExtension(&tenant, extension, req.Extensions)
	if err != nil {
		ginx.FailedPreconditionError(c, err)
		return
	}
	// write the compaction options to validation result
	validation.Result.ServerlessCompaction = compactionOptions

	if compactionOptions == nil && !validation.WillUpdate(tenant.Resources) {
		ginx.FailedPreconditionf(c, "unchanged by resources request and compaction extension")
		return
	}

	// validation.Result.Compactor only has two result: {nil} or {non nil and non zero replica}
	// reject this request when user provides both compactor and extension compaction.
	if req.Extensions != nil && req.Extensions.ServerlessCompaction != nil && validation.Result.Compactor != nil {
		ginx.FailedPreconditionf(c, "Illegal status, cannot update compactor resources and compaction extensions simultaneously")
		return
	}

	requestIsStandalone := validation.Result.Standalone != nil
	if extension != model.TenantExtensionNil && extension.Status == model.ExtensionStatusRunning && requestIsStandalone {
		ginx.FailedPreconditionf(c, "Illegal status, cannot convert to standalone when extensions compaction is running")
		return
	}
	if tenant.Resources.IsStandalone() != requestIsStandalone && !version.IsAutoScalingEnabled(tenant.ImageTag) {
		ginx.FailedPreconditionf(c, "Illegal status, cannot convert deployment mode for RisingWave < v1.7.0")
		return
	}
	if regular, nowIsRegular := tenant.Resources.AsTenantResourceSpecV1Regular(); requestIsStandalone && nowIsRegular && len(regular.ResourceGroups) != 0 {
		ginx.FailedPreconditionf(c, "Illegal status, cannot convert deployment with resource groups exist")
		return
	}

	// update resources
	_, err = controller.tenantOperationService.UpdateTenantResources(c, tenant.ID, validation.Result, validation.AllowsTrial)
	if err != nil {
		ginx.InternalError(c, eris.Wrap(err, "unable to update cluster resources"))
		return
	}
	ginx.Accepted(c, nil, "cluster update submitted")
}

func (controller *UserController) PostTenantsNsIdUpdateVersion(c *gin.Context, nsId uuid.UUID) {
	authInfo, err := ginauth.GetAuthInfo(c)
	if err != nil {
		ginx.InternalError(c, eris.Wrap(err, "unable to determine user info:"))
		return
	}

	var req spec.PostTenantUpdateVersionRequestBody
	if err = c.BindJSON(&req); err != nil {
		ginx.InvalidArgumentError(c, err)
		return
	}

	tenant, err := controller.tenantService.GetOrgTenantByNsID(c, authInfo.OrgID, nsId)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err)
		return
	}

	// check tenant running status
	tenantStatus := tenant.Status
	if tenantStatus == model.TenantStatusImageTagUpgrading {
		ginx.FailedPreconditionf(c, "the cluster currently is updating the image tag")
		return
	}
	if tenantStatus != model.TenantStatusRunning {
		ginx.FailedPreconditionf(c, "cannot update image tag since cluster is not running, current status: %s", tenantStatus)
		return
	}

	// get the latest image tag if not specified
	imageTag := config.Conf.Mgmt.RisingWave.Tag
	if req.Version != nil {
		imageTag = *req.Version
	}

	// check if the tenant is already on the target version
	if tenant.ImageTag == imageTag {
		ginx.Accepted(c, nil, fmt.Sprintf("The version is already %s", imageTag))
		return
	}

	// check if the target version is compatible with the current version
	if ok, err := version.IsUpgradeCompatible(tenant.ImageTag, imageTag); err != nil {
		logger.FromCtx(c).Error("failed to check upgrade compatibility", zap.Error(err))
		ginx.FailedPreconditionf(c, "invalid version %s", imageTag)
		return
	} else if !ok {
		ginx.FailedPreconditionf(c, "cannot upgrade from %s to %s, compatiblity constraint violation", tenant.ImageTag, imageTag)
		return
	}

	if !version.IsPgMetaStoreAvailable(tenant.ImageTag) && version.IsEtcdDeprecated(imageTag) {
		ginx.FailedPreconditionf(c, "cannot upgrade: new version doesn't support ETCD meta store, please upgrade to 2.0.5 first")
		return
	}
	if version.IsEtcdDeprecated(imageTag) && tenant.Resources.GetMetaStore().Type == metastoredef.Etcd {
		tenantSpec := metastoremanager.TenantSpec{
			TierID:    tenant.TierID,
			ClusterID: tenant.ClusterID,
			Resource:  tenant.Resources,
		}
		_, err = controller.metaStoreManager.FindAvailableMetaStore(c, tenantSpec, metastoremanager.PgMetaStore)
		if err != nil && ptr.Unwrap(eris.GetPropertyP[bool](err, metastoremanager.ErrorKeyMetaStoreUnavailable)) {
			ginx.FailedPreconditionf(c, "etcd is deprecated after new version, no available meta store found")
			return
		}
		if err != nil {
			ginx.InternalError(c, err)
			return
		}
	}
	isCloud, err := clustersettings.IsCloudCluster(c, controller.model, tenant.ClusterID)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	if !isCloud && version.IsPgMetaStoreAvailable(imageTag) {
		byocCluster, err := controller.clusterRepo.GetClusterByID(c, tenant.ClusterID)
		if err != nil {
			ginx.InternalError(c, err)
			return
		}
		byocVersion, err := byoc.GetClusterVersion(byocCluster)
		if err != nil {
			ginx.InternalError(c, err)
			return
		}
		if !byoc.IsPgMetaStoreSupported(byocVersion) {
			ginx.FailedPreconditionf(c, "pg metasore compatiable cluster version requires a BYOC environemnt with a minimum version of %s, your current BYOC environment version is %s, please upgrade your BYOC version and retry", byoc.MinPGMetaStoreVersion, byocVersion)
			return
		}
	}

	if rwsecret.IsRisingwaveSecretStoreEnabled() {
		err = controller.tenantService.SetTenantSecretStoreDek(c, tenant.ID)
		if err != nil {
			ginx.AdminInternalError(c, err)
			return
		}
	}

	_, err = controller.tenantOperationService.UpdateRwImageTag(c, tenant.ID, imageTag, tenant.ImageTag, false)
	if err != nil && eris.GetCode(err) == eris.CodeInvalidArgument {
		ginx.InvalidArgumentError(c, err)
		return
	}
	if err != nil {
		ginx.InternalError(c, err)
		return
	}
	ginx.Accepted(c, nil, "Cluster Update is in progress.")
}
