package controllerv2

import (
	"bytes"
	"context"
	"net/http"
	"strings"
	"time"

	"github.com/risingwavelabs/risingwave-cloud/internal/apiservice/middleware/tenantops"
	"github.com/risingwavelabs/risingwave-cloud/internal/metrics"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/jinzhu/copier"
	"github.com/prometheus/common/expfmt"
	"github.com/risingwavelabs/eris"
	"go.uber.org/zap"

	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/extensions"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/version"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/provision"

	"github.com/risingwavelabs/risingwave-cloud/internal/apiservice/controllers/common"
	"github.com/risingwavelabs/risingwave-cloud/internal/apiservice/dto"
	"github.com/risingwavelabs/risingwave-cloud/internal/apiservice/services"
	"github.com/risingwavelabs/risingwave-cloud/internal/apiservice/sku"
	"github.com/risingwavelabs/risingwave-cloud/internal/config"
	"github.com/risingwavelabs/risingwave-cloud/internal/managedcluster"
	"github.com/risingwavelabs/risingwave-cloud/internal/managedcluster/byoc"
	clustersettings "github.com/risingwavelabs/risingwave-cloud/internal/managedcluster/settings"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/client"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/rwdb"
	"github.com/risingwavelabs/risingwave-cloud/internal/rwproxy"
	metastoredef "github.com/risingwavelabs/risingwave-cloud/internal/tenant/metastore"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenant/metastoremanager"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenant/resourcereq"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenant/resourcespec"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/delete"
	"github.com/risingwavelabs/risingwave-cloud/shared/ginx"
	ginauth "github.com/risingwavelabs/risingwave-cloud/shared/ginx/auth"
	"github.com/risingwavelabs/risingwave-cloud/shared/logger"
	"github.com/risingwavelabs/risingwave-cloud/shared/namespace"
	"github.com/risingwavelabs/risingwave-cloud/shared/ptr"
	spec "github.com/risingwavelabs/risingwave-cloud/shared/spec/mgmt_spec_v2"
)

func (controller *UserController) PostTenants(c *gin.Context) {
	authInfo, err := ginauth.GetAuthInfo(c)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	tierInfo, err := tenantops.GetOrgTierInfo(c)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	var req spec.PostTenantsJSONRequestBody
	if err = c.BindJSON(&req); err != nil {
		ginx.InvalidArgumentError(c, err)
		return
	}

	// get tenant resource spec
	var tenantResources resourcereq.TenantResourceRequest
	var tierID string
	var configContent string

	var tenantSKU string
	if req.Sku != nil { // if sku is specified, then get the corresponding tenant resource
		tenantSKU = *req.Sku
		s, err := sku.GetSku(tenantSKU)
		if err != nil {
			ginx.InvalidArgumentError(c, err)
			return
		}
		tenantResources = s.Resources
		tierID = string(s.TierID)
	} else { // have to check the resource spec
		if req.Resources == nil || req.Tier == nil {
			ginx.InvalidArgumentf(c, "missing resources specification")
			return
		}
		tr, err := toTenantResources(req.Resources)
		if err != nil {
			ginx.InternalError(c, err)
			return
		}
		tenantResources = tr
		tierID = string(*req.Tier)
	}

	if tierID == string(spec.BYOC) && !tierInfo.AllowsByoc() {
		ginx.FailedPrecondition(c, "not allowed to provision a BYOC tenant")
		return
	}

	imageTag := config.Conf.Mgmt.RisingWave.Tag
	if req.ImageTag != nil && *req.ImageTag != "" {
		imageTag = *req.ImageTag
	}

	if !version.IsComputeCacheEnabled(imageTag) {
		tenantResources.ComputeFileCacheSizeGiB = 0
	}

	validation, err := resourcereq.ParseTenantResource(tierID, tenantResources)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}
	if !validation.IsValid {
		ginx.InvalidArgument(c, validation.Message)
		return
	}
	resources := validation.Result

	// Construct the serverless compaction options and reset the compactor replica to 0.
	var serverlessCompactionOpts *provision.ServerlessCompactionOptions
	if req.Extensions != nil && req.Extensions.ServerlessCompaction != nil {
		rc := config.GetResourceDef()
		tierDef := rc.GetTierByID(tierID)
		if tierDef == nil || !tierDef.ServerlessCompactionOptions.Allowed {
			ginx.FailedPreconditionf(c, "serverless compaction is not allowed for tier %s", tierID)
			return
		}
		serverlessCompactionOpts = &provision.ServerlessCompactionOptions{
			MaximumCPUSize: req.Extensions.ServerlessCompaction.MaximumCompactionConcurrency,
		}

		if resources.IsStandalone() {
			ginx.InvalidArgumentf(c, "serverless compaction is not supported for standalone deployment")
			return
		}

		t, ok := resources.AsTenantResourceSpecV1Regular()
		if !ok {
			ginx.InternalError(c, eris.New("failed to cast"))
			return
		}
		serverlessCompactionOpts.OriginalCompactorReplica = extensions.GetServerlessCompactionOriginalCompactorReplica(t.CompactorReplica, t.ComputeReplica)
		t.CompactorReplica = 0
		resources = resourcespec.FromValidTenantResourceSpec(&t)
	}

	createAsTrial := validation.AllowsTrial && tierInfo.AllowsTrial()
	if !createAsTrial && !tierInfo.IsPaying() {
		// Not possible as trial, but required for the customer.
		ginx.FailedPrecondition(c, "cannot provision tenant as trial")
		return
	}

	tenantName := req.TenantName
	// validate tenant name
	if !namespace.IsValidResourceName(tenantName) {
		ginx.InvalidArgumentf(c, "The cluster name must only contain letters, numbers, and hyphens, with a length of no more than 32 characters.")
		return
	}

	var tenantService = controller.tenantService
	if exist, err := tenantService.OrgTenantNameExists(c, authInfo.OrgID, tenantName); err != nil {
		ginx.InternalError(c, err)
		return
	} else if exist {
		ginx.AlreadyExistsf(c, "Cluster already exists")
		return
	}

	check, err := controller.accountAgent.CheckTenantAvailability(c, authInfo.OrgID, 0, tierID, tenantName, config.Conf.Region)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}
	if !check.Available {
		ginx.FailedPrecondition(c, check.Msg)
		return
	}

	if req.ConfigId != nil {
		cfg, err := controller.accountAgent.GetConfigByID(c, *req.ConfigId)
		if err != nil {
			ginx.InternalError(c, err)
			return
		}
		if cfg == nil {
			ginx.NotFoundf(c, "no such config id: %s", *req.ConfigId)
			return
		}
		if cfg.OrgId != authInfo.OrgID {
			ginx.InvalidArgumentf(c, "invalid config id")
			return
		}
		configContent = cfg.Content
	} else if req.RwConfig != nil {
		configContent = *req.RwConfig
	}

	etcdConfigContent := ""
	if req.EtcdConfig != nil {
		etcdConfigContent = *req.EtcdConfig
	}

	var cluster *model.Cluster
	var isCloud bool
	if req.ClusterName != nil {
		// Using BYOC cluster.
		if tierID != string(spec.BYOC) {
			ginx.InvalidArgumentf(c, "BYOC cluster must use BYOC tier")
			return
		}
		cluster, err = controller.model.GetClusterByOrgName(c, authInfo.OrgID, *req.ClusterName)
		if eris.Is(err, pgx.ErrNoRows) {
			ginx.InvalidArgumentf(c, "BYOC cluster not found")
			return
		}
		if err != nil {
			ginx.InternalError(c, err)
			return
		}
		if cluster.Status != string(managedcluster.ClusterStatusReady) {
			ginx.FailedPreconditionf(c, "BYOC cluster is not ready")
			return
		}
	} else {
		isCloud = true
		// Using cloud cluster, first pick the cluster.
		if tierID == string(spec.BYOC) {
			ginx.InvalidArgumentf(c, "BYOC tier can only be used on BYOC cluster")
			return
		}

		cluster, err = controller.model.PickCloudCluster(c, string(managedcluster.ServingTypeFreeTier))
		if err != nil {
			ginx.InternalError(c, err)
			return
		}
	}

	// TODO(CLOUD-3222)
	// if meta store is empty, decide meta store by mgmt backend
	metaStore := ptr.Ptr(resources.GetMetaStore())
	if metaStore.Type == metastoredef.Etcd && metaStore.Etcd.SizeGb == 0 {
		metaStore, err = controller.metaStoreManager.FindPreferMetaStore(c, metastoremanager.TenantSpec{
			TierID:    tierID,
			ClusterID: cluster.ID,
			Resource:  resources,
		})
		if err != nil && eris.GetCode(err) == eris.CodeInvalidArgument {
			ginx.InvalidArgumentError(c, err)
			return
		}
		if err != nil {
			ginx.InternalError(c, err)
			return
		}

		if resources.IsStandalone() {
			t, ok := resources.AsTenantResourceSpecV1Standalone()
			if !ok {
				ginx.InternalError(c, err)
				return
			}
			t.MetaStoreSpec = metaStore
			resources = resourcespec.FromValidTenantResourceSpec(&t)
		} else {
			t, ok := resources.AsTenantResourceSpecV1Regular()
			if !ok {
				ginx.InternalError(c, err)
				return
			}
			t.MetaStoreSpec = metaStore
			resources = resourcespec.FromValidTenantResourceSpec(&t)
		}
	}

	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	if !isCloud && metaStore.Type != metastoredef.Etcd {
		byocCluster, err := controller.clusterRepo.GetClusterByID(c, cluster.ID)
		if err != nil {
			ginx.InternalError(c, err)
			return
		}
		byocVersion, err := byoc.GetClusterVersion(byocCluster)
		if err != nil {
			ginx.InternalError(c, err)
			return
		}
		if !byoc.IsPgMetaStoreSupported(byocVersion) {
			ginx.FailedPreconditionf(c, "pg metasore compatiable cluster version requires a BYOC environemnt with a minimum version of %s, your current BYOC environment version is %s, please upgrade your BYOC version or explicitly specify etcd as metastore", byoc.MinPGMetaStoreVersion, byocVersion)
			return
		}
	}

	usageType := string(spec.TenantUsageTypeGeneral)
	if req.UsageType != nil {
		usageType = string(*req.UsageType)
	}

	tenantID, err := tenantService.ProvisionTenant(c, services.ProvisionTenantOption{
		OrgID:                authInfo.OrgID,
		TenantName:           tenantName,
		TierID:               tierID,
		ImageTag:             imageTag,
		Region:               config.Conf.Region,
		ClusterID:            cluster.ID,
		RwConfig:             configContent,
		EtcdConfig:           etcdConfigContent,
		Resource:             resources,
		AsTrial:              createAsTrial,
		ServerlessCompaction: serverlessCompactionOpts,
		SKU:                  tenantSKU,
		UsageType:            usageType,
	})
	if err != nil && eris.GetCode(err) == eris.CodeInvalidArgument {
		ginx.InvalidArgumentError(c, err)
		return
	}
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	tenant, err := tenantService.GetTenantByID(c, tenantID)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}
	rtn, err := tenantService.TenantToSpec(c, tenant)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	if rtn.NsID == uuid.Nil {
		nsID, _ := namespace.Parse(tenant.ResourceNamespace)
		rtn.NsID = nsID
	}

	c.JSON(202, &rtn)
}

func (controller *UserController) GetTenants(c *gin.Context, params spec.GetTenantsParams) {
	authInfo, err := ginauth.GetAuthInfo(c)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	var tenantService = controller.tenantService

	limit, offset, size, tenants, err := tenantService.GetTenantsByOrgID(c, authInfo.OrgID, params.Offset, params.Limit)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	ret := []spec.Tenant{}
	for _, tenant := range tenants {
		tenantDTO, err := tenantService.TenantToSpec(c, *tenant)
		if err != nil {
			ginx.InternalError(c, err)
			return
		}

		if tenantDTO.NsID == uuid.Nil {
			// Outdated or invalid namespace.
			// Query ASDB for the ID.
			accTenant, err := controller.accountAgent.GetTenantInAccountService(
				c, tenantDTO.Region, tenantDTO.ID,
			)
			if err != nil {
				ginx.InternalError(c, err)
				return
			}

			tenantDTO.NsID = accTenant.NsId
		}

		tenantSpec, err := toTenantSpec(&tenantDTO)
		if err != nil {
			ginx.InternalError(c, err)
			return
		}
		ret = append(ret, *tenantSpec)
	}

	c.JSON(200, spec.TenantPagination{
		Pagination: &spec.Pagination{
			Limit:  limit,
			Offset: offset,
			Size:   size,
		},
		Tenants: ret,
	})
}

func (controller *UserController) GetTenantsNsIdEndpoint(c *gin.Context, nsID uuid.UUID) {
	authInfo, err := ginauth.GetAuthInfo(c)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}
	var tenantService = controller.tenantService
	var endpoint rwproxy.TenantEndpoint

	tenant, err := controller.tenantService.GetOrgTenantByNsID(c, authInfo.OrgID, nsID)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err)
		return
	}

	endpoint, err = tenantService.GetEndpointByTenant(c, &tenant)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	rtn := spec.Endpoint{
		Database:     endpoint.Database,
		Host:         endpoint.PublicEndpoint.Host,
		Options:      endpoint.Options,
		Port:         endpoint.PublicEndpoint.Port,
		InternalHost: endpoint.InternalEndpoint.Host,
		InternalPort: endpoint.InternalEndpoint.Port,
	}
	if endpoint.AWSVPCEndpointService != nil {
		rtn.AwsServingPrivateLink = &spec.AWSServingPrivateLinkInfo{
			Host:        endpoint.AWSVPCEndpointService.AWSHost,
			Port:        endpoint.AWSVPCEndpointService.AWSPort,
			ServiceName: endpoint.AWSVPCEndpointService.AWSServiceName,
			Azs:         endpoint.AWSVPCEndpointService.AWSAZs,
		}
	}

	c.JSON(200, rtn)
}

func (controller *UserController) GetTenantsNsId(c *gin.Context, nsID uuid.UUID) {
	authInfo, err := ginauth.GetAuthInfo(c)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	tenant, err := controller.tenantService.GetOrgTenantByNsID(c, authInfo.OrgID, nsID)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err)
		return
	}

	tenantExtensions, err := controller.tenantExtensionService.GetSpecTenantExtensionsByTenantID(c, tenant.ID)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err)
		return
	}

	rtn, err := controller.tenantService.TenantToSpec(c, tenant)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}
	// Set the extensions explicitly.
	rtn.Extensions = tenantExtensions

	if rtn.NsID == uuid.Nil {
		// Outdated or invalid namespace.
		// Query ASDB for the ID.
		accTenant, err := controller.accountAgent.GetTenantInAccountService(
			c, rtn.Region, rtn.ID,
		)
		if err != nil {
			ginx.InternalError(c, err)
			return
		}

		rtn.NsID = accTenant.NsId
	}

	c.JSON(200, &rtn)
}

func (controller *UserController) DeleteTenantsNsId(c *gin.Context, nsID uuid.UUID) {
	authInfo, err := ginauth.GetAuthInfo(c)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	tenant, err := controller.tenantService.GetOrgTenantByNsID(c, authInfo.OrgID, nsID)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err)
		return
	}

	_, err = controller.tenantService.DeleteTenantByID(c, tenant.ID)
	switch {
	case eris.Is(err, delete.DeleteTenantInCreatingError):
		ginx.FailedPreconditionf(c, "Cannot delete cluster in creating.")
		return
	case eris.GetCode(err) == eris.CodeFailedPrecondition:
		ginx.FailedPreconditionError(c, err)
	case err != nil:
		ginx.InternalError(c, err)
		return
	default:
		ginx.Accepted(c, nil, "Cluster deletion is in progress.")
		return
	}
}

const DefaultExecutionTimeout = 5000
const DefaultMaxRows = 1000

func (controller *UserController) PostTenantsNsIdDatabasesDatabaseNameExecuteSQL(c *gin.Context, nsID uuid.UUID, databaseName string) {
	authInfo, err := ginauth.GetAuthInfo(c)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	var req spec.SqlExecutionRequestBody
	if err = c.BindJSON(&req); err != nil {
		ginx.InvalidArgumentError(c, err)
		return
	}

	tenant, err := controller.tenantService.GetOrgTenantByNsID(c, authInfo.OrgID, nsID)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err)
		return
	}

	err = validateTenantStatus(tenant)
	if err != nil {
		ginx.FailedPreconditionError(c, err)
		return
	}

	password, err := controller.accountAgent.GetDataPlaneOauthToken(c, authInfo.AsUserInfo().UserResourceID, authInfo.OrgID)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	rwClient, err := client.ObtainRisingWaveClient(c, controller.model, tenant.ID, databaseName, client.RisingWaveCredential{
		Username: rwdb.DefaultOauthUsername,
		Password: password,
	})
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	err = rwClient.Ping(c, tenant.ImageTag)
	if err != nil {
		common.PingErrElseFailedPrecondition(c, err)
		return
	}

	timeout := ptr.UnwrapOr(req.Timeout, DefaultExecutionTimeout)
	timeoutCtx, cancelFunc := context.WithTimeout(c, time.Duration(timeout)*time.Millisecond)
	defer cancelFunc()

	err = rwClient.ExecuteSQL(timeoutCtx, req.Query)
	if err != nil {
		common.RwErrElseInternalError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"msg": "Execution succeeded",
	})
}

func (controller *UserController) PostTenantsNsIdDatabasesDatabaseNameQuerySQL(c *gin.Context, nsID uuid.UUID, databaseName string) {
	authInfo, err := ginauth.GetAuthInfo(c)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	var req spec.SqlQueryRequestBody
	if err = c.BindJSON(&req); err != nil {
		ginx.InvalidArgumentError(c, err)
		return
	}

	tenant, err := controller.tenantService.GetOrgTenantByNsID(c, authInfo.OrgID, nsID)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err)
		return
	}

	err = validateTenantStatus(tenant)
	if err != nil {
		ginx.FailedPreconditionError(c, err)
		return
	}

	result, err := controller.rwc.QuerySQL(c, tenant.ID, databaseName, rwdb.QuerySQLOption{
		Query:       req.Query,
		MaxRowCount: req.MaxRowCount,
		Timeout:     req.Timeout,
	})
	if err != nil {
		common.RwErrElseInternalError(c, err)
		return
	}

	c.JSON(http.StatusOK, result)
}

func (controller *UserController) GetTenantsNsIdDatabasesDatabaseNameSchemas(c *gin.Context, nsID uuid.UUID, databaseName string) {
	authInfo, err := ginauth.GetAuthInfo(c)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	tenant, err := controller.tenantService.GetOrgTenantByNsID(c, authInfo.OrgID, nsID)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err)
		return
	}

	err = validateTenantStatus(tenant)
	if err != nil {
		ginx.FailedPreconditionError(c, err)
		return
	}

	err = controller.tenantService.PingTenant(c, tenant.ID, tenant.ImageTag)
	if err != nil {
		common.PingErrElseFailedPrecondition(c, err)
		return
	}

	schemas, err := controller.rwc.GetSchemas(c, tenant.ID, databaseName)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	specs, err := toSchemaInfo(schemas)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}
	c.JSON(http.StatusOK, spec.TenantSchemasInfo{
		Schemas: specs,
	})
}

func (controller *UserController) GetTenantsNsIdDatabasesDatabaseNameRelations(c *gin.Context, nsID uuid.UUID, databaseName string) {
	authInfo, err := ginauth.GetAuthInfo(c)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	tenant, err := controller.tenantService.GetOrgTenantByNsID(c, authInfo.OrgID, nsID)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err)
		return
	}

	password, err := controller.accountAgent.GetDataPlaneOauthToken(c, authInfo.AsUserInfo().UserResourceID, authInfo.OrgID)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	relations, err := controller.rwRelation.GetAllRelations(c.Request.Context(), tenant.ID, databaseName, rwdb.DefaultOauthUsername, password)
	if err != nil {
		common.RwErrElseInternalError(c, err)
		return
	}

	relationsSpec, err := toRelationInfoSpec(relations)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	c.JSON(http.StatusOK, spec.TenantRelationsInfo{
		Relations: relationsSpec,
	})
}

func (controller *UserController) DeleteTenantsNsIdDatabasesDatabaseNameRelationsRelName(c *gin.Context, nsID uuid.UUID, databaseName string, relName string) {
	authInfo, err := ginauth.GetAuthInfo(c)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	tenant, err := controller.tenantService.GetOrgTenantByNsID(c, authInfo.OrgID, nsID)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err)
		return
	}

	password, err := controller.accountAgent.GetDataPlaneOauthToken(c, authInfo.AsUserInfo().UserResourceID, authInfo.OrgID)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	err = controller.rwRelation.DropRelation(c.Request.Context(), tenant.ID, relName, databaseName, rwdb.DefaultOauthUsername, password)
	if err != nil {
		common.RwErrElseInternalError(c, err)
		return
	}

	ginx.JustOk(c)
}

func toTenantSpec(tenantDTO *dto.Tenant) (*spec.Tenant, error) {
	tenantSpec := &spec.Tenant{}
	err := copier.Copy(tenantSpec, tenantDTO)
	if err != nil {
		return nil, err
	}
	return tenantSpec, nil
}

func toTenantResourceRequestComponents(req *spec.PostTenantResourcesRequestBody) (*resourcereq.TenantResourceRequestComponents, error) {
	reqResource := &resourcereq.TenantResourceRequestComponents{}
	err := copier.Copy(reqResource, req)
	if err != nil {
		return nil, err
	}
	return reqResource, nil
}

func toTenantResources(req *spec.TenantResourceRequest) (resourcereq.TenantResourceRequest, error) {
	reqResource := resourcereq.TenantResourceRequest{}
	err := copier.Copy(&reqResource, req)
	if err != nil {
		return resourcereq.TenantResourceRequest{}, err
	}
	return reqResource, nil
}

func (controller *UserController) GetTenantsNsIdCaCert(c *gin.Context, nsID uuid.UUID) {
	authInfo, err := ginauth.GetAuthInfo(c)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	tenant, err := controller.tenantService.GetOrgTenantByNsID(c, authInfo.OrgID, nsID)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err)
		return
	}

	isCloud, err := clustersettings.IsCloudCluster(c, controller.model, tenant.ClusterID)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	var certPem []byte
	var certErr error
	if isCloud {
		certPem, certErr = rwproxy.GetRootCAModule().GetCertPem(c)
	} else {
		certPem, certErr = rwproxy.GetBYOCCertPem(c, controller.model, tenant.ClusterID)
	}
	if certErr != nil {
		ginx.InternalError(c, certErr)
		return
	}

	c.Header("content-disposition", "attachment; filename=root.crt")
	c.Header("content-transfer-encoding", "binary")
	c.Data(200, "application/x-pem-file", certPem)
}

func toRelationInfoSpec(relationsInfo []rwdb.RelationInfoWithBrief) ([]spec.RelationInfo, error) {
	if relationsInfo == nil {
		return nil, nil
	}
	relationsInfoSpec := make([]spec.RelationInfo, 0, len(relationsInfo))

	err := copier.Copy(&relationsInfoSpec, &relationsInfo)
	if err != nil {
		return nil, err
	}
	return relationsInfoSpec, nil
}

func toSchemaInfo(schemas []rwdb.SchemaInfo) ([]spec.SchemaInfo, error) {
	if schemas == nil {
		return nil, nil
	}
	schemaInfoSpec := make([]spec.SchemaInfo, len(schemas))
	err := copier.Copy(&schemaInfoSpec, &schemas)
	if err != nil {
		return nil, err
	}
	return schemaInfoSpec, nil
}

func (controller *UserController) GetTenantsNsIdMetrics(c *gin.Context, nsID uuid.UUID) {
	authInfo, err := ginauth.GetAuthInfo(c)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	tenant, err := controller.tenantService.GetOrgTenantByNsID(c, authInfo.OrgID, nsID)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err)
		return
	}

	if tenant.Status == model.TenantStatusStopped || tenant.Status == model.TenantStatusFailed {
		ginx.FailedPreconditionf(c, "tenant is in %s status", tenant.Status)
		return
	}

	const (
		accecptEncodingHeaderKey = "Accept-Encoding"
		contentEncodingHeaderKey = "Content-Encoding"
		gzipEncopding            = "gzip"
	)
	acceptedEncoding := ""

	if strings.Contains(c.Request.Header.Get(accecptEncodingHeaderKey), gzipEncopding) {
		acceptedEncoding = gzipEncopding
	}
	data, encoding, err := controller.metricsService.ScrapeMetrics(c, tenant, acceptedEncoding)
	if err != nil && eris.GetCode(err) == eris.CodeNotFound {
		// will return 404 when the namespace not found.
		// and not return the internal error message.
		ginx.NotFoundf(c, "cluster not exist")
		return
	}
	if err != nil && eris.GetCode(err) == eris.CodeFailedPrecondition {
		// will return 404 when the namespace not found.
		// and not return the internal error message.
		ginx.FailedPreconditionf(c, "%s, please add 'Accept-Encoding: gzip' to your request header and try again", eris.ToString(err, false))
		return
	}
	if err != nil {
		logger.FromCtx(c).Named("Tenant Service").Error("failed to scrape metrics", zap.Error(err))
		if tenant.Status != model.TenantStatusRunning {
			ginx.FailedPreconditionf(c, "illegal status, cluster is not running")
			return
		}
		ginx.InternalError(c, err)
		return
	}

	extraHeader := map[string]string{}
	if encoding != "" {
		extraHeader[contentEncodingHeaderKey] = encoding
	}

	c.DataFromReader(http.StatusOK, int64(len(data)), string(expfmt.NewFormat(expfmt.TypeTextPlain)), bytes.NewReader(data), extraHeader)
}

func (controller *UserController) PostTenantsNsIdTestAlert(c *gin.Context, nsID uuid.UUID) {
	authInfo, err := ginauth.GetAuthInfo(c)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	var req spec.PostTenantsTestAlertRequestBody
	if err := c.BindJSON(&req); err != nil {
		ginx.InvalidArgumentError(c, err)
		return
	}

	// Check if the tenant exists.
	tenant, err := controller.tenantService.GetOrgTenantByNsID(c, authInfo.OrgID, nsID)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err)
		return
	}

	metrics.ReportTenantTestAlert(tenant.NsID, req.Triggered)

	ginx.JustOk(c)
}
