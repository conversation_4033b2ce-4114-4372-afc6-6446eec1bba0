package controllerv2

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/risingwavelabs/eris"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"

	account_mock "github.com/risingwavelabs/risingwave-cloud/internal/account/mock"
	"github.com/risingwavelabs/risingwave-cloud/internal/apiservice/dto"
	"github.com/risingwavelabs/risingwave-cloud/internal/apiservice/errors"
	"github.com/risingwavelabs/risingwave-cloud/internal/apiservice/services"
	mock_service "github.com/risingwavelabs/risingwave-cloud/internal/apiservice/services/mock"
	"github.com/risingwavelabs/risingwave-cloud/internal/config"
	"github.com/risingwavelabs/risingwave-cloud/internal/managedcluster"
	"github.com/risingwavelabs/risingwave-cloud/internal/managedcluster/settings"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/rwdb"
	rwc_mock "github.com/risingwavelabs/risingwave-cloud/internal/risingwave/rwdb/mock"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenant/component"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenant/resourcespec"

	// "github.com/risingwavelabs/risingwave-cloud/internal/managedcluster/settings".
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	model_mock "github.com/risingwavelabs/risingwave-cloud/internal/model/mock"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/querier"
	"github.com/risingwavelabs/risingwave-cloud/internal/rwproxy"
	ginauth "github.com/risingwavelabs/risingwave-cloud/shared/ginx/auth"
	"github.com/risingwavelabs/risingwave-cloud/shared/org"
	"github.com/risingwavelabs/risingwave-cloud/shared/ptr"
	"github.com/risingwavelabs/risingwave-cloud/shared/spec/acc_admin"
	spec "github.com/risingwavelabs/risingwave-cloud/shared/spec/mgmt_spec_v2"
)

func TestPostTenants(t *testing.T) {
	userID := uuid.New()
	orgID := uuid.New()
	nsID := uuid.New()
	userInfo := &ginauth.ControllerUserInfo{
		Email:          "<EMAIL>",
		UserResourceID: userID,
		OrgID:          orgID,
	}
	tenantName := "test-tenant"
	byocClusterName := "byoc-cluster"

	//
	// Resource Definitions

	// We define small and large components/resources, indicated with `_S` and
	// `_L`, respectively. The small components can be used as trial tenants.
	// That way, we can test if the decisions on trial tenants work as expected.

	availableComponentTypes := []*config.AvailableComponentType{
		{ComponentType: &config.ComponentType{ID: "p-1c4g"}, MaximumReplica: 1, MaximumTrialReplica: 1},
		{ComponentType: &config.ComponentType{ID: "p-2c8g"}, MaximumReplica: 1},
	}

	resourcerequestS := &spec.ComponentResourceRequest{ComponentTypeId: "p-1c4g", Replica: 1}
	resourcerequestL := &spec.ComponentResourceRequest{ComponentTypeId: "p-2c8g", Replica: 1}

	resourcesrequestS := &spec.TenantResourceRequest{
		Components: spec.TenantResourceRequestComponents{
			Compute:   resourcerequestS,
			Frontend:  resourcerequestS,
			Compactor: resourcerequestS,
			Meta:      resourcerequestS,
			Etcd:      resourcerequestS,
		},
		EtcdVolumeSizeGiB: ptr.Ptr(1),
	}
	resourcesrequestL := &spec.TenantResourceRequest{
		Components: spec.TenantResourceRequestComponents{
			Compute:   resourcerequestL,
			Frontend:  resourcerequestL,
			Compactor: resourcerequestL,
			Meta:      resourcerequestL,
			Etcd:      resourcerequestL,
		},
		EtcdVolumeSizeGiB: ptr.Ptr(1),
	}

	initializeConfigResourceDef(availableComponentTypes)

	tenant := model.Tenant{
		ID:           1,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
		OrgID:        orgID,
		TenantName:   tenantName,
		Status:       "Creating",
		HealthStatus: "Unknown",
	}
	tenantDTO := dto.Tenant{
		CreatedAt:    tenant.CreatedAt,
		HealthStatus: "Unknown",
		ID:           1,
		NsID:         nsID,
		OrgID:        orgID,
		Status:       "Creating",
		TenantName:   tenantName,
		UpdatedAt:    tenant.UpdatedAt,
	}

	tests := []struct {
		name             string
		userInfo         *ginauth.ControllerUserInfo
		orgTier          org.TierInfo
		request          *spec.PostTenantsJSONRequestBody
		computeCacheSize int32
		imageTag         string
		mockService      func(*mock_service.MockTenantServiceInterface)
		mockAccount      func(*account_mock.MockAgent)
		mockQuerier      func(*model_mock.MockQuerier)

		exceptCode int
		exceptMsg  string
	}{
		{
			name:     "cloud",
			userInfo: userInfo,
			orgTier:  org.PreAccTierInvited(),
			request: &spec.PostTenantsJSONRequestBody{
				TenantName: tenantName,
				Tier:       ptr.Ptr(spec.Standard),
				Resources:  resourcesrequestS,
			},
			mockService: func(service *mock_service.MockTenantServiceInterface) {
				service.EXPECT().OrgTenantNameExists(gomock.Any(), gomock.Eq(orgID), gomock.Eq(tenantName)).
					Times(1).Return(false, nil)
				service.EXPECT().ProvisionTenant(gomock.Any(), gomock.Any()).
					Times(1).Return(uint64(1), nil)
				service.EXPECT().GetTenantByID(gomock.Any(), gomock.Eq(uint64(1))).
					Times(1).Return(tenant, nil)
				service.EXPECT().TenantToSpec(gomock.Any(), gomock.Eq(tenant)).
					Times(1).Return(tenantDTO, nil)
			},
			mockAccount: func(agent *account_mock.MockAgent) {
				agent.EXPECT().CheckTenantAvailability(gomock.Any(), gomock.Eq(orgID), gomock.Eq(uint64(0)), gomock.Eq(string(spec.Standard)), gomock.Eq(tenantName), gomock.Eq(config.Conf.Region)).
					Times(1).Return(&acc_admin.AvailabilityResponse{Available: true}, nil)
			},
			mockQuerier: func(queries *model_mock.MockQuerier) {
				queries.EXPECT().PickCloudClusterByServingType(gomock.Any(), gomock.Eq(string(managedcluster.ServingTypeFreeTier))).Times(1).Return(&querier.ManagedCluster{
					Status: string(managedcluster.ClusterStatusReady),
				}, nil)
			},

			exceptCode: 202,
		},
		{
			name:     "cloud - enable compute cache with supported version",
			userInfo: userInfo,
			orgTier:  org.PreAccTierInvited(),
			request: &spec.PostTenantsJSONRequestBody{
				TenantName: tenantName,
				Tier:       ptr.Ptr(spec.Standard),
				Resources:  resourcesrequestS,
			},
			computeCacheSize: 50,
			imageTag:         "v1.10.1",
			mockService: func(service *mock_service.MockTenantServiceInterface) {
				service.EXPECT().OrgTenantNameExists(gomock.Any(), gomock.Eq(orgID), gomock.Eq(tenantName)).
					Times(1).Return(false, nil)
				service.EXPECT().ProvisionTenant(gomock.Any(), EqComputeCacheSize(50)).
					Times(1).Return(uint64(1), nil)
				service.EXPECT().GetTenantByID(gomock.Any(), gomock.Eq(uint64(1))).
					Times(1).Return(tenant, nil)
				service.EXPECT().TenantToSpec(gomock.Any(), gomock.Eq(tenant)).
					Times(1).Return(tenantDTO, nil)
			},
			mockAccount: func(agent *account_mock.MockAgent) {
				agent.EXPECT().CheckTenantAvailability(gomock.Any(), gomock.Eq(orgID), gomock.Eq(uint64(0)), gomock.Eq(string(spec.Standard)), gomock.Eq(tenantName), gomock.Eq(config.Conf.Region)).
					Times(1).Return(&acc_admin.AvailabilityResponse{Available: true}, nil)
			},
			mockQuerier: func(queries *model_mock.MockQuerier) {
				queries.EXPECT().PickCloudClusterByServingType(gomock.Any(), gomock.Eq(string(managedcluster.ServingTypeFreeTier))).Times(1).Return(&querier.ManagedCluster{
					Status: string(managedcluster.ClusterStatusReady),
				}, nil)
			},

			exceptCode: 202,
		},
		{
			name:     "cloud - enable compute cache with non-supported version",
			userInfo: userInfo,
			orgTier:  org.PreAccTierInvited(),
			request: &spec.PostTenantsJSONRequestBody{
				TenantName: tenantName,
				Tier:       ptr.Ptr(spec.Standard),
				Resources:  resourcesrequestS,
			},
			computeCacheSize: 50,
			imageTag:         "v1.9.1",
			mockService: func(service *mock_service.MockTenantServiceInterface) {
				service.EXPECT().OrgTenantNameExists(gomock.Any(), gomock.Eq(orgID), gomock.Eq(tenantName)).
					Times(1).Return(false, nil)
				service.EXPECT().ProvisionTenant(gomock.Any(), EqComputeCacheSize(0)).
					Times(1).Return(uint64(1), nil)
				service.EXPECT().GetTenantByID(gomock.Any(), gomock.Eq(uint64(1))).
					Times(1).Return(tenant, nil)
				service.EXPECT().TenantToSpec(gomock.Any(), gomock.Eq(tenant)).
					Times(1).Return(tenantDTO, nil)
			},
			mockAccount: func(agent *account_mock.MockAgent) {
				agent.EXPECT().CheckTenantAvailability(gomock.Any(), gomock.Eq(orgID), gomock.Eq(uint64(0)), gomock.Eq(string(spec.Standard)), gomock.Eq(tenantName), gomock.Eq(config.Conf.Region)).
					Times(1).Return(&acc_admin.AvailabilityResponse{Available: true}, nil)
			},
			mockQuerier: func(queries *model_mock.MockQuerier) {
				queries.EXPECT().PickCloudClusterByServingType(gomock.Any(), gomock.Eq(string(managedcluster.ServingTypeFreeTier))).Times(1).Return(&querier.ManagedCluster{
					Status: string(managedcluster.ClusterStatusReady),
				}, nil)
			},

			exceptCode: 202,
		},
		{
			name:     "cloud invalid arg",
			userInfo: userInfo,
			orgTier:  org.PreAccTierInvited(),
			request: &spec.PostTenantsJSONRequestBody{
				TenantName: tenantName,
				Tier:       ptr.Ptr(spec.Standard),
				Resources:  resourcesrequestS,
			},
			mockService: func(service *mock_service.MockTenantServiceInterface) {
				service.EXPECT().OrgTenantNameExists(gomock.Any(), gomock.Eq(orgID), gomock.Eq(tenantName)).
					Times(1).Return(false, nil)
				service.EXPECT().ProvisionTenant(gomock.Any(), gomock.Any()).
					Times(1).Return(uint64(0), eris.New("error").WithCode(eris.CodeInvalidArgument))
			},
			mockAccount: func(agent *account_mock.MockAgent) {
				agent.EXPECT().CheckTenantAvailability(gomock.Any(), gomock.Eq(orgID), gomock.Eq(uint64(0)), gomock.Eq(string(spec.Standard)), gomock.Eq(tenantName), gomock.Eq(config.Conf.Region)).
					Times(1).Return(&acc_admin.AvailabilityResponse{Available: true}, nil)
			},
			mockQuerier: func(queries *model_mock.MockQuerier) {
				queries.EXPECT().PickCloudClusterByServingType(gomock.Any(), gomock.Eq(string(managedcluster.ServingTypeFreeTier))).Times(1).Return(&querier.ManagedCluster{
					Status: string(managedcluster.ClusterStatusReady),
				}, nil)
			},

			exceptCode: 400,
			exceptMsg:  "Invalid request params: code(invalid argument) error",
		},
		{
			name:     "byoc",
			userInfo: userInfo,
			orgTier:  org.PreAccTierInvited(),
			request: &spec.PostTenantsJSONRequestBody{
				TenantName:  tenantName,
				Tier:        ptr.Ptr(spec.BYOC),
				Resources:   resourcesrequestS,
				ClusterName: ptr.Ptr(byocClusterName),
			},
			mockService: func(service *mock_service.MockTenantServiceInterface) {
				service.EXPECT().OrgTenantNameExists(gomock.Any(), gomock.Eq(orgID), gomock.Eq(tenantName)).
					Times(1).Return(false, nil)
				service.EXPECT().ProvisionTenant(gomock.Any(), gomock.Any()).
					Times(1).Return(uint64(1), nil)
				service.EXPECT().GetTenantByID(gomock.Any(), gomock.Eq(uint64(1))).
					Times(1).Return(tenant, nil)
				service.EXPECT().TenantToSpec(gomock.Any(), gomock.Eq(tenant)).
					Times(1).Return(tenantDTO, nil)
			},
			mockAccount: func(agent *account_mock.MockAgent) {
				agent.EXPECT().CheckTenantAvailability(gomock.Any(), gomock.Eq(orgID), gomock.Eq(uint64(0)), gomock.Eq("BYOC"), gomock.Eq(tenantName), gomock.Eq(config.Conf.Region)).
					Times(1).Return(&acc_admin.AvailabilityResponse{Available: true}, nil)
			},
			mockQuerier: func(queries *model_mock.MockQuerier) {
				queries.EXPECT().GetClusterByOrgName(gomock.Any(), gomock.Eq(querier.GetClusterByOrgNameParams{
					Org:  orgID,
					Name: byocClusterName,
				})).Times(1).Return(&querier.ManagedCluster{
					Status: string(managedcluster.ClusterStatusReady),
				}, nil)
			},

			exceptCode: 202,
		},
		{
			name:     "forbid byoc tier on cloud cluster",
			userInfo: userInfo,
			orgTier:  org.PreAccTierInvited(),
			request: &spec.PostTenantsJSONRequestBody{
				TenantName: tenantName,
				Tier:       ptr.Ptr(spec.BYOC),
				Resources:  resourcesrequestS,
			},
			mockService: func(service *mock_service.MockTenantServiceInterface) {
				service.EXPECT().OrgTenantNameExists(gomock.Any(), gomock.Eq(orgID), gomock.Eq(tenantName)).
					Times(1).Return(false, nil)
			},
			mockAccount: func(agent *account_mock.MockAgent) {
				agent.EXPECT().CheckTenantAvailability(gomock.Any(), gomock.Eq(orgID), gomock.Eq(uint64(0)), gomock.Eq("BYOC"), gomock.Eq(tenantName), gomock.Eq(config.Conf.Region)).
					Times(1).Return(&acc_admin.AvailabilityResponse{Available: true}, nil)
			},

			exceptCode: 400,
			exceptMsg:  "BYOC tier can only be used on BYOC cluster",
		},
		{
			name:     "forbid cloud tier on byoc cluster",
			userInfo: userInfo,
			orgTier:  org.PreAccTierInvited(),
			request: &spec.PostTenantsJSONRequestBody{
				TenantName:  tenantName,
				Tier:        ptr.Ptr(spec.Standard),
				Resources:   resourcesrequestS,
				ClusterName: ptr.Ptr("byoc-cluster"),
			},
			mockService: func(service *mock_service.MockTenantServiceInterface) {
				service.EXPECT().OrgTenantNameExists(gomock.Any(), gomock.Eq(orgID), gomock.Eq(tenantName)).
					Times(1).Return(false, nil)
			},
			mockAccount: func(agent *account_mock.MockAgent) {
				agent.EXPECT().CheckTenantAvailability(gomock.Any(), gomock.Eq(orgID), gomock.Eq(uint64(0)), gomock.Eq("Standard"), gomock.Eq(tenantName), gomock.Eq(config.Conf.Region)).
					Times(1).Return(&acc_admin.AvailabilityResponse{Available: true}, nil)
			},
			mockQuerier: func(*model_mock.MockQuerier) {
			},

			exceptCode: 400,
			exceptMsg:  "BYOC cluster must use BYOC tier",
		},

		//
		// Test cases for different account tiers.

		{
			name:     "non-paying/during-trial/no-tenant/Standard-S",
			userInfo: userInfo,
			orgTier: org.TierInfo{
				HasPaymentMethod:  false,
				HasInvitationCode: false,
				DuringTrial:       true,
				HasTrialTenant:    false,
			},
			request: &spec.PostTenantsJSONRequestBody{
				TenantName: tenantName,
				Tier:       ptr.Ptr(spec.Standard),
				Resources:  resourcesrequestS,
			},
			mockService: func(service *mock_service.MockTenantServiceInterface) {
				service.EXPECT().OrgTenantNameExists(gomock.Any(), gomock.Eq(orgID), gomock.Eq(tenantName)).
					Times(1).Return(false, nil)
				service.EXPECT().ProvisionTenant(gomock.Any(), gomock.Cond(func(x any) bool {
					prov, ok := x.(services.ProvisionTenantOption)
					return ok && prov.AsTrial
				})).
					Times(1).Return(uint64(1), nil)
				service.EXPECT().GetTenantByID(gomock.Any(), gomock.Eq(uint64(1))).
					Times(1).Return(tenant, nil)
				service.EXPECT().TenantToSpec(gomock.Any(), gomock.Eq(tenant)).
					Times(1).Return(tenantDTO, nil)
			},
			mockAccount: func(agent *account_mock.MockAgent) {
				agent.EXPECT().CheckTenantAvailability(gomock.Any(), gomock.Eq(orgID), gomock.Eq(uint64(0)), gomock.Eq(string(spec.Standard)), gomock.Eq(tenantName), gomock.Eq(config.Conf.Region)).
					Times(1).Return(&acc_admin.AvailabilityResponse{Available: true}, nil)
			},
			mockQuerier: func(queries *model_mock.MockQuerier) {
				queries.EXPECT().PickCloudClusterByServingType(gomock.Any(), gomock.Eq(string(managedcluster.ServingTypeFreeTier))).Times(1).Return(&querier.ManagedCluster{
					Status: string(managedcluster.ClusterStatusReady),
				}, nil)
			},

			exceptCode: 202,
		},
		{
			name:     "non-paying/during-trial/no-tenant/Standard-L",
			userInfo: userInfo,
			orgTier: org.TierInfo{
				HasPaymentMethod:  false,
				HasInvitationCode: false,
				DuringTrial:       true,
				HasTrialTenant:    false,
			},
			request: &spec.PostTenantsJSONRequestBody{
				TenantName: tenantName,
				Tier:       ptr.Ptr(spec.Standard),
				Resources:  resourcesrequestL,
			},
			mockService: func(*mock_service.MockTenantServiceInterface) {},
			mockAccount: func(*account_mock.MockAgent) {},
			mockQuerier: func(*model_mock.MockQuerier) {},

			exceptCode: 400,
			exceptMsg:  "cannot provision tenant as trial",
		},
		{
			name:     "paying/during-trial/no-tenant/Standard-S",
			userInfo: userInfo,
			orgTier: org.TierInfo{
				HasPaymentMethod:  true,
				HasInvitationCode: false,
				DuringTrial:       true,
				HasTrialTenant:    false,
			},
			request: &spec.PostTenantsJSONRequestBody{
				TenantName: tenantName,
				Tier:       ptr.Ptr(spec.Standard),
				Resources:  resourcesrequestS,
			},
			mockService: func(service *mock_service.MockTenantServiceInterface) {
				service.EXPECT().OrgTenantNameExists(gomock.Any(), gomock.Eq(orgID), gomock.Eq(tenantName)).
					Times(1).Return(false, nil)
				service.EXPECT().ProvisionTenant(gomock.Any(), gomock.Cond(func(x any) bool {
					prov, ok := x.(services.ProvisionTenantOption)
					return ok && prov.AsTrial
				})).
					Times(1).Return(uint64(1), nil)
				service.EXPECT().GetTenantByID(gomock.Any(), gomock.Eq(uint64(1))).
					Times(1).Return(tenant, nil)
				service.EXPECT().TenantToSpec(gomock.Any(), gomock.Eq(tenant)).
					Times(1).Return(tenantDTO, nil)
			},
			mockAccount: func(agent *account_mock.MockAgent) {
				agent.EXPECT().CheckTenantAvailability(gomock.Any(), gomock.Eq(orgID), gomock.Eq(uint64(0)), gomock.Eq(string(spec.Standard)), gomock.Eq(tenantName), gomock.Eq(config.Conf.Region)).
					Times(1).Return(&acc_admin.AvailabilityResponse{Available: true}, nil)
			},
			mockQuerier: func(queries *model_mock.MockQuerier) {
				queries.EXPECT().PickCloudClusterByServingType(gomock.Any(), gomock.Eq(string(managedcluster.ServingTypeFreeTier))).Times(1).Return(&querier.ManagedCluster{
					Status: string(managedcluster.ClusterStatusReady),
				}, nil)
			},

			exceptCode: 202,
		},
		{
			name:     "paying/during-trial/no-tenant/Standard-L",
			userInfo: userInfo,
			orgTier: org.TierInfo{
				HasPaymentMethod:  true,
				HasInvitationCode: false,
				DuringTrial:       true,
				HasTrialTenant:    false,
			},
			request: &spec.PostTenantsJSONRequestBody{
				TenantName: tenantName,
				Tier:       ptr.Ptr(spec.Standard),
				Resources:  resourcesrequestL,
			},
			mockService: func(service *mock_service.MockTenantServiceInterface) {
				service.EXPECT().OrgTenantNameExists(gomock.Any(), gomock.Eq(orgID), gomock.Eq(tenantName)).
					Times(1).Return(false, nil)
				service.EXPECT().ProvisionTenant(gomock.Any(), gomock.Cond(func(x any) bool {
					prov, ok := x.(services.ProvisionTenantOption)
					return ok && !prov.AsTrial
				})).
					Times(1).Return(uint64(1), nil)
				service.EXPECT().GetTenantByID(gomock.Any(), gomock.Eq(uint64(1))).
					Times(1).Return(tenant, nil)
				service.EXPECT().TenantToSpec(gomock.Any(), gomock.Eq(tenant)).
					Times(1).Return(tenantDTO, nil)
			},
			mockAccount: func(agent *account_mock.MockAgent) {
				agent.EXPECT().CheckTenantAvailability(gomock.Any(), gomock.Eq(orgID), gomock.Eq(uint64(0)), gomock.Eq(string(spec.Standard)), gomock.Eq(tenantName), gomock.Eq(config.Conf.Region)).
					Times(1).Return(&acc_admin.AvailabilityResponse{Available: true}, nil)
			},
			mockQuerier: func(queries *model_mock.MockQuerier) {
				queries.EXPECT().PickCloudClusterByServingType(gomock.Any(), gomock.Eq(string(managedcluster.ServingTypeFreeTier))).Times(1).Return(&querier.ManagedCluster{
					Status: string(managedcluster.ClusterStatusReady),
				}, nil)
			},

			exceptCode: 202,
		},
		{
			name:     "paying/during-trial/has-tenant/Standard-S",
			userInfo: userInfo,
			orgTier: org.TierInfo{
				HasPaymentMethod:  true,
				HasInvitationCode: false,
				DuringTrial:       true,
				HasTrialTenant:    true,
			},
			request: &spec.PostTenantsJSONRequestBody{
				TenantName: tenantName,
				Tier:       ptr.Ptr(spec.Standard),
				Resources:  resourcesrequestS,
			},
			mockService: func(service *mock_service.MockTenantServiceInterface) {
				service.EXPECT().OrgTenantNameExists(gomock.Any(), gomock.Eq(orgID), gomock.Eq(tenantName)).
					Times(1).Return(false, nil)
				service.EXPECT().ProvisionTenant(gomock.Any(), gomock.Cond(func(x any) bool {
					prov, ok := x.(services.ProvisionTenantOption)
					return ok && !prov.AsTrial
				})).
					Times(1).Return(uint64(1), nil)
				service.EXPECT().GetTenantByID(gomock.Any(), gomock.Eq(uint64(1))).
					Times(1).Return(tenant, nil)
				service.EXPECT().TenantToSpec(gomock.Any(), gomock.Eq(tenant)).
					Times(1).Return(tenantDTO, nil)
			},
			mockAccount: func(agent *account_mock.MockAgent) {
				agent.EXPECT().CheckTenantAvailability(gomock.Any(), gomock.Eq(orgID), gomock.Eq(uint64(0)), gomock.Eq(string(spec.Standard)), gomock.Eq(tenantName), gomock.Eq(config.Conf.Region)).
					Times(1).Return(&acc_admin.AvailabilityResponse{Available: true}, nil)
			},
			mockQuerier: func(queries *model_mock.MockQuerier) {
				queries.EXPECT().PickCloudClusterByServingType(gomock.Any(), gomock.Eq(string(managedcluster.ServingTypeFreeTier))).Times(1).Return(&querier.ManagedCluster{
					Status: string(managedcluster.ClusterStatusReady),
				}, nil)
			},

			exceptCode: 202,
		},
		{
			name:     "paying/past-trial/Standard-S",
			userInfo: userInfo,
			orgTier: org.TierInfo{
				HasPaymentMethod:  true,
				HasInvitationCode: false,
				DuringTrial:       false,
				HasTrialTenant:    false,
			},
			request: &spec.PostTenantsJSONRequestBody{
				TenantName: tenantName,
				Tier:       ptr.Ptr(spec.Standard),
				Resources:  resourcesrequestS,
			},
			mockService: func(service *mock_service.MockTenantServiceInterface) {
				service.EXPECT().OrgTenantNameExists(gomock.Any(), gomock.Eq(orgID), gomock.Eq(tenantName)).
					Times(1).Return(false, nil)
				service.EXPECT().ProvisionTenant(gomock.Any(), gomock.Cond(func(x any) bool {
					prov, ok := x.(services.ProvisionTenantOption)
					return ok && !prov.AsTrial
				})).
					Times(1).Return(uint64(1), nil)
				service.EXPECT().GetTenantByID(gomock.Any(), gomock.Eq(uint64(1))).
					Times(1).Return(tenant, nil)
				service.EXPECT().TenantToSpec(gomock.Any(), gomock.Eq(tenant)).
					Times(1).Return(tenantDTO, nil)
			},
			mockAccount: func(agent *account_mock.MockAgent) {
				agent.EXPECT().CheckTenantAvailability(gomock.Any(), gomock.Eq(orgID), gomock.Eq(uint64(0)), gomock.Eq(string(spec.Standard)), gomock.Eq(tenantName), gomock.Eq(config.Conf.Region)).
					Times(1).Return(&acc_admin.AvailabilityResponse{Available: true}, nil)
			},
			mockQuerier: func(queries *model_mock.MockQuerier) {
				queries.EXPECT().PickCloudClusterByServingType(gomock.Any(), gomock.Eq(string(managedcluster.ServingTypeFreeTier))).Times(1).Return(&querier.ManagedCluster{
					Status: string(managedcluster.ClusterStatusReady),
				}, nil)
			},

			exceptCode: 202,
		},
		{
			name:     "paying/BYOC",
			userInfo: userInfo,
			orgTier: org.TierInfo{
				HasPaymentMethod:  true,
				HasInvitationCode: false,
			},
			request: &spec.PostTenantsJSONRequestBody{
				TenantName:  tenantName,
				Tier:        ptr.Ptr(spec.BYOC),
				Resources:   resourcesrequestS,
				ClusterName: ptr.Ptr(byocClusterName),
			},
			mockService: func(*mock_service.MockTenantServiceInterface) {},
			mockAccount: func(*account_mock.MockAgent) {},
			mockQuerier: func(*model_mock.MockQuerier) {},

			exceptCode: 400,
			exceptMsg:  "not allowed to provision a BYOC tenant",
		},
		{
			name:     "invited/BYOC",
			userInfo: userInfo,
			orgTier: org.TierInfo{
				HasInvitationCode: true,
			},
			request: &spec.PostTenantsJSONRequestBody{
				TenantName:  tenantName,
				Tier:        ptr.Ptr(spec.BYOC),
				Resources:   resourcesrequestS,
				ClusterName: ptr.Ptr(byocClusterName),
			},
			mockService: func(service *mock_service.MockTenantServiceInterface) {
				service.EXPECT().OrgTenantNameExists(gomock.Any(), gomock.Eq(orgID), gomock.Eq(tenantName)).
					Times(1).Return(false, nil)
				service.EXPECT().ProvisionTenant(gomock.Any(), gomock.Any()).
					Times(1).Return(uint64(1), nil)
				service.EXPECT().GetTenantByID(gomock.Any(), gomock.Eq(uint64(1))).
					Times(1).Return(tenant, nil)
				service.EXPECT().TenantToSpec(gomock.Any(), gomock.Eq(tenant)).
					Times(1).Return(tenantDTO, nil)
			},
			mockAccount: func(agent *account_mock.MockAgent) {
				agent.EXPECT().CheckTenantAvailability(gomock.Any(), gomock.Eq(orgID), gomock.Eq(uint64(0)), gomock.Eq("BYOC"), gomock.Eq(tenantName), gomock.Eq(config.Conf.Region)).
					Times(1).Return(&acc_admin.AvailabilityResponse{Available: true}, nil)
			},
			mockQuerier: func(queries *model_mock.MockQuerier) {
				queries.EXPECT().GetClusterByOrgName(gomock.Any(), gomock.Eq(querier.GetClusterByOrgNameParams{
					Org:  orgID,
					Name: byocClusterName,
				})).Times(1).Return(&querier.ManagedCluster{
					Status: string(managedcluster.ClusterStatusReady),
				}, nil)
			},

			exceptCode: 202,
		},
		{
			name:     "create standard tenant with serverless compaction",
			userInfo: userInfo,
			orgTier:  org.PreAccTierInvited(),
			request: &spec.PostTenantsJSONRequestBody{
				TenantName:  tenantName,
				Tier:        ptr.Ptr(spec.Standard),
				Resources:   resourcesrequestS,
				ClusterName: ptr.Ptr(byocClusterName),
				Extensions: &spec.TenantExtensionsRequest{
					ServerlessCompaction: &spec.TenantExtensionServerlessCompactionRequest{
						MaximumCompactionConcurrency: 4,
					},
				},
			},
			exceptCode: 400,
			exceptMsg:  "serverless compaction is not allowed for tier Standard",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			svc := mock_service.NewMockTenantServiceInterface(ctrl)
			querier := model_mock.NewMockQuerier(ctrl)
			account := account_mock.NewMockAgent(ctrl)
			m := model.NewModel(querier)

			controller := &UserController{
				tenantService: svc,
				accountAgent:  account,
				model:         m,
			}
			recorder := httptest.NewRecorder()
			ginCtx, _ := gin.CreateTestContext(recorder)
			ginCtx.Set("tenant-operation", &tt.orgTier)

			if tt.userInfo != nil {
				ginCtx.Set(ginauth.ControllerAuthInfoContextKey, tt.userInfo.ToAuthInfo())
			}
			if tt.request != nil {
				if tt.imageTag != "" {
					tt.request.ImageTag = &tt.imageTag
				}
				tt.request.Resources.ComputeFileCacheSizeGiB = int(tt.computeCacheSize)
				body, err := json.Marshal(tt.request)
				require.NoError(t, err)
				ginCtx.Request, err = http.NewRequest(http.MethodPost, "http://localhost/api/v2/tenants", bytes.NewReader(body))
				require.NoError(t, err)
			}
			if tt.mockService != nil {
				tt.mockService(svc)
			}
			if tt.mockAccount != nil {
				tt.mockAccount(account)
			}
			if tt.mockQuerier != nil {
				tt.mockQuerier(querier)
			}

			controller.PostTenants(ginCtx)

			require.EqualValues(t, tt.exceptCode, recorder.Code)
			resp := acc_admin.DefaultResponse{}
			err := json.Unmarshal(recorder.Body.Bytes(), &resp)
			require.NoError(t, err)
			assert.EqualValues(t, tt.exceptMsg, resp.Msg)
		})
	}
}

func TestDeleteTenantsNsId(t *testing.T) {
	userID := uuid.New()
	orgID := uuid.New()
	nsIDNotExisted := uuid.New()
	userInfo := &ginauth.ControllerUserInfo{
		Email:          "<EMAIL>",
		UserResourceID: userID,
		OrgID:          orgID,
	}
	tests := []struct {
		name        string
		userInfo    *ginauth.ControllerUserInfo
		nsID        uuid.UUID
		mockService func(*mock_service.MockTenantServiceInterface)
		mockQuerier func(*model_mock.MockQuerier)

		exceptCode int
		exceptMsg  string
	}{
		{
			name:     "delete not existed cluster",
			userInfo: userInfo,
			nsID:     nsIDNotExisted,
			mockService: func(service *mock_service.MockTenantServiceInterface) {
				service.EXPECT().GetOrgTenantByNsID(gomock.Any(), gomock.Any(), gomock.Any()).Return(model.TenantNil, errors.ErrClusterNotExist)
			},
			mockQuerier: func(*model_mock.MockQuerier) {
			},
			exceptCode: 404,
			exceptMsg:  "cluster not found",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			svc := mock_service.NewMockTenantServiceInterface(ctrl)
			querier := model_mock.NewMockQuerier(ctrl)
			m := model.NewModel(querier)

			controller := &UserController{
				tenantService: svc,
				model:         m,
			}
			recorder := httptest.NewRecorder()
			ginCtx, _ := gin.CreateTestContext(recorder)

			if tt.userInfo != nil {
				ginCtx.Set(ginauth.ControllerAuthInfoContextKey, tt.userInfo.ToAuthInfo())
			}
			if tt.mockService != nil {
				tt.mockService(svc)
			}
			if tt.mockQuerier != nil {
				tt.mockQuerier(querier)
			}

			controller.DeleteTenantsNsId(ginCtx, tt.nsID)

			require.EqualValues(t, tt.exceptCode, recorder.Code)
			resp := acc_admin.DefaultResponse{}
			err := json.Unmarshal(recorder.Body.Bytes(), &resp)
			require.NoError(t, err)
			assert.EqualValues(t, tt.exceptMsg, resp.Msg)
		})
	}
}

func TestPostTenantsNsIdStart(t *testing.T) {
	userID := uuid.New()
	orgID := uuid.New()
	nsID := uuid.New()
	var tenantID uint64 = 1
	userInfo := &ginauth.ControllerUserInfo{
		Email:          "<EMAIL>",
		UserResourceID: userID,
		OrgID:          orgID,
	}
	tests := []struct {
		name        string
		userInfo    *ginauth.ControllerUserInfo
		mockService func(*mock_service.MockTenantServiceInterface)

		exceptCode int
		exceptMsg  string
	}{
		{
			name:     "start not stopped or expired cluster",
			userInfo: userInfo,
			mockService: func(service *mock_service.MockTenantServiceInterface) {
				service.EXPECT().GetOrgTenantByNsID(gomock.Any(), gomock.Eq(orgID), gomock.Eq(nsID)).
					Times(1).Return(model.Tenant{
					ID:     tenantID,
					Status: model.TenantStatusRunning,
				}, nil)
			},
			exceptCode: 400,
			exceptMsg:  "illegal state: cluster should be stopped or expired",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			svc := mock_service.NewMockTenantServiceInterface(ctrl)
			querier := model_mock.NewMockQuerier(ctrl)
			account := account_mock.NewMockAgent(ctrl)
			m := model.NewModel(querier)

			controller := &UserController{
				tenantService: svc,
				accountAgent:  account,
				model:         m,
			}
			recorder := httptest.NewRecorder()
			ginCtx, _ := gin.CreateTestContext(recorder)

			if tt.userInfo != nil {
				ginCtx.Set(ginauth.ControllerAuthInfoContextKey, tt.userInfo.ToAuthInfo())
			}
			if tt.mockService != nil {
				tt.mockService(svc)
			}
			controller.PostTenantsNsIdStart(ginCtx, nsID)

			require.EqualValues(t, tt.exceptCode, recorder.Code)
			resp := acc_admin.DefaultResponse{}
			err := json.Unmarshal(recorder.Body.Bytes(), &resp)
			require.NoError(t, err)
			assert.EqualValues(t, tt.exceptMsg, resp.Msg)
		})
	}
}

func TestPostTenantsNsIdStop(t *testing.T) {
	userID := uuid.New()
	orgID := uuid.New()
	nsID := uuid.New()
	var tenantID uint64 = 1
	userInfo := &ginauth.ControllerUserInfo{
		Email:          "<EMAIL>",
		UserResourceID: userID,
		OrgID:          orgID,
	}
	tests := []struct {
		name        string
		userInfo    *ginauth.ControllerUserInfo
		mockService func(*mock_service.MockTenantServiceInterface)

		exceptCode int
		exceptMsg  string
	}{
		{
			name:     "start not running cluster",
			userInfo: userInfo,
			mockService: func(service *mock_service.MockTenantServiceInterface) {
				service.EXPECT().GetOrgTenantByNsID(gomock.Any(), gomock.Eq(orgID), gomock.Eq(nsID)).
					Times(1).Return(model.Tenant{
					ID:     tenantID,
					Status: model.TenantStatusDeleting,
				}, nil)
			},
			exceptCode: 400,
			exceptMsg:  "failed to stop the cluster when it is already stopped or in creation",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			svc := mock_service.NewMockTenantServiceInterface(ctrl)
			querier := model_mock.NewMockQuerier(ctrl)
			m := model.NewModel(querier)

			controller := &UserController{
				tenantService: svc,
				model:         m,
			}
			recorder := httptest.NewRecorder()
			ginCtx, _ := gin.CreateTestContext(recorder)

			if tt.userInfo != nil {
				ginCtx.Set(ginauth.ControllerAuthInfoContextKey, tt.userInfo.ToAuthInfo())
			}
			if tt.mockService != nil {
				tt.mockService(svc)
			}
			controller.PostTenantsNsIdStop(ginCtx, nsID)

			require.EqualValues(t, tt.exceptCode, recorder.Code)
			resp := acc_admin.DefaultResponse{}
			err := json.Unmarshal(recorder.Body.Bytes(), &resp)
			require.NoError(t, err)
			assert.EqualValues(t, tt.exceptMsg, resp.Msg)
		})
	}
}

func TestPostTenantsNsIdUpdateResource(t *testing.T) {
	userID := uuid.New()
	orgID := uuid.New()
	nsID := uuid.New()
	var tenantID uint64 = 1
	userInfo := &ginauth.ControllerUserInfo{
		Email:          "<EMAIL>",
		UserResourceID: userID,
		OrgID:          orgID,
	}
	resourceRequest := &spec.ComponentResourceRequest{
		ComponentTypeId: "p-1c4g",
		Replica:         1,
	}
	resourcesRequest := &spec.PostTenantResourcesRequestBody{
		Compute:   resourceRequest,
		Frontend:  resourceRequest,
		Compactor: resourceRequest,
		Meta:      resourceRequest,
	}

	tests := []struct {
		name                 string
		userInfo             *ginauth.ControllerUserInfo
		request              *spec.PostTenantResourcesRequestBody
		mockService          func(*mock_service.MockTenantServiceInterface)
		mockExtensionService func(service *mock_service.MockTenantExtensionService)

		exceptCode int
		exceptMsg  string
	}{
		{
			name:     "update already updating cluster resource",
			userInfo: userInfo,
			request:  resourcesRequest,
			mockService: func(service *mock_service.MockTenantServiceInterface) {
				service.EXPECT().GetOrgTenantByNsID(gomock.Any(), gomock.Eq(orgID), gomock.Eq(nsID)).
					Times(1).Return(model.Tenant{
					ID:     tenantID,
					Status: model.TenantStatusUpdating,
					TierID: string(spec.Standard),
				}, nil)
			},
			exceptCode: 400,
			exceptMsg:  "cluster is already updating",
		},
		{
			name:     "update not running cluster resource",
			userInfo: userInfo,
			request:  resourcesRequest,
			mockService: func(service *mock_service.MockTenantServiceInterface) {
				service.EXPECT().GetOrgTenantByNsID(gomock.Any(), gomock.Eq(orgID), gomock.Eq(nsID)).
					Times(1).Return(model.Tenant{
					ID:     tenantID,
					Status: model.TenantStatusCreating,
					TierID: string(spec.Standard),
				}, nil)
			},
			exceptCode: 400,
			exceptMsg:  "cluster is not running",
		},
		{
			name:     "convert to standalone from regular tenant with resource group",
			userInfo: userInfo,
			request: &spec.PostTenantResourcesRequestBody{
				Standalone: resourceRequest,
			},
			mockService: func(service *mock_service.MockTenantServiceInterface) {
				service.EXPECT().GetOrgTenantByNsID(gomock.Any(), gomock.Eq(orgID), gomock.Eq(nsID)).
					Times(1).Return(model.Tenant{
					ID: tenantID,
					Resources: resourcespec.FromValidTenantResourceSpec(&resourcespec.TenantResourceSpecV1Regular{
						ResourceGroups: []resourcespec.TenantResourceGroup{
							{
								Name: "resource-group",
							},
						},
					}),
					ImageTag: "v2.3.0",
					Status:   model.TenantStatusRunning,
					TierID:   string(spec.Standard),
				}, nil)
			},
			mockExtensionService: func(service *mock_service.MockTenantExtensionService) {
				service.EXPECT().GetTenantExtensionByTenantIDAndResourceType(gomock.Any(), tenantID, model.ExtensionsResourceTypeCompaction).
					Return(model.TenantExtensionNil, errors.ErrTenantExtensionNotExist)
			},
			exceptCode: 400,
			exceptMsg:  "Illegal status, cannot convert deployment with resource groups exist",
		},
	}
	availableComponentTypes := []*config.AvailableComponentType{
		{
			ComponentType:  &config.ComponentType{ID: "p-1c4g"},
			MaximumReplica: 1,
		},
	}
	initializeConfigResourceDef(availableComponentTypes)

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			svc := mock_service.NewMockTenantServiceInterface(ctrl)
			extSvc := mock_service.NewMockTenantExtensionService(ctrl)
			querier := model_mock.NewMockQuerier(ctrl)
			m := model.NewModel(querier)

			controller := &UserController{
				tenantService:          svc,
				tenantExtensionService: extSvc,
				model:                  m,
			}
			recorder := httptest.NewRecorder()
			ginCtx, _ := gin.CreateTestContext(recorder)

			if tt.userInfo != nil {
				ginCtx.Set(ginauth.ControllerAuthInfoContextKey, tt.userInfo.ToAuthInfo())
			}
			if tt.request != nil {
				body, err := json.Marshal(tt.request)
				require.NoError(t, err)
				ginCtx.Request, err = http.NewRequest(http.MethodPost, fmt.Sprintf("http://localhost/api/v2/tenants/%s/updateResource", nsID), bytes.NewReader(body))
				require.NoError(t, err)
			}
			if tt.mockService != nil {
				tt.mockService(svc)
			}
			if tt.mockExtensionService != nil {
				tt.mockExtensionService(extSvc)
			}
			controller.PostTenantsNsIdUpdateResource(ginCtx, nsID)

			require.EqualValues(t, tt.exceptCode, recorder.Code)
			resp := acc_admin.DefaultResponse{}
			err := json.Unmarshal(recorder.Body.Bytes(), &resp)
			require.NoError(t, err)
			assert.EqualValues(t, tt.exceptMsg, resp.Msg)
		})
	}
}

func TestPostTenantsNsIdUpdateResourceWithServerlessCompaction(t *testing.T) {
	userID := uuid.New()
	orgID := uuid.New()
	nsID := uuid.New()
	var tenantID uint64 = 1
	userInfo := &ginauth.ControllerUserInfo{
		Email:          "<EMAIL>",
		UserResourceID: userID,
		OrgID:          orgID,
	}
	resourceRequest := &spec.ComponentResourceRequest{
		ComponentTypeId: "p-1c4g",
		Replica:         1,
	}
	resourceSpecSample := component.ResourceSpec{
		ID:            "p-1c4g",
		CPURequest:    "0",
		CPULimit:      "0",
		MemoryRequest: "0",
		MemoryLimit:   "0",
	}
	resourcesRequest := &spec.PostTenantResourcesRequestBody{
		Compute:  resourceRequest,
		Frontend: resourceRequest,
		Meta:     resourceRequest,
		Extensions: &spec.TenantExtensionsRequest{
			ServerlessCompaction: &spec.TenantExtensionServerlessCompactionRequest{
				MaximumCompactionConcurrency: 10,
			},
		},
	}

	tests := []struct {
		name                 string
		userInfo             *ginauth.ControllerUserInfo
		request              *spec.PostTenantResourcesRequestBody
		mockService          func(*mock_service.MockTenantServiceInterface)
		mockExtensionService func(service *mock_service.MockTenantExtensionService)

		exceptCode int
		exceptMsg  string
	}{
		{
			name:     "update compaction extension only",
			userInfo: userInfo,
			request:  resourcesRequest,
			mockService: func(service *mock_service.MockTenantServiceInterface) {
				service.EXPECT().GetOrgTenantByNsID(gomock.Any(), gomock.Eq(orgID), gomock.Eq(nsID)).
					Times(1).Return(model.Tenant{
					ID:     tenantID,
					Status: model.TenantStatusRunning,
					TierID: string(spec.Invited),
					Resources: resourcespec.FromValidTenantResourceSpec(&resourcespec.TenantResourceSpecV1Regular{
						ComputeReplica:   1,
						FrontendReplica:  1,
						MetaReplica:      1,
						CompactorReplica: 1,
					}),
				}, nil)
			},
			mockExtensionService: func(service *mock_service.MockTenantExtensionService) {
				service.EXPECT().GetTenantExtensionByTenantIDAndResourceType(gomock.Any(), tenantID, model.ExtensionsResourceTypeCompaction).
					Return(model.TenantExtensionNil, errors.ErrTenantExtensionNotExist)
			},
			exceptCode: 202,
			exceptMsg:  "cluster update submitted",
		},
		{
			name:     "send not change request when there exists disabled compaction extension",
			userInfo: userInfo,
			request: &spec.PostTenantResourcesRequestBody{
				Compactor:  resourceRequest,
				Compute:    resourceRequest,
				Frontend:   resourceRequest,
				Meta:       resourceRequest,
				Extensions: &spec.TenantExtensionsRequest{},
			},
			mockService: func(service *mock_service.MockTenantServiceInterface) {
				service.EXPECT().GetOrgTenantByNsID(gomock.Any(), gomock.Eq(orgID), gomock.Eq(nsID)).
					Times(1).Return(model.Tenant{
					ID:     tenantID,
					Status: model.TenantStatusRunning,
					TierID: string(spec.Invited),
					Resources: resourcespec.FromValidTenantResourceSpec(&resourcespec.TenantResourceSpecV1Regular{
						ComputeReplica:        1,
						ComputeResourceSpec:   resourceSpecSample,
						FrontendReplica:       1,
						FrontendResourceSpec:  resourceSpecSample,
						MetaReplica:           1,
						MetaResourceSpec:      resourceSpecSample,
						CompactorReplica:      1,
						CompactorResourceSpec: resourceSpecSample,
					}),
				}, nil)
			},
			mockExtensionService: func(service *mock_service.MockTenantExtensionService) {
				service.EXPECT().GetTenantExtensionByTenantIDAndResourceType(gomock.Any(), tenantID, model.ExtensionsResourceTypeCompaction).
					Return(model.TenantExtension{
						Status: model.ExtensionStatusDisabled,
					}, nil)
			},
			exceptCode: 400,
			exceptMsg:  "unchanged by resources request and compaction extension",
		},
		{
			name:     "disable serverless extension",
			userInfo: userInfo,
			request: &spec.PostTenantResourcesRequestBody{
				Compactor:  resourceRequest,
				Compute:    resourceRequest,
				Frontend:   resourceRequest,
				Meta:       resourceRequest,
				Extensions: &spec.TenantExtensionsRequest{},
			},
			mockService: func(service *mock_service.MockTenantServiceInterface) {
				service.EXPECT().GetOrgTenantByNsID(gomock.Any(), gomock.Eq(orgID), gomock.Eq(nsID)).
					Times(1).Return(model.Tenant{
					ID:     tenantID,
					Status: model.TenantStatusRunning,
					TierID: string(spec.Standard),
					Resources: resourcespec.FromValidTenantResourceSpec(&resourcespec.TenantResourceSpecV1Regular{
						ComputeReplica:        1,
						ComputeResourceSpec:   resourceSpecSample,
						FrontendReplica:       1,
						FrontendResourceSpec:  resourceSpecSample,
						MetaReplica:           1,
						MetaResourceSpec:      resourceSpecSample,
						CompactorReplica:      1,
						CompactorResourceSpec: resourceSpecSample,
					}),
				}, nil)
			},
			mockExtensionService: func(service *mock_service.MockTenantExtensionService) {
				service.EXPECT().GetTenantExtensionByTenantIDAndResourceType(gomock.Any(), tenantID, model.ExtensionsResourceTypeCompaction).
					Return(model.TenantExtension{
						TenantID: tenantID,
						Config:   ptr.Ptr(`{"Scaler":{"MaxReplicas":4}}`),
						Status:   model.ExtensionStatusRunning,
					}, nil)
			},
			exceptCode: 202,
			exceptMsg:  "cluster update submitted",
		},
		{
			name:     "update compaction extension for standard tenant",
			userInfo: userInfo,
			request:  resourcesRequest,
			mockService: func(service *mock_service.MockTenantServiceInterface) {
				service.EXPECT().GetOrgTenantByNsID(gomock.Any(), gomock.Eq(orgID), gomock.Eq(nsID)).
					Times(1).Return(model.Tenant{
					ID:     tenantID,
					Status: model.TenantStatusRunning,
					TierID: string(spec.Standard),
					Resources: resourcespec.FromValidTenantResourceSpec(&resourcespec.TenantResourceSpecV1Regular{
						ComputeReplica:   1,
						FrontendReplica:  1,
						MetaReplica:      1,
						CompactorReplica: 1,
					}),
				}, nil)
			},
			mockExtensionService: func(service *mock_service.MockTenantExtensionService) {
				service.EXPECT().GetTenantExtensionByTenantIDAndResourceType(gomock.Any(), tenantID, model.ExtensionsResourceTypeCompaction).
					Return(model.TenantExtensionNil, errors.ErrTenantExtensionNotExist)
			},
			exceptCode: 400,
			exceptMsg:  "code(permission denied) serverless compaction operation is not allowed for tier Standard",
		},
	}
	availableComponentTypes := []*config.AvailableComponentType{
		{
			ComponentType:  &config.ComponentType{ID: "p-1c4g"},
			MaximumReplica: 1,
		},
	}
	initializeConfigResourceDef(availableComponentTypes)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			svc := mock_service.NewMockTenantServiceInterface(ctrl)
			extSvc := mock_service.NewMockTenantExtensionService(ctrl)
			tenantOpSvc := mock_service.NewMockTenantOperationService(ctrl)
			tenantOpSvc.EXPECT().
				UpdateTenantResources(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return(nsID, nil).
				AnyTimes()
			mockQuerier := model_mock.NewMockQuerier(ctrl)
			m := model.NewModel(mockQuerier)

			controller := &UserController{
				tenantService:          svc,
				tenantExtensionService: extSvc,
				tenantOperationService: tenantOpSvc,
				model:                  m,
			}
			recorder := httptest.NewRecorder()
			ginCtx, _ := gin.CreateTestContext(recorder)

			if tt.userInfo != nil {
				ginCtx.Set(ginauth.ControllerAuthInfoContextKey, tt.userInfo.ToAuthInfo())
			}
			if tt.request != nil {
				body, err := json.Marshal(tt.request)
				require.NoError(t, err)
				ginCtx.Request, err = http.NewRequest(http.MethodPost, fmt.Sprintf("http://localhost/api/v2/tenants/%s/updateResource", nsID), bytes.NewReader(body))
				require.NoError(t, err)
			}
			if tt.mockService != nil {
				tt.mockService(svc)
			}
			if tt.mockExtensionService != nil {
				tt.mockExtensionService(extSvc)
			}
			// Set the compaction extension config to ensure it is not nil
			config.Conf.Mgmt.RisingWaveExtensions.Compaction.Compactor.CPULimit = "1000m"
			controller.PostTenantsNsIdUpdateResource(ginCtx, nsID)

			require.EqualValues(t, tt.exceptCode, recorder.Code)
			resp := acc_admin.DefaultResponse{}
			err := json.Unmarshal(recorder.Body.Bytes(), &resp)
			require.NoError(t, err)
			assert.EqualValues(t, tt.exceptMsg, resp.Msg)
		})
	}
}

func initializeConfigResourceDef(availableComponentTypes []*config.AvailableComponentType) {
	config.ResourcesDefinitions = &config.ResourcesDef{
		Tiers: map[string]config.Tier{
			"BYOC": {
				ID:                          "BYOC",
				AvailableComputeTypeList:    availableComponentTypes,
				AvailableFrontendTypeList:   availableComponentTypes,
				AvailableCompactorTypeList:  availableComponentTypes,
				AvailableMetaTypeList:       availableComponentTypes,
				AvailableStandaloneTypeList: availableComponentTypes,
				AvailableMetaStore: &config.AvailableMetaStore{
					Etcd: &config.AvailableMetaStoreEtcd{
						TypeList:      availableComponentTypes,
						MaximumSizeGB: 10,
					},
				},
			},
			"Standard": {
				ID:                          "Standard",
				AvailableComputeTypeList:    availableComponentTypes,
				AvailableFrontendTypeList:   availableComponentTypes,
				AvailableCompactorTypeList:  availableComponentTypes,
				AvailableMetaTypeList:       availableComponentTypes,
				AvailableStandaloneTypeList: availableComponentTypes,
				AvailableMetaStore: &config.AvailableMetaStore{
					Etcd: &config.AvailableMetaStoreEtcd{
						TypeList:      availableComponentTypes,
						MaximumSizeGB: 10,
					},
				},
				MaximumComputeFileCacheGB: 50,
			},
			"Invited": {
				ID:                       "Invited",
				AvailableComputeTypeList: availableComponentTypes,
				ServerlessCompactionOptions: config.ServerlessCompactionOptions{
					Allowed: true,
				},
				AvailableFrontendTypeList:   availableComponentTypes,
				AvailableCompactorTypeList:  availableComponentTypes,
				AvailableMetaTypeList:       availableComponentTypes,
				AvailableStandaloneTypeList: availableComponentTypes,
			},
		},
		ComponentTypes: map[string]config.ComponentType{
			"p-1c4g": {ID: "p-1c4g"},
			"p-2c8g": {ID: "p-2c8g"},
		},
	}
}

func TestTenantsNsIdCaCert(t *testing.T) {
	userID := uuid.New()
	orgID := uuid.New()
	nsID := uuid.New()
	tenantID := uint64(1)
	clusterID := uint64(1)
	userInfo := &ginauth.ControllerUserInfo{
		Email:          "<EMAIL>",
		UserResourceID: userID,
		OrgID:          orgID,
	}

	ctx := context.Background()
	err := rwproxy.GetRootCAModule().Load(ctx)
	require.NoError(t, err)
	cloudCaCert, err := rwproxy.GetRootCAModule().GetCertPem(ctx)
	require.NoError(t, err)

	tests := []struct {
		name        string
		userInfo    *ginauth.ControllerUserInfo
		nsID        uuid.UUID
		mockService func(*mock_service.MockTenantServiceInterface)
		mockQuerier func(*model_mock.MockQuerier)

		expectedCode    int
		expectedCertPem string
	}{
		{
			name:     "get cacert for byoc cluster",
			userInfo: userInfo,
			nsID:     nsID,
			mockService: func(service *mock_service.MockTenantServiceInterface) {
				service.EXPECT().GetOrgTenantByNsID(gomock.Any(), gomock.Eq(orgID), gomock.Eq(nsID)).
					Times(1).Return(model.Tenant{
					ID:        tenantID,
					ClusterID: clusterID,
					Status:    model.TenantStatusRunning,
				}, nil)
			},
			mockQuerier: func(queries *model_mock.MockQuerier) {
				queries.EXPECT().GetClusterById(gomock.Any(), gomock.Eq(clusterID)).Times(1).Return(&querier.ManagedCluster{
					Org: uuid.New(),
				}, nil)
				queries.EXPECT().GetClusterSettings(gomock.Any(), gomock.Eq(clusterID)).
					Times(1).
					Return([]*model.ClusterSetting{
						{
							Key:   settings.SettingsKeyBYOCRWProxyCACert,
							Value: "testcert",
						},
					}, nil)
			},
			expectedCode:    200,
			expectedCertPem: "testcert",
		},
		{
			name:     "get cacert for cloud cluster",
			userInfo: userInfo,
			nsID:     nsID,
			mockService: func(service *mock_service.MockTenantServiceInterface) {
				service.EXPECT().GetOrgTenantByNsID(gomock.Any(), gomock.Eq(orgID), gomock.Eq(nsID)).
					Times(1).Return(model.Tenant{
					ID:        tenantID,
					ClusterID: clusterID,
					Status:    model.TenantStatusRunning,
				}, nil)
			},
			mockQuerier: func(queries *model_mock.MockQuerier) {
				queries.EXPECT().GetClusterById(gomock.Any(), gomock.Eq(clusterID)).Times(1).Return(&querier.ManagedCluster{
					Org: uuid.MustParse(settings.CloudClusterOrg),
				}, nil)
			},
			expectedCode:    200,
			expectedCertPem: string(cloudCaCert),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			svc := mock_service.NewMockTenantServiceInterface(ctrl)
			querier := model_mock.NewMockQuerier(ctrl)
			m := model.NewModel(querier)

			controller := &UserController{
				tenantService: svc,
				model:         m,
			}
			recorder := httptest.NewRecorder()
			ginCtx, _ := gin.CreateTestContext(recorder)

			if tt.userInfo != nil {
				ginCtx.Set(ginauth.ControllerAuthInfoContextKey, tt.userInfo.ToAuthInfo())
			}
			if tt.mockService != nil {
				tt.mockService(svc)
			}
			if tt.mockQuerier != nil {
				tt.mockQuerier(querier)
			}

			controller.GetTenantsNsIdCaCert(ginCtx, tt.nsID)

			require.EqualValues(t, tt.expectedCode, recorder.Code)
			require.Equal(t, "attachment; filename=root.crt", recorder.Header().Get("content-disposition"))
			require.Equal(t, "binary", recorder.Header().Get("content-transfer-encoding"))
			require.Equal(t, "application/x-pem-file", recorder.Header().Get("Content-Type"))
			require.Equal(t, tt.expectedCertPem, recorder.Body.String())
		})
	}
}

func TestUserController_GetTenantsNsIdDatabasesDatabaseNameSchemas(t *testing.T) {
	userID := uuid.New()
	orgID := uuid.New()
	nsID := uuid.New()
	tenantID := uint64(1)
	clusterID := uint64(1)
	userInfo := &ginauth.ControllerUserInfo{
		Email:          "<EMAIL>",
		UserResourceID: userID,
		OrgID:          orgID,
	}

	tests := []struct {
		name        string
		userInfo    *ginauth.ControllerUserInfo
		nsID        uuid.UUID
		database    string
		mockService func(*mock_service.MockTenantServiceInterface)
		mockRwc     func(*rwc_mock.MockRisingWaveClientInterface)

		expectedCode int
		expectedBody spec.TenantSchemasInfo
	}{
		{
			name:     "list schemas",
			userInfo: userInfo,
			nsID:     nsID,
			database: "testdb",
			mockService: func(service *mock_service.MockTenantServiceInterface) {
				service.EXPECT().GetOrgTenantByNsID(gomock.Any(), gomock.Eq(orgID), gomock.Eq(nsID)).
					Times(1).Return(model.Tenant{
					ID:           tenantID,
					ClusterID:    clusterID,
					Status:       model.TenantStatusRunning,
					HealthStatus: model.HealthStatusHealthy,
					ImageTag:     "image",
				}, nil)
				service.EXPECT().PingTenant(gomock.Any(), tenantID, "image").Times(1).Return(nil)
			},
			mockRwc: func(rwc *rwc_mock.MockRisingWaveClientInterface) {
				rwc.EXPECT().GetSchemas(gomock.Any(), tenantID, "testdb").Times(1).Return([]rwdb.SchemaInfo{
					{
						CatalogName: "testdb",
						SchemaName:  "test",
						SchemaOwner: "postgres",
					},
				}, nil)
			},
			expectedCode: 200,
			expectedBody: spec.TenantSchemasInfo{
				Schemas: []spec.SchemaInfo{
					{
						CatalogName: "testdb",
						SchemaName:  "test",
						SchemaOwner: "postgres",
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			svc := mock_service.NewMockTenantServiceInterface(ctrl)
			rwc := rwc_mock.NewMockRisingWaveClientInterface(ctrl)

			controller := &UserController{
				tenantService: svc,
				rwc:           rwc,
			}
			recorder := httptest.NewRecorder()
			ginCtx, _ := gin.CreateTestContext(recorder)

			if tt.userInfo != nil {
				ginCtx.Set(ginauth.ControllerAuthInfoContextKey, tt.userInfo.ToAuthInfo())
			}
			if tt.mockService != nil {
				tt.mockService(svc)
			}
			if tt.mockRwc != nil {
				tt.mockRwc(rwc)
			}

			controller.GetTenantsNsIdDatabasesDatabaseNameSchemas(ginCtx, tt.nsID, tt.database)
			require.EqualValues(t, tt.expectedCode, recorder.Code)

			expectedJSON, err := json.Marshal(tt.expectedBody)
			require.NoError(t, err)
			require.JSONEq(t, string(expectedJSON), recorder.Body.String())
		})
	}
}

func TestGetTenantsNsIdMetrics(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	var (
		orgID          = uuid.New()
		tenantID       = uuid.New()
		userResourceID = uuid.New()

		metricsStr = `stream_barrier_sync_storage_duration_seconds_bucket{le="0.1"} 0`
	)

	tenantSvc := mock_service.NewMockTenantServiceInterface(ctrl)
	svc := mock_service.NewMockMetricsServiceInterface(ctrl)
	c1, r1 := newTestContext(orgID, userResourceID)
	var err error
	c1.Request, err = http.NewRequest(http.MethodGet, "", nil)
	require.NoError(t, err)

	controller := &UserController{
		tenantService:  tenantSvc,
		metricsService: svc,
	}

	// test for normal.
	tenant := model.Tenant{
		NsID:   tenantID,
		Status: model.TenantStatusRunning,
	}

	tenantSvc.
		EXPECT().
		GetOrgTenantByNsID(c1, orgID, tenantID).
		Return(tenant, nil)

	svc.
		EXPECT().
		ScrapeMetrics(c1, tenant, "gzip").
		Return([]byte(metricsStr), "gzip", nil)

	c1.Request.Header.Set("Accept-Encoding", "gzip")
	controller.GetTenantsNsIdMetrics(c1, tenantID)
	assert.Equal(t, http.StatusOK, c1.Writer.Status())
	assert.Equal(t, metricsStr, r1.Body.String())

	// test for not found.
	c2, _ := newTestContext(orgID, userResourceID)
	tenantSvc.
		EXPECT().
		GetOrgTenantByNsID(c2, orgID, tenantID).
		Return(model.Tenant{}, errors.ErrClusterNotExist)

	controller.GetTenantsNsIdMetrics(c2, tenantID)
	assert.Equal(t, http.StatusNotFound, c2.Writer.Status())

	c3, _ := newTestContext(orgID, userResourceID)
	tenantSvc.
		EXPECT().
		GetOrgTenantByNsID(c3, orgID, tenantID).
		Return(model.Tenant{
			Status: model.TenantStatusStopped,
		}, nil)

	controller.GetTenantsNsIdMetrics(c3, tenantID)
	assert.Equal(t, http.StatusBadRequest, c3.Writer.Status())
}

func TestPostTenantsNsIdTestAlert(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	var (
		orgID          = uuid.New()
		tenantID       = uuid.New()
		userResourceID = uuid.New()
	)

	tenantSvc := mock_service.NewMockTenantServiceInterface(ctrl)
	c, _ := newTestContext(orgID, userResourceID)
	var err error
	body, err := json.Marshal(spec.PostTenantsNsIdTestAlertJSONRequestBody{
		Triggered: true,
	})
	require.NoError(t, err)
	c.Request, err = http.NewRequest(http.MethodPost, "", bytes.NewReader(body))
	require.NoError(t, err)

	controller := &UserController{
		tenantService: tenantSvc,
	}

	tenant := model.Tenant{
		NsID:   tenantID,
		Status: model.TenantStatusRunning,
	}

	tenantSvc.
		EXPECT().
		GetOrgTenantByNsID(c, orgID, tenantID).
		Return(tenant, nil)

	c.Request.Header.Set("Accept-Encoding", "gzip")
	controller.PostTenantsNsIdTestAlert(c, tenantID)
	assert.Equal(t, http.StatusOK, c.Writer.Status())
}
