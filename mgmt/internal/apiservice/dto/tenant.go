package dto

import (
	"time"

	openapi_types "github.com/oapi-codegen/runtime/types"

	spec "github.com/risingwavelabs/risingwave-cloud/shared/spec/mgmt_spec_v2"
)

type TenantStatusCount struct {
	Count  int64  `json:"count"`
	Status string `json:"status"`
}

type BackupSnapshotItem struct {
	CreatedAtUnixMills int64              `json:"created_at_unix_mills"`
	ID                 openapi_types.UUID `json:"id"`
	MetaSnapshotID     *int64             `json:"meta_snapshot_id,omitempty"`
	Status             string             `json:"status"`
	RwVersion          *string            `json:"rw_version"`
	Type               *string            `json:"type"`
}

type AvailableComponentType struct {
	CPU     string `json:"cpu"`
	ID      string `json:"id"`
	Maximum int    `json:"maximum"`
	Memory  string `json:"memory"`
}

type TierID string

type Tier struct {
	AvailableCompactorNodes            []AvailableComponentType `json:"availableCompactorNodes"`
	AvailableComputeNodes              []AvailableComponentType `json:"availableComputeNodes"`
	AvailableFrontendNodes             []AvailableComponentType `json:"availableFrontendNodes"`
	AvailableMetaNodes                 []AvailableComponentType `json:"availableMetaNodes"`
	AvailableStandaloneNodes           []AvailableComponentType `json:"availableStandaloneNodes"`
	ID                                 *TierID                  `json:"id,omitempty"`
	MaximumComputeNodeFileCacheSizeGiB int                      `json:"maximumComputeNodeFileCacheSizeGiB"`
	RetentionPeriod                    int                      `json:"retentionPeriod"`
	ValidityPeriod                     int                      `json:"validityPeriod"`
	AvailableMetaStore                 *AvailableMetaStore      `json:"availableMetaStore,omitempty"`
}

type ComponentResource struct {
	ComponentTypeID string `json:"componentTypeId"`
	CPU             string `json:"cpu"`
	Memory          string `json:"memory"`
	Replica         int    `json:"replica"`
}

type AvailableMetaStore struct {
	Etcd        *AvailableMetaStoreEtcd        `json:"etcd,omitempty"`
	PostgreSQL  *AvailableMetaStorePostgreSQL  `json:"postgresql,omitempty"`
	AwsRds      *AvailableMetaStoreAwsRds      `json:"aws_rds,omitempty"`
	GcpCloudSQL *AvailableMetaStoreGcpCloudSQL `json:"gcp_cloudsql,omitempty"`
	AzrPostgres *AvailableMetaStoreAzrPostgres `json:"azr_postgres,omitempty"`
	SharingPg   *AvailableMetaStoreSharingPg   `json:"sharing_pg,omitempty"`
}

type AvailableMetaStoreEtcd struct {
	Nodes          []AvailableComponentType `json:"nodes"`
	MaximumSizeGiB int                      `json:"maximumSizeGiB"`
}

type AvailableMetaStorePostgreSQL struct {
	Nodes          []AvailableComponentType `json:"nodes"`
	MaximumSizeGiB int                      `json:"maximumSizeGiB"`
}

// RDS / sharing_pg spec is fully decided by control plane.

type AvailableMetaStoreAwsRds struct {
	Enabled bool `json:"enabled"` // useless field for openapi-codegen
}
type AvailableMetaStoreGcpCloudSQL struct {
	Enabled bool `json:"enabled"` // useless field for openapi-codegen
}
type AvailableMetaStoreAzrPostgres struct {
	Enabled bool `json:"enabled"` // useless field for openapi-codegen
}
type AvailableMetaStoreSharingPg struct {
	Enabled bool `json:"enabled"` // useless field for openapi-codegen
}

type TenantResourceComponents struct {
	Compactor  *ComponentResource `json:"compactor,omitempty"`
	Compute    *ComponentResource `json:"compute,omitempty"`
	Etcd       *ComponentResource `json:"etcd,omitempty"`
	Frontend   *ComponentResource `json:"frontend,omitempty"`
	Meta       *ComponentResource `json:"meta,omitempty"`
	Standalone *ComponentResource `json:"standalone,omitempty"`
}

type TenantResource struct {
	Components        TenantResourceComponents    `json:"components"`
	EtcdVolumeSizeGiB *int                        `json:"etcdVolumeSizeGiB,omitempty"`
	ComputeCache      *TenantResourceComputeCache `json:"computeCache,omitempty"`
	MetaStore         *TenantResourceMetaStore    `json:"metaStore,omitempty"`
	ResourceGroups    *[]TenantResourceGroup      `json:"resourceGroups,omitempty"`
}

type TenantResourceComputeCache struct {
	SizeGb int32 `json:"sizeGb"`
}

type TenantResourceMetaStore struct {
	Type        string                `json:"type"`
	Rwu         string                `json:"rwu"`
	Etcd        *MetaStoreEtcd        `json:"etcd,omitempty"`
	PostgreSQL  *MetaStorePostgreSQL  `json:"postgresql,omitempty"`
	AwsRds      *MetaStoreAwsRds      `json:"aws_rds,omitempty"`
	GcpCloudSQL *MetaStoreGcpCloudSQL `json:"gcp_cloudsql,omitempty"`
	AzrPostgres *MetaStoreAzrPostgres `json:"azr_postgres,omitempty"`
}

type TenantResourceGroup struct {
	Name         string                     `json:"name"`
	Resource     ComponentResource          `json:"resource"`
	ComputeCache TenantResourceComputeCache `json:"computeCache"`
}

type MetaStoreEtcd struct {
	Resource ComponentResource `json:"resource"`
	SizeGb   int               `json:"sizeGb"`
}

type MetaStorePostgreSQL struct {
	Resource ComponentResource `json:"resource"`
	SizeGb   int               `json:"sizeGb"`
}

type MetaStoreAwsRds struct {
	InstanceClass string `json:"instanceClass"`
	SizeGb        int    `json:"sizeGb"`
}

type MetaStoreGcpCloudSQL struct {
	Tier   string `json:"tier"`
	SizeGb int    `json:"sizeGb"`
}

type MetaStoreAzrPostgres struct {
	Sku    string `json:"sku"`
	SizeGb int    `json:"sizeGb"`
}

type TenantHealthStatus string

type TenantStatus string

type Tenant struct {
	CreatedAt            time.Time              `json:"createdAt"`
	EtcdConfig           string                 `json:"etcd_config"`
	HealthStatus         TenantHealthStatus     `json:"health_status"`
	ID                   uint64                 `json:"id"`
	ImageTag             string                 `json:"imageTag"`
	LatestImageTag       string                 `json:"latestImageTag"`
	NsID                 openapi_types.UUID     `json:"nsId"`
	OrgID                openapi_types.UUID     `json:"orgId"`
	Region               string                 `json:"region"`
	Resources            TenantResource         `json:"resources"`
	RwConfig             string                 `json:"rw_config"`
	Status               TenantStatus           `json:"status"`
	TenantName           string                 `json:"tenantName"`
	Tier                 TierID                 `json:"tier"`
	UpdatedAt            time.Time              `json:"updatedAt"`
	UsageType            string                 `json:"usageType"`
	UserID               uint64                 `json:"userId"`
	ClusterName          string                 `json:"clusterName"`
	Extensions           *spec.TenantExtensions `json:"extensions,omitempty"`
	UpcomingSnapshotTime *time.Time             `json:"upcomingSnapshotTime,omitempty"`
}
