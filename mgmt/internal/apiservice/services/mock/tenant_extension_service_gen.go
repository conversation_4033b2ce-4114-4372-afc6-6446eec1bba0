// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/risingwavelabs/risingwave-cloud/internal/apiservice/services (interfaces: TenantExtensionService)
//
// Generated by this command:
//
//	mockgen-v0.5.0 -package=services -destination=internal/apiservice/services/mock/tenant_extension_service_gen.go github.com/risingwavelabs/risingwave-cloud/internal/apiservice/services TenantExtensionService
//

// Package services is a generated GoMock package.
package services

import (
	context "context"
	reflect "reflect"

	uuid "github.com/google/uuid"
	services "github.com/risingwavelabs/risingwave-cloud/internal/apiservice/services"
	querier "github.com/risingwavelabs/risingwave-cloud/internal/model/querier"
	tenantext "github.com/risingwavelabs/risingwave-cloud/internal/tenantext"
	mgmt_spec_v2 "github.com/risingwavelabs/risingwave-cloud/shared/spec/mgmt_spec_v2"
	gomock "go.uber.org/mock/gomock"
)

// MockTenantExtensionService is a mock of TenantExtensionService interface.
type MockTenantExtensionService struct {
	ctrl     *gomock.Controller
	recorder *MockTenantExtensionServiceMockRecorder
	isgomock struct{}
}

// MockTenantExtensionServiceMockRecorder is the mock recorder for MockTenantExtensionService.
type MockTenantExtensionServiceMockRecorder struct {
	mock *MockTenantExtensionService
}

// NewMockTenantExtensionService creates a new mock instance.
func NewMockTenantExtensionService(ctrl *gomock.Controller) *MockTenantExtensionService {
	mock := &MockTenantExtensionService{ctrl: ctrl}
	mock.recorder = &MockTenantExtensionServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTenantExtensionService) EXPECT() *MockTenantExtensionServiceMockRecorder {
	return m.recorder
}

// CompareAndUpdateTenantExtensionStatusByTenantIDAndResourceTypeAndStatuses mocks base method.
func (m *MockTenantExtensionService) CompareAndUpdateTenantExtensionStatusByTenantIDAndResourceTypeAndStatuses(ctx context.Context, tenantID uint64, resourceType, status string, oldStatuses []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CompareAndUpdateTenantExtensionStatusByTenantIDAndResourceTypeAndStatuses", ctx, tenantID, resourceType, status, oldStatuses)
	ret0, _ := ret[0].(error)
	return ret0
}

// CompareAndUpdateTenantExtensionStatusByTenantIDAndResourceTypeAndStatuses indicates an expected call of CompareAndUpdateTenantExtensionStatusByTenantIDAndResourceTypeAndStatuses.
func (mr *MockTenantExtensionServiceMockRecorder) CompareAndUpdateTenantExtensionStatusByTenantIDAndResourceTypeAndStatuses(ctx, tenantID, resourceType, status, oldStatuses any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CompareAndUpdateTenantExtensionStatusByTenantIDAndResourceTypeAndStatuses", reflect.TypeOf((*MockTenantExtensionService)(nil).CompareAndUpdateTenantExtensionStatusByTenantIDAndResourceTypeAndStatuses), ctx, tenantID, resourceType, status, oldStatuses)
}

// CreateTenantExtension mocks base method.
func (m *MockTenantExtensionService) CreateTenantExtension(ctx context.Context, tenantID uint64, resourceType, config, version, status string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTenantExtension", ctx, tenantID, resourceType, config, version, status)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateTenantExtension indicates an expected call of CreateTenantExtension.
func (mr *MockTenantExtensionServiceMockRecorder) CreateTenantExtension(ctx, tenantID, resourceType, config, version, status any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTenantExtension", reflect.TypeOf((*MockTenantExtensionService)(nil).CreateTenantExtension), ctx, tenantID, resourceType, config, version, status)
}

// DisableExtensionIcebergCompaction mocks base method.
func (m *MockTenantExtensionService) DisableExtensionIcebergCompaction(ctx context.Context, tenantID uint64) (uuid.UUID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DisableExtensionIcebergCompaction", ctx, tenantID)
	ret0, _ := ret[0].(uuid.UUID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DisableExtensionIcebergCompaction indicates an expected call of DisableExtensionIcebergCompaction.
func (mr *MockTenantExtensionServiceMockRecorder) DisableExtensionIcebergCompaction(ctx, tenantID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DisableExtensionIcebergCompaction", reflect.TypeOf((*MockTenantExtensionService)(nil).DisableExtensionIcebergCompaction), ctx, tenantID)
}

// DisableExtensionsCompaction mocks base method.
func (m *MockTenantExtensionService) DisableExtensionsCompaction(ctx context.Context, tenantID uint64) (uuid.UUID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DisableExtensionsCompaction", ctx, tenantID)
	ret0, _ := ret[0].(uuid.UUID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DisableExtensionsCompaction indicates an expected call of DisableExtensionsCompaction.
func (mr *MockTenantExtensionServiceMockRecorder) DisableExtensionsCompaction(ctx, tenantID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DisableExtensionsCompaction", reflect.TypeOf((*MockTenantExtensionService)(nil).DisableExtensionsCompaction), ctx, tenantID)
}

// DisableExtensionsServerlessBackfill mocks base method.
func (m *MockTenantExtensionService) DisableExtensionsServerlessBackfill(ctx context.Context, tenantID uint64) (uuid.UUID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DisableExtensionsServerlessBackfill", ctx, tenantID)
	ret0, _ := ret[0].(uuid.UUID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DisableExtensionsServerlessBackfill indicates an expected call of DisableExtensionsServerlessBackfill.
func (mr *MockTenantExtensionServiceMockRecorder) DisableExtensionsServerlessBackfill(ctx, tenantID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DisableExtensionsServerlessBackfill", reflect.TypeOf((*MockTenantExtensionService)(nil).DisableExtensionsServerlessBackfill), ctx, tenantID)
}

// EnableExtensionIcebergCompaction mocks base method.
func (m *MockTenantExtensionService) EnableExtensionIcebergCompaction(ctx context.Context, tenantID uint64, opts *services.IcebergCompactionOptions) (uuid.UUID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EnableExtensionIcebergCompaction", ctx, tenantID, opts)
	ret0, _ := ret[0].(uuid.UUID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EnableExtensionIcebergCompaction indicates an expected call of EnableExtensionIcebergCompaction.
func (mr *MockTenantExtensionServiceMockRecorder) EnableExtensionIcebergCompaction(ctx, tenantID, opts any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnableExtensionIcebergCompaction", reflect.TypeOf((*MockTenantExtensionService)(nil).EnableExtensionIcebergCompaction), ctx, tenantID, opts)
}

// EnableExtensionsCompaction mocks base method.
func (m *MockTenantExtensionService) EnableExtensionsCompaction(ctx context.Context, tenantID uint64) (uuid.UUID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EnableExtensionsCompaction", ctx, tenantID)
	ret0, _ := ret[0].(uuid.UUID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EnableExtensionsCompaction indicates an expected call of EnableExtensionsCompaction.
func (mr *MockTenantExtensionServiceMockRecorder) EnableExtensionsCompaction(ctx, tenantID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnableExtensionsCompaction", reflect.TypeOf((*MockTenantExtensionService)(nil).EnableExtensionsCompaction), ctx, tenantID)
}

// EnableExtensionsServerlessBackfill mocks base method.
func (m *MockTenantExtensionService) EnableExtensionsServerlessBackfill(ctx context.Context, tenantID uint64) (uuid.UUID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EnableExtensionsServerlessBackfill", ctx, tenantID)
	ret0, _ := ret[0].(uuid.UUID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EnableExtensionsServerlessBackfill indicates an expected call of EnableExtensionsServerlessBackfill.
func (mr *MockTenantExtensionServiceMockRecorder) EnableExtensionsServerlessBackfill(ctx, tenantID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnableExtensionsServerlessBackfill", reflect.TypeOf((*MockTenantExtensionService)(nil).EnableExtensionsServerlessBackfill), ctx, tenantID)
}

// GetExtensionIcebergCompaction mocks base method.
func (m *MockTenantExtensionService) GetExtensionIcebergCompaction(ctx context.Context, tenantID uint64) (mgmt_spec_v2.IcebergCompaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExtensionIcebergCompaction", ctx, tenantID)
	ret0, _ := ret[0].(mgmt_spec_v2.IcebergCompaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExtensionIcebergCompaction indicates an expected call of GetExtensionIcebergCompaction.
func (mr *MockTenantExtensionServiceMockRecorder) GetExtensionIcebergCompaction(ctx, tenantID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExtensionIcebergCompaction", reflect.TypeOf((*MockTenantExtensionService)(nil).GetExtensionIcebergCompaction), ctx, tenantID)
}

// GetSpecTenantExtensionsByTenantID mocks base method.
func (m *MockTenantExtensionService) GetSpecTenantExtensionsByTenantID(ctx context.Context, tenantID uint64) (*mgmt_spec_v2.TenantExtensions, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSpecTenantExtensionsByTenantID", ctx, tenantID)
	ret0, _ := ret[0].(*mgmt_spec_v2.TenantExtensions)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSpecTenantExtensionsByTenantID indicates an expected call of GetSpecTenantExtensionsByTenantID.
func (mr *MockTenantExtensionServiceMockRecorder) GetSpecTenantExtensionsByTenantID(ctx, tenantID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSpecTenantExtensionsByTenantID", reflect.TypeOf((*MockTenantExtensionService)(nil).GetSpecTenantExtensionsByTenantID), ctx, tenantID)
}

// GetTenantExtensionByTenantIDAndResourceType mocks base method.
func (m *MockTenantExtensionService) GetTenantExtensionByTenantIDAndResourceType(ctx context.Context, tenantID uint64, resourceType string) (querier.TenantExtension, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTenantExtensionByTenantIDAndResourceType", ctx, tenantID, resourceType)
	ret0, _ := ret[0].(querier.TenantExtension)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantExtensionByTenantIDAndResourceType indicates an expected call of GetTenantExtensionByTenantIDAndResourceType.
func (mr *MockTenantExtensionServiceMockRecorder) GetTenantExtensionByTenantIDAndResourceType(ctx, tenantID, resourceType any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantExtensionByTenantIDAndResourceType", reflect.TypeOf((*MockTenantExtensionService)(nil).GetTenantExtensionByTenantIDAndResourceType), ctx, tenantID, resourceType)
}

// GetTenantExtensionsByTenantID mocks base method.
func (m *MockTenantExtensionService) GetTenantExtensionsByTenantID(ctx context.Context, tenantID uint64) ([]querier.TenantExtension, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTenantExtensionsByTenantID", ctx, tenantID)
	ret0, _ := ret[0].([]querier.TenantExtension)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantExtensionsByTenantID indicates an expected call of GetTenantExtensionsByTenantID.
func (mr *MockTenantExtensionServiceMockRecorder) GetTenantExtensionsByTenantID(ctx, tenantID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantExtensionsByTenantID", reflect.TypeOf((*MockTenantExtensionService)(nil).GetTenantExtensionsByTenantID), ctx, tenantID)
}

// UpdateExtensionIcebergCompaction mocks base method.
func (m *MockTenantExtensionService) UpdateExtensionIcebergCompaction(ctx context.Context, tenantID uint64, opts services.IcebergCompactionOptions) (uuid.UUID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateExtensionIcebergCompaction", ctx, tenantID, opts)
	ret0, _ := ret[0].(uuid.UUID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateExtensionIcebergCompaction indicates an expected call of UpdateExtensionIcebergCompaction.
func (mr *MockTenantExtensionServiceMockRecorder) UpdateExtensionIcebergCompaction(ctx, tenantID, opts any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateExtensionIcebergCompaction", reflect.TypeOf((*MockTenantExtensionService)(nil).UpdateExtensionIcebergCompaction), ctx, tenantID, opts)
}

// UpdateExtensionsCompaction mocks base method.
func (m *MockTenantExtensionService) UpdateExtensionsCompaction(ctx context.Context, tenantID uint64, option tenantext.ServerlessCompactionParams) (uuid.UUID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateExtensionsCompaction", ctx, tenantID, option)
	ret0, _ := ret[0].(uuid.UUID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateExtensionsCompaction indicates an expected call of UpdateExtensionsCompaction.
func (mr *MockTenantExtensionServiceMockRecorder) UpdateExtensionsCompaction(ctx, tenantID, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateExtensionsCompaction", reflect.TypeOf((*MockTenantExtensionService)(nil).UpdateExtensionsCompaction), ctx, tenantID, option)
}

// UpdateExtensionsServerlessBackfill mocks base method.
func (m *MockTenantExtensionService) UpdateExtensionsServerlessBackfill(ctx context.Context, tenantID uint64, version string) (uuid.UUID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateExtensionsServerlessBackfill", ctx, tenantID, version)
	ret0, _ := ret[0].(uuid.UUID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateExtensionsServerlessBackfill indicates an expected call of UpdateExtensionsServerlessBackfill.
func (mr *MockTenantExtensionServiceMockRecorder) UpdateExtensionsServerlessBackfill(ctx, tenantID, version any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateExtensionsServerlessBackfill", reflect.TypeOf((*MockTenantExtensionService)(nil).UpdateExtensionsServerlessBackfill), ctx, tenantID, version)
}

// UpdateTenantExtensionStatusByTenantIDAndResourceType mocks base method.
func (m *MockTenantExtensionService) UpdateTenantExtensionStatusByTenantIDAndResourceType(ctx context.Context, tenantID uint64, resourceType, status string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTenantExtensionStatusByTenantIDAndResourceType", ctx, tenantID, resourceType, status)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateTenantExtensionStatusByTenantIDAndResourceType indicates an expected call of UpdateTenantExtensionStatusByTenantIDAndResourceType.
func (mr *MockTenantExtensionServiceMockRecorder) UpdateTenantExtensionStatusByTenantIDAndResourceType(ctx, tenantID, resourceType, status any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTenantExtensionStatusByTenantIDAndResourceType", reflect.TypeOf((*MockTenantExtensionService)(nil).UpdateTenantExtensionStatusByTenantIDAndResourceType), ctx, tenantID, resourceType, status)
}

// ValidateExtensionServerlessBackfillCanDisable mocks base method.
func (m *MockTenantExtensionService) ValidateExtensionServerlessBackfillCanDisable(c context.Context, tenantID uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateExtensionServerlessBackfillCanDisable", c, tenantID)
	ret0, _ := ret[0].(error)
	return ret0
}

// ValidateExtensionServerlessBackfillCanDisable indicates an expected call of ValidateExtensionServerlessBackfillCanDisable.
func (mr *MockTenantExtensionServiceMockRecorder) ValidateExtensionServerlessBackfillCanDisable(c, tenantID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateExtensionServerlessBackfillCanDisable", reflect.TypeOf((*MockTenantExtensionService)(nil).ValidateExtensionServerlessBackfillCanDisable), c, tenantID)
}

// ValidateExtensionServerlessBackfillCanEnable mocks base method.
func (m *MockTenantExtensionService) ValidateExtensionServerlessBackfillCanEnable(c context.Context, tenantID uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateExtensionServerlessBackfillCanEnable", c, tenantID)
	ret0, _ := ret[0].(error)
	return ret0
}

// ValidateExtensionServerlessBackfillCanEnable indicates an expected call of ValidateExtensionServerlessBackfillCanEnable.
func (mr *MockTenantExtensionServiceMockRecorder) ValidateExtensionServerlessBackfillCanEnable(c, tenantID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateExtensionServerlessBackfillCanEnable", reflect.TypeOf((*MockTenantExtensionService)(nil).ValidateExtensionServerlessBackfillCanEnable), c, tenantID)
}

// ValidateExtensionServerlessBackfillCanPostVersion mocks base method.
func (m *MockTenantExtensionService) ValidateExtensionServerlessBackfillCanPostVersion(c context.Context, tenantID uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateExtensionServerlessBackfillCanPostVersion", c, tenantID)
	ret0, _ := ret[0].(error)
	return ret0
}

// ValidateExtensionServerlessBackfillCanPostVersion indicates an expected call of ValidateExtensionServerlessBackfillCanPostVersion.
func (mr *MockTenantExtensionServiceMockRecorder) ValidateExtensionServerlessBackfillCanPostVersion(c, tenantID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateExtensionServerlessBackfillCanPostVersion", reflect.TypeOf((*MockTenantExtensionService)(nil).ValidateExtensionServerlessBackfillCanPostVersion), c, tenantID)
}
