package services

import (
	"context"
	"regexp"
	"slices"
	"strings"

	"github.com/google/uuid"
	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/risingwave-cloud/internal/apiservice/dto"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/rwdb"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/version"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenant/resourcereq"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenant/resourcespec"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/resourcegroup"
)

const (
	resourceGroupPattern        = "[a-z0-9]([a-z0-9-]{0,18}[a-z0-9])?"
	resourceGroupBackfillPrefix = "backfill"
	ResourceGroupDefault        = "default"
)

var resourceGroupRegEx = regexp.MustCompile("^" + resourceGroupPattern + "$")

type ResourceGroupService struct {
	resourceGroupWorkflow resourcegroup.ResourceGroupWorkflowService
	rwClient              rwdb.RisingWaveClientInterface
	model                 *model.Model
}

func NewResourceGroupService(
	resourceGroupWorkflow resourcegroup.ResourceGroupWorkflowService,
	rwClient rwdb.RisingWaveClientInterface,
	model *model.Model,
) *ResourceGroupService {
	return &ResourceGroupService{
		resourceGroupWorkflow: resourceGroupWorkflow,
		rwClient:              rwClient,
		model:                 model,
	}
}

func (service *ResourceGroupService) GetResourceGroupDetails(ctx context.Context, tenant model.Tenant) ([]dto.ResourceGroupDetails, error) {
	result := []dto.ResourceGroupDetails{}
	if tenant.Resources.IsStandalone() {
		return result, nil
	}

	cnt, err := service.model.GetRwDatabasesCountGroupByResourceGroup(ctx, tenant.ID)
	if err != nil {
		return nil, err
	}

	regular, ok := tenant.Resources.AsTenantResourceSpecV1Regular()
	if !ok {
		return nil, eris.New("Failed to cast resource spec as regular")
	}
	for _, resourceGroup := range regular.ResourceGroups {
		resource, err := toSpecComponentResource(resourceGroup.ResourceSpec, int(resourceGroup.Replica))
		if err != nil {
			return nil, eris.Wrap(err, "Failed to convert resource spec to component resource")
		}

		result = append(result, dto.ResourceGroupDetails{
			Name:          resourceGroup.Name,
			Resource:      resource,
			DatabaseCount: cnt[resourceGroup.Name],
			ComputeCache:  *toSpecComputeCache(resourceGroup.ComputeCacheSpec),
		})
	}

	return result, nil
}

func (service *ResourceGroupService) CreateResourceGroup(
	ctx context.Context, tenant model.Tenant, req resourcereq.TenantResourceGroupRequest,
) (uuid.UUID, error) {
	supported, err := version.IsResourceGroupSupported(tenant.ImageTag)
	if err != nil {
		return uuid.Nil, eris.Wrapf(err, "Failed to parse image %s", tenant.ImageTag)
	}
	if !supported {
		return uuid.Nil, eris.New("Don't support resource group for version <2.3.0").WithCode(eris.CodeFailedPrecondition)
	}

	if tenant.Resources.IsStandalone() {
		return uuid.Nil, eris.New("Don't support resource group for standalone cluster").WithCode(eris.CodeFailedPrecondition)
	}
	regular, ok := tenant.Resources.AsTenantResourceSpecV1Regular()
	if !ok {
		return uuid.Nil, eris.New("Failed to cast resource spec as regular")
	}

	if strings.HasPrefix(req.Name, resourceGroupBackfillPrefix) {
		return uuid.Nil, eris.New("Invalid name, resource group name cannot start with 'backfill'").WithCode(eris.CodeInvalidArgument)
	}
	if req.Name == ResourceGroupDefault {
		return uuid.Nil, eris.New("Invalid name, resource group name cannot be 'default'").WithCode(eris.CodeInvalidArgument)
	}
	if !resourceGroupRegEx.MatchString(req.Name) {
		return uuid.Nil, eris.Errorf("Invalid name, resource group name should match '%s'", resourceGroupPattern).WithCode(eris.CodeInvalidArgument)
	}

	resourceGroups := slices.Clone(regular.ResourceGroups)
	for _, resourceGroup := range resourceGroups {
		if resourceGroup.Name == req.Name {
			return uuid.Nil, eris.Errorf("Resource group '%s' already exist", req.Name).WithCode(eris.CodeAlreadyExists)
		}
	}

	validation, err := resourcereq.ParseResourceGroup(tenant.TierID, req)
	if err != nil {
		return uuid.Nil, err
	}
	if !validation.IsValid {
		return uuid.Nil, eris.New(validation.Message).WithCode(eris.CodeInvalidArgument)
	}

	resourceGroups = append(resourceGroups, validation.Result)

	workflowID, err := service.resourceGroupWorkflow.UpdateResourceGroups(ctx, tenant.ID, resourceGroups)
	if err != nil {
		return uuid.Nil, err
	}

	return workflowID, nil
}

func (service *ResourceGroupService) UpdateResourceGroup(
	ctx context.Context, tenant model.Tenant, req resourcereq.TenantResourceGroupRequest,
) (uuid.UUID, error) {
	regular, ok := tenant.Resources.AsTenantResourceSpecV1Regular()
	if !ok {
		return uuid.Nil, eris.New("Failed to cast resource spec as regular")
	}

	resourceGroups := slices.Clone(regular.ResourceGroups)
	index := slices.IndexFunc(resourceGroups, func(resourceGroup resourcespec.TenantResourceGroup) bool {
		return resourceGroup.Name == req.Name
	})
	if index == -1 {
		return uuid.Nil, eris.Errorf("Resource group %s not found", req.Name).WithCode(eris.CodeNotFound)
	}

	validation, err := resourcereq.ParseResourceGroup(tenant.TierID, req)
	if err != nil {
		return uuid.Nil, err
	}
	if !validation.IsValid {
		return uuid.Nil, eris.New(validation.Message).WithCode(eris.CodeInvalidArgument)
	}

	resourceGroups[index].ResourceSpec = validation.Result.ResourceSpec
	resourceGroups[index].Replica = validation.Result.Replica
	// keep ComputeCacheSpec unchanged, we don't support update disk cache in this workflow

	workflowID, err := service.resourceGroupWorkflow.UpdateResourceGroups(ctx, tenant.ID, resourceGroups)
	if err != nil {
		return uuid.Nil, err
	}

	return workflowID, nil
}

func (service *ResourceGroupService) DeleteResourceGroup(
	ctx context.Context, tenant model.Tenant, resourceGroup string,
) (uuid.UUID, error) {
	regular, ok := tenant.Resources.AsTenantResourceSpecV1Regular()
	if !ok {
		return uuid.Nil, eris.New("Failed to cast resource spec as regular")
	}

	resourceGroups := slices.Clone(regular.ResourceGroups)
	index := -1
	for idx, group := range resourceGroups {
		if group.Name == resourceGroup {
			index = idx
			break
		}
	}
	if index == -1 {
		return uuid.Nil, eris.New("Resource group not found").WithCode(eris.CodeNotFound)
	}

	databases, err := service.rwClient.GetDatabasesByResourceGroup(ctx, tenant.ID, resourceGroup)
	if err != nil {
		return uuid.Nil, eris.Wrap(err, "Failed to get databases by resource group")
	}
	if len(databases) > 0 {
		return uuid.Nil, eris.Errorf("Resource group %s still has databases: %s", resourceGroup, strings.Join(databases, ",")).WithCode(eris.CodeFailedPrecondition)
	}

	streamingJobs, err := service.rwClient.GetStreamJobsByResourceGroup(ctx, tenant.ID, resourceGroup)
	if err != nil {
		return uuid.Nil, eris.Wrap(err, "Failed to get streaming jobs by resource group")
	}
	if len(streamingJobs) > 0 {
		return uuid.Nil, eris.Errorf("Resource group %s still has streaming jobs, such as %s", resourceGroup, streamingJobs[0]).WithCode(eris.CodeFailedPrecondition)
	}

	resourceGroups = slices.Delete(resourceGroups, index, index+1)

	workflowID, err := service.resourceGroupWorkflow.UpdateResourceGroups(ctx, tenant.ID, resourceGroups)
	if err != nil {
		return uuid.Nil, err
	}

	return workflowID, nil
}
