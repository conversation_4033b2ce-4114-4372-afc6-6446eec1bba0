package services

import (
	"context"

	"github.com/google/uuid"
	"go.uber.org/zap"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/utils/ptr"

	"github.com/risingwavelabs/eris"

	apierr "github.com/risingwavelabs/risingwave-cloud/internal/apiservice/errors"
	"github.com/risingwavelabs/risingwave-cloud/internal/config"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/modeltx"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/querier"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/extensions/backfill"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/extensions/compaction"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/extensions/iceberg"
	"github.com/risingwavelabs/risingwave-cloud/shared/ginx"
	"github.com/risingwavelabs/risingwave-cloud/shared/logger"
	spec "github.com/risingwavelabs/risingwave-cloud/shared/spec/mgmt_spec_v2"
)

type IcebergCompactionOptions struct {
	Config          string
	Replicas        int
	ComponentTypeID string
}

type TenantExtensionService interface {
	EnableExtensionsCompaction(ctx context.Context, tenantID TenantID) (uuid.UUID, error)
	DisableExtensionsCompaction(ctx context.Context, tenantID TenantID) (uuid.UUID, error)
	EnableExtensionsServerlessBackfill(ctx context.Context, tenantID TenantID) (uuid.UUID, error)
	DisableExtensionsServerlessBackfill(ctx context.Context, tenantID TenantID) (uuid.UUID, error)
	GetTenantExtensionByTenantIDAndResourceType(ctx context.Context, tenantID uint64, resourceType string) (model.TenantExtension, error)
	CreateTenantExtension(ctx context.Context, tenantID TenantID, resourceType string, config string, version string, status string) error
	UpdateTenantExtensionStatusByTenantIDAndResourceType(ctx context.Context, tenantID TenantID, resourceType string, status string) error
	CompareAndUpdateTenantExtensionStatusByTenantIDAndResourceTypeAndStatuses(ctx context.Context, tenantID TenantID, resourceType string, status string, oldStatuses []string) error
	UpdateExtensionsCompaction(ctx context.Context, tenantID TenantID, option UpdateTenantExtensionUpdateOption) (uuid.UUID, error)
	UpdateExtensionsServerlessBackfill(ctx context.Context, tenantID TenantID, version string) (uuid.UUID, error)
	ValidateExtensionServerlessBackfillCanPostVersion(c context.Context, tenantID uint64) error
	ValidateExtensionServerlessBackfillCanDisable(c context.Context, tenantID uint64) error
	ValidateExtensionServerlessBackfillCanEnable(c context.Context, tenantID uint64) error
	GetTenantExtensionsByTenantID(ctx context.Context, tenantID uint64) ([]model.TenantExtension, error)
	GetSpecTenantExtensionsByTenantID(ctx context.Context, tenantID uint64) (*spec.TenantExtensions, error)

	// GetExtensionIcebergCompaction returns the iceberg compaction extension config for the given tenant ID.
	GetExtensionIcebergCompaction(ctx context.Context, tenantID uint64) (spec.IcebergCompaction, error)

	// EnableExtensionIcebergCompaction enables the iceberg compaction extension for the given tenant ID.
	// Note:
	// - If opts is nil, it will use the default config so the extension must have been created before!
	EnableExtensionIcebergCompaction(ctx context.Context, tenantID TenantID, opts *IcebergCompactionOptions) (uuid.UUID, error)

	// DisableExtensionIcebergCompaction disables the iceberg compaction extension for the given tenant ID.
	DisableExtensionIcebergCompaction(ctx context.Context, tenantID TenantID) (uuid.UUID, error)

	// UpdateExtensionIcebergCompaction updates the iceberg compaction extension config for the given tenant ID.
	UpdateExtensionIcebergCompaction(ctx context.Context, tenantID TenantID, opts IcebergCompactionOptions) (uuid.UUID, error)
}

type tenantExtensionServiceImpl struct {
	model        *model.Model
	txProvider   modeltx.TxProvider
	workflowRepo *workflow.Repository

	enableExtensionsCompactionWorkflow          *compaction.EnableWorkflowService
	disableExtensionsCompactionWorkflow         *compaction.DisableWorkflowService
	updateExtensionsCompactionWorkflow          *compaction.UpdateWorkflowService
	enableExtensionsServerlessBackfillWorkflow  *backfill.EnableWorkflowService
	updateExtensionsServerlessBackfillWorkflow  *backfill.UpdateWorkflowService
	disableExtensionsServerlessBackfillWorkflow *backfill.DisableWorkflowService
	icebergCompactionWorkflowService            *iceberg.CompactionWorkflowService
}

func NewTenantExtensionService(
	model *model.Model,
	txProvider modeltx.TxProvider,
	enableExtensionsCompactionWorkflow *compaction.EnableWorkflowService,
	disableExtensionsCompactionWorkflow *compaction.DisableWorkflowService,
	updateExtensionsCompactionWorkflow *compaction.UpdateWorkflowService,
	enableExtensionsServerlessBackfillWorkflow *backfill.EnableWorkflowService,
	updateExtensionsServerlessBackfillWorkflow *backfill.UpdateWorkflowService,
	disableExtensionsServerlessBackfillWorkflow *backfill.DisableWorkflowService,
	icebergCompactionWorkflowService *iceberg.CompactionWorkflowService,
	workflowRepo *workflow.Repository,
) TenantExtensionService {
	return &tenantExtensionServiceImpl{
		model:                                       model,
		txProvider:                                  txProvider,
		enableExtensionsCompactionWorkflow:          enableExtensionsCompactionWorkflow,
		disableExtensionsCompactionWorkflow:         disableExtensionsCompactionWorkflow,
		updateExtensionsCompactionWorkflow:          updateExtensionsCompactionWorkflow,
		enableExtensionsServerlessBackfillWorkflow:  enableExtensionsServerlessBackfillWorkflow,
		updateExtensionsServerlessBackfillWorkflow:  updateExtensionsServerlessBackfillWorkflow,
		disableExtensionsServerlessBackfillWorkflow: disableExtensionsServerlessBackfillWorkflow,
		icebergCompactionWorkflowService:            icebergCompactionWorkflowService,
		workflowRepo:                                workflowRepo,
	}
}

func (service *tenantExtensionServiceImpl) GetTenantExtensionsByTenantID(ctx context.Context, tenantID uint64) ([]model.TenantExtension, error) {
	return service.model.GetTenantExtensionsByTenantID(ctx, tenantID)
}

func (service *tenantExtensionServiceImpl) GetTenantExtensionByTenantIDAndResourceType(ctx context.Context, tenantID uint64, resourceType string) (model.TenantExtension, error) {
	return service.model.GetTenantExtensionByTenantIDAndResourceType(ctx, tenantID, resourceType)
}

func (service *tenantExtensionServiceImpl) CreateTenantExtension(ctx context.Context, tenantID TenantID, resourceType string, config string, version string, status string) error {
	return service.model.CreateTenantExtension(ctx, tenantID, resourceType, config, version, status)
}

func (service *tenantExtensionServiceImpl) UpdateTenantExtensionStatusByTenantIDAndResourceType(ctx context.Context, tenantID TenantID, resourceType string, status string) error {
	return service.model.UpdateTenantExtensionStatusByTenantIDAndResourceType(ctx, tenantID, resourceType, status)
}

func (service *tenantExtensionServiceImpl) CompareAndUpdateTenantExtensionStatusByTenantIDAndResourceTypeAndStatuses(ctx context.Context, tenantID TenantID, resourceType string, status string, oldStatuses []string) error {
	return service.model.CompareAndUpdateTenantExtensionStatusByTenantIDAndResourceTypeAndStatuses(ctx, tenantID, resourceType, status, oldStatuses)
}

// Compaction Extension

func (service *tenantExtensionServiceImpl) EnableExtensionsCompaction(ctx context.Context, tenantID TenantID) (uuid.UUID, error) {
	workflowID, err := service.enableExtensionsCompactionWorkflow.StartEnableWorkflow(ctx, tenantID)
	if err != nil {
		return uuid.UUID{}, err
	}

	logger.L().Named("Tenant").Info("Enable extensions compaction workflow",
		zap.String("request_id", ginx.ExtractRequestID(ctx)),
		zap.String("workflow_id", workflowID.String()),
		zap.Error(err),
	)

	return workflowID, nil
}

func (service *tenantExtensionServiceImpl) DisableExtensionsCompaction(ctx context.Context, tenantID TenantID) (uuid.UUID, error) {
	workflowID, err := service.disableExtensionsCompactionWorkflow.StartDisableWorkflow(ctx, tenantID, 1 /* TODO: make it configurable */)
	if err != nil {
		return uuid.UUID{}, err
	}

	logger.L().Named("Tenant").Info("Disable extensions compaction workflow",
		zap.String("request_id", ginx.ExtractRequestID(ctx)),
		zap.String("workflow_id", workflowID.String()),
		zap.Error(err),
	)

	return workflowID, nil
}

func (service *tenantExtensionServiceImpl) DisableExtensionsServerlessBackfill(ctx context.Context, tenantID TenantID) (uuid.UUID, error) {
	workflowID, err := service.disableExtensionsServerlessBackfillWorkflow.StartDisableExtensionsServerlessBackfillWorkflow(ctx, tenantID)
	if err != nil {
		return uuid.UUID{}, err
	}

	logger.L().Named("Tenant").Info("Disable extensions serverless backfill workflow",
		zap.String("request_id", ginx.ExtractRequestID(ctx)),
		zap.String("workflow_id", workflowID.String()),
		zap.Error(err),
	)

	return workflowID, nil
}

type UpdateTenantExtensionUpdateOption = tenantext.ServerlessCompactionParams

func (service *tenantExtensionServiceImpl) UpdateExtensionsCompaction(ctx context.Context, tenantID TenantID, params tenantext.ServerlessCompactionParams) (uuid.UUID, error) {
	workflowID, err := service.updateExtensionsCompactionWorkflow.StartUpdateWorkflow(ctx, tenantID, params)
	if err != nil {
		return uuid.UUID{}, err
	}

	logger.L().Named("Tenant").Info("Enable extensions compaction workflow",
		zap.String("request_id", ginx.ExtractRequestID(ctx)),
		zap.String("workflow_id", workflowID.String()),
		zap.Error(err),
	)

	return workflowID, nil
}

// Serverless Backfill Extension

func TenantNotFoundOrInternalErrorResponseErr(err error, _ uint64) error {
	if apierr.IsTenantNotExistError(err) {
		return eris.WithCode(err, eris.CodeNotFound)
	}
	if err != nil {
		return err
	}
	return nil
}

func CanNotEnableExtension(extension querier.TenantExtension) bool {
	return extension != model.TenantExtensionNil && extension.Status != model.ExtensionStatusDisabled && extension.Status != model.ExtensionStatusFailed
}

// ValidateExtensionServerlessBackfillCanDisable validates if the the extension can be disabled.
// Returns error with status code on failure, else nil.
func (service *tenantExtensionServiceImpl) ValidateExtensionServerlessBackfillCanDisable(c context.Context, tenantID uint64) error {
	tenant, err := service.model.GetTenantByID(c, tenantID)
	if err != nil {
		return TenantNotFoundOrInternalErrorResponseErr(err, tenantID)
	}

	if tenant.Resources.IsStandalone() {
		return eris.Errorf("extension serverless backfill isn't supported for standalone deployment").WithCode(eris.CodeFailedPrecondition)
	}

	runningWorkflow, err := service.workflowRepo.GetRunningWorkflowOfTenant(c, backfill.DisableWorkflowType, tenantID)
	if err != nil {
		return err
	}

	if runningWorkflow != nil {
		return eris.Errorf("Illegal status, already exist running workflow").WithCode(eris.CodeFailedPrecondition)
	}

	if tenant.Status != model.TenantStatusRunning {
		return eris.Errorf("Illegal status, tenant should be Running").WithCode(eris.CodeFailedPrecondition)
	}

	extension, err := service.GetTenantExtensionByTenantIDAndResourceType(c, tenantID, model.ExtensionsResourceTypeServerlessBackfill)
	if err != nil {
		return err
	}
	if extension.Status != model.ExtensionStatusRunning {
		return eris.Errorf("Illegal status, cannot disable extensions serverless backfill").WithCode(eris.CodeFailedPrecondition)
	}
	return nil
}

// ValidateExtensionServerlessBackfillCanPostVersion validates if the the extension can be updated.
// Returns error with status code on failure, else nil.
func (service *tenantExtensionServiceImpl) ValidateExtensionServerlessBackfillCanPostVersion(c context.Context, tenantID uint64) error {
	tenant, err := service.model.GetTenantByID(c, tenantID)
	if err != nil {
		return TenantNotFoundOrInternalErrorResponseErr(err, tenantID)
	}

	if tenant.Resources.IsStandalone() {
		return eris.Errorf("extension serverless compaction isn't supported for standalone deployment").WithCode(
			eris.CodeFailedPrecondition)
	}

	runningWorkflow, err := service.workflowRepo.GetRunningWorkflowOfTenant(c, backfill.UpdateExtensionsServerlessBackfillWorkflowType, tenantID)
	if err != nil {
		return err
	}

	if runningWorkflow != nil {
		return eris.Errorf("Illegal status, already exist running workflow").WithCode(eris.CodeFailedPrecondition)
	}

	if tenant.Status != model.TenantStatusRunning {
		return eris.Errorf("Illegal status, tenant should be Running").WithCode(eris.CodeFailedPrecondition)
	}

	// check extension status
	extension, err := service.GetTenantExtensionByTenantIDAndResourceType(c, tenantID, model.ExtensionsResourceTypeServerlessBackfill)
	// If extension not exist, we cannot update it
	if err != nil {
		return err
	}

	// check extension status whether can be updated
	if extension == model.TenantExtensionNil {
		return eris.Errorf("Illegal status: %s, cannot update extensions serverless backfill", extension.Status).WithCode(eris.CodeFailedPrecondition)
	}
	if extension != model.TenantExtensionNil && extension.Status != model.ExtensionStatusRunning {
		return eris.Errorf("Illegal status: %s, cannot update extensions serverless backfill", extension.Status).WithCode(eris.CodeFailedPrecondition)
	}
	return nil
}

// ValidateExtensionServerlessBackfillCanEnable validates if the the extension can be enabled.
// Returns error with status code on failure, else nil.
func (service *tenantExtensionServiceImpl) ValidateExtensionServerlessBackfillCanEnable(c context.Context, tenantID uint64) error {
	tenant, err := service.model.GetTenantByID(c, tenantID)
	if err != nil {
		return TenantNotFoundOrInternalErrorResponseErr(err, tenantID)
	}

	if tenant.Resources.IsStandalone() {
		return eris.Errorf("extension serverless backfill isn't supported for standalone deployment").WithCode(eris.CodeFailedPrecondition)
	}

	runningWorkflow, err := service.workflowRepo.GetRunningWorkflowOfTenant(c, backfill.EnableExtensionsServerlessBackfillWorkflowType, tenantID)
	if err != nil {
		return err
	}

	if runningWorkflow != nil {
		return eris.Errorf("Illegal status, already exist running workflow").WithCode(eris.CodeFailedPrecondition)
	}

	if tenant.Status != model.TenantStatusRunning {
		return eris.Errorf("Illegal status, tenant should be Running").WithCode(eris.CodeFailedPrecondition)
	}

	// check extension status
	extension, err := service.GetTenantExtensionByTenantIDAndResourceType(c, tenantID, model.ExtensionsResourceTypeServerlessBackfill)

	// If extension not exist, will create it in enable workflow
	if err != nil && !eris.Is(err, apierr.ErrTenantExtensionNotExist) {
		return err
	}

	// check extension status whether can enable
	if err == nil && CanNotEnableExtension(extension) {
		return eris.Errorf("Illegal status: %s, cannot enable extensions serverless backfill", extension.Status).WithCode(eris.CodeFailedPrecondition)
	}
	return nil
}

func (service *tenantExtensionServiceImpl) UpdateExtensionsServerlessBackfill(ctx context.Context, tenantID TenantID, version string) (uuid.UUID, error) {
	workflowID, err := service.updateExtensionsServerlessBackfillWorkflow.StartUpdateExtensionsServerlessBackfillWorkflow(ctx, tenantID, version)

	if err != nil {
		return uuid.UUID{}, err
	}

	logger.L().Named("Tenant").Info("Enable extensions compaction workflow",

		zap.String("request_id", ginx.ExtractRequestID(ctx)),
		zap.String("workflow_id", workflowID.String()),
		zap.Error(err),
	)

	return workflowID, nil
}

func (service *tenantExtensionServiceImpl) EnableExtensionsServerlessBackfill(ctx context.Context, tenantID TenantID) (uuid.UUID, error) {
	workflowID, err := service.enableExtensionsServerlessBackfillWorkflow.StartEnableExtensionsServerlessBackfillWorkflow(ctx, tenantID)
	if err != nil {
		return uuid.UUID{}, err
	}

	logger.L().Named("Tenant").Info("Enable extensions serverless backfill workflow",
		zap.String("request_id", ginx.ExtractRequestID(ctx)),
		zap.String("workflow_id", workflowID.String()),
		zap.Error(err),
	)
	return workflowID, nil
}

func (service *tenantExtensionServiceImpl) GetSpecTenantExtensionsByTenantID(ctx context.Context, tenantID uint64) (*spec.TenantExtensions, error) {
	extensions, err := service.GetTenantExtensionsByTenantID(ctx, tenantID)
	if err != nil {
		return nil, err
	}

	// Helper function to parse serverless compaction extension.
	parseServerlessCompaction := func(ext model.TenantExtension) (*spec.TenantExtensionServerlessCompaction, error) {
		if ext.ResourceType != model.ExtensionsResourceTypeCompaction {
			return nil, nil
		}

		cfg := config.NewRisingWaveExtensionsCompactionWithRWConfig()
		err := cfg.UnmarshalJSON([]byte(ptr.Deref(ext.Config, "")))
		if err != nil {
			return nil, eris.Wrapf(err, "cannot unmarshal rising wave extension config")
		}

		cpuQuantity, err := resource.ParseQuantity(cfg.Compactor.CPULimit)
		if err != nil {
			return nil, eris.Wrapf(err, "failed to parse cpu limit")
		}

		return &spec.TenantExtensionServerlessCompaction{
			Enabled:                      model.IsExtensionEnabled(ext.Status),
			MaximumCompactionConcurrency: int(cfg.Scaler.MaxReplicas * cpuQuantity.MilliValue() / 1000),
		}, nil
	}

	tenantExtensions := &spec.TenantExtensions{}

	for _, ext := range extensions {
		switch ext.ResourceType { // nolint:gocritic
		case model.ExtensionsResourceTypeCompaction:
			serverlessCompaction, err := parseServerlessCompaction(ext)
			if err != nil {
				return nil, eris.Wrapf(err, "failed to parse serverless compaction")
			}
			tenantExtensions.ServerlessCompaction = serverlessCompaction
		}
	}

	return tenantExtensions, nil
}

func (service *tenantExtensionServiceImpl) EnableExtensionIcebergCompaction(ctx context.Context, tenantID TenantID, opts *IcebergCompactionOptions) (uuid.UUID, error) {
	var cc *iceberg.CompactionConfig

	if opts != nil {
		if opts.Replicas <= 0 {
			return uuid.UUID{}, eris.Errorf("replicas must be greater than 0").WithCode(eris.CodeInvalidArgument)
		}
		rd := config.GetResourceDef()
		componentType := rd.GetComponentTypeByID(opts.ComponentTypeID)
		if componentType == nil {
			return uuid.UUID{}, eris.Errorf("component type %s not found", opts.ComponentTypeID).WithCode(eris.CodeInvalidArgument)
		}

		cc = &iceberg.CompactionConfig{
			Replicas:        int32(opts.Replicas),
			ComponentTypeID: opts.ComponentTypeID,
			Resources: corev1.ResourceRequirements{
				Limits: corev1.ResourceList{
					corev1.ResourceCPU:    componentType.CPULimit,
					corev1.ResourceMemory: componentType.MemoryLimit,
				},
				Requests: corev1.ResourceList{
					corev1.ResourceCPU:    componentType.CPURequest,
					corev1.ResourceMemory: componentType.MemoryRequest,
				},
			},
			Config: opts.Config,
		}
	}

	return service.icebergCompactionWorkflowService.StartEnableWorkflow(ctx, tenantID, cc)
}

func (service *tenantExtensionServiceImpl) DisableExtensionIcebergCompaction(ctx context.Context, tenantID TenantID) (uuid.UUID, error) {
	return service.icebergCompactionWorkflowService.StartDisableWorkflow(ctx, tenantID)
}

func (service *tenantExtensionServiceImpl) UpdateExtensionIcebergCompaction(ctx context.Context, tenantID TenantID, opts IcebergCompactionOptions) (uuid.UUID, error) {
	if opts.Replicas < 0 {
		return uuid.UUID{}, eris.Errorf("replicas must be greater than or equal to 0").WithCode(eris.CodeInvalidArgument)
	}

	rd := config.GetResourceDef()
	componentType := rd.GetComponentTypeByID(opts.ComponentTypeID)
	if componentType == nil {
		return uuid.UUID{}, eris.Errorf("component type %s not found", opts.ComponentTypeID).WithCode(eris.CodeInvalidArgument)
	}

	return service.icebergCompactionWorkflowService.StartUpdateWorkflow(ctx, tenantID, iceberg.CompactionConfig{
		Replicas:        int32(opts.Replicas),
		ComponentTypeID: opts.ComponentTypeID,
		Resources: corev1.ResourceRequirements{
			Limits: corev1.ResourceList{
				corev1.ResourceCPU:    componentType.CPULimit,
				corev1.ResourceMemory: componentType.MemoryLimit,
			},
			Requests: corev1.ResourceList{
				corev1.ResourceCPU:    componentType.CPURequest,
				corev1.ResourceMemory: componentType.MemoryRequest,
			},
		},
		Config: opts.Config,
	})
}

func (service *tenantExtensionServiceImpl) GetExtensionIcebergCompaction(ctx context.Context, tenantID uint64) (spec.IcebergCompaction, error) {
	tenantExt, err := service.GetTenantExtensionByTenantIDAndResourceType(ctx, tenantID, model.ExtensionsResourceTypeIcebergCompaction)
	if err != nil {
		if eris.Is(err, apierr.ErrTenantExtensionNotExist) {
			return spec.IcebergCompaction{}, eris.WithCode(err, eris.CodeNotFound)
		}
		return spec.IcebergCompaction{}, err
	}

	// If the extension is not enabled, return empty config.
	if tenantExt.Config == nil {
		return spec.IcebergCompaction{
			Status: tenantExt.Status,
		}, nil
	}

	var cc iceberg.CompactionConfig
	if err := cc.FromJSON(ptr.Deref(tenantExt.Config, "")); err != nil {
		return spec.IcebergCompaction{}, eris.Wrapf(err, "failed to parse iceberg compaction config")
	}

	cpuLimit := cc.Resources.Limits[corev1.ResourceCPU]
	memoryLimit := cc.Resources.Limits[corev1.ResourceMemory]
	return spec.IcebergCompaction{
		Config: ptr.To(cc.Config),
		Resources: &spec.ComponentResource{
			ComponentTypeId: cc.ComponentTypeID,
			Cpu:             cpuLimit.String(),
			Memory:          memoryLimit.String(),
			Replica:         int(cc.Replicas),
		},
		Status: tenantExt.Status,
	}, nil
}
