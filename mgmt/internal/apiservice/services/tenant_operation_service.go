package services

import (
	"context"

	"github.com/google/uuid"
	"github.com/risingwavelabs/eris"
	"go.uber.org/zap"

	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/rwconfig"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/config"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/configset"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/version"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/configupdate"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/etcd"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/imagetag"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/stopstart"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/update"
	"github.com/risingwavelabs/risingwave-cloud/shared/ginx"
	"github.com/risingwavelabs/risingwave-cloud/shared/logger"
	"github.com/risingwavelabs/risingwave-cloud/shared/ptr"
)

type TenantOperationService interface {
	StartInstance(ctx context.Context, tenantID TenantID) error
	StopInstance(ctx context.Context, tenantID TenantID) error
	RestartInstance(ctx context.Context, tenantID TenantID) error
	UpdateConfig(ctx context.Context, tenant model.Tenant, rwConfigRaw string, component *string, nodeGroup *string, noRestart bool) (uuid.UUID, error)
	UpdateEtcdConfig(ctx context.Context, tenantID TenantID, etcdEnv map[string]string) (uuid.UUID, error)
	UpdateRwImageTag(ctx context.Context, tenantID TenantID, imageTag string, previousImageTag string, skipMetaBackup bool) (uuid.UUID, error)
	UpdateTenantResources(ctx context.Context, tenantID uint64, request update.TenantResourcesRequest, allowsTrial bool) (uuid.UUID, error)
}

type TenantOperationServiceImpl struct {
	configSetRepository      configset.Repository
	stopstartWorkflow        *stopstart.StopStartTenantWorkflowService
	configWorkflow           configupdate.UpdateWorkflowService
	imageTagWorkflow         *imagetag.ImageTagUpdateWorkflowService
	updateEtcdConfigWorkflow *etcd.ConfigUpdateWorkflowService
	updateResourcesWorkflow  *update.UpdateTenantResourcesService
}

func NewTenantOperationService(
	configSetRepository configset.Repository,
	stopstartWorkflow *stopstart.StopStartTenantWorkflowService,
	configWorkflow configupdate.UpdateWorkflowService,
	imageTagWorkflow *imagetag.ImageTagUpdateWorkflowService,
	updateEtcdConfigWorkflow *etcd.ConfigUpdateWorkflowService,
	updateResourcesWorkflow *update.UpdateTenantResourcesService,
) TenantOperationService {
	return &TenantOperationServiceImpl{
		configSetRepository:      configSetRepository,
		stopstartWorkflow:        stopstartWorkflow,
		configWorkflow:           configWorkflow,
		imageTagWorkflow:         imageTagWorkflow,
		updateEtcdConfigWorkflow: updateEtcdConfigWorkflow,
		updateResourcesWorkflow:  updateResourcesWorkflow,
	}
}

func (service *TenantOperationServiceImpl) StartInstance(ctx context.Context, tenantID TenantID) error {
	workflowID, err := service.stopstartWorkflow.StartTenantWithWorkflow(ctx, tenantID)
	if err != nil {
		return err
	}

	logger.L().Named("Tenant").Info("Start Tenant Workflow",
		zap.String("request_id", ginx.ExtractRequestID(ctx)),
		zap.String("workflow_id", workflowID.String()),
		zap.Error(err),
	)

	return nil
}

func (service *TenantOperationServiceImpl) StopInstance(ctx context.Context, tenantID TenantID) error {
	workflowID, err := service.stopstartWorkflow.StopTenantWithWorkflow(ctx, tenantID)
	if err != nil {
		return err
	}

	logger.L().Named("Tenant").Info("Stop Tenant Workflow",
		zap.String("request_id", ginx.ExtractRequestID(ctx)),
		zap.String("workflow_id", workflowID.String()),
		zap.Error(err),
	)

	return nil
}

func (service *TenantOperationServiceImpl) RestartInstance(ctx context.Context, tenantID TenantID) error {
	workflowID, err := service.stopstartWorkflow.RestartTenantWithWorkflow(ctx, tenantID)
	if err != nil {
		return err
	}

	logger.L().Named("Tenant").Info("Start Tenant Workflow",
		zap.String("request_id", ginx.ExtractRequestID(ctx)),
		zap.String("workflow_id", workflowID.String()),
		zap.Error(err),
	)

	return nil
}

func (service *TenantOperationServiceImpl) UpdateConfig(ctx context.Context, tenant model.Tenant, rwConfigRaw string, component *string, nodeGroup *string, noRestart bool) (uuid.UUID, error) {
	if (ptr.Unwrap(component) == "default" && ptr.Unwrap(nodeGroup) == "default") ||
		(ptr.Unwrap(component) == "" && ptr.Unwrap(nodeGroup) == "") {
		component = nil
		nodeGroup = nil
	}
	_, err := config.RawToMap(rwConfigRaw)
	if err != nil {
		return uuid.UUID{}, eris.WithCode(eris.Wrapf(err, "invalid toml file"), eris.CodeInvalidArgument)
	}

	set, err := service.configSetRepository.GetConfigSet(ctx, tenant.NsID)
	if err != nil {
		return uuid.UUID{}, err
	}

	err = validateConfigPatch(tenant, set, rwConfigRaw, component, nodeGroup, noRestart)
	if err != nil {
		return uuid.UUID{}, err
	}
	configPatch := configupdate.ConfigPatch{
		RawConfig: rwConfigRaw,
		Component: component,
		NodeGroup: nodeGroup,
	}

	workflowID, err := service.configWorkflow.UpdateConfigWithWorkflow(ctx, tenant.ID, configPatch, noRestart)
	if err != nil {
		return uuid.UUID{}, err
	}

	logger.L().Named("Tenant").Info("Update config workflow",
		zap.String("request_id", ginx.ExtractRequestID(ctx)),
		zap.String("workflow_id", workflowID.String()),
		zap.Error(err),
	)

	return workflowID, nil
}

func validateConfigPatch(tenant model.Tenant, set *configset.ConfigSet, raw string, component *string, nodeGroup *string, noRestart bool) error {
	// update default config
	if component == nil && nodeGroup == nil {
		return set.UpdateAllConfigs(raw)
	}
	// update config for a node group
	if component == nil || nodeGroup == nil {
		return eris.New("component or nodeGroup cannot be nil").WithCode(eris.CodeInvalidArgument)
	}

	if tenant.Resources.IsStandalone() {
		return eris.New("don't support independent config for standalone").WithCode(eris.CodeFailedPrecondition)
	}
	_, err := rwconfig.GetNodeGroup(tenant, *component, *nodeGroup)
	if err != nil && eris.GetCode(err) == eris.CodeNotFound {
		return eris.WithCode(eris.Wrap(err, "invalid node group"), eris.CodeInvalidArgument)
	}
	if err != nil {
		return err
	}

	if set.IsLegacy && noRestart {
		return eris.New("don't support convert to independent config without restart").WithCode(eris.CodeFailedPrecondition)
	}

	set.IsLegacy = false
	return set.UpdateConfigs([]configset.ConfigKey{{
		Component: *component,
		NodeGroup: *nodeGroup,
	}}, raw)
}

func (service *TenantOperationServiceImpl) UpdateEtcdConfig(ctx context.Context, tenantID TenantID, etcdEnv map[string]string) (uuid.UUID, error) {
	workflowID, err := service.updateEtcdConfigWorkflow.UpdateEtcdConfigWithWorkflow(ctx, tenantID, etcdEnv)
	if err != nil {
		return uuid.UUID{}, err
	}

	logger.L().Named("Tenant").Info("Update Etcd config workflow",
		zap.String("request_id", ginx.ExtractRequestID(ctx)),
		zap.String("workflow_id", workflowID.String()),
		zap.Error(err),
	)

	return workflowID, nil
}

func (service *TenantOperationServiceImpl) UpdateRwImageTag(ctx context.Context, tenantID TenantID, imageTag string, previousImageTag string, skipMetaBackup bool) (uuid.UUID, error) {
	if err := version.ValidateCloudImageTag(imageTag); err != nil {
		return uuid.UUID{}, eris.WithCode(eris.Wrapf(err, "invalid image tag %v", imageTag), eris.CodeInvalidArgument)
	}
	// need to create a update image tag workflow
	workflowID, err := service.imageTagWorkflow.UpdateImageTagWithWorkflow(ctx, tenantID, imageTag, skipMetaBackup)
	if err != nil {
		return uuid.UUID{}, err
	}

	logger.L().Named("Tenant").Info("Update Rw image tag workflow",
		zap.String("request_id", ginx.ExtractRequestID(ctx)),
		zap.String("workflow_id", workflowID.String()),
		zap.String("image_before", previousImageTag),
		zap.String("image_after", imageTag),
		zap.Error(err),
	)

	return workflowID, nil
}

func (service *TenantOperationServiceImpl) UpdateTenantResources(ctx context.Context, tenantID uint64, request update.TenantResourcesRequest, allowsTrial bool) (uuid.UUID, error) {
	workflowID, err := service.updateResourcesWorkflow.Submit(ctx, tenantID, request, allowsTrial)
	if err != nil {
		return uuid.UUID{}, err
	}

	logger.L().Named("Tenant").Info("Run UpdateTenantResourcesWorkflow.",
		zap.String("request_id", ginx.ExtractRequestID(ctx)),
		zap.String("workflow_id", workflowID.String()),
		zap.Error(err),
	)

	return workflowID, nil
}
