package services

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/risingwavelabs/eris"
	"go.uber.org/zap"
	"k8s.io/apimachinery/pkg/api/resource"

	agentprovider "github.com/risingwavelabs/risingwave-cloud/internal/agent/provider"
	"github.com/risingwavelabs/risingwave-cloud/internal/apiservice/dto"
	"github.com/risingwavelabs/risingwave-cloud/internal/config"
	k8sutils "github.com/risingwavelabs/risingwave-cloud/internal/k8s"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/extensions"
	"github.com/risingwavelabs/risingwave-cloud/internal/metrics"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/modeltx"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/client"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/rwdb"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/rwsecret"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/version"
	"github.com/risingwavelabs/risingwave-cloud/internal/rwproxy"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenant/component"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenant/metastore"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenant/resourcespec"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/computecache"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/delete"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/metabackup"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/provision"
	"github.com/risingwavelabs/risingwave-cloud/shared/ginx"
	"github.com/risingwavelabs/risingwave-cloud/shared/logger"
	"github.com/risingwavelabs/risingwave-cloud/shared/namespace"
	"github.com/risingwavelabs/risingwave-cloud/shared/paging"
	spec "github.com/risingwavelabs/risingwave-cloud/shared/spec/mgmt_spec_v2"
)

type TenantID = uint64
type OrgID = uuid.UUID
type NsID = uuid.UUID

type TenantServiceInterface interface {
	PingTenant(ctx context.Context, tenantID TenantID, imageTag string) error
	PingTenantWithDBUser(ctx context.Context, tenantID TenantID, imageTag, username, password, database string) error
	ProvisionTenant(ctx context.Context, option ProvisionTenantOption) (TenantID, error)
	RestoreTenant(ctx context.Context, oldTenant model.Tenant, newTenantName string, snapshotID uuid.UUID, oldTenantExtSpec *spec.TenantExtensions) (*dto.Tenant, error)
	DeleteTenantByID(ctx context.Context, tenantID TenantID) (uuid.UUID, error)
	GetTenantByID(ctx context.Context, tenantID uint64) (model.Tenant, error)
	GetTenantByNsID(ctx context.Context, nsID uuid.UUID) (model.Tenant, error)
	GetTenantsByOrgID(ctx context.Context, orgID OrgID, _offset *uint64, _limit *uint64) (uint64, uint64, uint64, []*model.Tenant, error)
	GetTenants(ctx context.Context, _offset *uint64, _limit *uint64) (paging.Page, []*model.Tenant, error)
	GetRootCredentials(ctx context.Context) client.RisingWaveCredential
	GetEndpointByTenant(ctx context.Context, tenant *model.Tenant) (rwproxy.TenantEndpoint, error)
	GetEndpointByTenantID(ctx context.Context, tenantID TenantID) (rwproxy.TenantEndpoint, error)
	GetEndpointByTenantName(ctx context.Context, orgID OrgID, tenantName string) (rwproxy.TenantEndpoint, error)
	GetRwDatabases(ctx context.Context, tenantID TenantID) ([]rwdb.Database, error)
	GetRwDatabasesPaginated(ctx context.Context, tenantID TenantID, _offset *uint64, _limit *uint64) (uint64, uint64, uint64, []rwdb.Database, error)
	GetTenantStatusCount(ctx context.Context, orgID OrgID) ([]dto.TenantStatusCount, error)
	GetTenantClusterInfo(ctx context.Context, tenantID uint64) (string, error)
	GetAllTiers() ([]dto.Tier, error)
	TenantToSpec(ctx context.Context, tenant model.Tenant) (dto.Tenant, error)
	GetOrgTenantByID(ctx context.Context, orgID OrgID, tenantID TenantID) (model.Tenant, error)
	GetOrgTenantByNsID(ctx context.Context, orgID OrgID, nsID uuid.UUID) (model.Tenant, error)
	GetOrgTenantByName(ctx context.Context, orgID OrgID, tenantName string) (model.Tenant, error)
	GetTenantsCountByOrgID(ctx context.Context, orgID OrgID) (uint64, error)
	OrgTenantIDExists(ctx context.Context, orgID OrgID, tenantID TenantID) (bool, error)
	OrgTenantNameExists(ctx context.Context, orgID OrgID, tenantName string) (bool, error)
	UpdateComputeCache(ctx context.Context, tenantID uint64, computeCacheSpec *resourcespec.ComputeCacheSpec, resourceGroupComputeCacheSpecs []computecache.ResourceGroupComputeCacheSpec) (uuid.UUID, error)
	SetTenantSecretStoreDek(ctx context.Context, tenantID uint64) error
	GetUpcomingSnapshotTime(ctx context.Context, tenantID uint64) (*time.Time, error)
}

var _ TenantServiceInterface = (*TenantService)(nil)

type TenantService struct {
	model        *model.Model
	txProvider   modeltx.TxProvider
	workflowRepo *workflow.Repository

	agentProvider              agentprovider.CloudAgentProviderInterface
	provisionWorkflow          provision.IProvisionTenantWorkflowService
	deleteWorkflow             *delete.DeleteTenantWorkflowService
	updateComputeCacheWorkflow *computecache.UpdateComputeCacheWorkflowService
}

func NewTenantService(
	model *model.Model,
	txProvider modeltx.TxProvider,
	workflowRepo *workflow.Repository,

	agentProvider agentprovider.CloudAgentProviderInterface,
	provisionWorkflow provision.IProvisionTenantWorkflowService,
	deleteWorkflow *delete.DeleteTenantWorkflowService,
	updateComputeCacheWorkflow *computecache.UpdateComputeCacheWorkflowService,
) TenantServiceInterface {
	return &TenantService{
		agentProvider:              agentProvider,
		deleteWorkflow:             deleteWorkflow,
		model:                      model,
		provisionWorkflow:          provisionWorkflow,
		updateComputeCacheWorkflow: updateComputeCacheWorkflow,
		txProvider:                 txProvider,
		workflowRepo:               workflowRepo,
	}
}
func (service *TenantService) PingTenant(ctx context.Context, tenantID TenantID, imageTag string) error {
	rwClient, err := client.ObtainRisingWaveClient(ctx, service.model, tenantID, client.DefaultDb, client.RootCredential)
	if err != nil {
		return eris.Wrap(err, "failed to obtain risingwave client")
	}
	err = rwClient.Ping(ctx, imageTag)
	if err != nil {
		return eris.Wrap(err, "failed to ping cluster")
	}
	return nil
}

func (service *TenantService) PingTenantWithDBUser(ctx context.Context, tenantID TenantID, imageTag, username, password, database string) error {
	rwClient, err := client.ObtainRisingWaveClient(ctx, service.model, tenantID, database, client.RisingWaveCredential{
		Username: username,
		Password: password,
	})
	if err != nil {
		return eris.Wrap(err, "failed to obtain risingwave client")
	}
	err = rwClient.Ping(ctx, imageTag)
	if err != nil {
		return eris.Wrap(err, "failed to ping cluster")
	}
	return nil
}

type ProvisionTenantOption struct {
	OrgID                uuid.UUID
	TenantName           string
	TierID               string
	ImageTag             string
	Region               string
	ClusterID            uint64
	RwConfig             string
	EtcdConfig           string
	Resource             resourcespec.TenantResourceSpec
	AsTrial              bool
	SKU                  string
	ServerlessCompaction *provision.ServerlessCompactionOptions
	Restore              *provision.RestoreOptions
	UsageType            string
}

func (service *TenantService) ProvisionTenant(ctx context.Context, option ProvisionTenantOption) (TenantID, error) {
	if err := version.ValidateCloudImageTag(option.ImageTag); err != nil {
		return 0, eris.WithCode(eris.Wrapf(err, "invalid image tag %v", option.ImageTag), eris.CodeInvalidArgument)
	}

	rc := config.GetResourceDef()
	tier := rc.GetTierByID(option.TierID)
	if tier == nil {
		return 0, eris.Errorf("no such tier id %s", option.TierID)
	}

	var encryptedSecretStorePrivateKey string
	if rwsecret.IsRisingwaveSecretStoreEnabled() {
		secretStorePrivateKey, err := rwsecret.GenerateRandomSecretStorePrivateKey()
		if err != nil {
			return 0, eris.Wrap(err, "failed to generate private key for secret store")
		}
		encryptedSecretStorePrivateKey, err = rwsecret.EncryptSecretStorePrivateKey(secretStorePrivateKey)
		if err != nil {
			return 0, eris.Wrap(err, "failed to encrypt private key for secret store")
		}
	}

	nsID := namespace.NewUUIDv7()

	// Create tenant record and run workflow
	tenantID, workflowID, err := service.provisionWorkflow.StartProvisionTenant(ctx,
		model.Tenant{
			OrgID:             option.OrgID,
			TenantName:        option.TenantName,
			ResourceNamespace: namespace.Build(nsID, option.TenantName),
			NsID:              nsID,
			Region:            option.Region,
			ImageTag:          option.ImageTag,
			RwConfig:          option.RwConfig,
			EtcdConfig:        option.EtcdConfig,
			ClusterID:         option.ClusterID,
			// resource
			TierID:                       tier.ID,
			RetentionDays:                tier.DefaultRetentionPeriod,
			ValidityDays:                 tier.DefaultValidityPeriod,
			Resources:                    option.Resource,
			SecretStoreDataEncryptionKey: encryptedSecretStorePrivateKey,
			SKU:                          option.SKU,
			UsageType:                    option.UsageType,
		},
		// Options, including trial, serverless compaction
		provision.WithAsTrial(option.AsTrial),
		provision.WithServerlessCompaction(option.ServerlessCompaction),
		provision.AsRestoration(option.Restore))
	if err != nil {
		return 0, eris.Wrap(err, "failed to create provision workflow")
	}

	metrics.TenantCreatedTotal.With(metrics.Labels{}).Inc()
	logger.L().Named("Tenant Service").Info("Run ProvisionTenantWorkflow.",
		zap.String("request_id", ginx.ExtractRequestID(ctx)),
		zap.Uint64("tenant_id", tenantID),
		zap.String("tenant_name", option.TenantName),
		zap.String("org_id", option.OrgID.String()),
		zap.String("tier", option.TierID),
		zap.String("image_tag", option.ImageTag),
		zap.String("workflow_id", workflowID.String()),
	)

	return tenantID, nil
}

func (service *TenantService) DeleteTenantByID(ctx context.Context, tenantID TenantID) (uuid.UUID, error) {
	workflowID, err := service.deleteWorkflow.DeleteTenantWithWorkflow(ctx, tenantID)
	if err != nil {
		return uuid.UUID{}, err
	}

	logger.L().Named("Tenant").Info("Run DeleteTenantWorkflow.",
		zap.String("request_id", ginx.ExtractRequestID(ctx)),
		zap.String("workflow_id", workflowID.String()),
		zap.Error(err),
	)

	return workflowID, nil
}

func (service *TenantService) GetTenantByID(ctx context.Context, tenantID uint64) (model.Tenant, error) {
	return service.model.GetTenantByID(ctx, tenantID)
}

func (service *TenantService) GetTenantByNsID(ctx context.Context, nsID uuid.UUID) (model.Tenant, error) {
	return service.model.GetTenantByNsID(ctx, nsID)
}

func (service *TenantService) GetTenantsByOrgID(ctx context.Context, orgID OrgID, _offset *uint64, _limit *uint64) (uint64, uint64, uint64, []*model.Tenant, error) {
	page := paging.HTTPPager.Paging(_offset, _limit)

	size, err := service.model.GetTenantsCountByOrgID(ctx, orgID)
	if err != nil {
		return 0, 0, 0, nil, err
	}

	tenantList, err := service.model.GetTenantsByOrgID(ctx, orgID, page.Offset, page.Limit)
	if err != nil {
		return 0, 0, 0, nil, err
	}

	return page.Limit, page.Offset, size, tenantList, err
}

// GetTenants retrieves all tenants with pagination options.
func (service *TenantService) GetTenants(ctx context.Context, _offset *uint64, _limit *uint64) (paging.Page, []*model.Tenant, error) {
	page := paging.HTTPPager.Paging(_offset, _limit)
	tenants, err := service.model.GetTenants(ctx, page.Offset, page.Limit)
	if err != nil {
		return page, nil, err
	}

	return page, tenants, nil
}

func (service *TenantService) GetEndpointByTenant(ctx context.Context, tenant *model.Tenant) (rwproxy.TenantEndpoint, error) {
	return rwproxy.GetTenantEndpoint(ctx, service.model, tenant)
}

func (service *TenantService) GetEndpointByTenantID(ctx context.Context, tenantID TenantID) (rwproxy.TenantEndpoint, error) {
	return rwproxy.GetTenantEndpointByTenantID(ctx, service.model, tenantID)
}

func (service *TenantService) GetRootCredentials(context.Context) client.RisingWaveCredential {
	rootCredential := client.RisingWaveCredential{
		Username: client.RootCredential.Username,
		Password: config.Conf.Mgmt.RisingWave.Password,
	}
	return rootCredential
}

func (service *TenantService) GetEndpointByTenantName(ctx context.Context, orgID OrgID, tenantName string) (rwproxy.TenantEndpoint, error) {
	return rwproxy.GetTenantEndpointByTenantName(ctx, service.model, orgID, tenantName)
}

func (service *TenantService) GetRwDatabases(ctx context.Context, tenantID TenantID) ([]rwdb.Database, error) {
	databases, err := service.model.GetRwDatabases(ctx, tenantID)
	if err != nil {
		return nil, err
	}
	ret := make([]rwdb.Database, len(databases))

	for i := range databases {
		ret[i].Name = databases[i].Name
		ret[i].ResourceGroup = databases[i].ResourceGroup
	}

	return ret, nil
}

func (service *TenantService) GetRwDatabasesPaginated(ctx context.Context, tenantID TenantID, _offset *uint64, _limit *uint64) (uint64, uint64, uint64, []rwdb.Database, error) {
	page := paging.HTTPPager.Paging(_offset, _limit)

	size, err := service.model.GetRwDatabasesCount(ctx, tenantID)
	if err != nil {
		return 0, 0, 0, nil, err
	}

	databaseList, err := service.model.GetRwDatabasesPaginated(ctx, tenantID, page.Offset, page.Limit)
	if err != nil {
		return 0, 0, 0, nil, err
	}
	ret := make([]rwdb.Database, len(databaseList))
	for i := range databaseList {
		ret[i].Name = databaseList[i].Name
		ret[i].ResourceGroup = databaseList[i].ResourceGroup
	}

	return page.Limit, page.Offset, size, ret, err
}

func (service *TenantService) GetTenantStatusCount(ctx context.Context, orgID OrgID) ([]dto.TenantStatusCount, error) {
	tenantModel := service.model

	statusCounts, err := tenantModel.GetTenantStatusCount(ctx, orgID)
	if err != nil {
		return nil, err
	}
	if len(statusCounts) == 0 {
		return nil, nil
	}

	skuCounts, err := tenantModel.GetTenantSkuCount(ctx, orgID)
	if err != nil {
		return nil, err
	}
	if len(skuCounts) == 0 {
		return nil, nil
	}

	statusCopy := make([]dto.TenantStatusCount, len(statusCounts))
	for i := range statusCounts {
		statusCopy[i] = dto.TenantStatusCount{
			Count:  statusCounts[i].Count,
			Status: statusCounts[i].Status,
		}
	}

	return statusCopy, nil
}

func (service *TenantService) GetTenantClusterInfo(ctx context.Context, tenantID uint64) (string, error) {
	if service == nil {
		return "", eris.New("service is nil")
	}

	tenant, err := service.model.GetTenantByID(ctx, tenantID)
	if err != nil {
		return "", err
	}

	agent, err := service.agentProvider.GetAgent(ctx, tenant.ClusterID)
	if err != nil {
		return "", err
	}
	defer agent.Close()

	return agent.GetRisingWaveClusterInfo(ctx, k8sutils.DefaultRisingWaveName, tenant.ResourceNamespace)
}

func beautifyDigit(p float32) string {
	if p-float32(int(p)) < 0.05 { // if it has no point, do not show the point
		return fmt.Sprintf("%d", int(p))
	}
	return fmt.Sprintf("%.1f", p)
}

/* prase CPU resource quantity format to human readable format. */
func parseCPU(cpu string) (string, error) {
	var v float32
	if strings.Contains(cpu, "m") {
		n, err := strconv.Atoi(strings.TrimRight(cpu, "m"))
		if err != nil {
			return "", eris.Wrapf(err, "failed to parse %s", cpu)
		}
		v = float32(n)
	} else {
		n, err := strconv.Atoi(cpu)
		if err != nil {
			return "", eris.Wrapf(err, "failed to parse %s", cpu)
		}
		v = float32(n * 1000)
	}
	// round to 0.1. for example 3055m is 3 cores; 501m is 0.5 cores
	p := float32(int(v/100)*100) / 1000
	return beautifyDigit(p), nil
}

/* prase Memory resource quantity format to human readable format. */
func parseMemory(mem string) (string, error) {
	memQ, err := resource.ParseQuantity(mem)
	if err != nil {
		return "", eris.Wrapf(err, "failed to parse memory quanity %s", mem)
	}
	memBytes, ok := memQ.AsInt64()
	if !ok {
		return "", eris.Errorf("failed to get bytes of memory quantity %s", mem)
	}
	mb := memBytes / 1024 / 1024
	if mb >= 1024 { // larger than 1 Gigabytes
		return fmt.Sprintf("%s GB", beautifyDigit(float32(mb)/1024)), nil
	}
	return fmt.Sprintf("%d MB", mb), nil
}

func convertComponentTypesToSpecComponentTypes(arr []*config.AvailableComponentType) ([]dto.AvailableComponentType, error) {
	rtn := []dto.AvailableComponentType{}
	for _, item := range arr {
		c := item.ComponentType
		cpu, err := parseCPU(c.CPULimit.String())
		if err != nil {
			return nil, eris.Wrapf(err, "failed to parse %s", c.CPULimit.String())
		}
		mem, err := parseMemory(c.MemoryLimit.String())
		if err != nil {
			return nil, eris.Wrapf(err, "failed to parse %s", c.MemoryLimit.String())
		}
		rtn = append(rtn, dto.AvailableComponentType{
			ID:      c.ID,
			CPU:     cpu,
			Memory:  mem,
			Maximum: int(item.MaximumReplica),
		})
	}
	return rtn, nil
}

func convertMetaStoreToSpecMetaStore(metaStore *config.AvailableMetaStore) (*dto.AvailableMetaStore, error) {
	if metaStore == nil {
		return nil, nil
	}
	rtn := &dto.AvailableMetaStore{}
	if metaStore.Etcd != nil {
		nodes, err := convertComponentTypesToSpecComponentTypes(metaStore.Etcd.TypeList)
		if err != nil {
			return nil, err
		}
		rtn.Etcd = &dto.AvailableMetaStoreEtcd{
			Nodes:          nodes,
			MaximumSizeGiB: int(metaStore.Etcd.MaximumSizeGB),
		}
	}
	if metaStore.PostgreSQL != nil {
		nodes, err := convertComponentTypesToSpecComponentTypes(metaStore.PostgreSQL.TypeList)
		if err != nil {
			return nil, err
		}
		rtn.PostgreSQL = &dto.AvailableMetaStorePostgreSQL{
			Nodes:          nodes,
			MaximumSizeGiB: int(metaStore.PostgreSQL.MaximumSizeGB),
		}
	}
	if metaStore.AwsRds != nil {
		rtn.AwsRds = &dto.AvailableMetaStoreAwsRds{
			Enabled: true,
		}
	}
	if metaStore.GcpCloudSQL != nil {
		rtn.GcpCloudSQL = &dto.AvailableMetaStoreGcpCloudSQL{
			Enabled: true,
		}
	}
	if metaStore.AzrPostgres != nil {
		rtn.AzrPostgres = &dto.AvailableMetaStoreAzrPostgres{
			Enabled: true,
		}
	}
	if metaStore.SharingPg != nil {
		rtn.SharingPg = &dto.AvailableMetaStoreSharingPg{
			Enabled: true,
		}
	}
	return rtn, nil
}

func (service *TenantService) GetAllTiers() ([]dto.Tier, error) {
	r := config.GetResourceDef()
	tiers := r.GetAllTiers()
	rtn := []dto.Tier{}
	for _, t := range tiers {
		tID := t.ID
		cns := t.AvailableComputeTypeList
		cps := t.AvailableCompactorTypeList
		ms := t.AvailableMetaTypeList
		fs := t.AvailableFrontendTypeList
		st := t.AvailableStandaloneTypeList
		availableComputes, err := convertComponentTypesToSpecComponentTypes(cns)
		if err != nil {
			return nil, eris.Wrap(err, "failed to convert available compute")
		}
		availableCompactors, err := convertComponentTypesToSpecComponentTypes(cps)
		if err != nil {
			return nil, eris.Wrap(err, "failed to convert available compactor")
		}
		availableMetas, err := convertComponentTypesToSpecComponentTypes(ms)
		if err != nil {
			return nil, eris.Wrap(err, "failed to convert available meta")
		}
		availableFrontends, err := convertComponentTypesToSpecComponentTypes(fs)
		if err != nil {
			return nil, eris.Wrap(err, "failed to convert available frontend")
		}
		availableStandalone, err := convertComponentTypesToSpecComponentTypes(st)
		if err != nil {
			return nil, eris.Wrap(err, "failed to convert available standalone")
		}
		metaStore, err := convertMetaStoreToSpecMetaStore(t.AvailableMetaStore)
		if err != nil {
			return nil, eris.Wrap(err, "failed to convert meta store")
		}
		rtn = append(rtn, dto.Tier{
			ID:                                 (*dto.TierID)(&tID),
			AvailableComputeNodes:              availableComputes,
			AvailableCompactorNodes:            availableCompactors,
			AvailableFrontendNodes:             availableFrontends,
			AvailableMetaNodes:                 availableMetas,
			AvailableStandaloneNodes:           availableStandalone,
			MaximumComputeNodeFileCacheSizeGiB: int(t.MaximumComputeFileCacheGB),
			RetentionPeriod:                    int(t.DefaultRetentionPeriod),
			ValidityPeriod:                     int(t.DefaultValidityPeriod),
			AvailableMetaStore:                 metaStore,
		})
	}
	return rtn, nil
}

func getResourceDescription(m component.ResourceSpec) (string, string, error) {
	cpu, err := parseCPU(m.CPULimit)
	if err != nil {
		return "", "", err
	}
	mem, err := parseMemory(m.MemoryLimit)
	if err != nil {
		return "", "", err
	}
	return cpu, mem, nil
}

func toSpecComponentResource(s component.ResourceSpec, replicas int) (dto.ComponentResource, error) {
	cpu, mem, err := getResourceDescription(s)
	if err != nil {
		return dto.ComponentResource{}, err
	}

	return dto.ComponentResource{
		ComponentTypeID: s.ID,
		CPU:             cpu,
		Memory:          mem,
		Replica:         replicas,
	}, nil
}

func getComponentResourceFor(s resourcespec.TenantResourceSpec, component component.ComponentType) (dto.ComponentResource, error) {
	replicas, resources, err := s.GetReplicasAndResources(component)
	if err != nil {
		return dto.ComponentResource{}, err
	}
	componentResource, err := toSpecComponentResource(resources, int(replicas))
	if err != nil {
		return dto.ComponentResource{}, eris.Wrapf(err, "failed to parse %s component resource", component)
	}
	return componentResource, nil
}

func toSpecTenantResourceComponents(s resourcespec.TenantResourceSpec) (dto.TenantResourceComponents, error) {
	// return empty etcd component resource if tenant don't use etcd.
	etcd := dto.ComponentResource{}
	metaStore := s.GetMetaStore()
	if metaStore.Type == metastore.Etcd {
		cpu, mem, err := getResourceDescription(metaStore.Etcd.ResourceSpec)
		if err != nil {
			return dto.TenantResourceComponents{}, eris.Wrap(err, "failed to parse ETCD resource description")
		}
		etcd = dto.ComponentResource{
			ComponentTypeID: metaStore.Etcd.ResourceSpec.ID,
			CPU:             cpu,
			Memory:          mem,
			Replica:         int(metaStore.Etcd.Replica),
		}
	}

	if s.IsStandalone() {
		standalone, err := getComponentResourceFor(s, component.Standalone)
		if err != nil {
			return dto.TenantResourceComponents{}, err
		}
		return dto.TenantResourceComponents{
			Etcd:       &etcd,
			Standalone: &standalone,
		}, nil
	}
	meta, err := getComponentResourceFor(s, component.Meta)
	if err != nil {
		return dto.TenantResourceComponents{}, err
	}
	frontend, err := getComponentResourceFor(s, component.Frontend)
	if err != nil {
		return dto.TenantResourceComponents{}, err
	}
	compute, err := getComponentResourceFor(s, component.Compute)
	if err != nil {
		return dto.TenantResourceComponents{}, err
	}
	compactor, err := getComponentResourceFor(s, component.Compactor)
	if err != nil {
		return dto.TenantResourceComponents{}, err
	}
	return dto.TenantResourceComponents{
		Compactor: &compactor,
		Compute:   &compute,
		Etcd:      &etcd,
		Frontend:  &frontend,
		Meta:      &meta,
	}, nil
}

func (service *TenantService) TenantToSpec(ctx context.Context, tenant model.Tenant) (dto.Tenant, error) {
	tenantResource := tenant.Resources
	componentsSpec, err := toSpecTenantResourceComponents(tenantResource)
	if err != nil {
		return dto.Tenant{}, err
	}

	imageTag := config.Conf.Mgmt.RisingWave.Tag
	nsID, _ := namespace.Parse(tenant.ResourceNamespace)

	metaStore, err := toSpecMetaStore(tenant.Resources)
	if err != nil {
		return dto.Tenant{}, err
	}

	resourceGroups, err := toSpecResourceGroups(tenant.Resources)
	if err != nil {
		return dto.Tenant{}, err
	}

	cluster, err := service.model.GetClusterByID(ctx, tenant.ClusterID)
	if err != nil {
		return dto.Tenant{}, err
	}

	upcomingSnapshotTime, err := service.GetUpcomingSnapshotTime(ctx, tenant.ID)
	if err != nil {
		return dto.Tenant{}, err
	}

	return dto.Tenant{
		CreatedAt:      tenant.CreatedAt,
		UpdatedAt:      tenant.UpdatedAt,
		HealthStatus:   dto.TenantHealthStatus(tenant.HealthStatus),
		ID:             tenant.ID,
		LatestImageTag: imageTag,
		NsID:           nsID,
		ImageTag:       tenant.ImageTag,
		Region:         tenant.Region,
		RwConfig:       tenant.RwConfig,
		Status:         dto.TenantStatus(tenant.Status),
		TenantName:     tenant.TenantName,
		Tier:           dto.TierID(tenant.TierID),
		OrgID:          tenant.OrgID,
		UsageType:      tenant.UsageType,
		Resources: dto.TenantResource{
			Components:     componentsSpec,
			ComputeCache:   toSpecComputeCache(tenantResource.RisingWaveOptions().GetComputeCacheSpec()),
			MetaStore:      metaStore,
			ResourceGroups: &resourceGroups,
		},
		EtcdConfig:           tenant.EtcdConfig,
		ClusterName:          cluster.Name,
		UpcomingSnapshotTime: upcomingSnapshotTime,
	}, nil
}

func (service *TenantService) GetUpcomingSnapshotTime(ctx context.Context, tenantID uint64) (*time.Time, error) {
	w, err := service.workflowRepo.GetRunningWorkflowOfTenant(ctx, metabackup.AutoBackupWorkflowType, tenantID)
	if err != nil {
		return nil, eris.Wrapf(err, "failed to retrieve autobackup workflow")
	}
	// auto backup workflow has not started yet
	if w == nil {
		return nil, nil
	}
	wctx, ok := w.Context.(*metabackup.AutoMetaBackupContext)
	if !ok {
		return nil, eris.Wrapf(err, "failed to cast autobackup context")
	}
	return &wctx.UpcomingSnapshotTime, nil
}

func toSpecMetaStore(resources resourcespec.TenantResourceSpec) (*dto.TenantResourceMetaStore, error) {
	metaStore := resources.GetMetaStore()
	res := &dto.TenantResourceMetaStore{
		Type: string(metaStore.Type),
	}
	if metaStore.Etcd != nil {
		comp, err := toSpecComponentResource(metaStore.Etcd.ResourceSpec, int(metaStore.Etcd.Replica))
		if err != nil {
			return nil, err
		}
		res.Etcd = &dto.MetaStoreEtcd{
			Resource: comp,
			SizeGb:   int(metaStore.Etcd.SizeGb),
		}
	}
	if metaStore.PostgreSQL != nil {
		comp, err := toSpecComponentResource(metaStore.PostgreSQL.ResourceSpec, int(metaStore.PostgreSQL.Replica))
		if err != nil {
			return nil, err
		}
		res.PostgreSQL = &dto.MetaStorePostgreSQL{
			Resource: comp,
			SizeGb:   int(metaStore.PostgreSQL.SizeGb),
		}
	}
	if metaStore.AwsRds != nil {
		res.AwsRds = &dto.MetaStoreAwsRds{
			InstanceClass: metaStore.AwsRds.InstanceClass,
			SizeGb:        int(metaStore.AwsRds.SizeGb),
		}
	}
	if metaStore.GcpCloudSQL != nil {
		res.GcpCloudSQL = &dto.MetaStoreGcpCloudSQL{
			Tier:   metaStore.GcpCloudSQL.Tier,
			SizeGb: int(metaStore.GcpCloudSQL.SizeGb),
		}
	}
	if metaStore.AzrPostgres != nil {
		res.AzrPostgres = &dto.MetaStoreAzrPostgres{
			Sku:    metaStore.AzrPostgres.Sku,
			SizeGb: int(metaStore.AzrPostgres.SizeGb),
		}
	}

	rwuSize := "0"
	switch metaStore.Type {
	case metastore.Etcd:
		rwuSize = metaStore.Etcd.ResourceSpec.CPULimit
	case metastore.Postgresql:
		rwuSize = metaStore.PostgreSQL.ResourceSpec.CPULimit
	case metastore.AwsRds:
		rwuSize = fmt.Sprintf("%dm", metaStore.AwsRds.RwuMilli)
	case metastore.GcpCloudSQL:
		rwuSize = fmt.Sprintf("%dm", metaStore.GcpCloudSQL.RwuMilli)
	case metastore.AzrPostgres:
		rwuSize = fmt.Sprintf("%dm", metaStore.AzrPostgres.RwuMilli)
	case metastore.SharingPg:
		rwuSize = "0"
	}
	rwu, err := parseCPU(rwuSize)
	if err != nil {
		return nil, eris.Wrapf(err, "failed to parse rwu string '%s'", rwuSize)
	}
	res.Rwu = rwu

	return res, nil
}

func toSpecResourceGroups(resources resourcespec.TenantResourceSpec) ([]dto.TenantResourceGroup, error) {
	if resources.IsStandalone() {
		return []dto.TenantResourceGroup{}, nil
	}
	regular, ok := resources.AsTenantResourceSpecV1Regular()
	if !ok {
		return nil, eris.New("failed to cast to TenantResourceSpecV1Regular")
	}
	rtn := []dto.TenantResourceGroup{}
	for _, it := range regular.ResourceGroups {
		componentResource, err := toSpecComponentResource(it.ResourceSpec, int(it.Replica))
		if err != nil {
			return nil, eris.Wrapf(err, "failed to parse resource group resource %s", it.Name)
		}

		rtn = append(rtn, dto.TenantResourceGroup{
			Name:         it.Name,
			Resource:     componentResource,
			ComputeCache: *toSpecComputeCache(it.ComputeCacheSpec),
		})
	}
	return rtn, nil
}

func toSpecComputeCache(computeCache resourcespec.ComputeCacheSpec) *dto.TenantResourceComputeCache {
	return &dto.TenantResourceComputeCache{
		SizeGb: computeCache.SizeGb,
	}
}

func (service *TenantService) GetOrgTenantByID(ctx context.Context, orgID OrgID, tenantID TenantID) (model.Tenant, error) {
	return service.model.GetOrgTenantByID(ctx, orgID, tenantID)
}
func (service *TenantService) GetOrgTenantByNsID(ctx context.Context, orgID OrgID, nsID uuid.UUID) (model.Tenant, error) {
	return service.model.GetOrgTenantByNsID(ctx, orgID, nsID)
}
func (service *TenantService) GetOrgTenantByName(ctx context.Context, orgID OrgID, tenantName string) (model.Tenant, error) {
	return service.model.GetOrgTenantByName(ctx, orgID, tenantName)
}

func (service *TenantService) GetTenantsCountByOrgID(ctx context.Context, orgID OrgID) (uint64, error) {
	return service.model.GetTenantsCountByOrgID(ctx, orgID)
}

func (service *TenantService) OrgTenantIDExists(ctx context.Context, orgID OrgID, tenantID TenantID) (bool, error) {
	return service.model.OrgTenantIDExists(ctx, orgID, tenantID)
}

func (service *TenantService) OrgTenantNameExists(ctx context.Context, orgID OrgID, tenantName string) (bool, error) {
	return service.model.OrgTenantNameExists(ctx, orgID, tenantName)
}

func (service *TenantService) UpdateComputeCache(ctx context.Context, tenantID uint64, computeCacheSpec *resourcespec.ComputeCacheSpec, resourceGroupComputeCacheSpecs []computecache.ResourceGroupComputeCacheSpec) (uuid.UUID, error) {
	workflowID, err := service.updateComputeCacheWorkflow.UpdateComputeCacheWorkflow(ctx, tenantID, computeCacheSpec, resourceGroupComputeCacheSpecs)
	if err != nil {
		return uuid.Nil, err
	}

	logger.L().Named("Tenant").Info("Run enable compute cache workflow.",
		zap.String("request_id", ginx.ExtractRequestID(ctx)),
		zap.String("workflow_id", workflowID.String()),
		zap.Error(err),
	)

	return workflowID, nil
}

func (service *TenantService) SetTenantSecretStoreDek(ctx context.Context, tenantID TenantID) error {
	privateKey, err := rwsecret.GenerateRandomSecretStorePrivateKey()
	if err != nil {
		return eris.Wrap(err, "failed to generate secret store data encryption key")
	}

	encryptedPrivateKey, err := rwsecret.EncryptSecretStorePrivateKey(privateKey)
	if err != nil {
		return eris.Wrap(err, "failed to encrypt secret store data encryption key")
	}

	err = modeltx.Exec(ctx, service.txProvider, func(ctx context.Context, model *model.Model) error {
		tenant, err := model.GetTenantByID(ctx, tenantID)
		if err != nil {
			return eris.Wrapf(err, "failed to get tenant. tenant id: %d", tenantID)
		}
		if tenant.SecretStoreDataEncryptionKey != "" {
			return nil
		}

		err = model.SetValueForEmptyTenantSecretStoreDekByID(ctx, tenantID, encryptedPrivateKey)
		if err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		return err
	}

	return nil
}

func (service *TenantService) RestoreTenant(ctx context.Context, oldTenant model.Tenant, newTenantName string, snapshotID uuid.UUID, oldTenantExtSpecs *spec.TenantExtensions) (*dto.Tenant, error) {
	// tenant validation
	if oldTenant.Resources.GetMetaStore().Type == metastore.Etcd {
		return nil, eris.New("cluster restore only supported for SQL backends").WithCode(eris.CodeFailedPrecondition)
	}

	// new tenant name validation
	if exist, err := service.OrgTenantNameExists(ctx, oldTenant.OrgID, newTenantName); err != nil {
		return nil, eris.Wrap(err, "could not check tenant exixstence")
	} else if exist {
		return nil, eris.New("tenant already exists").WithCode(eris.CodeAlreadyExists)
	}

	snapshot, err := service.model.GetSnapshot(ctx, oldTenant.ID, snapshotID)
	if err != nil {
		if eris.Is(err, pgx.ErrNoRows) {
			return nil, eris.Errorf("meta snapshot %v does not exist", snapshotID).WithCode(eris.CodeNotFound)
		}
		return nil, eris.Wrap(err, "could not get snapshot")
	}
	if snapshot.Status != string(model.SnapshotStatusAvailable) {
		return nil, eris.Errorf("can only restore from available snapshot but snapshot is in %s", snapshot.Status).WithCode(eris.CodeInvalidArgument)
	}
	err = service.model.UpdateSnapshotStatusFromStatus(ctx, oldTenant.ID, snapshotID, model.SnapshotStatusAvailable, model.SnapshotStatusRestoring)
	if err != nil {
		return nil, eris.Wrapf(err, "could not update snapshot status from %s to restoring", snapshot.Status)
	}

	var serverlessCompactionOpts *provision.ServerlessCompactionOptions
	if oldTenantExtSpecs != nil && oldTenantExtSpecs.ServerlessCompaction != nil && oldTenantExtSpecs.ServerlessCompaction.Enabled {
		serverlessCompactionOpts = &provision.ServerlessCompactionOptions{
			MaximumCPUSize: oldTenantExtSpecs.ServerlessCompaction.MaximumCompactionConcurrency,
		}

		t, ok := oldTenant.Resources.AsTenantResourceSpecV1Regular()
		if !ok {
			return nil, eris.New("failed to cast resources as spec")
		}

		serverlessCompactionOpts.OriginalCompactorReplica = extensions.GetServerlessCompactionOriginalCompactorReplica(t.CompactorReplica, t.ComputeReplica)
		t.CompactorReplica = 0
		oldTenant.Resources = resourcespec.FromValidTenantResourceSpec(&t)
	}

	newTenantID, err := service.ProvisionTenant(ctx, ProvisionTenantOption{
		OrgID:                oldTenant.OrgID,
		TenantName:           newTenantName,
		TierID:               oldTenant.TierID,
		ImageTag:             snapshot.RwVersion,
		Region:               config.Conf.Region,
		ClusterID:            oldTenant.ClusterID,
		RwConfig:             oldTenant.RwConfig,
		EtcdConfig:           oldTenant.EtcdConfig,
		Resource:             oldTenant.Resources,
		AsTrial:              false,
		ServerlessCompaction: serverlessCompactionOpts,
		Restore: &provision.RestoreOptions{
			OldTenant:      oldTenant,
			MSDBSnapshotID: snapshotID,
		},
	})
	if ok := service.model.UpdateSnapshotStatus(ctx, oldTenant.ID, snapshotID, model.SnapshotStatusAvailable); ok != nil {
		return nil, eris.Wrap(err, "could not update snapshot status from restoring to available")
	}
	if err != nil {
		return nil, err
	}

	newTenant, err := service.GetTenantByID(ctx, newTenantID)
	if err != nil {
		return nil, err
	}
	rtn, err := service.TenantToSpec(ctx, newTenant)
	if err != nil {
		return nil, err
	}
	if rtn.NsID == uuid.Nil {
		nsID, _ := namespace.Parse(newTenant.ResourceNamespace)
		rtn.NsID = nsID
	}
	return &rtn, nil
}
