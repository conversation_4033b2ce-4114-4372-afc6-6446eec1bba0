package config

import (
	"encoding/json"
	"reflect"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
)

type RisingWaveExtensionsCompactionCompactor struct {
	Replica       int64  `json:"Replica"`
	MetaEndpoint  string `json:"MetaEndpoint"`
	ConfigMap     string `json:"ConfigMap"`
	CPURequest    string `json:"CpuRequest"`
	CPULimit      string `json:"CpuLimit"`
	MemoryRequest string `json:"MemoryRequest"`
	MemoryLimit   string `json:"MemoryLimit"`
}

func (c *RisingWaveExtensionsCompactionCompactor) ToResourceRequirements() (corev1.ResourceRequirements, error) {
	resources := corev1.ResourceRequirements{
		Limits:   make(corev1.ResourceList),
		Requests: make(corev1.ResourceList),
	}
	var err error
	if c.CPULimit != "" {
		resources.Limits[corev1.ResourceCPU], err = resource.ParseQuantity(c.CPULimit)
		if err != nil {
			return corev1.ResourceRequirements{}, err
		}
	}
	if c.MemoryLimit != "" {
		resources.Limits[corev1.ResourceMemory], err = resource.ParseQuantity(c.MemoryLimit)
		if err != nil {
			return corev1.ResourceRequirements{}, err
		}
	}
	if c.CPURequest != "" {
		resources.Requests[corev1.ResourceCPU], err = resource.ParseQuantity(c.CPURequest)
		if err != nil {
			return corev1.ResourceRequirements{}, err
		}
	}
	if c.MemoryRequest != "" {
		resources.Requests[corev1.ResourceMemory], err = resource.ParseQuantity(c.MemoryRequest)
		if err != nil {
			return corev1.ResourceRequirements{}, err
		}
	}
	return resources, nil
}

type RisingWaveExtensionsCompactionScaler struct {
	PollingInterval               int64 `json:"PollingInterval"`
	CollectInterval               int64 `json:"CollectInterval"`
	ScaleDownToZeroRequiredTimes  int64 `json:"ScaleDownToZeroRequiredTimes"`
	ScaleDownToNRequiredTimes     int64 `json:"ScaleDownToNRequiredTimes"`
	CoolDownPeriod                int64 `json:"CoolDownPeriod"`
	MinReplicas                   int64 `json:"MinReplicas"`
	MaxReplicas                   int64 `json:"MaxReplicas"`
	DesiredReplicas               int64 `json:"DesiredReplicas"`
	DefaultParallelism            int64 `json:"DefaultParallelism"`
	ExpirationExpireTime          int64 `json:"ExpirationExpireTime"`
	DefaultCapacityReservedBuffer int64 `json:"DefaultCapacityReservedBuffer"`
}

type RisingwaveExtensionsCompactionChart struct {
	URL string `json:"Url"`
	Tag string `json:"Tag"`
}

type RisingwaveExtensionsCompactionImage struct {
	Repository string `json:"Repository"`
	Tag        string `json:"Tag"`
}

type RisingWaveExtensionsCompaction struct {
	Chart     RisingwaveExtensionsCompactionChart     `json:"Chart"`
	Image     RisingwaveExtensionsCompactionImage     `json:"Image"`
	Compactor RisingWaveExtensionsCompactionCompactor `json:"Compactor"`
	Scaler    RisingWaveExtensionsCompactionScaler    `json:"Scaler"`
	ImageMap  map[string]string                       `json:"ImageMap"`
}

type RisingWaveExtensionsCompactionWithRWConfig struct {
	ResourceSpec string                                  `json:"ResourceSpec"`
	Chart        RisingwaveExtensionsCompactionChart     `json:"Chart"`
	Image        RisingwaveExtensionsCompactionImage     `json:"Image"`
	Compactor    RisingWaveExtensionsCompactionCompactor `json:"Compactor"`
	Scaler       RisingWaveExtensionsCompactionScaler    `json:"Scaler"`
}

// Convert RisingWaveExtensionsCompactionWithRWConfig to RisingWaveExtensionsCompaction.
func ToRisingWaveExtensionsCompaction(config *RisingWaveExtensionsCompactionWithRWConfig) *RisingWaveExtensionsCompaction {
	return &RisingWaveExtensionsCompaction{
		Chart:     config.Chart,
		Image:     config.Image,
		Compactor: config.Compactor,
		Scaler:    config.Scaler,
	}
}

// NewRisingWaveExtensionsCompactionWithRWConfig creates a new instance with default values.
func NewRisingWaveExtensionsCompactionWithRWConfig() *RisingWaveExtensionsCompactionWithRWConfig {
	return &RisingWaveExtensionsCompactionWithRWConfig{
		ResourceSpec: "",
		Chart:        Conf.Mgmt.RisingWaveExtensions.Compaction.Chart,
		Image:        Conf.Mgmt.RisingWaveExtensions.Compaction.Image,
		Compactor:    Conf.Mgmt.RisingWaveExtensions.Compaction.Compactor,
		Scaler:       Conf.Mgmt.RisingWaveExtensions.Compaction.Scaler,
	}
}

func (rw *RisingWaveExtensionsCompactionWithRWConfig) mergeStructs(defaultValues *RisingWaveExtensionsCompactionWithRWConfig) {
	vRW := reflect.ValueOf(rw).Elem()
	vDefault := reflect.ValueOf(defaultValues).Elem()

	// call recursive function to merge fields
	mergeStructFields(vRW, vDefault)
}

// mergeStructFields merges fields of two structs.
func mergeStructFields(vRW, vDefault reflect.Value) {
	for i := 0; i < vRW.NumField(); i++ {
		fieldRW := vRW.Field(i)
		fieldDefault := vDefault.Field(i)

		// if field is a struct, call the function recursively
		if fieldRW.Kind() == reflect.Struct {
			mergeStructFields(fieldRW, fieldDefault)
		} else if reflect.DeepEqual(fieldRW.Interface(), reflect.Zero(fieldRW.Type()).Interface()) {
			// if field is not a struct, check if it's zero value and set it to default value
			fieldRW.Set(fieldDefault)
		}
	}
}

// Custom UnmarshalJSON to ensure default values are applied.
func (rw *RisingWaveExtensionsCompactionWithRWConfig) UnmarshalJSON(data []byte) error {
	// Create an alias to avoid recursion
	type Alias RisingWaveExtensionsCompactionWithRWConfig

	// Initialize with default values
	defaultConfig := NewRisingWaveExtensionsCompactionWithRWConfig()

	// Unmarshal data into a temporary struct
	if err := json.Unmarshal(data, (*Alias)(rw)); err != nil {
		// If unmarshaling fails, return error
		return err
	}

	// Merge the unmarshaled values with the default configuration
	rw.mergeStructs(defaultConfig)

	return nil
}

// Marshal to JSON string.
func (rw *RisingWaveExtensionsCompactionWithRWConfig) Marshal() (string, error) {
	data, err := json.Marshal(rw)
	if err != nil {
		return "", err
	}
	return string(data), nil
}
