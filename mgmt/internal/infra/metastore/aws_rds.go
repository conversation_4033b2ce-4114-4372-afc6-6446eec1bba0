package metastore

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/risingwavelabs/eris"

	pbaws "github.com/risingwavelabs/cloudagent/pbgen/common/aws"
	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbrw "github.com/risingwavelabs/cloudagent/pbgen/common/rw"
	pbawssvc "github.com/risingwavelabs/cloudagent/pbgen/services/aws"
	pbk8ssvc "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"

	"github.com/risingwavelabs/risingwave-cloud/internal/account"
	"github.com/risingwavelabs/risingwave-cloud/internal/agent"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	metastoredef "github.com/risingwavelabs/risingwave-cloud/internal/tenant/metastore"
	"github.com/risingwavelabs/risingwave-cloud/shared/namespace"
)

const (
	AwsRdsEngine        = "postgres"
	AwsRdsEngineVersion = "14"
	AwsRdsPort          = 5432

	AwsRdsUsernameSecretKey = "username"
	AwsRdsPasswordSecretKey = "password"

	AWSTagKeyEnvID     = "EnvID"
	AWSTagKeyProject   = "Project"
	AWSTagValueProject = "RisingWave"
)

type AwsRdsConfig struct {
	Agent              *agent.CloudAgent
	AccountAgent       account.Agent
	DbSubnetGroupName  string
	VpcSecurityGroupID string
	EnvID              string
}

type AwsRdsProvider struct {
	agent              *agent.CloudAgent
	accountAgent       account.Agent
	DbSubnetGroupName  string
	VpcSecurityGroupID string
	envID              string
}

func NewAwsRdsProvider(config AwsRdsConfig) *AwsRdsProvider {
	return &AwsRdsProvider{
		agent:              config.Agent,
		accountAgent:       config.AccountAgent,
		DbSubnetGroupName:  config.DbSubnetGroupName,
		VpcSecurityGroupID: config.VpcSecurityGroupID,
		envID:              config.EnvID,
	}
}

type AwsRdsOption struct {
	TenantID          uint64
	TenantName        string
	NsID              uuid.UUID
	ResourceNamespace string

	InstanceClass string
	StorageSizeGB int32
	RwuMilli      int32

	ResourceID string

	DatabaseName string
	Username     string
}

func toAwsRdsOption(tenant model.Tenant, spec metastoredef.Spec) (AwsRdsOption, error) {
	metaStore := spec
	if metaStore.AwsRds == nil {
		return AwsRdsOption{}, eris.New("aws rds meta store not exist").WithCode(eris.CodeInvalidArgument)
	}
	uid, err := namespace.Parse(tenant.ResourceNamespace)
	if err != nil {
		return AwsRdsOption{}, eris.Wrap(err, "parse namespace error")
	}

	return AwsRdsOption{
		TenantID:          tenant.ID,
		TenantName:        tenant.TenantName,
		NsID:              uid,
		ResourceNamespace: tenant.ResourceNamespace,

		InstanceClass: metaStore.AwsRds.InstanceClass,
		StorageSizeGB: metaStore.AwsRds.SizeGb,
		RwuMilli:      metaStore.AwsRds.RwuMilli,

		ResourceID: fmt.Sprintf("rwc-%s-rds", namespace.ToBase32(uid)),

		DatabaseName: fmt.Sprintf("rwc_%s_db", namespace.ToBase32(uid)),
		Username:     fmt.Sprintf("rwc_%s_user", namespace.ToBase32(uid)),
	}, nil
}

func (p *AwsRdsProvider) CreateMetaStoreTargetAndAwait(ctx context.Context, tenant model.Tenant, target metastoredef.Spec) (*Result, error) {
	option, err := toAwsRdsOption(tenant, target)
	if err != nil {
		return nil, err
	}

	err = p.agent.CreateK8sSecret(ctx, &pbk8ssvc.CreateSecretRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        option.ResourceID, // secret use the same id as db
			Namespace: option.ResourceNamespace,
		},
		SecretSpec: &pbk8s.Secret{
			StringData: map[string]string{
				AwsRdsUsernameSecretKey: option.Username,
				AwsRdsPasswordSecretKey: option.ResourceID, // password is same as db id
			},
		},
	})
	if err != nil {
		return nil, eris.Wrap(err, "failed to create rds password secret")
	}

	result, err := p.agent.CreateAwsDBInstanceAndWait(ctx, &pbawssvc.CreateDBInstanceRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        option.ResourceID,
			Namespace: option.ResourceNamespace,
		},
		Spec: &pbaws.DBInstanceSpec{
			DbInstanceIdentifier: option.ResourceID,
			DbInstanceClass:      option.InstanceClass,
			AllocatedStorage:     uint32(option.StorageSizeGB),
			Engine:               AwsRdsEngine,
			EngineVersion:        AwsRdsEngineVersion,
			DbName:               option.DatabaseName,
			MasterUsername:       option.Username,
			MasterUserPassword: &pbaws.PasswordSecretRef{
				Name:      option.ResourceID,
				Namespace: option.ResourceNamespace,
				Key:       AwsRdsPasswordSecretKey,
			},
			DbSubnetGroupName: p.DbSubnetGroupName,
			VpcSecurityGroupIds: []string{
				p.VpcSecurityGroupID,
			},
			StorageEncrypted: true,
			Tags: map[string]string{
				AWSTagKeyProject: AWSTagValueProject,
				AWSTagKeyEnvID:   p.envID,
			},
		},
	}, option.TenantID)
	if err != nil {
		return nil, eris.Wrap(err, "failed to create db instance")
	}
	err = p.accountAgent.UpsertTenantResource(ctx, option.NsID, string(metastoredef.AwsRds), int64(option.RwuMilli))
	if err != nil {
		return nil, eris.Wrap(err, "failed to create tenant resource in account service")
	}
	return toMetaStoreResultAws(result, option)
}

func toMetaStoreResultAws(result *pbawssvc.GetDBInstanceResponse, option AwsRdsOption) (*Result, error) {
	var host = ""
	var psql *PostgreSQLResult

	if result.GetEndpoint() != nil {
		host = result.GetEndpoint().GetAddress()
		sqlEndpoint := fmt.Sprintf(
			"postgres://%s:%s@%s:%d/%s",
			option.Username, option.ResourceID,
			host, AwsRdsPort, option.DatabaseName,
		)
		psql = &PostgreSQLResult{
			SQLEndpoint: sqlEndpoint,
			Username:    option.Username,
			Password:    option.ResourceID,
		}
	}

	return &Result{
		Spec: &pbrw.MetaStoreSpec{
			PostgresqlBackend: &pbrw.MetaStoreBackendPostgreSql{
				Host:     host,
				Port:     AwsRdsPort,
				Database: option.DatabaseName,
				Credentials: &pbrw.PostgreSqlCredentials{
					UsernameKeyRef: AwsRdsUsernameSecretKey,
					PasswordKeyRef: AwsRdsPasswordSecretKey,
					SecretName:     option.ResourceID,
				},
			},
		},
		PostgreSQL: psql,
	}, nil
}

func (p *AwsRdsProvider) StartMetaStoreTargetAndAwait(ctx context.Context, tenant model.Tenant, target metastoredef.Spec) error {
	option, err := toAwsRdsOption(tenant, target)
	if err != nil {
		return err
	}
	_, err = p.agent.StartRDSAndWait(ctx, &pbawssvc.StartDBInstanceRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        option.ResourceID,
			Namespace: option.ResourceNamespace,
		},
	})
	if err != nil {
		return err
	}
	return nil
}

func (p *AwsRdsProvider) StopMetaStoreTargetAndAwait(ctx context.Context, tenant model.Tenant, target metastoredef.Spec) error {
	option, err := toAwsRdsOption(tenant, target)
	if err != nil {
		return err
	}
	_, err = p.agent.StopRDSAndWait(ctx, &pbawssvc.StopDBInstanceRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        option.ResourceID,
			Namespace: option.ResourceNamespace,
		},
	})
	if err != nil {
		return err
	}
	return nil
}

func (p *AwsRdsProvider) DeleteMetaStoreTargetAndAwait(ctx context.Context, tenant model.Tenant, target metastoredef.Spec) error {
	option, err := toAwsRdsOption(tenant, target)
	if err != nil {
		return err
	}

	// ACK will delete the k8s DbInstance immediately. It won't wait to the instance really shutdown.
	_, err = p.agent.DeleteAwsDBInstanceAndWait(ctx, &pbawssvc.DeleteDBInstanceRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        option.ResourceID,
			Namespace: option.ResourceNamespace,
		},
	})
	if err != nil {
		return err
	}
	err = p.accountAgent.DeleteTenantResource(ctx, option.NsID, string(metastoredef.AwsRds))
	if err != nil {
		return eris.Wrap(err, "failed to delete tenant resource in account service")
	}

	err = p.agent.DeleteK8sSecret(ctx, &pbk8ssvc.DeleteSecretRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        option.ResourceID,
			Namespace: option.ResourceNamespace,
		},
	})
	if err != nil {
		return err
	}
	return nil
}

func (p *AwsRdsProvider) GetMetaStoreTarget(ctx context.Context, tenant model.Tenant, target metastoredef.Spec) (*Result, error) {
	option, err := toAwsRdsOption(tenant, target)
	if err != nil {
		return nil, err
	}

	result, err := p.agent.GetAwsDBInstance(ctx, &pbawssvc.GetDBInstanceRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        option.ResourceID,
			Namespace: tenant.ResourceNamespace,
		},
	})
	if err != nil {
		return nil, eris.Wrap(err, "failed to get db instance")
	}
	return toMetaStoreResultAws(result, option)
}

func (p *AwsRdsProvider) CreateMetaStoreAndAwait(ctx context.Context, tenant model.Tenant) (*Result, error) {
	return p.CreateMetaStoreTargetAndAwait(ctx, tenant, tenant.Resources.GetMetaStore())
}

func (p *AwsRdsProvider) StartMetaStoreAndAwait(ctx context.Context, tenant model.Tenant) error {
	return p.StartMetaStoreTargetAndAwait(ctx, tenant, tenant.Resources.GetMetaStore())
}

func (p *AwsRdsProvider) StopMetaStoreAndAwait(ctx context.Context, tenant model.Tenant) error {
	return p.StopMetaStoreTargetAndAwait(ctx, tenant, tenant.Resources.GetMetaStore())
}

func (p *AwsRdsProvider) DeleteMetaStoreAndAwait(ctx context.Context, tenant model.Tenant) error {
	return p.DeleteMetaStoreTargetAndAwait(ctx, tenant, tenant.Resources.GetMetaStore())
}

func (p *AwsRdsProvider) GetMetaStore(ctx context.Context, tenant model.Tenant) (*Result, error) {
	return p.GetMetaStoreTarget(ctx, tenant, tenant.Resources.GetMetaStore())
}

func (p *AwsRdsProvider) PostCreateMetaStore(context.Context, string) error {
	return nil
}

func (p *AwsRdsProvider) PostStartMetaStore(context.Context, string) error {
	return nil
}

func (p *AwsRdsProvider) PostStopMetaStore(context.Context, string) error {
	return nil
}

var _ Provider = (*AwsRdsProvider)(nil)
