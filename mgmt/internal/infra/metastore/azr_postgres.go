package metastore

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/risingwavelabs/eris"

	pbazr "github.com/risingwavelabs/cloudagent/pbgen/common/azr"
	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbrw "github.com/risingwavelabs/cloudagent/pbgen/common/rw"
	pbazrsvc "github.com/risingwavelabs/cloudagent/pbgen/services/azr"
	pbk8ssvc "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"

	"github.com/risingwavelabs/risingwave-cloud/internal/account"
	"github.com/risingwavelabs/risingwave-cloud/internal/agent"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	metastoredef "github.com/risingwavelabs/risingwave-cloud/internal/tenant/metastore"
	"github.com/risingwavelabs/risingwave-cloud/shared/namespace"
)

const (
	AzrPostgresPort     = 5432
	AzrPostgresDatabase = "postgres"

	AzrPostgresUsernameSecretKey = "username"
	AzrPostgresPasswordSecretKey = "password"

	AZRTagKeyEnvID     = "envid"
	AZRTagKeyProject   = "project"
	AZRTagValueProject = "risingwave"
)

type AzrPostgresConfig struct {
	Agent           *agent.CloudAgent
	AccountAgent    account.Agent
	DelegatedSubnet string
	PrivateDNSZone  string
	EnvID           string
}

type AzrPostgresProvider struct {
	agent           *agent.CloudAgent
	accountAgent    account.Agent
	delegatedSubnet string
	privateDNSZone  string
	envID           string
}

func NewAzrPostgresProvider(config AzrPostgresConfig) *AzrPostgresProvider {
	return &AzrPostgresProvider{
		agent:           config.Agent,
		accountAgent:    config.AccountAgent,
		delegatedSubnet: config.DelegatedSubnet,
		privateDNSZone:  config.PrivateDNSZone,
		envID:           config.EnvID,
	}
}

type AzrPostgresOption struct {
	TenantID          uint64
	TenantName        string
	NsID              uuid.UUID
	ResourceNamespace string

	Sku        string
	DiskSizeGb int32
	RwuMilli   int32

	ResourceID string
	Username   string
}

func toAzrPostgresOption(tenant model.Tenant, spec metastoredef.Spec) (AzrPostgresOption, error) {
	metaStore := spec
	if metaStore.AzrPostgres == nil {
		return AzrPostgresOption{}, eris.New("azr postgres meta store not exist").WithCode(eris.CodeInvalidArgument)
	}
	uid, err := namespace.Parse(tenant.ResourceNamespace)
	if err != nil {
		return AzrPostgresOption{}, eris.Wrap(err, "parse namespace error")
	}

	return AzrPostgresOption{
		TenantID:          tenant.ID,
		TenantName:        tenant.TenantName,
		NsID:              uid,
		ResourceNamespace: tenant.ResourceNamespace,

		Sku:        metaStore.AzrPostgres.Sku,
		DiskSizeGb: metaStore.AzrPostgres.SizeGb,
		RwuMilli:   metaStore.AzrPostgres.RwuMilli,

		ResourceID: fmt.Sprintf("rwc-%s-azr-pg", namespace.ToBase32(uid)),
		Username:   fmt.Sprintf("rwc_%s_user", namespace.ToBase32(uid)),
	}, nil
}

func (p *AzrPostgresProvider) CreateMetaStoreTargetAndAwait(ctx context.Context, tenant model.Tenant, target metastoredef.Spec) (*Result, error) {
	option, err := toAzrPostgresOption(tenant, target)
	if err != nil {
		return nil, err
	}

	err = p.agent.CreateK8sSecret(ctx, &pbk8ssvc.CreateSecretRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        option.ResourceID,
			Namespace: option.ResourceNamespace,
		},
		SecretSpec: &pbk8s.Secret{
			StringData: map[string]string{
				AzrPostgresUsernameSecretKey: option.Username,
				AzrPostgresPasswordSecretKey: option.ResourceID, // password is same as db id
			},
		},
	})
	if err != nil {
		return nil, eris.Wrap(err, "failed to create azr postgres password secret")
	}

	result, err := p.agent.CreateAzrPGServerAndWait(ctx, &pbazrsvc.CreatePGServerRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        option.ResourceID,
			Namespace: option.ResourceNamespace,
		},
		Spec: &pbazr.PGServerSpec{
			AzureName: option.ResourceID,
			Version:   "14",
			Sku: &pbazr.PGServerSku{
				Name: option.Sku,
				Tier: "Burstable",
			},
			Storage: &pbazr.PGServerStorage{
				StorageSizeGb: option.DiskSizeGb,
			},
			AdministratorLogin: option.Username,
			AdministratorLoginPassword: &pbazr.SecretKeyRef{
				Key:  AzrPostgresPasswordSecretKey,
				Name: option.ResourceID,
			},
			Network: &pbazr.PGServerNetwork{
				DelegatedSubnet: &pbazr.ResourceReference{
					ArmId: p.delegatedSubnet,
				},
				PrivateDnsZone: &pbazr.ResourceReference{
					ArmId: p.privateDNSZone,
				},
			},
			Tags: map[string]string{
				AZRTagKeyProject: AZRTagValueProject,
				AZRTagKeyEnvID:   p.envID,
			},
		},
	}, option.TenantID)
	if err != nil {
		return nil, eris.Wrap(err, "failed to create azr postgres")
	}
	err = p.accountAgent.UpsertTenantResource(ctx, option.NsID, string(metastoredef.AzrPostgres), int64(option.RwuMilli))
	if err != nil {
		return nil, eris.Wrap(err, "failed to create tenant resource in account service")
	}
	return toMetaStoreResultAzr(result, option)
}

func toMetaStoreResultAzr(result *pbazrsvc.GetPGServerResponse, option AzrPostgresOption) (*Result, error) {
	var host = ""
	var psql *PostgreSQLResult

	if result.DomainName != nil {
		host = result.GetDomainName()
		sqlEndpoint := fmt.Sprintf(
			"postgres://%s:%s@%s:%d/%s",
			option.Username, option.ResourceID,
			host, AzrPostgresPort, AzrPostgresDatabase,
		)
		psql = &PostgreSQLResult{
			SQLEndpoint: sqlEndpoint,
			Username:    option.Username,
			Password:    option.ResourceID,
		}
	}

	return &Result{
		Spec: &pbrw.MetaStoreSpec{
			PostgresqlBackend: &pbrw.MetaStoreBackendPostgreSql{
				Host:     host,
				Port:     AzrPostgresPort,
				Database: AzrPostgresDatabase,
				Credentials: &pbrw.PostgreSqlCredentials{
					UsernameKeyRef: AzrPostgresUsernameSecretKey,
					PasswordKeyRef: AzrPostgresPasswordSecretKey,
					SecretName:     option.ResourceID,
				},
			},
		},
		PostgreSQL: psql,
	}, nil
}

func (p *AzrPostgresProvider) StartMetaStoreTargetAndAwait(ctx context.Context, tenant model.Tenant, target metastoredef.Spec) error {
	option, err := toAzrPostgresOption(tenant, target)
	if err != nil {
		return err
	}
	_, err = p.agent.StartAzrPGServerAndWait(ctx, &pbazrsvc.StartPGServerRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        option.ResourceID,
			Namespace: option.ResourceNamespace,
		},
	})
	if err != nil {
		return err
	}
	return nil
}

func (p *AzrPostgresProvider) StopMetaStoreTargetAndAwait(ctx context.Context, tenant model.Tenant, target metastoredef.Spec) error {
	option, err := toAzrPostgresOption(tenant, target)
	if err != nil {
		return err
	}
	_, err = p.agent.StopAzrPGServerAndWait(ctx, &pbazrsvc.StopPGServerRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        option.ResourceID,
			Namespace: option.ResourceNamespace,
		},
	})
	if err != nil {
		return err
	}
	return nil
}

func (p *AzrPostgresProvider) DeleteMetaStoreTargetAndAwait(ctx context.Context, tenant model.Tenant, target metastoredef.Spec) error {
	option, err := toAzrPostgresOption(tenant, target)
	if err != nil {
		return err
	}

	_, err = p.agent.DeleteAzrPGServerAndWait(ctx, &pbazrsvc.DeletePGServerRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        option.ResourceID,
			Namespace: option.ResourceNamespace,
		},
	})
	if err != nil {
		return err
	}
	err = p.accountAgent.DeleteTenantResource(ctx, option.NsID, string(metastoredef.AzrPostgres))
	if err != nil {
		return eris.Wrap(err, "failed to delete tenant resource in account service")
	}

	err = p.agent.DeleteK8sSecret(ctx, &pbk8ssvc.DeleteSecretRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        option.ResourceID,
			Namespace: option.ResourceNamespace,
		},
	})
	if err != nil {
		return err
	}
	return nil
}

func (p *AzrPostgresProvider) GetMetaStoreTarget(ctx context.Context, tenant model.Tenant, target metastoredef.Spec) (*Result, error) {
	option, err := toAzrPostgresOption(tenant, target)
	if err != nil {
		return nil, err
	}

	result, err := p.agent.GetAzrPGServer(ctx, &pbazrsvc.GetPGServerRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        option.ResourceID,
			Namespace: tenant.ResourceNamespace,
		},
	})
	if err != nil {
		return nil, eris.Wrap(err, "failed to get postgres")
	}
	return toMetaStoreResultAzr(result, option)
}

func (p *AzrPostgresProvider) CreateMetaStoreAndAwait(ctx context.Context, tenant model.Tenant) (*Result, error) {
	return p.CreateMetaStoreTargetAndAwait(ctx, tenant, tenant.Resources.GetMetaStore())
}

func (p *AzrPostgresProvider) StartMetaStoreAndAwait(ctx context.Context, tenant model.Tenant) error {
	return p.StartMetaStoreTargetAndAwait(ctx, tenant, tenant.Resources.GetMetaStore())
}

func (p *AzrPostgresProvider) StopMetaStoreAndAwait(ctx context.Context, tenant model.Tenant) error {
	return p.StopMetaStoreTargetAndAwait(ctx, tenant, tenant.Resources.GetMetaStore())
}

func (p *AzrPostgresProvider) DeleteMetaStoreAndAwait(ctx context.Context, tenant model.Tenant) error {
	return p.DeleteMetaStoreTargetAndAwait(ctx, tenant, tenant.Resources.GetMetaStore())
}

func (p *AzrPostgresProvider) GetMetaStore(ctx context.Context, tenant model.Tenant) (*Result, error) {
	return p.GetMetaStoreTarget(ctx, tenant, tenant.Resources.GetMetaStore())
}

func (p *AzrPostgresProvider) PostCreateMetaStore(context.Context, string) error {
	return nil
}

func (p *AzrPostgresProvider) PostStartMetaStore(context.Context, string) error {
	return nil
}

func (p *AzrPostgresProvider) PostStopMetaStore(context.Context, string) error {
	return nil
}

var _ Provider = (*AzrPostgresProvider)(nil)
