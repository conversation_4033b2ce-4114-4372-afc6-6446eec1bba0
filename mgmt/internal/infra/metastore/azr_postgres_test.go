package metastore

import (
	"context"
	"fmt"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"

	pbazr "github.com/risingwavelabs/cloudagent/pbgen/common/azr"
	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbrw "github.com/risingwavelabs/cloudagent/pbgen/common/rw"
	pbazrsvc "github.com/risingwavelabs/cloudagent/pbgen/services/azr"
	pbk8ssvc "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"

	account_mock "github.com/risingwavelabs/risingwave-cloud/internal/account/mock"
	"github.com/risingwavelabs/risingwave-cloud/internal/agent"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/querier"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenant/metastore"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenant/resourcespec"
	namespaces "github.com/risingwavelabs/risingwave-cloud/shared/namespace"
	"github.com/risingwavelabs/risingwave-cloud/shared/ptr"
)

func genTenantAzrPostgres(modifiers ...func(*model.Tenant, *resourcespec.TenantResourceSpecV1Regular)) *model.Tenant {
	resource := &resourcespec.TenantResourceSpecV1Regular{
		TenantResourceSpecV1Common: resourcespec.TenantResourceSpecV1Common{
			MetaStoreSpec: &metastore.Spec{
				Type: metastore.AzrPostgres,
				AzrPostgres: &metastore.AzrPostgresSpec{
					Sku:      "test-sku",
					SizeGb:   10,
					RwuMilli: 1000,
				},
			},
		},
	}
	tenant := &model.Tenant{
		ID:                1,
		TenantName:        "test_tenant",
		ResourceNamespace: "test_namespace",
		Resources:         resourcespec.FromValidTenantResourceSpec(resource),
	}
	for _, modifier := range modifiers {
		modifier(tenant, resource)
	}
	return tenant
}

func TestAzrPostgresProvider_CreateMetaStoreAndAwait(t *testing.T) {
	tenantID := uint64(1)
	uid := uuid.New()
	namespace := namespaces.Build(uid, "test")
	type getPGServerResult struct {
		res *pbazrsvc.GetPGServerResponse
		err error
	}
	type createPGServerResult struct {
		res *pbazrsvc.CreatePGServerResponse
		err error
	}
	resourceMeta := &pbresource.Meta{
		Id:        fmt.Sprintf("rwc-%s-azr-pg", namespaces.ToBase32(uid)),
		Namespace: namespace,
	}

	tests := []struct {
		name             string
		tenant           *model.Tenant
		want             *Result
		createMockResult *createPGServerResult
		getMockResults   []getPGServerResult
		wantCreateReq    *pbazrsvc.CreatePGServerRequest
		wantErr          bool
	}{
		{
			name: "regular case",
			tenant: genTenantAzrPostgres(func(tenant *model.Tenant, _ *resourcespec.TenantResourceSpecV1Regular) {
				tenant.ResourceNamespace = namespace
			}),
			wantCreateReq: &pbazrsvc.CreatePGServerRequest{
				ResourceMeta: resourceMeta,
				Spec: &pbazr.PGServerSpec{
					AzureName: resourceMeta.GetId(),
					Version:   "14",
					Sku: &pbazr.PGServerSku{
						Name: "test-sku",
						Tier: "Burstable",
					},
					Storage: &pbazr.PGServerStorage{
						StorageSizeGb: 10,
					},
					AdministratorLogin: fmt.Sprintf("rwc_%s_user", namespaces.ToBase32(uid)),
					AdministratorLoginPassword: &pbazr.SecretKeyRef{
						Key:  AzrPostgresPasswordSecretKey,
						Name: resourceMeta.GetId(),
					},
					Network: &pbazr.PGServerNetwork{
						DelegatedSubnet: &pbazr.ResourceReference{
							ArmId: "test-delegated-subnet",
						},
						PrivateDnsZone: &pbazr.ResourceReference{
							ArmId: "test-private-dns-zone",
						},
					},
					Tags: map[string]string{
						AZRTagKeyProject: AZRTagValueProject,
						AZRTagKeyEnvID:   "test-env-id",
					},
				},
			},
			createMockResult: &createPGServerResult{
				res: &pbazrsvc.CreatePGServerResponse{
					Status: &pbcreation.Status{
						Code: pbcreation.StatusCode_SCHEDULED,
					},
				},
			},
			getMockResults: []getPGServerResult{
				{
					res: &pbazrsvc.GetPGServerResponse{
						Status: &pbresource.Status{
							Code: pbresource.StatusCode_NOT_READY,
						},
					},
				},
				{
					res: &pbazrsvc.GetPGServerResponse{
						Status: &pbresource.Status{
							Code: pbresource.StatusCode_READY,
						},
						DomainName:  ptr.Ptr("test-postgres-domain"),
						ServerState: pbazr.PGServerState_READY,
					},
				},
			},
			want: &Result{
				Spec: &pbrw.MetaStoreSpec{
					PostgresqlBackend: &pbrw.MetaStoreBackendPostgreSql{
						Host:     "test-postgres-domain",
						Port:     AzrPostgresPort,
						Database: AzrPostgresDatabase,
						Credentials: &pbrw.PostgreSqlCredentials{
							UsernameKeyRef: AzrPostgresUsernameSecretKey,
							PasswordKeyRef: AzrPostgresPasswordSecretKey,
							SecretName:     resourceMeta.GetId(),
						},
					},
				},
				PostgreSQL: &PostgreSQLResult{
					SQLEndpoint: fmt.Sprintf(
						"postgres://%s:%s@%s:%d/%s",
						fmt.Sprintf("rwc_%s_user", namespaces.ToBase32(uid)), resourceMeta.GetId(),
						"test-postgres-domain", AzrPostgresPort, AzrPostgresDatabase,
					),
					Username: fmt.Sprintf("rwc_%s_user", namespaces.ToBase32(uid)),
					Password: resourceMeta.GetId(),
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			agentWrapper := agent.NewMockAgentWrapper(ctrl)
			accountAgent := account_mock.NewMockAgent(ctrl)

			p := &AzrPostgresProvider{
				agent:           agentWrapper.Agent,
				accountAgent:    accountAgent,
				delegatedSubnet: "test-delegated-subnet",
				privateDNSZone:  "test-private-dns-zone",
				envID:           "test-env-id",
			}
			if tt.createMockResult != nil {
				agentWrapper.K8sStub.EXPECT().CreateSecret(gomock.Any(), &pbk8ssvc.CreateSecretRequest{
					ResourceMeta: &pbresource.Meta{
						Id:        resourceMeta.GetId(),
						Namespace: namespace,
					},
					SecretSpec: &pbk8s.Secret{
						StringData: map[string]string{
							AzrPostgresUsernameSecretKey: fmt.Sprintf("rwc_%s_user", namespaces.ToBase32(uid)),
							AzrPostgresPasswordSecretKey: resourceMeta.GetId(),
						},
					},
				}).Return(&pbk8ssvc.CreateSecretResponse{Status: &pbcreation.Status{Code: pbcreation.StatusCode_CREATED}}, nil)
				agentWrapper.Querier.EXPECT().UpsertTenantCloudResource(
					gomock.Any(),
					gomock.Eq(
						querier.UpsertTenantCloudResourceParams{
							ID:              resourceMeta.GetId(),
							Namespace:       namespace,
							ResourceType:    model.CloudResourceAzrPGServer,
							TenantID:        tenantID,
							ExtraAttributes: []byte("{}"),
						},
					),
				).Return(nil)
				agentWrapper.AZRStub.EXPECT().CreatePGServer(
					gomock.Any(),
					agent.EqProto(tt.wantCreateReq),
				).Return(
					tt.createMockResult.res,
					tt.createMockResult.err,
				)
			}
			for _, r := range tt.getMockResults {
				agentWrapper.AZRStub.EXPECT().GetPGServer(
					gomock.Any(),
					agent.EqProto(&pbazrsvc.GetPGServerRequest{
						ResourceMeta: resourceMeta,
					}),
				).Return(
					r.res,
					r.err,
				)
			}
			accountAgent.EXPECT().UpsertTenantResource(gomock.Any(), uid, string(metastore.AzrPostgres), int64(1000)).Return(nil)

			got, err := p.CreateMetaStoreAndAwait(context.Background(), *tt.tenant)
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			assert.EqualValues(t, tt.want, got)
		})
	}
}
