package metastore

import (
	"context"
	"fmt"
	"strconv"

	"github.com/google/uuid"
	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/risingwave-cloud/internal/account"
	"github.com/risingwavelabs/risingwave-cloud/internal/agent"
	"github.com/risingwavelabs/risingwave-cloud/internal/managedcluster/settings"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenant/metastore"
	"github.com/risingwavelabs/risingwave-cloud/shared/namespace"
)

type Factory interface {
	GetProviderFromType(context.Context, *agent.CloudAgent, metastore.MetaStoreType, uint64) (Provider, error)
	NewProvider(context.Context, *agent.CloudAgent, model.Tenant) (Provider, error)
}

type FactoryImpl struct {
	m   *model.Model
	acc account.Agent
}

func NewFactory(m *model.Model, acc account.Agent) Factory {
	return &FactoryImpl{
		m:   m,
		acc: acc,
	}
}

func (f *FactoryImpl) GetProviderFromType(ctx context.Context, agent *agent.CloudAgent, metaStoreType metastore.MetaStoreType, clusterID uint64) (Provider, error) {
	switch metaStoreType {
	case metastore.Etcd:
		return NewEtcdProvider(EtcdConfig{
			Agent: agent,
		}), nil
	case metastore.Postgresql:
		return NewPostgreSQLProvider(PostgreSQLConfig{
			Agent: agent,
		}), nil
	case metastore.AwsRds:
		config, err := f.m.GetClusterSettings(ctx, clusterID)
		if err != nil {
			return nil, eris.Wrap(err, "failed to get managed cluster settings")
		}

		envID, isCloudCluster, err := GetEnvIDAndIsCloudCluster(ctx, f.m, clusterID, config)
		if err != nil {
			return nil, err
		}
		var rdsConfig AwsRdsConfig
		if isCloudCluster {
			rdsConfig = AwsRdsConfig{
				Agent:              agent,
				AccountAgent:       f.acc,
				DbSubnetGroupName:  config[settings.SettingsKeyAWSMetaRdsDbSubnetGroupName],
				VpcSecurityGroupID: config[settings.SettingsKeyAWSMetaRdsSecurityGroupID],
				EnvID:              envID,
			}
		} else {
			rdsConfig = AwsRdsConfig{
				Agent:              agent,
				AccountAgent:       f.acc,
				DbSubnetGroupName:  config[settings.SettingsKeyBYOCAWSRdsDbSubnetGroupName],
				VpcSecurityGroupID: config[settings.SettingsKeyBYOCAWSRdsSecurityGroupID],
				EnvID:              envID,
			}
		}
		return NewAwsRdsProvider(rdsConfig), nil

	case metastore.GcpCloudSQL:
		config, err := f.m.GetClusterSettings(ctx, clusterID)
		if err != nil {
			return nil, eris.Wrap(err, "failed to get managed cluster settings")
		}

		envID, isCloudCluster, err := GetEnvIDAndIsCloudCluster(ctx, f.m, clusterID, config)
		if err != nil {
			return nil, err
		}
		var cloudSQLConfig GCPCloudSQLConfig
		if isCloudCluster {
			cloudSQLConfig = GCPCloudSQLConfig{
				Agent:          agent,
				AccountAgent:   f.acc,
				VpcSelfLink:    config[settings.SettingsKeyGcpVpcSelfLink],
				PsaIPRangeName: config[settings.SettingsKeyGcpCloudSQLPsaIPRangeName],
				EnvID:          envID,
			}
		} else {
			cloudSQLConfig = GCPCloudSQLConfig{
				Agent:          agent,
				AccountAgent:   f.acc,
				VpcSelfLink:    config[settings.SettingsKeyBYOCGCPVpcSelfLink],
				PsaIPRangeName: config[settings.SettingsKeyBYOCGCPCloudSQLPsaIPRangeName],
				EnvID:          envID,
			}
		}
		return NewGCPCloudSQLProvider(cloudSQLConfig), nil
	case metastore.AzrPostgres:
		config, err := f.m.GetClusterSettings(ctx, clusterID)
		if err != nil {
			return nil, eris.Wrap(err, "failed to get managed cluster settings")
		}

		envID, isCloudCluster, err := GetEnvIDAndIsCloudCluster(ctx, f.m, clusterID, config)
		if err != nil {
			return nil, err
		}
		var azrPostgresConfig AzrPostgresConfig
		if isCloudCluster {
			azrPostgresConfig = AzrPostgresConfig{
				Agent:           agent,
				AccountAgent:    f.acc,
				DelegatedSubnet: config[settings.SettingsKeyAzrDb4PgDelegatedSubnetID],
				PrivateDNSZone:  config[settings.SettingsKeyAzrDb4PgPrivateDNSZoneID],
				EnvID:           envID,
			}
		} else {
			azrPostgresConfig = AzrPostgresConfig{
				Agent:           agent,
				AccountAgent:    f.acc,
				DelegatedSubnet: config[settings.SettingsKeyBYOCAZRDb4PgDelegatedSubnetID],
				PrivateDNSZone:  config[settings.SettingsKeyBYOCAZRDb4PgPrivateDNSZoneID],
				EnvID:           envID,
			}
		}
		return NewAzrPostgresProvider(azrPostgresConfig), nil
	case metastore.SharingPg:
		config, err := f.m.GetClusterSettings(ctx, clusterID)
		if err != nil {
			return nil, eris.Wrap(err, "failed to get managed cluster settings")
		}
		instances, err := loadSharingPgInstances(config)
		if err != nil {
			return nil, eris.Wrap(err, "failed to load sharing pg instances")
		}
		return NewSharingPgProvider(SharingPgConfig{
			Agent:     agent,
			Instances: instances,
		}), nil
	default:
		return nil, eris.Errorf("unknown metastore type '%s'", metaStoreType).WithCode(eris.CodeInvalidArgument)
	}
}

func (f *FactoryImpl) NewProvider(ctx context.Context, agent *agent.CloudAgent, tenant model.Tenant) (Provider, error) {
	return f.GetProviderFromType(ctx, agent, tenant.Resources.GetMetaStore().Type, tenant.ClusterID)
}

func loadSharingPgInstances(config map[string]string) (map[string]SharingPgInstance, error) {
	instances := make(map[string]SharingPgInstance)
	if err := settings.RequireAllFields([]string{settings.SettingsKeySharingPgCount}, config); err != nil {
		return nil, eris.Wrap(err, "missing required fields for sharing pg")
	}
	sharingPgCount, err := strconv.Atoi(config[settings.SettingsKeySharingPgCount])
	if err != nil {
		return nil, eris.Wrap(err, "failed to parse sharing pg count")
	}
	for i := range sharingPgCount {
		instanceID := config[fmt.Sprintf("sharing_pg_%d_instance_id", i)]
		host := config[fmt.Sprintf("sharing_pg_%d_host", i)]
		port, err := strconv.Atoi(config[fmt.Sprintf("sharing_pg_%d_port", i)])
		if err != nil {
			return nil, eris.Wrap(err, "failed to parse sharing pg port")
		}
		database := config[fmt.Sprintf("sharing_pg_%d_database", i)]
		username := config[fmt.Sprintf("sharing_pg_%d_username", i)]
		password := config[fmt.Sprintf("sharing_pg_%d_password", i)]

		instances[instanceID] = SharingPgInstance{
			Host:     host,
			Port:     port,
			Database: database,
			Username: username,
			Password: password,
		}
	}
	return instances, nil
}

type FakeFactory struct {
	provider Provider
}

func NewFakeFactory(provider Provider) Factory {
	return &FakeFactory{
		provider: provider,
	}
}

func (f FakeFactory) GetProviderFromType(_ context.Context, _ *agent.CloudAgent, _ metastore.MetaStoreType, _ uint64) (Provider, error) {
	return f.provider, nil
}

func (f FakeFactory) NewProvider(_ context.Context, _ *agent.CloudAgent, _ model.Tenant) (Provider, error) {
	return f.provider, nil
}

func GetEnvIDAndIsCloudCluster(ctx context.Context, m *model.Model, clusterID uint64, setting map[string]string) (string, bool, error) {
	cluster, err := m.GetClusterByID(ctx, clusterID)
	if err != nil {
		return "", false, eris.Wrap(err, "failed to get managed cluster")
	}
	if cluster.Org.String() == settings.CloudClusterOrg {
		return cluster.Name, true, nil
	}
	uid, err := uuid.Parse(setting[settings.SettingsKeyUUID])
	if err != nil {
		return "", false, eris.Wrap(err, "failed to parse uuid")
	}
	return namespace.ToBase32(uid), false, nil
}
