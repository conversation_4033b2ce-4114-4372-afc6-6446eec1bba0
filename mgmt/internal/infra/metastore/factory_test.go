package metastore

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"

	"github.com/risingwavelabs/risingwave-cloud/internal/managedcluster/settings"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	model_mock "github.com/risingwavelabs/risingwave-cloud/internal/model/mock"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/querier"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenant/metastore"
	"github.com/risingwavelabs/risingwave-cloud/shared/namespace"
)

func TestFactory_GetProviderFromType(t *testing.T) {
	clusterName := "test-cluster"
	clusterUUID := uuid.New()
	tests := []struct {
		name          string
		metaStoreType metastore.MetaStoreType
		mockQuerier   func(*model_mock.MockQuerier)
		want          Provider
		wantErr       bool
	}{
		{
			name:          "etcd",
			metaStoreType: metastore.Etcd,
			want:          &EtcdProvider{},
		},
		{
			name:          "postgresql",
			metaStoreType: metastore.Postgresql,
			want:          &PostgreSQLProvider{},
		},
		{
			name:          "aws_rds (cloud)",
			metaStoreType: metastore.AwsRds,
			mockQuerier: func(q *model_mock.MockQuerier) {
				q.EXPECT().GetClusterById(gomock.Any(), uint64(0)).Return(&querier.ManagedCluster{
					Name: clusterName,
					Org:  uuid.UUID{},
				}, nil)
				q.EXPECT().GetClusterSettings(gomock.Any(), uint64(0)).Return([]*querier.ManagedClusterSetting{
					{
						Key:   settings.SettingsKeyAWSMetaRdsDbSubnetGroupName,
						Value: "test-db-subnet",
					},
					{
						Key:   settings.SettingsKeyAWSMetaRdsSecurityGroupID,
						Value: "test-security-group",
					},
				}, nil)
			},
			want: &AwsRdsProvider{
				DbSubnetGroupName:  "test-db-subnet",
				VpcSecurityGroupID: "test-security-group",
				envID:              clusterName,
			},
		},
		{
			name:          "aws_rds (byoc)",
			metaStoreType: metastore.AwsRds,
			mockQuerier: func(q *model_mock.MockQuerier) {
				q.EXPECT().GetClusterById(gomock.Any(), uint64(0)).Return(&querier.ManagedCluster{
					Org: uuid.New(),
				}, nil)
				q.EXPECT().GetClusterSettings(gomock.Any(), uint64(0)).Return([]*querier.ManagedClusterSetting{
					{
						Key:   settings.SettingsKeyUUID,
						Value: clusterUUID.String(),
					},
					{
						Key:   settings.SettingsKeyBYOCAWSRdsDbSubnetGroupName,
						Value: "test-db-subnet",
					},
					{
						Key:   settings.SettingsKeyBYOCAWSRdsSecurityGroupID,
						Value: "test-security-group",
					},
				}, nil)
			},
			want: &AwsRdsProvider{
				DbSubnetGroupName:  "test-db-subnet",
				VpcSecurityGroupID: "test-security-group",
				envID:              namespace.ToBase32(clusterUUID),
			},
		},
		{
			name:          "gcp_cloudsql (cloud)",
			metaStoreType: metastore.GcpCloudSQL,
			mockQuerier: func(q *model_mock.MockQuerier) {
				q.EXPECT().GetClusterById(gomock.Any(), uint64(0)).Return(&querier.ManagedCluster{
					Name: clusterName,
					Org:  uuid.UUID{},
				}, nil)
				q.EXPECT().GetClusterSettings(gomock.Any(), uint64(0)).Return([]*querier.ManagedClusterSetting{
					{
						Key:   settings.SettingsKeyGcpVpcSelfLink,
						Value: "test-vpc-selflink",
					},
					{
						Key:   settings.SettingsKeyGcpCloudSQLPsaIPRangeName,
						Value: "test-psa-ip-range",
					},
				}, nil)
			},
			want: &GCPCloudSQLProvider{
				vpcSelfLink:    "test-vpc-selflink",
				psaIPRangeName: "test-psa-ip-range",
				envID:          clusterName,
			},
		},
		{
			name:          "gcp_cloudsql (byoc)",
			metaStoreType: metastore.GcpCloudSQL,
			mockQuerier: func(q *model_mock.MockQuerier) {
				q.EXPECT().GetClusterById(gomock.Any(), uint64(0)).Return(&querier.ManagedCluster{
					Org: uuid.New(),
				}, nil)
				q.EXPECT().GetClusterSettings(gomock.Any(), uint64(0)).Return([]*querier.ManagedClusterSetting{
					{
						Key:   settings.SettingsKeyUUID,
						Value: clusterUUID.String(),
					},
					{
						Key:   settings.SettingsKeyBYOCGCPVpcSelfLink,
						Value: "test-vpc-selflink",
					},
					{
						Key:   settings.SettingsKeyBYOCGCPCloudSQLPsaIPRangeName,
						Value: "test-psa-ip-range",
					},
				}, nil)
			},
			want: &GCPCloudSQLProvider{
				vpcSelfLink:    "test-vpc-selflink",
				psaIPRangeName: "test-psa-ip-range",
				envID:          namespace.ToBase32(clusterUUID),
			},
		},
		{
			name:          "azr_postgres (cloud)",
			metaStoreType: metastore.AzrPostgres,
			mockQuerier: func(q *model_mock.MockQuerier) {
				q.EXPECT().GetClusterById(gomock.Any(), uint64(0)).Return(&querier.ManagedCluster{
					Name: clusterName,
					Org:  uuid.UUID{},
				}, nil)
				q.EXPECT().GetClusterSettings(gomock.Any(), uint64(0)).Return([]*querier.ManagedClusterSetting{
					{
						Key:   settings.SettingsKeyAzrDb4PgDelegatedSubnetID,
						Value: "test-subnet",
					},
					{
						Key:   settings.SettingsKeyAzrDb4PgPrivateDNSZoneID,
						Value: "test-private-dns",
					},
				}, nil)
			},
			want: &AzrPostgresProvider{
				delegatedSubnet: "test-subnet",
				privateDNSZone:  "test-private-dns",
				envID:           clusterName,
			},
		},
		{
			name:          "azr_postgres (byoc)",
			metaStoreType: metastore.AzrPostgres,
			mockQuerier: func(q *model_mock.MockQuerier) {
				q.EXPECT().GetClusterById(gomock.Any(), uint64(0)).Return(&querier.ManagedCluster{
					Org: uuid.New(),
				}, nil)
				q.EXPECT().GetClusterSettings(gomock.Any(), uint64(0)).Return([]*querier.ManagedClusterSetting{
					{
						Key:   settings.SettingsKeyUUID,
						Value: clusterUUID.String(),
					},
					{
						Key:   settings.SettingsKeyBYOCAZRDb4PgDelegatedSubnetID,
						Value: "test-subnet",
					},
					{
						Key:   settings.SettingsKeyBYOCAZRDb4PgPrivateDNSZoneID,
						Value: "test-private-dns",
					},
				}, nil)
			},
			want: &AzrPostgresProvider{
				delegatedSubnet: "test-subnet",
				privateDNSZone:  "test-private-dns",
				envID:           namespace.ToBase32(clusterUUID),
			},
		},
		{
			name:          "illegal",
			metaStoreType: "illegal",
			wantErr:       true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			q := model_mock.NewMockQuerier(ctrl)

			if tt.mockQuerier != nil {
				tt.mockQuerier(q)
			}

			f := NewFactory(model.NewModel(q), nil)
			got, err := f.GetProviderFromType(context.Background(), nil, tt.metaStoreType, 0)
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			assert.EqualValues(t, tt.want, got)
		})
	}
}
