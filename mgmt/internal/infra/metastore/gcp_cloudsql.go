package metastore

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/risingwavelabs/eris"

	pbgcp "github.com/risingwavelabs/cloudagent/pbgen/common/gcp"
	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbrw "github.com/risingwavelabs/cloudagent/pbgen/common/rw"
	pbgcpsvc "github.com/risingwavelabs/cloudagent/pbgen/services/gcp"
	pbk8ssvc "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"

	"github.com/risingwavelabs/risingwave-cloud/internal/account"
	"github.com/risingwavelabs/risingwave-cloud/internal/agent"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	metastoredef "github.com/risingwavelabs/risingwave-cloud/internal/tenant/metastore"
	"github.com/risingwavelabs/risingwave-cloud/shared/namespace"
)

const (
	GCPCloudSQLPort     = 5432
	GCPCloudSQLDatabase = "postgres"
	GCPCloudSQLUser     = "postgres"

	GCPCloudSQLUsernameSecretKey = "username"
	GCPCloudSQLPasswordSecretKey = "password"

	GCPTagKeyEnvID     = "envid"
	GCPTagKeyProject   = "project"
	GCPTagValueProject = "risingwave"
)

type GCPCloudSQLConfig struct {
	Agent          *agent.CloudAgent
	AccountAgent   account.Agent
	VpcSelfLink    string
	PsaIPRangeName string
	EnvID          string
}

type GCPCloudSQLProvider struct {
	agent          *agent.CloudAgent
	accountAgent   account.Agent
	vpcSelfLink    string
	psaIPRangeName string
	envID          string
}

func NewGCPCloudSQLProvider(config GCPCloudSQLConfig) *GCPCloudSQLProvider {
	return &GCPCloudSQLProvider{
		agent:          config.Agent,
		accountAgent:   config.AccountAgent,
		vpcSelfLink:    config.VpcSelfLink,
		psaIPRangeName: config.PsaIPRangeName,
		envID:          config.EnvID,
	}
}

type GCPCloudSQLOption struct {
	TenantID          uint64
	TenantName        string
	NsID              uuid.UUID
	ResourceNamespace string

	Tier       string
	DiskSizeGb int32
	RwuMilli   int32

	ResourceID string
}

func toGCPCloudSQLOption(tenant model.Tenant, spec metastoredef.Spec) (GCPCloudSQLOption, error) {
	metaStore := spec
	if metaStore.GcpCloudSQL == nil {
		return GCPCloudSQLOption{}, eris.New("gcp cloudsql meta store not exist").WithCode(eris.CodeInvalidArgument)
	}
	uid, err := namespace.Parse(tenant.ResourceNamespace)
	if err != nil {
		return GCPCloudSQLOption{}, eris.Wrap(err, "parse namespace error")
	}

	return GCPCloudSQLOption{
		TenantID:          tenant.ID,
		TenantName:        tenant.TenantName,
		NsID:              uid,
		ResourceNamespace: tenant.ResourceNamespace,

		Tier:       metaStore.GcpCloudSQL.Tier,
		DiskSizeGb: metaStore.GcpCloudSQL.SizeGb,
		RwuMilli:   metaStore.GcpCloudSQL.RwuMilli,

		ResourceID: fmt.Sprintf("rwc-%s-cloudsql", namespace.ToBase32(uid)),
	}, nil
}

func (p *GCPCloudSQLProvider) CreateMetaStoreTargetAndAwait(ctx context.Context, tenant model.Tenant, target metastoredef.Spec) (*Result, error) {
	option, err := toGCPCloudSQLOption(tenant, target)
	if err != nil {
		return nil, err
	}

	err = p.agent.CreateK8sSecret(ctx, &pbk8ssvc.CreateSecretRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        option.ResourceID,
			Namespace: option.ResourceNamespace,
		},
		SecretSpec: &pbk8s.Secret{
			StringData: map[string]string{
				GCPCloudSQLUsernameSecretKey: GCPCloudSQLUser,
				GCPCloudSQLPasswordSecretKey: option.ResourceID, // password is same as db id
			},
		},
	})
	if err != nil {
		return nil, eris.Wrap(err, "failed to create cloudsql password secret")
	}

	result, err := p.agent.CreateGcpSQLInstanceAndWait(ctx, &pbgcpsvc.CreateSQLInstanceRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        option.ResourceID,
			Namespace: option.ResourceNamespace,
		},
		Spec: &pbgcp.SQLInstanceSpec{
			ResourceId:      option.ResourceID,
			InstanceType:    "CLOUD_SQL_INSTANCE",
			DatabaseVersion: "POSTGRES_14",
			RootPassword: &pbgcp.RootPassword{
				Value: &option.ResourceID, // password is same as db id
			},
			Settings: &pbgcp.SQLInstanceSettings{
				Tier: option.Tier,
				IpConfiguration: &pbgcp.IPConfiguration{
					Ipv4Enabled: false,
					PrivateNetworkRef: &pbgcp.PrivateNetworkRef{
						External: p.vpcSelfLink,
					},
					AllocatedIpRange: p.psaIPRangeName,
					SslMode:          pbgcp.CloudSQLSSLMode_CloudSQLSSLMode_ENCRYPTED_ONLY,
				},
				DeletionProtectionEnabled: false,
				DiskSize:                  option.DiskSizeGb,
			},
		},
		Labels: map[string]string{
			GCPTagKeyProject: GCPTagValueProject,
			GCPTagKeyEnvID:   p.envID,
		},
	}, option.TenantID)
	if err != nil {
		return nil, eris.Wrap(err, "failed to create sql instance")
	}
	err = p.accountAgent.UpsertTenantResource(ctx, option.NsID, string(metastoredef.GcpCloudSQL), int64(option.RwuMilli))
	if err != nil {
		return nil, eris.Wrap(err, "failed to create tenant resource in account service")
	}
	return toMetaStoreResultGcp(result, option)
}

func toMetaStoreResultGcp(result *pbgcpsvc.GetSQLInstanceResponse, option GCPCloudSQLOption) (*Result, error) {
	var host = ""
	var psql *PostgreSQLResult

	if result.PrivateIpAddress != nil {
		host = result.GetPrivateIpAddress()
		sqlEndpoint := fmt.Sprintf(
			"postgres://%s:%s@%s:%d/%s",
			GCPCloudSQLUser, option.ResourceID,
			host, GCPCloudSQLPort, GCPCloudSQLDatabase,
		)
		psql = &PostgreSQLResult{
			SQLEndpoint: sqlEndpoint,
			Username:    GCPCloudSQLUser,
			Password:    option.ResourceID,
		}
	}

	return &Result{
		Spec: &pbrw.MetaStoreSpec{
			PostgresqlBackend: &pbrw.MetaStoreBackendPostgreSql{
				Host:     host,
				Port:     GCPCloudSQLPort,
				Database: GCPCloudSQLDatabase,
				Credentials: &pbrw.PostgreSqlCredentials{
					UsernameKeyRef: GCPCloudSQLUsernameSecretKey,
					PasswordKeyRef: GCPCloudSQLPasswordSecretKey,
					SecretName:     option.ResourceID,
				},
			},
		},
		PostgreSQL: psql,
	}, nil
}

func (p *GCPCloudSQLProvider) StartMetaStoreTargetAndAwait(ctx context.Context, tenant model.Tenant, target metastoredef.Spec) error {
	option, err := toGCPCloudSQLOption(tenant, target)
	if err != nil {
		return err
	}
	_, err = p.agent.StartGcpSQLInstanceAndWait(ctx, &pbgcpsvc.StartSQLInstanceRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        option.ResourceID,
			Namespace: option.ResourceNamespace,
		},
	})
	if err != nil {
		return err
	}
	return nil
}

func (p *GCPCloudSQLProvider) StopMetaStoreTargetAndAwait(ctx context.Context, tenant model.Tenant, target metastoredef.Spec) error {
	option, err := toGCPCloudSQLOption(tenant, target)
	if err != nil {
		return err
	}
	_, err = p.agent.StopGcpSQLInstanceAndWait(ctx, &pbgcpsvc.StopSQLInstanceRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        option.ResourceID,
			Namespace: option.ResourceNamespace,
		},
	})
	if err != nil {
		return err
	}
	return nil
}

func (p *GCPCloudSQLProvider) DeleteMetaStoreTargetAndAwait(ctx context.Context, tenant model.Tenant, target metastoredef.Spec) error {
	option, err := toGCPCloudSQLOption(tenant, target)
	if err != nil {
		return err
	}

	_, err = p.agent.DeleteGcpSQLInstanceAndWait(ctx, &pbgcpsvc.DeleteSQLInstanceRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        option.ResourceID,
			Namespace: option.ResourceNamespace,
		},
	})
	if err != nil {
		return err
	}
	err = p.accountAgent.DeleteTenantResource(ctx, option.NsID, string(metastoredef.GcpCloudSQL))
	if err != nil {
		return eris.Wrap(err, "failed to delete tenant resource in account service")
	}

	err = p.agent.DeleteK8sSecret(ctx, &pbk8ssvc.DeleteSecretRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        option.ResourceID,
			Namespace: option.ResourceNamespace,
		},
	})
	if err != nil {
		return err
	}
	return nil
}

func (p *GCPCloudSQLProvider) GetMetaStoreTarget(ctx context.Context, tenant model.Tenant, target metastoredef.Spec) (*Result, error) {
	option, err := toGCPCloudSQLOption(tenant, target)
	if err != nil {
		return nil, err
	}

	result, err := p.agent.GetGcpSQLInstance(ctx, &pbgcpsvc.GetSQLInstanceRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        option.ResourceID,
			Namespace: tenant.ResourceNamespace,
		},
	})
	if err != nil {
		return nil, eris.Wrap(err, "failed to get sql instance")
	}
	return toMetaStoreResultGcp(result, option)
}

func (p *GCPCloudSQLProvider) CreateMetaStoreAndAwait(ctx context.Context, tenant model.Tenant) (*Result, error) {
	return p.CreateMetaStoreTargetAndAwait(ctx, tenant, tenant.Resources.GetMetaStore())
}

func (p *GCPCloudSQLProvider) StartMetaStoreAndAwait(ctx context.Context, tenant model.Tenant) error {
	return p.StartMetaStoreTargetAndAwait(ctx, tenant, tenant.Resources.GetMetaStore())
}

func (p *GCPCloudSQLProvider) StopMetaStoreAndAwait(ctx context.Context, tenant model.Tenant) error {
	return p.StopMetaStoreTargetAndAwait(ctx, tenant, tenant.Resources.GetMetaStore())
}

func (p *GCPCloudSQLProvider) DeleteMetaStoreAndAwait(ctx context.Context, tenant model.Tenant) error {
	return p.DeleteMetaStoreTargetAndAwait(ctx, tenant, tenant.Resources.GetMetaStore())
}

func (p *GCPCloudSQLProvider) GetMetaStore(ctx context.Context, tenant model.Tenant) (*Result, error) {
	return p.GetMetaStoreTarget(ctx, tenant, tenant.Resources.GetMetaStore())
}

func (p *GCPCloudSQLProvider) PostCreateMetaStore(context.Context, string) error {
	return nil
}

func (p *GCPCloudSQLProvider) PostStartMetaStore(context.Context, string) error {
	return nil
}

func (p *GCPCloudSQLProvider) PostStopMetaStore(context.Context, string) error {
	return nil
}

var _ Provider = (*GCPCloudSQLProvider)(nil)
