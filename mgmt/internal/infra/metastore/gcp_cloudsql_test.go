package metastore

import (
	"context"
	"fmt"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"

	pbgcp "github.com/risingwavelabs/cloudagent/pbgen/common/gcp"
	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbrw "github.com/risingwavelabs/cloudagent/pbgen/common/rw"
	pbgcpsvc "github.com/risingwavelabs/cloudagent/pbgen/services/gcp"
	pbk8ssvc "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"

	account_mock "github.com/risingwavelabs/risingwave-cloud/internal/account/mock"
	"github.com/risingwavelabs/risingwave-cloud/internal/agent"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/querier"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenant/metastore"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenant/resourcespec"
	namespaces "github.com/risingwavelabs/risingwave-cloud/shared/namespace"
	"github.com/risingwavelabs/risingwave-cloud/shared/ptr"
)

func genTenantGcpSQLCloud(modifiers ...func(*model.Tenant, *resourcespec.TenantResourceSpecV1Regular)) *model.Tenant {
	resource := &resourcespec.TenantResourceSpecV1Regular{
		TenantResourceSpecV1Common: resourcespec.TenantResourceSpecV1Common{
			MetaStoreSpec: &metastore.Spec{
				Type: metastore.GcpCloudSQL,
				GcpCloudSQL: &metastore.GcpCloudSQLSpec{
					Tier:     "test-tier",
					SizeGb:   10,
					RwuMilli: 1000,
				},
			},
		},
	}
	tenant := &model.Tenant{
		ID:                1,
		TenantName:        "test_tenant",
		ResourceNamespace: "test_namespace",
		Resources:         resourcespec.FromValidTenantResourceSpec(resource),
	}
	for _, modifier := range modifiers {
		modifier(tenant, resource)
	}
	return tenant
}

func TestGcpCloudSqlProvider_CreateMetaStoreAndAwait(t *testing.T) {
	tenantID := uint64(1)
	uid := uuid.New()
	namespace := namespaces.Build(uid, "test")
	type getSQLInstanceResult struct {
		res *pbgcpsvc.GetSQLInstanceResponse
		err error
	}
	type createSQLInstanceResult struct {
		res *pbgcpsvc.CreateSQLInstanceResponse
		err error
	}
	resourceMeta := &pbresource.Meta{
		Id:        fmt.Sprintf("rwc-%s-cloudsql", namespaces.ToBase32(uid)),
		Namespace: namespace,
	}

	tests := []struct {
		name             string
		tenant           *model.Tenant
		want             *Result
		createMockResult *createSQLInstanceResult
		getMockResults   []getSQLInstanceResult
		wantCreateReq    *pbgcpsvc.CreateSQLInstanceRequest
		wantErr          bool
	}{
		{
			name: "regular case",
			tenant: genTenantGcpSQLCloud(func(tenant *model.Tenant, _ *resourcespec.TenantResourceSpecV1Regular) {
				tenant.ResourceNamespace = namespace
			}),
			wantCreateReq: &pbgcpsvc.CreateSQLInstanceRequest{
				ResourceMeta: resourceMeta,
				Spec: &pbgcp.SQLInstanceSpec{
					ResourceId:      resourceMeta.GetId(),
					InstanceType:    "CLOUD_SQL_INSTANCE",
					DatabaseVersion: "POSTGRES_14",
					RootPassword: &pbgcp.RootPassword{
						Value: ptr.Ptr(resourceMeta.GetId()),
					},
					Settings: &pbgcp.SQLInstanceSettings{
						Tier: "test-tier",
						IpConfiguration: &pbgcp.IPConfiguration{
							Ipv4Enabled: false,
							PrivateNetworkRef: &pbgcp.PrivateNetworkRef{
								External: "test-vpc-self-link",
							},
							AllocatedIpRange: "test-psa-ip-range",
							SslMode:          pbgcp.CloudSQLSSLMode_CloudSQLSSLMode_ENCRYPTED_ONLY,
						},
						DeletionProtectionEnabled: false,
						DiskSize:                  10,
					},
				},
				Labels: map[string]string{
					GCPTagKeyProject: GCPTagValueProject,
					GCPTagKeyEnvID:   "test-env-id",
				},
			},
			createMockResult: &createSQLInstanceResult{
				res: &pbgcpsvc.CreateSQLInstanceResponse{
					Status: &pbcreation.Status{
						Code: pbcreation.StatusCode_SCHEDULED,
					},
				},
			},
			getMockResults: []getSQLInstanceResult{
				{
					res: &pbgcpsvc.GetSQLInstanceResponse{
						Status: &pbresource.Status{
							Code: pbresource.StatusCode_NOT_READY,
						},
					},
				},
				{
					res: &pbgcpsvc.GetSQLInstanceResponse{
						Status: &pbresource.Status{
							Code: pbresource.StatusCode_READY,
						},
						PrivateIpAddress: ptr.Ptr("test-cloudsql-ip"),
						InstanceStatus:   pbgcp.SQLInstanceStatus_UP_TO_DATE,
					},
				},
			},
			want: &Result{
				Spec: &pbrw.MetaStoreSpec{
					PostgresqlBackend: &pbrw.MetaStoreBackendPostgreSql{
						Host:     "test-cloudsql-ip",
						Port:     GCPCloudSQLPort,
						Database: GCPCloudSQLDatabase,
						Credentials: &pbrw.PostgreSqlCredentials{
							UsernameKeyRef: GCPCloudSQLUsernameSecretKey,
							PasswordKeyRef: GCPCloudSQLPasswordSecretKey,
							SecretName:     resourceMeta.GetId(),
						},
					},
				},
				PostgreSQL: &PostgreSQLResult{
					SQLEndpoint: fmt.Sprintf(
						"postgres://%s:%s@%s:%d/%s",
						GCPCloudSQLUser, resourceMeta.GetId(),
						"test-cloudsql-ip", GCPCloudSQLPort, GCPCloudSQLDatabase,
					),
					Username: GCPCloudSQLUser,
					Password: resourceMeta.GetId(),
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			agentWrapper := agent.NewMockAgentWrapper(ctrl)
			accountAgent := account_mock.NewMockAgent(ctrl)

			p := &GCPCloudSQLProvider{
				agent:          agentWrapper.Agent,
				accountAgent:   accountAgent,
				vpcSelfLink:    "test-vpc-self-link",
				psaIPRangeName: "test-psa-ip-range",
				envID:          "test-env-id",
			}
			if tt.createMockResult != nil {
				agentWrapper.K8sStub.EXPECT().CreateSecret(gomock.Any(), &pbk8ssvc.CreateSecretRequest{
					ResourceMeta: &pbresource.Meta{
						Id:        resourceMeta.GetId(),
						Namespace: namespace,
					},
					SecretSpec: &pbk8s.Secret{
						StringData: map[string]string{
							GCPCloudSQLUsernameSecretKey: GCPCloudSQLUser,
							GCPCloudSQLPasswordSecretKey: resourceMeta.GetId(),
						},
					},
				}).Return(&pbk8ssvc.CreateSecretResponse{Status: &pbcreation.Status{Code: pbcreation.StatusCode_CREATED}}, nil)
				agentWrapper.Querier.EXPECT().UpsertTenantCloudResource(
					gomock.Any(),
					gomock.Eq(
						querier.UpsertTenantCloudResourceParams{
							ID:              resourceMeta.GetId(),
							Namespace:       namespace,
							ResourceType:    model.CloudResourceGcpCloudSQL,
							TenantID:        tenantID,
							ExtraAttributes: []byte("{}"),
						},
					),
				).Return(nil)
				agentWrapper.GCPStub.EXPECT().CreateSQLInstance(
					gomock.Any(),
					agent.EqProto(tt.wantCreateReq),
				).Return(
					tt.createMockResult.res,
					tt.createMockResult.err,
				)
			}
			for _, r := range tt.getMockResults {
				agentWrapper.GCPStub.EXPECT().GetSQLInstance(
					gomock.Any(),
					agent.EqProto(&pbgcpsvc.GetSQLInstanceRequest{
						ResourceMeta: resourceMeta,
					}),
				).Return(
					r.res,
					r.err,
				)
			}
			accountAgent.EXPECT().UpsertTenantResource(gomock.Any(), uid, string(metastore.GcpCloudSQL), int64(1000)).Return(nil)

			got, err := p.CreateMetaStoreAndAwait(context.Background(), *tt.tenant)
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			assert.EqualValues(t, tt.want, got)
		})
	}
}
