package network

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	"github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pbgen/services/aws"
	pbk8ssvc "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/risingwave-cloud/internal/agent"
	"github.com/risingwavelabs/risingwave-cloud/shared/namespace"
	"github.com/risingwavelabs/risingwave-cloud/shared/tenant/tier"
)

const (
	RWSecurityGroupIDEnvKey = "RW_VPC_SECURITY_GROUP_ID"
	AWSTagEnvKey            = "EnvID"
)

type AWSProvider struct {
	agent             *agent.CloudAgent
	tenantVPCESubnets []string
	vpcCIDRRange      string
	clusterName       string
	byocClusterUID    uuid.UUID
}

type AWSConfig struct {
	Agent             *agent.CloudAgent
	TenantVPCESubnets []string
	VPCCIDRRange      string
	ClusterName       string
	BYOCClusterUID    uuid.UUID
}

func NewAWSProvider(cfg *AWSConfig) *AWSProvider {
	return &AWSProvider{
		agent:             cfg.Agent,
		tenantVPCESubnets: cfg.TenantVPCESubnets,
		vpcCIDRRange:      cfg.VPCCIDRRange,
		clusterName:       cfg.ClusterName,
		byocClusterUID:    cfg.BYOCClusterUID,
	}
}

func rwsgResourceIDDeprecated(resourceNamespace string) string {
	return fmt.Sprintf("%s-rwsg", resourceNamespace)
}

func rwsgResourceID(resourceNamespace string) (string, error) {
	uuidBase32, err := namespace.ParseToBase32(resourceNamespace)
	if err != nil {
		return "", eris.Wrapf(err, "failed to extract uuid from namsespace: %v", resourceNamespace)
	}
	return fmt.Sprintf("%s-rwsg", uuidBase32), nil
}

func epsgResourceIDDeprecated(resourceNamespace string) string {
	return fmt.Sprintf("%s-epsg", resourceNamespace)
}

func epsgResourceID(resourceNamespace string) (string, error) {
	uuidBase32, err := namespace.ParseToBase32(resourceNamespace)
	if err != nil {
		return "", eris.Wrapf(err, "failed to extract uuid from namsespace: %v", resourceNamespace)
	}
	return fmt.Sprintf("%s-epsg", uuidBase32), nil
}

func sgpolicyResourceIDDeprecated(resourceNamespace string) string {
	return fmt.Sprintf("%s-policy", resourceNamespace)
}

func sgpolicyResourceID(resourceNamespace string) (string, error) {
	uuidBase32, err := namespace.ParseToBase32(resourceNamespace)
	if err != nil {
		return "", eris.Wrapf(err, "failed to extract uuid from namsespace: %v", resourceNamespace)
	}
	return fmt.Sprintf("%s-policy", uuidBase32), nil
}

func (p *AWSProvider) CreateTenantFirewallAwait(ctx context.Context, option CreateTenantFirewallOption) error {
	rwSecurityGroupID, err := rwsgResourceID(option.ResourceNamespace)
	if err != nil {
		return errors.Wrapf(err, "failed to create ID for RW security group")
	}
	rwsgRes, err := p.agent.CreateSecurityGroupAwait(ctx, &aws.CreateSecurityGroupRequest{
		ResourceMeta: &resource.Meta{
			Id:        rwSecurityGroupID,
			Namespace: option.ResourceNamespace,
		},
		InboundIpPermissions: []*aws.IPPermission{
			{
				Protocol:    "-1",
				Cidrs:       []string{p.vpcCIDRRange},
				FromPort:    0,
				ToPort:      0,
				Description: "allow all traffic",
			},
		},
		OutboundIpPermissions: []*aws.IPPermission{
			{
				Protocol:    "-1",
				Cidrs:       []string{"0.0.0.0/0"},
				FromPort:    0,
				ToPort:      0,
				Description: "allow all traffic",
			},
		},
	}, option.TenantID)
	if err != nil {
		return errors.Wrapf(err, "failed to create security group for risingwave: %s", option.ResourceNamespace)
	}

	epSecurityGroupID, err := epsgResourceID(option.ResourceNamespace)
	if err != nil {
		return errors.Wrapf(err, "failed to create ID for endpoint security group")
	}
	epsgRes, err := p.agent.CreateSecurityGroupAwait(ctx, &aws.CreateSecurityGroupRequest{
		ResourceMeta: &resource.Meta{
			Id:        epSecurityGroupID,
			Namespace: option.ResourceNamespace,
		},
		InboundIpPermissions: []*aws.IPPermission{
			{
				Protocol:               "-1",
				FromPort:               0,
				ToPort:                 0,
				Description:            "allow all traffic",
				SourceSecurityGroupIds: []string{rwsgRes.GetSecurityGroupId()},
			},
		},
		OutboundIpPermissions: []*aws.IPPermission{
			{
				Protocol:               "-1",
				FromPort:               0,
				ToPort:                 0,
				Description:            "allow all traffic",
				SourceSecurityGroupIds: []string{rwsgRes.GetSecurityGroupId()},
			},
		},
	}, option.TenantID)
	if err != nil {
		return errors.Wrapf(err, "failed to create security group for endpoint: %s", option.ResourceNamespace)
	}
	if epsgRes.GetSecurityGroupId() == "" {
		return errors.Wrapf(err, "missing sg ID in epsgRes: %s", option.ResourceNamespace)
	}

	sgPolicyID, err := sgpolicyResourceID(option.ResourceNamespace)
	if err != nil {
		return errors.Wrapf(err, "failed to create ID for security group policy")
	}
	_, err = p.agent.CreateSecurityGroupPolicy(ctx, &aws.CreateSecurityGroupPolicyRequest{
		ResourceMeta: &resource.Meta{
			Id:        sgPolicyID,
			Namespace: option.ResourceNamespace,
		},
		SecurityGroupIds:  []string{rwsgRes.GetSecurityGroupId()},
		PodLabelsSelector: option.PodLabelSelector,
	}, option.TenantID)
	if err != nil {
		return errors.Wrapf(err, "failed to create security group policy: %s", option.ResourceNamespace)
	}

	err = p.createK8sNetworkPolicy(ctx, option.ResourceNamespace)
	if err != nil {
		return errors.Wrapf(err, "failed to create k8s network policy: %s", option.ResourceNamespace)
	}
	return nil
}

func (p *AWSProvider) ApplyDefaultK8sNetworkPolicy(ctx context.Context, namespace string) error {
	err := p.agent.CreateOrUpdateNetworkPolicy(ctx, &pbk8ssvc.CreateOrUpdateNetworkPolicyRequest{
		ResourceMeta: getDefaultNetworkPolicyResourceMeta(namespace),
		Spec:         p.getDefaultK8sNetworkPolicySpec(namespace),
	})
	if err != nil {
		return eris.Wrap(err, "failed to create k8s network policy")
	}
	return nil
}

func (p *AWSProvider) getDefaultK8sNetworkPolicySpec(namespace string) *pbk8s.NetworkPolicySpec {
	return &pbk8s.NetworkPolicySpec{
		PodSelector: &pbk8s.LabelSelector{},
		Ingress:     createTenantIngressPolicyPeers(namespace),
		PolicyTypes: []pbk8s.NetworkPolicyType{pbk8s.NetworkPolicyType_NETWORK_POLICY_INGRESS},
	}
}

func (p *AWSProvider) createK8sNetworkPolicy(ctx context.Context, namespace string) error {
	err := p.agent.CreateNetworkPolicy(ctx, &pbk8ssvc.CreateNetworkPolicyRequest{
		ResourceMeta: getDefaultNetworkPolicyResourceMeta(namespace),
		Spec:         p.getDefaultK8sNetworkPolicySpec(namespace),
	})
	if err != nil {
		return eris.Wrap(err, "failed to create k8s network policy")
	}
	return nil
}

func (p *AWSProvider) DeleteTenantFirewallAwait(ctx context.Context, tenantName, resourceNamespace string, _ uint64) error {
	epSecurityGroupID, err := epsgResourceID(resourceNamespace)
	if err != nil {
		return errors.Wrapf(err, "failed to create ID for endpoint security group")
	}
	_, err = p.agent.DeleteSecurityGroupAwait(ctx, &aws.DeleteSecurityGroupRequest{
		ResourceMeta: &resource.Meta{
			Id:        epSecurityGroupID,
			Namespace: resourceNamespace,
		},
	})
	if err != nil {
		return errors.Wrapf(err, "failed to delete security group for endpoint: %s", resourceNamespace)
	}

	rwSecurityGroupID, err := rwsgResourceID(resourceNamespace)
	if err != nil {
		return errors.Wrapf(err, "failed to create ID for RW security group")
	}
	_, err = p.agent.DeleteSecurityGroupAwait(ctx, &aws.DeleteSecurityGroupRequest{
		ResourceMeta: &resource.Meta{
			Id:        rwSecurityGroupID,
			Namespace: resourceNamespace,
		},
	})
	if err != nil {
		return errors.Wrapf(err, "failed to delete security group for risingwave: %s", resourceNamespace)
	}

	sgPolicyID, err := sgpolicyResourceID(resourceNamespace)
	if err != nil {
		return errors.Wrapf(err, "failed to create ID for security group policy")
	}
	_, err = p.agent.DeleteSecurityGroupPolicy(ctx, &aws.DeleteSecurityGroupPolicyRequest{
		ResourceMeta: &resource.Meta{
			Id:        sgPolicyID,
			Namespace: resourceNamespace,
		},
	})
	if err != nil {
		return errors.Wrapf(err, "failed to delete security group policy: %s", resourceNamespace)
	}

	return p.deleteTenantFirewallAwaitDeprecated(ctx, tenantName, resourceNamespace)
}

func (p *AWSProvider) deleteTenantFirewallAwaitDeprecated(ctx context.Context, _, resourceNamespace string) error {
	_, err := p.agent.DeleteSecurityGroupAwait(ctx, &aws.DeleteSecurityGroupRequest{
		ResourceMeta: &resource.Meta{
			Id:        epsgResourceIDDeprecated(resourceNamespace),
			Namespace: resourceNamespace,
		},
	})
	if err != nil {
		return errors.Wrapf(err, "failed to delete deprecated security group for endpoint: %s", resourceNamespace)
	}

	_, err = p.agent.DeleteSecurityGroupAwait(ctx, &aws.DeleteSecurityGroupRequest{
		ResourceMeta: &resource.Meta{
			Id:        rwsgResourceIDDeprecated(resourceNamespace),
			Namespace: resourceNamespace,
		},
	})
	if err != nil {
		return errors.Wrapf(err, "failed to delete security group for risingwave: %s", resourceNamespace)
	}

	_, err = p.agent.DeleteSecurityGroupPolicy(ctx, &aws.DeleteSecurityGroupPolicyRequest{
		ResourceMeta: &resource.Meta{
			Id:        sgpolicyResourceIDDeprecated(resourceNamespace),
			Namespace: resourceNamespace,
		},
	})
	if err != nil {
		return errors.Wrapf(err, "failed to delete security group policy: %s", resourceNamespace)
	}

	return nil
}

func (p *AWSProvider) getEndpointSecurityGroupInner(ctx context.Context, id, ns string) (*aws.GetSecurityGroupResponse, error) {
	sg, err := p.agent.GetSecurityGroup(ctx, &aws.GetSecurityGroupRequest{
		ResourceMeta: &resource.Meta{
			Id:        id,
			Namespace: ns,
		},
	})
	if err != nil {
		return nil, eris.Wrapf(err, "error getting security group for vpc endpoint")
	}
	if sg.GetStatus().GetCode() == resource.StatusCode_READY {
		return sg, nil
	}
	if sg.GetStatus().GetCode() == resource.StatusCode_NOT_FOUND {
		return nil, eris.Errorf("endpoint security group is not found, id: %v, res: %v", id, sg).WithCode(eris.CodeNotFound)
	}
	return nil, eris.Errorf("endpoint security group is in unexpected status, res: %v", sg)
}

func (p *AWSProvider) getEndpointSecurityGroup(ctx context.Context, ns string) (*aws.GetSecurityGroupResponse, error) {
	epsgID, err := epsgResourceID(ns)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to create ID for endpoint security group")
	}
	sg, err := p.getEndpointSecurityGroupInner(ctx, epsgID, ns)
	if err != nil {
		// If the sg is not found, fall back to the legacy name.
		if eris.GetCode(err) == eris.CodeNotFound {
			return p.getEndpointSecurityGroupInner(ctx, epsgResourceIDDeprecated(ns), ns)
		}
		return nil, eris.Wrapf(err, "endpoint security group is in unexpected status, id: %v", epsgID)
	}

	return sg, nil
}

func (p *AWSProvider) CreatePrivateLinkAndWait(ctx context.Context, option CreatePrivateLinkOption) (*PrivateLinkMeta, error) {
	if len(p.tenantVPCESubnets) == 0 {
		return nil, eris.New("privatelink is not supported in the cluster").WithCode(eris.CodeUnimplemented)
	}
	ns := option.Namespace
	id := option.PrivateLinkID
	tenantID := option.TenantID
	resourceName, err := createResourceNameFromPrivateLinkID(ns, id)
	if err != nil {
		return nil, eris.Wrapf(err, "error creating privatelink %v name", id)
	}

	sg, err := p.getEndpointSecurityGroup(ctx, ns)
	if err != nil {
		return nil, eris.Wrapf(err, "error getting security group for vpc endpoint")
	}

	tagEnvValue := p.clusterName
	if option.Tier == string(tier.BYOC) {
		tagEnvValue = namespace.ToBase32(p.byocClusterUID)
	}
	plRes, err := p.agent.CreateAWSPrivateLinkAndWait(ctx, &aws.CreateVPCEndpointRequest{
		ResourceMeta: &resource.Meta{
			Id:        resourceName,
			Namespace: ns,
		},
		ServiceName:      option.Target,
		SubnetIds:        p.tenantVPCESubnets,
		SecurityGroupIds: []string{sg.GetSecurityGroupId()},
		ExtraTags: map[string]string{
			AWSTagEnvKey: tagEnvValue,
		},
	},
		tenantID,
	)
	if err != nil {
		return nil, err
	}

	connectionState := vpcEndpointStateFromSpec(plRes.GetEndpointState())

	return &PrivateLinkMeta{
		Endpoint:        plRes.GetEndpointDns(),
		ConnectionState: connectionState,
	}, nil
}

func (p *AWSProvider) DeletePrivateLinkAndWait(ctx context.Context, namespace, privateLinkID string) error {
	privateLinkName, err := createResourceNameFromPrivateLinkID(namespace, privateLinkID)
	if err != nil {
		return eris.Wrap(err, "failed to generate private link name")
	}

	_, err = p.agent.DeleteAWSPrivateLinkAndWait(ctx, &aws.DeleteVPCEndpointRequest{
		ResourceMeta: &resource.Meta{
			Id:        privateLinkName,
			Namespace: namespace,
		},
	})
	if err != nil {
		return eris.Wrapf(err, "could not delete private link")
	}

	return nil
}

func (p *AWSProvider) GetPrivateLink(ctx context.Context, namespace, privateLinkID string) (*PrivateLinkMeta, error) {
	privateLinkName, err := createResourceNameFromPrivateLinkID(namespace, privateLinkID)
	if err != nil {
		return nil, eris.Wrap(err, "failed to generate private link name")
	}

	res, err := p.agent.GetAWSPrivateLink(ctx, &aws.GetVPCEndpointRequest{
		ResourceMeta: &resource.Meta{
			Id:        privateLinkName,
			Namespace: namespace,
		},
	})
	if err != nil {
		return nil, eris.Wrapf(err, "could not get private link")
	}
	if res.GetStatus().GetCode() == resource.StatusCode_NOT_FOUND {
		return nil, eris.Errorf("privatelink doesn't exist, id: %s, ns: %s", privateLinkID, namespace).WithCode(eris.CodeNotFound)
	}

	connectionState := vpcEndpointStateFromSpec(res.GetEndpointState())

	return &PrivateLinkMeta{
		Endpoint:        res.GetEndpointDns(),
		ConnectionState: connectionState,
	}, nil
}

func vpcEndpointStateFromSpec(status aws.VPCEndpointStatus) PrivateLinkConnectionState {
	switch status {
	case aws.VPCEndpointStatus_AVAILABLE:
		return PrivateLinkConnectionAccepted
	case aws.VPCEndpointStatus_PENDING, aws.VPCEndpointStatus_PENDING_ACCEPTANCE:
		return PrivateLinkConnectionPending
	case aws.VPCEndpointStatus_REJECTED, aws.VPCEndpointStatus_FAILED:
		return PrivateLinkConnectionRejected
	case aws.VPCEndpointStatus_DELETED, aws.VPCEndpointStatus_DELETING, aws.VPCEndpointStatus_EXPIRED:
		return PrivateLinkConnectionClosed
	case aws.VPCEndpointStatus_STATUS_UNSPECIFIED:
		return PrivateLinkConnectionUnspecified
	}
	return PrivateLinkConnectionUnspecified
}
