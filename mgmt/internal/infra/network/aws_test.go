package network

import (
	"context"
	"testing"

	"github.com/google/uuid"
	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	"github.com/risingwavelabs/cloudagent/pbgen/services/aws"
	pbsvck8s "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	"github.com/risingwavelabs/eris"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"

	"github.com/risingwavelabs/risingwave-cloud/cli/operations/byoc/utils/namespace"
	"github.com/risingwavelabs/risingwave-cloud/internal/agent"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/podstrategy"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/querier"
	"github.com/risingwavelabs/risingwave-cloud/shared/tenant/tier"
)

const (
	testClusterName = "test-cluster"
	testNamespace   = "rwc-g1h8bikpmjft4bobe659bf95b5-test"
	testTenantID    = 1
	testCIDR        = "10.0.0.0/8"
)

var (
	testLabelSelector = map[string]string{"foo": "bar"}
)

func TestCreateSecurityGroup(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	agentWrapper := agent.NewMockAgentWrapper(ctrl)
	agentWrapper.Querier.EXPECT().UpsertTenantCloudResource(
		gomock.Any(),
		gomock.Eq(
			querier.UpsertTenantCloudResourceParams{
				ID:              "g1h8bikpmjft4bobe659bf95b5-rwsg",
				Namespace:       testNamespace,
				ResourceType:    model.CloudResourceAWSSecurityGroup,
				TenantID:        testTenantID,
				ExtraAttributes: []byte("{}"),
			},
		),
	).Return(nil)
	agentWrapper.Querier.EXPECT().UpsertTenantCloudResource(
		gomock.Any(),
		gomock.Eq(
			querier.UpsertTenantCloudResourceParams{
				ID:              "g1h8bikpmjft4bobe659bf95b5-epsg",
				Namespace:       testNamespace,
				ResourceType:    model.CloudResourceAWSSecurityGroup,
				TenantID:        testTenantID,
				ExtraAttributes: []byte("{}"),
			},
		),
	).Return(nil)
	agentWrapper.Querier.EXPECT().UpsertTenantCloudResource(
		gomock.Any(),
		gomock.Eq(
			querier.UpsertTenantCloudResourceParams{
				ID:              "g1h8bikpmjft4bobe659bf95b5-policy",
				Namespace:       testNamespace,
				ResourceType:    model.CloudResourceAWSSecurityGroupPolicy,
				TenantID:        testTenantID,
				ExtraAttributes: []byte("{}"),
			},
		),
	).Return(nil)

	agentWrapper.AWSStub.EXPECT().CreateSecurityGroup(
		gomock.Any(),
		agent.EqProto(&aws.CreateSecurityGroupRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        "g1h8bikpmjft4bobe659bf95b5-rwsg",
				Namespace: testNamespace,
			},
			InboundIpPermissions: []*aws.IPPermission{
				{
					Protocol:    "-1",
					Cidrs:       []string{testCIDR},
					FromPort:    0,
					ToPort:      0,
					Description: "allow all traffic",
				},
			},
			OutboundIpPermissions: []*aws.IPPermission{
				{
					Protocol:    "-1",
					Cidrs:       []string{"0.0.0.0/0"},
					FromPort:    0,
					ToPort:      0,
					Description: "allow all traffic",
				},
			},
		}),
	).Return(
		&aws.CreateSecurityGroupResponse{
			Status: &pbcreation.Status{
				Code: pbcreation.StatusCode_SCHEDULED,
			},
		},
		nil,
	)
	agentWrapper.AWSStub.EXPECT().GetSecurityGroup(
		gomock.Any(),
		agent.EqProto(&aws.GetSecurityGroupRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        "g1h8bikpmjft4bobe659bf95b5-rwsg",
				Namespace: testNamespace,
			},
		}),
	).Return(
		&aws.GetSecurityGroupResponse{
			Status: &pbresource.Status{
				Code: pbresource.StatusCode_READY,
			},
			SecurityGroupId: "idrw",
		},
		nil,
	)

	agentWrapper.AWSStub.EXPECT().CreateSecurityGroup(
		gomock.Any(),
		agent.EqProto(&aws.CreateSecurityGroupRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        "g1h8bikpmjft4bobe659bf95b5-epsg",
				Namespace: testNamespace,
			},
			InboundIpPermissions: []*aws.IPPermission{
				{
					Protocol:               "-1",
					FromPort:               0,
					ToPort:                 0,
					Description:            "allow all traffic",
					SourceSecurityGroupIds: []string{"idrw"},
				},
			},
			OutboundIpPermissions: []*aws.IPPermission{
				{
					Protocol:               "-1",
					FromPort:               0,
					ToPort:                 0,
					Description:            "allow all traffic",
					SourceSecurityGroupIds: []string{"idrw"},
				},
			},
		}),
	).Return(
		&aws.CreateSecurityGroupResponse{
			Status: &pbcreation.Status{
				Code: pbcreation.StatusCode_SCHEDULED,
			},
		},
		nil,
	)
	agentWrapper.AWSStub.EXPECT().GetSecurityGroup(
		gomock.Any(),
		agent.EqProto(&aws.GetSecurityGroupRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        "g1h8bikpmjft4bobe659bf95b5-epsg",
				Namespace: testNamespace,
			},
		}),
	).Return(
		&aws.GetSecurityGroupResponse{
			Status: &pbresource.Status{
				Code: pbresource.StatusCode_READY,
			},
			SecurityGroupId: "idep",
		},
		nil,
	)

	agentWrapper.AWSStub.EXPECT().CreateSecurityGroupPolicy(
		gomock.Any(),
		agent.EqProto(&aws.CreateSecurityGroupPolicyRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        "g1h8bikpmjft4bobe659bf95b5-policy",
				Namespace: testNamespace,
			},
			SecurityGroupIds:  []string{"idrw"},
			PodLabelsSelector: testLabelSelector,
		}),
	).Return(
		&aws.CreateSecurityGroupPolicyResponse{
			Status: &pbcreation.Status{
				Code: pbcreation.StatusCode_SCHEDULED,
			},
		},
		nil,
	)
	agentWrapper.AWSStub.EXPECT().GetSecurityGroupPolicy(
		gomock.Any(),
		agent.EqProto(&aws.GetSecurityGroupPolicyRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        "g1h8bikpmjft4bobe659bf95b5-policy",
				Namespace: testNamespace,
			},
		}),
	).Return(
		&aws.GetSecurityGroupPolicyResponse{
			Status: &pbresource.Status{
				Code: pbresource.StatusCode_READY,
			},
		},
		nil,
	)

	agentWrapper.K8sStub.EXPECT().CreateNetworkPolicy(
		gomock.Any(),
		agent.EqProto(&pbsvck8s.CreateNetworkPolicyRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        defaultNetworkPolicyName,
				Namespace: testNamespace,
			},
			Spec: &pbk8s.NetworkPolicySpec{
				PodSelector: &pbk8s.LabelSelector{},
				Ingress: []*pbk8s.NetworkPolicyIngressRule{
					{
						// Allow only ingress from same namespace and non-tenant namespace
						From: []*pbk8s.NetworkPolicyPeer{
							{
								NamespaceSelector: &pbk8s.LabelSelector{
									MatchLabels: map[string]string{
										podstrategy.TenantResourceNamespace: testNamespace,
									},
								},
							},
							{
								NamespaceSelector: &pbk8s.LabelSelector{
									MatchExpressions: []*pbk8s.LabelSelectorRequirement{
										{
											Key:      podstrategy.TenantResourceNamespace,
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_DOES_NOT_EXIST,
										},
									},
								},
							},
						},
					},
				},
				PolicyTypes: []pbk8s.NetworkPolicyType{
					pbk8s.NetworkPolicyType_NETWORK_POLICY_INGRESS,
				},
			},
		}),
	).Return(
		&pbsvck8s.CreateNetworkPolicyResponse{
			Status: &pbcreation.Status{
				Code: pbcreation.StatusCode_CREATED,
			},
		},
		nil,
	)

	provider := AWSProvider{
		agent:        agentWrapper.Agent,
		vpcCIDRRange: testCIDR,
	}
	err := provider.CreateTenantFirewallAwait(context.Background(), CreateTenantFirewallOption{
		TenantID:          1,
		TenantName:        testClusterName,
		ResourceNamespace: testNamespace,
		PodLabelSelector:  testLabelSelector,
	})
	require.NoError(t, err)
}

func TestDeleteSecurityGroup(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	agentWrapper := agent.NewMockAgentWrapper(ctrl)
	agentWrapper.Querier.EXPECT().DeleteTenantCloudResource(
		gomock.Any(),
		gomock.Eq(
			querier.DeleteTenantCloudResourceParams{
				ID:           "rwc-g1h8bikpmjft4bobe659bf95b5-test-rwsg",
				Namespace:    testNamespace,
				ResourceType: model.CloudResourceAWSSecurityGroup,
			},
		),
	).Return(nil)
	agentWrapper.Querier.EXPECT().DeleteTenantCloudResource(
		gomock.Any(),
		gomock.Eq(
			querier.DeleteTenantCloudResourceParams{
				ID:           "g1h8bikpmjft4bobe659bf95b5-rwsg",
				Namespace:    testNamespace,
				ResourceType: model.CloudResourceAWSSecurityGroup,
			},
		),
	).Return(nil)
	agentWrapper.Querier.EXPECT().DeleteTenantCloudResource(
		gomock.Any(),
		gomock.Eq(
			querier.DeleteTenantCloudResourceParams{
				ID:           "rwc-g1h8bikpmjft4bobe659bf95b5-test-epsg",
				Namespace:    testNamespace,
				ResourceType: model.CloudResourceAWSSecurityGroup,
			},
		),
	).Return(nil)
	agentWrapper.Querier.EXPECT().DeleteTenantCloudResource(
		gomock.Any(),
		gomock.Eq(
			querier.DeleteTenantCloudResourceParams{
				ID:           "g1h8bikpmjft4bobe659bf95b5-epsg",
				Namespace:    testNamespace,
				ResourceType: model.CloudResourceAWSSecurityGroup,
			},
		),
	).Return(nil)
	agentWrapper.Querier.EXPECT().DeleteTenantCloudResource(
		gomock.Any(),
		gomock.Eq(
			querier.DeleteTenantCloudResourceParams{
				ID:           "rwc-g1h8bikpmjft4bobe659bf95b5-test-policy",
				Namespace:    testNamespace,
				ResourceType: model.CloudResourceAWSSecurityGroupPolicy,
			},
		),
	).Return(nil)
	agentWrapper.Querier.EXPECT().DeleteTenantCloudResource(
		gomock.Any(),
		gomock.Eq(
			querier.DeleteTenantCloudResourceParams{
				ID:           "g1h8bikpmjft4bobe659bf95b5-policy",
				Namespace:    testNamespace,
				ResourceType: model.CloudResourceAWSSecurityGroupPolicy,
			},
		),
	).Return(nil)

	agentWrapper.AWSStub.EXPECT().DeleteSecurityGroup(
		gomock.Any(),
		agent.EqProto(&aws.DeleteSecurityGroupRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        "rwc-g1h8bikpmjft4bobe659bf95b5-test-rwsg",
				Namespace: testNamespace,
			},
		}),
	).Return(
		&aws.DeleteSecurityGroupResponse{
			Status: &pbdeletion.Status{
				Code: pbdeletion.StatusCode_NOT_FOUND,
			},
		},
		nil,
	)
	agentWrapper.AWSStub.EXPECT().DeleteSecurityGroup(
		gomock.Any(),
		agent.EqProto(&aws.DeleteSecurityGroupRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        "g1h8bikpmjft4bobe659bf95b5-rwsg",
				Namespace: testNamespace,
			},
		}),
	).Return(
		&aws.DeleteSecurityGroupResponse{
			Status: &pbdeletion.Status{
				Code: pbdeletion.StatusCode_SCHEDULED,
			},
		},
		nil,
	)
	agentWrapper.AWSStub.EXPECT().GetSecurityGroup(
		gomock.Any(),
		agent.EqProto(&aws.GetSecurityGroupRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        "g1h8bikpmjft4bobe659bf95b5-rwsg",
				Namespace: testNamespace,
			},
		}),
	).Return(
		&aws.GetSecurityGroupResponse{
			Status: &pbresource.Status{
				Code: pbresource.StatusCode_NOT_FOUND,
			},
		},
		nil,
	)

	agentWrapper.AWSStub.EXPECT().DeleteSecurityGroup(
		gomock.Any(),
		agent.EqProto(&aws.DeleteSecurityGroupRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        "rwc-g1h8bikpmjft4bobe659bf95b5-test-epsg",
				Namespace: testNamespace,
			},
		}),
	).Return(
		&aws.DeleteSecurityGroupResponse{
			Status: &pbdeletion.Status{
				Code: pbdeletion.StatusCode_NOT_FOUND,
			},
		},
		nil,
	)
	agentWrapper.AWSStub.EXPECT().DeleteSecurityGroup(
		gomock.Any(),
		agent.EqProto(&aws.DeleteSecurityGroupRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        "g1h8bikpmjft4bobe659bf95b5-epsg",
				Namespace: testNamespace,
			},
		}),
	).Return(
		&aws.DeleteSecurityGroupResponse{
			Status: &pbdeletion.Status{
				Code: pbdeletion.StatusCode_SCHEDULED,
			},
		},
		nil,
	)
	agentWrapper.AWSStub.EXPECT().GetSecurityGroup(
		gomock.Any(),
		agent.EqProto(&aws.GetSecurityGroupRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        "g1h8bikpmjft4bobe659bf95b5-epsg",
				Namespace: testNamespace,
			},
		}),
	).Return(
		&aws.GetSecurityGroupResponse{
			Status: &pbresource.Status{
				Code: pbresource.StatusCode_NOT_FOUND,
			},
		},
		nil,
	)

	agentWrapper.AWSStub.EXPECT().DeleteSecurityGroupPolicy(
		gomock.Any(),
		agent.EqProto(&aws.DeleteSecurityGroupPolicyRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        "rwc-g1h8bikpmjft4bobe659bf95b5-test-policy",
				Namespace: testNamespace,
			},
		}),
	).Return(
		&aws.DeleteSecurityGroupPolicyResponse{
			Status: &pbdeletion.Status{
				Code: pbdeletion.StatusCode_NOT_FOUND,
			},
		},
		nil,
	)
	agentWrapper.AWSStub.EXPECT().DeleteSecurityGroupPolicy(
		gomock.Any(),
		agent.EqProto(&aws.DeleteSecurityGroupPolicyRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        "g1h8bikpmjft4bobe659bf95b5-policy",
				Namespace: testNamespace,
			},
		}),
	).Return(
		&aws.DeleteSecurityGroupPolicyResponse{
			Status: &pbdeletion.Status{
				Code: pbdeletion.StatusCode_SCHEDULED,
			},
		},
		nil,
	)
	agentWrapper.AWSStub.EXPECT().GetSecurityGroupPolicy(
		gomock.Any(),
		agent.EqProto(&aws.GetSecurityGroupPolicyRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        "g1h8bikpmjft4bobe659bf95b5-policy",
				Namespace: testNamespace,
			},
		}),
	).Return(
		&aws.GetSecurityGroupPolicyResponse{
			Status: &pbresource.Status{
				Code: pbresource.StatusCode_NOT_FOUND,
			},
		},
		nil,
	)

	provider := AWSProvider{
		agent: agentWrapper.Agent,
	}
	require.NoError(t, provider.DeleteTenantFirewallAwait(context.Background(), testClusterName, testNamespace, 1))
}

func TestCreateAWSPrivateLink(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	testNamespace := "rwc-g1h8bikpmjft4bobe659bf95b5-test"
	testID := uuid.New()
	testServiceName := "testServiceName"
	testSubnets := []string{"testSubnet"}
	testSGID := "1234"
	byocClusterUID := uuid.New()

	type testCase struct {
		tier           string
		byocClusterUID uuid.UUID
		clusterName    string
	}
	testCases := []testCase{
		{
			tier:           string(tier.BYOC),
			byocClusterUID: byocClusterUID,
		},
		{
			tier:        string(tier.Invited),
			clusterName: "testCluster",
		},
	}

	for _, tc := range testCases {
		privateLinkName, err := createResourceNameFromPrivateLinkID(testNamespace, testID.String())
		assert.NoError(t, err)

		agentWrapper := agent.NewMockAgentWrapper(ctrl)

		// create private service
		agentWrapper.AWSStub.EXPECT().GetSecurityGroup(
			gomock.Any(),
			agent.EqProto(&aws.GetSecurityGroupRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        "g1h8bikpmjft4bobe659bf95b5-epsg",
					Namespace: testNamespace,
				},
			}),
		).Return(
			&aws.GetSecurityGroupResponse{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_READY,
				},
				SecurityGroupId: "1234",
			},
			nil,
		)

		agentWrapper.Querier.EXPECT().UpsertTenantCloudResource(
			gomock.Any(),
			gomock.Eq(
				querier.UpsertTenantCloudResourceParams{
					ID:              privateLinkName,
					Namespace:       testNamespace,
					ResourceType:    model.CloudResourceAWSPrivateLink,
					TenantID:        testTenantID,
					ExtraAttributes: []byte("{}"),
				},
			),
		).Return(nil)

		agentWrapper.AWSStub.EXPECT().GetVPCEndpoint(
			gomock.Any(),
			agent.EqProto(&aws.GetVPCEndpointRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        privateLinkName,
					Namespace: testNamespace,
				},
			}),
		).Return(
			&aws.GetVPCEndpointResponse{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_READY,
				},
				EndpointState: aws.VPCEndpointStatus_AVAILABLE,
				EndpointId:    testNamespace,
			},
			nil,
		)

		provider := AWSProvider{
			agent:             agentWrapper.Agent,
			tenantVPCESubnets: testSubnets,
			byocClusterUID:    byocClusterUID,
			clusterName:       tc.clusterName,
		}

		tagEnvValue := tc.clusterName
		if tc.tier == string(tier.BYOC) {
			tagEnvValue = namespace.ToBase32(tc.byocClusterUID)
		}
		agentWrapper.AWSStub.EXPECT().CreateVPCEndpoint(
			gomock.Any(),
			agent.EqProto(&aws.CreateVPCEndpointRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        privateLinkName,
					Namespace: testNamespace,
				},
				ServiceName:      testServiceName,
				SubnetIds:        testSubnets,
				SecurityGroupIds: []string{testSGID},
				ExtraTags:        map[string]string{"EnvID": tagEnvValue},
			}),
		).Return(
			&aws.CreateVPCEndpointResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_SCHEDULED,
				},
			},
			nil,
		)
		plMeta, err := provider.CreatePrivateLinkAndWait(context.Background(), CreatePrivateLinkOption{
			TenantID:      testTenantID,
			Target:        testServiceName,
			PrivateLinkID: testID.String(),
			Namespace:     testNamespace,
			Tier:          tc.tier,
		})
		require.NoError(t, err)
		assert.Equal(t, PrivateLinkConnectionAccepted, plMeta.ConnectionState)
	}
}

func TestCreateAWSPrivateLinkUnsupportedCluster(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testNamespace := "rwc-g1h8bikpmjft4bobe659bf95b5-test"
	testID := uuid.New()
	testServiceName := "testServiceName"
	testSubnets := []string{}

	agentWrapper := agent.NewMockAgentWrapper(ctrl)

	provider := AWSProvider{
		agent:             agentWrapper.Agent,
		tenantVPCESubnets: testSubnets,
	}

	_, err := provider.CreatePrivateLinkAndWait(context.Background(), CreatePrivateLinkOption{
		TenantID:      testTenantID,
		Target:        testServiceName,
		PrivateLinkID: testID.String(),
		Namespace:     testNamespace,
	})
	require.Error(t, err)
	require.Equal(t, eris.GetCode(err), eris.CodeUnimplemented)
}

func TestDeleteAWSPrivateLink(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testNamespace := "rwc-g1h8bikpmjft4bobe659bf95b5-test"
	testPrivateLinkID := uuid.New()

	privateLinkName, err := createResourceNameFromPrivateLinkID(testNamespace, testPrivateLinkID.String())
	assert.NoError(t, err)

	agentWrapper := agent.NewMockAgentWrapper(ctrl)

	// delete private link
	agentWrapper.AWSStub.EXPECT().DeleteVPCEndpoint(
		gomock.Any(),
		agent.EqProto(&aws.DeleteVPCEndpointRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        privateLinkName,
				Namespace: testNamespace,
			},
		}),
	).Return(
		&aws.DeleteVPCEndpointResponse{
			Status: &pbdeletion.Status{
				Code: pbdeletion.StatusCode_SCHEDULED,
			},
		},
		nil,
	)
	agentWrapper.Querier.EXPECT().DeleteTenantCloudResource(
		gomock.Any(),
		gomock.Eq(
			querier.DeleteTenantCloudResourceParams{
				ID:           privateLinkName,
				Namespace:    testNamespace,
				ResourceType: model.CloudResourceAWSPrivateLink,
			}),
	).Return(nil).AnyTimes()

	agentWrapper.AWSStub.EXPECT().GetVPCEndpoint(
		gomock.Any(),
		agent.EqProto(&aws.GetVPCEndpointRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        privateLinkName,
				Namespace: testNamespace,
			},
		}),
	).Return(
		&aws.GetVPCEndpointResponse{
			Status: &pbresource.Status{
				Code: pbresource.StatusCode_NOT_FOUND,
			},
		},
		nil,
	)

	provider := AWSProvider{
		agent: agentWrapper.Agent,
	}
	err = provider.DeletePrivateLinkAndWait(context.Background(), testNamespace, testPrivateLinkID.String())
	require.NoError(t, err)
}

func TestGetAWSPrivateLink(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testNamespace := "rwc-g1h8bikpmjft4bobe659bf95b5-test"
	testID := uuid.New()
	testEndpointID := "testID"
	testEndpointDNS := "testDNS"
	privateLinkName, err := createResourceNameFromPrivateLinkID(testNamespace, testID.String())
	assert.NoError(t, err)

	agentWrapper := agent.NewMockAgentWrapper(ctrl)

	agentWrapper.AWSStub.EXPECT().GetVPCEndpoint(
		gomock.Any(),
		agent.EqProto(&aws.GetVPCEndpointRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        privateLinkName,
				Namespace: testNamespace,
			},
		}),
	).Return(
		&aws.GetVPCEndpointResponse{
			Status: &pbresource.Status{
				Code: pbresource.StatusCode_READY,
			},
			EndpointState: aws.VPCEndpointStatus_AVAILABLE,
			EndpointId:    testEndpointID,
			EndpointDns:   testEndpointDNS,
		},
		nil,
	)

	agentWrapper.AWSStub.EXPECT().GetVPCEndpoint(
		gomock.Any(),
		agent.EqProto(&aws.GetVPCEndpointRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        "g1h8bikpmjft4bobe659bf95b5-random",
				Namespace: testNamespace,
			},
		}),
	).Return(
		&aws.GetVPCEndpointResponse{
			Status: &pbresource.Status{
				Code: pbresource.StatusCode_NOT_FOUND,
			},
		},
		nil,
	)

	provider := AWSProvider{
		agent: agentWrapper.Agent,
	}

	res, err := provider.GetPrivateLink(context.Background(), testNamespace, testID.String())
	assert.NoError(t, err)
	assert.Equal(t, testEndpointDNS, res.Endpoint)
	assert.Equal(t, PrivateLinkConnectionAccepted, res.ConnectionState)

	_, err = provider.GetPrivateLink(context.Background(), testNamespace, "random")
	assert.Equal(t, eris.GetCode(err), eris.CodeNotFound)
}

func TestVPCEndpointStateFromSpec(t *testing.T) {
	res := vpcEndpointStateFromSpec(aws.VPCEndpointStatus_AVAILABLE)
	assert.Equal(t, PrivateLinkConnectionAccepted, res)

	res = vpcEndpointStateFromSpec(aws.VPCEndpointStatus_PENDING_ACCEPTANCE)
	assert.Equal(t, PrivateLinkConnectionPending, res)

	res = vpcEndpointStateFromSpec(aws.VPCEndpointStatus_REJECTED)
	assert.Equal(t, PrivateLinkConnectionRejected, res)

	res = vpcEndpointStateFromSpec(aws.VPCEndpointStatus_DELETED)
	assert.Equal(t, PrivateLinkConnectionClosed, res)

	res = vpcEndpointStateFromSpec(aws.VPCEndpointStatus_FAILED)
	assert.Equal(t, PrivateLinkConnectionRejected, res)

	res = vpcEndpointStateFromSpec(aws.VPCEndpointStatus_STATUS_UNSPECIFIED)
	assert.Equal(t, PrivateLinkConnectionUnspecified, res)
}

func TestCreateAWSPrivateLinkDeprecatedSGName(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testNamespace := "rwc-g1h8bikpmjft4bobe659bf95b5-test"
	testID := uuid.New()
	testServiceName := "testServiceName"
	testSubnets := []string{"testSubnet"}
	testSGID := "1234"

	privateLinkName, err := createResourceNameFromPrivateLinkID(testNamespace, testID.String())
	assert.NoError(t, err)

	agentWrapper := agent.NewMockAgentWrapper(ctrl)

	// create private service
	agentWrapper.AWSStub.EXPECT().GetSecurityGroup(
		gomock.Any(),
		agent.EqProto(&aws.GetSecurityGroupRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        "g1h8bikpmjft4bobe659bf95b5-epsg",
				Namespace: testNamespace,
			},
		}),
	).Return(
		&aws.GetSecurityGroupResponse{
			Status: &pbresource.Status{
				Code: pbresource.StatusCode_NOT_FOUND,
			},
		},
		nil,
	)

	agentWrapper.AWSStub.EXPECT().GetSecurityGroup(
		gomock.Any(),
		agent.EqProto(&aws.GetSecurityGroupRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        "rwc-g1h8bikpmjft4bobe659bf95b5-test-epsg",
				Namespace: testNamespace,
			},
		}),
	).Return(
		&aws.GetSecurityGroupResponse{
			Status: &pbresource.Status{
				Code: pbresource.StatusCode_READY,
			},
			SecurityGroupId: "1234",
		},
		nil,
	)

	agentWrapper.AWSStub.EXPECT().CreateVPCEndpoint(
		gomock.Any(),
		agent.EqProto(&aws.CreateVPCEndpointRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        privateLinkName,
				Namespace: testNamespace,
			},
			ServiceName:      testServiceName,
			SubnetIds:        testSubnets,
			SecurityGroupIds: []string{testSGID},
			ExtraTags:        map[string]string{"EnvID": "testCluster"},
		}),
	).Return(
		&aws.CreateVPCEndpointResponse{
			Status: &pbcreation.Status{
				Code: pbcreation.StatusCode_SCHEDULED,
			},
		},
		nil,
	)

	agentWrapper.Querier.EXPECT().UpsertTenantCloudResource(
		gomock.Any(),
		gomock.Eq(
			querier.UpsertTenantCloudResourceParams{
				ID:              privateLinkName,
				Namespace:       testNamespace,
				ResourceType:    model.CloudResourceAWSPrivateLink,
				TenantID:        testTenantID,
				ExtraAttributes: []byte("{}"),
			},
		),
	).Return(nil)

	agentWrapper.AWSStub.EXPECT().GetVPCEndpoint(
		gomock.Any(),
		agent.EqProto(&aws.GetVPCEndpointRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        privateLinkName,
				Namespace: testNamespace,
			},
		}),
	).Return(
		&aws.GetVPCEndpointResponse{
			Status: &pbresource.Status{
				Code: pbresource.StatusCode_READY,
			},
			EndpointState: aws.VPCEndpointStatus_AVAILABLE,
			EndpointId:    testNamespace,
		},
		nil,
	)

	provider := AWSProvider{
		agent:             agentWrapper.Agent,
		tenantVPCESubnets: testSubnets,
		clusterName:       "testCluster",
	}

	plMeta, err := provider.CreatePrivateLinkAndWait(context.Background(), CreatePrivateLinkOption{
		TenantID:      testTenantID,
		Target:        testServiceName,
		PrivateLinkID: testID.String(),
		Namespace:     testNamespace,
		Tier:          string(tier.Invited),
	})
	require.NoError(t, err)
	assert.Equal(t, PrivateLinkConnectionAccepted, plMeta.ConnectionState)
}
