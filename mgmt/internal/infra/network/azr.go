package network

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pbgen/services/azr"
	pbk8ssvc "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/risingwave-cloud/cli/operations/byoc/utils/namespace"
	"github.com/risingwavelabs/risingwave-cloud/internal/agent"
	"github.com/risingwavelabs/risingwave-cloud/shared/tenant/tier"
)

type AZ<PERSON>rovider struct {
	agent                           *agent.CloudAgent
	tenantPrivateEndpointSubnetID   string
	tenantPrivateEndpointSubnetCIDR string
	clusterName                     string
	byocClusterUID                  uuid.UUID
}

type AZRConfig struct {
	Agent                           *agent.CloudAgent
	TenantPrivateEndpointSubnetID   string
	TenantPrivateEndpointSubnetCIDR string
	ClusterName                     string
	BYOCClusterUID                  uuid.UUID
}

func NewAZRProvider(cfg *AZRConfig) *AZRProvider {
	return &AZRProvider{
		agent:                           cfg.Agent,
		tenantPrivateEndpointSubnetID:   cfg.TenantPrivateEndpointSubnetID,
		tenantPrivateEndpointSubnetCIDR: cfg.TenantPrivateEndpointSubnetCIDR,
		clusterName:                     cfg.ClusterName,
		byocClusterUID:                  cfg.BYOCClusterUID,
	}
}

func (p *AZRProvider) CreateTenantFirewallAwait(ctx context.Context, option CreateTenantFirewallOption) error {
	err := p.agent.CreateNetworkPolicy(ctx, &pbk8ssvc.CreateNetworkPolicyRequest{
		ResourceMeta: getDefaultNetworkPolicyResourceMeta(option.ResourceNamespace),
		Spec:         p.getDefaultK8sNetworkPolicySpec(option.ResourceNamespace),
	})
	if err != nil {
		return eris.Wrap(err, "failed to create network policy")
	}
	return nil
}

func (p *AZRProvider) ApplyDefaultK8sNetworkPolicy(ctx context.Context, namespace string) error {
	err := p.agent.CreateOrUpdateNetworkPolicy(ctx, &pbk8ssvc.CreateOrUpdateNetworkPolicyRequest{
		ResourceMeta: getDefaultNetworkPolicyResourceMeta(namespace),
		Spec:         p.getDefaultK8sNetworkPolicySpec(namespace),
	})
	if err != nil {
		return eris.Wrap(err, "failed to create k8s network policy")
	}
	return nil
}

func (p *AZRProvider) getDefaultK8sNetworkPolicySpec(namespace string) *pbk8s.NetworkPolicySpec {
	policyTypes := []pbk8s.NetworkPolicyType{pbk8s.NetworkPolicyType_NETWORK_POLICY_EGRESS, pbk8s.NetworkPolicyType_NETWORK_POLICY_INGRESS}
	ingressPolicyPeers := createTenantIngressPolicyPeers(namespace)

	return &pbk8s.NetworkPolicySpec{
		PodSelector: &pbk8s.LabelSelector{},
		Egress: []*pbk8s.NetworkPolicyEgressRule{
			{
				// Allow all egress except PSC subnet
				To: []*pbk8s.NetworkPolicyPeer{
					{
						IpBlock: &pbk8s.IPBlock{
							Cidr:   "0.0.0.0/0",
							Except: []string{p.tenantPrivateEndpointSubnetCIDR},
						},
						// AKS cluster will Cilium CNI requires namespaceSelector and podSelector.
						// https://learn.microsoft.com/en-us/azure/aks/azure-cni-powered-by-cilium#frequently-asked-questions
					},
					{
						NamespaceSelector: &pbk8s.LabelSelector{},
					},
					{
						PodSelector: &pbk8s.LabelSelector{},
					},
				},
			},
		},
		Ingress:     ingressPolicyPeers,
		PolicyTypes: policyTypes,
	}
}

func (p *AZRProvider) DeleteTenantFirewallAwait(context.Context, string, string, uint64) error {
	return nil
}

func (p *AZRProvider) createPrivateLinkNetworkPolicyAWait(ctx context.Context, option CreatePrivateLinkNetworkPolicyOption) error {
	err := p.agent.CreateNetworkPolicy(ctx, &pbk8ssvc.CreateNetworkPolicyRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        fmt.Sprintf("%s-%s", privateLinkPolicyPrefix, option.PrivateLinkID),
			Namespace: option.Namespace,
		},
		Spec: &pbk8s.NetworkPolicySpec{
			PodSelector: &pbk8s.LabelSelector{},
			Egress: []*pbk8s.NetworkPolicyEgressRule{
				{
					// Allow egress only to provisioned ip
					To: []*pbk8s.NetworkPolicyPeer{
						{
							IpBlock: &pbk8s.IPBlock{
								Cidr: fmt.Sprintf("%s/32", option.Endpoint),
							},
							// AKS cluster will Cilium CNI requires namespaceSelector and podSelector.
							// https://learn.microsoft.com/en-us/azure/aks/azure-cni-powered-by-cilium#frequently-asked-questions
						},
						{
							NamespaceSelector: &pbk8s.LabelSelector{},
						},
						{
							PodSelector: &pbk8s.LabelSelector{},
						},
					},
				},
			},
			PolicyTypes: []pbk8s.NetworkPolicyType{pbk8s.NetworkPolicyType_NETWORK_POLICY_EGRESS},
		},
	})
	if err != nil {
		return eris.Wrap(err, "failed to create network policy")
	}
	return nil
}

func (p *AZRProvider) CreatePrivateLinkAndWait(ctx context.Context, option CreatePrivateLinkOption) (*PrivateLinkMeta, error) {
	ns := option.Namespace
	id := option.PrivateLinkID
	tenantID := option.TenantID

	privateEndpointReq, err := p.generateCreatePrivateLinkRequest(option)
	if err != nil {
		return nil, err
	}

	privateEndpointResp, err := p.agent.CreateAZRPrivateLinkAndWait(ctx, privateEndpointReq, tenantID)
	if err != nil {
		return nil, eris.Wrapf(err, "could not create private link")
	}

	err = p.createPrivateLinkNetworkPolicyAWait(ctx, CreatePrivateLinkNetworkPolicyOption{
		PrivateLinkID: id,
		Namespace:     ns,
		Endpoint:      privateEndpointResp.GetPrivateEndpointIp(),
	})
	if err != nil {
		return nil, err
	}

	connectionState := privateEndpointResp.GetPrivateEndpointStatus()
	return &PrivateLinkMeta{
		Endpoint:        privateEndpointResp.GetPrivateEndpointIp(),
		ConnectionState: azrConnectionStateFromSpec(connectionState),
	}, nil
}

func (p *AZRProvider) DeletePrivateLinkAndWait(ctx context.Context, namespace, privateLinkID string) error {
	privateLinkName, err := createResourceNameFromPrivateLinkID(namespace, privateLinkID)
	if err != nil {
		return eris.Wrap(err, "failed to generate private link name")
	}

	_, err = p.agent.DeleteAZRPrivateLinkAndWait(ctx, &azr.DeletePrivateEndpointRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        privateLinkName,
			Namespace: namespace,
		},
	})
	if err != nil {
		return eris.Wrapf(err, "could not delete private link")
	}

	return nil
}

func (p *AZRProvider) GetPrivateLink(ctx context.Context, namespace, privateLinkID string) (*PrivateLinkMeta, error) {
	resourceName, err := createResourceNameFromPrivateLinkID(namespace, privateLinkID)
	if err != nil {
		return nil, eris.Wrap(err, "failed to generate private link name")
	}

	resp, err := p.agent.GetAZRPrivateLink(ctx, &azr.GetPrivateEndpointRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        resourceName,
			Namespace: namespace,
		},
	})
	if err != nil {
		return nil, eris.Wrapf(err, "could not get private link")
	}
	if resp.GetStatus().GetCode() == pbresource.StatusCode_NOT_FOUND {
		return nil, eris.Errorf("privatelink doesn't exist, id: %s, ns: %s", privateLinkID, namespace).WithCode(eris.CodeNotFound)
	}

	return &PrivateLinkMeta{
		Endpoint:        resp.GetPrivateEndpointIp(),
		ConnectionState: azrConnectionStateFromSpec(resp.GetPrivateEndpointStatus()),
	}, nil
}

func (p *AZRProvider) generateCreatePrivateLinkRequest(option CreatePrivateLinkOption) (*azr.CreatePrivateEndpointRequest, error) {
	name, err := createResourceNameFromPrivateLinkID(option.Namespace, option.PrivateLinkID)
	if err != nil {
		return nil, eris.Wrap(err, "failed to generate private link name")
	}

	tagEnvValue := p.clusterName
	if option.Tier == string(tier.BYOC) {
		tagEnvValue = namespace.ToBase32(p.byocClusterUID)
	}
	req := &azr.CreatePrivateEndpointRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        name,
			Namespace: option.Namespace,
		},
		PrivateLinkSubnetId:  p.tenantPrivateEndpointSubnetID,
		PrivateLinkServiceId: option.Target,
		ExtraTags: map[string]string{
			TagEnvKey: tagEnvValue,
		},
	}
	return req, nil
}

func azrConnectionStateFromSpec(status azr.PrivateEndpointStatus) PrivateLinkConnectionState {
	switch status {
	case azr.PrivateEndpointStatus_SUCCEEDED:
		return PrivateLinkConnectionAccepted
	case azr.PrivateEndpointStatus_FAILED:
		return PrivateLinkConnectionRejected
	case azr.PrivateEndpointStatus_UPDATING:
		return PrivateLinkConnectionPending
	case azr.PrivateEndpointStatus_DELETING:
		return PrivateLinkConnectionClosed
	case azr.PrivateEndpointStatus_STATUS_UNSPECIFIED:
		return PrivateLinkConnectionUnspecified
	}
	return PrivateLinkConnectionUnspecified
}
