package network

import (
	"context"
	"fmt"
	"testing"

	"github.com/google/uuid"
	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	pbazrsvc "github.com/risingwavelabs/cloudagent/pbgen/services/azr"
	pbsvck8s "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	"github.com/risingwavelabs/eris"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"

	"github.com/risingwavelabs/risingwave-cloud/cli/operations/byoc/utils/namespace"
	"github.com/risingwavelabs/risingwave-cloud/internal/agent"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/podstrategy"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/querier"
	"github.com/risingwavelabs/risingwave-cloud/shared/tenant/tier"
)

var (
	testTenantPrivateEndpointCIDR     = "0.0.0.0/0"
	testTenantPrivateEndpointSubnetID = "/subscriptions/12345678-90ab-cdef-1234-567890abcdef/resourceGroups/myResourceGroup/providers/Microsoft.Network/virtualNetworks/myVirtualNetwork/subnets/mySubnet"
	testPrivateLinkServiceID          = "/subscriptions/12345678-90ab-cdef-1234-567890abcdef/resourceGroups/myResourceGroup/providers/Microsoft.Network/privateLinkServices/myPrivateLinkService"
)

func TestAZRCreateFirewall(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testNamespace := "rwc-g1h8bikpmjft4bobe659bf95b5-test"
	testCIDR := "***********/18"

	agentWrapper := agent.NewMockAgentWrapper(ctrl)

	agentWrapper.K8sStub.EXPECT().CreateNetworkPolicy(
		gomock.Any(),
		agent.EqProto(&pbsvck8s.CreateNetworkPolicyRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        defaultNetworkPolicyName,
				Namespace: testNamespace,
			},
			Spec: &pbk8s.NetworkPolicySpec{
				PodSelector: &pbk8s.LabelSelector{},
				Egress: []*pbk8s.NetworkPolicyEgressRule{
					{
						// Allow all egress except PSC subnet
						To: []*pbk8s.NetworkPolicyPeer{
							{
								IpBlock: &pbk8s.IPBlock{
									Cidr:   "0.0.0.0/0",
									Except: []string{testCIDR},
								},
							},
							{
								NamespaceSelector: &pbk8s.LabelSelector{},
							},
							{
								PodSelector: &pbk8s.LabelSelector{},
							},
						},
					},
				},
				Ingress: []*pbk8s.NetworkPolicyIngressRule{
					{
						// Allow only ingress from same namespace and non-tenant namespace
						From: []*pbk8s.NetworkPolicyPeer{
							{
								NamespaceSelector: &pbk8s.LabelSelector{
									MatchLabels: map[string]string{
										podstrategy.TenantResourceNamespace: testNamespace,
									},
								},
							},
							{
								NamespaceSelector: &pbk8s.LabelSelector{
									MatchExpressions: []*pbk8s.LabelSelectorRequirement{
										{
											Key:      podstrategy.TenantResourceNamespace,
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_DOES_NOT_EXIST,
										},
									},
								},
							},
						},
					},
				},

				PolicyTypes: []pbk8s.NetworkPolicyType{
					pbk8s.NetworkPolicyType_NETWORK_POLICY_EGRESS,
					pbk8s.NetworkPolicyType_NETWORK_POLICY_INGRESS,
				},
			},
		}),
	).Return(
		&pbsvck8s.CreateNetworkPolicyResponse{
			Status: &pbcreation.Status{
				Code: pbcreation.StatusCode_CREATED,
			},
		},
		nil,
	)

	provider := AZRProvider{
		agent:                           agentWrapper.Agent,
		tenantPrivateEndpointSubnetCIDR: testCIDR,
	}
	err := provider.CreateTenantFirewallAwait(context.Background(), CreateTenantFirewallOption{
		TenantID:          1,
		TenantName:        testClusterName,
		ResourceNamespace: testNamespace,
		PodLabelSelector:  testLabelSelector,
	})
	require.NoError(t, err)
}

func TestCreateAZRPrivateLinkPolicy(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testNamespace := "rwc-g1h8bikpmjft4bobe659bf95b5-test"
	testCIDR := "***********/32"
	testID := "1234"

	agentWrapper := agent.NewMockAgentWrapper(ctrl)

	agentWrapper.K8sStub.EXPECT().CreateNetworkPolicy(
		gomock.Any(),
		agent.EqProto(&pbsvck8s.CreateNetworkPolicyRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        fmt.Sprintf("%s-%s", privateLinkPolicyPrefix, testID),
				Namespace: testNamespace,
			},
			Spec: &pbk8s.NetworkPolicySpec{
				PodSelector: &pbk8s.LabelSelector{},
				Egress: []*pbk8s.NetworkPolicyEgressRule{
					{
						To: []*pbk8s.NetworkPolicyPeer{
							{
								IpBlock: &pbk8s.IPBlock{
									Cidr: testCIDR,
								},
							},
							{
								NamespaceSelector: &pbk8s.LabelSelector{},
							},
							{
								PodSelector: &pbk8s.LabelSelector{},
							},
						},
					},
				},
				PolicyTypes: []pbk8s.NetworkPolicyType{pbk8s.NetworkPolicyType_NETWORK_POLICY_EGRESS},
			},
		}),
	).Return(
		&pbsvck8s.CreateNetworkPolicyResponse{
			Status: &pbcreation.Status{
				Code: pbcreation.StatusCode_CREATED,
			},
		},
		nil,
	)

	provider := AZRProvider{
		agent:                           agentWrapper.Agent,
		tenantPrivateEndpointSubnetCIDR: testCIDR,
	}
	err := provider.createPrivateLinkNetworkPolicyAWait(context.Background(), CreatePrivateLinkNetworkPolicyOption{
		PrivateLinkID: testID,
		Namespace:     testNamespace,
		Endpoint:      "***********",
	})
	require.NoError(t, err)
}

func TestCreatePrivateEndpoint(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	byocClusterUID := uuid.New()
	privateLinkID := uuid.New().String()

	type testCase struct {
		tier           string
		byocClusterUID uuid.UUID
		clusterName    string
	}
	testCases := []testCase{
		{
			tier:           string(tier.BYOC),
			byocClusterUID: byocClusterUID,
		},
		{
			tier:        string(tier.Invited),
			clusterName: "testCluster",
		},
	}

	for _, tc := range testCases {
		agentWrapper := agent.NewMockAgentWrapper(ctrl)
		id, err := createResourceNameFromPrivateLinkID(testNamespace, privateLinkID)
		require.NoError(t, err)

		// query private endpoint
		agentWrapper.Querier.EXPECT().UpsertTenantCloudResource(
			gomock.Any(),
			gomock.Eq(
				querier.UpsertTenantCloudResourceParams{
					ID:              id,
					Namespace:       testNamespace,
					ResourceType:    model.CloudResourceAZRPrivateEndpoint,
					TenantID:        testTenantID,
					ExtraAttributes: []byte("{}"),
				},
			),
		).Return(nil)

		// create private endpoint
		tagEnvValue := tc.clusterName
		if tc.tier == string(tier.BYOC) {
			tagEnvValue = namespace.ToBase32(tc.byocClusterUID)
		}
		agentWrapper.AZRStub.EXPECT().CreatePrivateEndpoint(
			gomock.Any(),
			agent.EqProto(&pbazrsvc.CreatePrivateEndpointRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        id,
					Namespace: testNamespace,
				},
				PrivateLinkSubnetId:  testTenantPrivateEndpointSubnetID,
				PrivateLinkServiceId: testPrivateLinkServiceID,
				ExtraTags:            map[string]string{"envid": tagEnvValue},
			}),
		).Return(
			&pbazrsvc.CreatePrivateEndpointResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_SCHEDULED,
				},
			},
			nil,
		)

		agentWrapper.AZRStub.EXPECT().GetPrivateEndpoint(
			gomock.Any(),
			agent.EqProto(&pbazrsvc.GetPrivateEndpointRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        id,
					Namespace: testNamespace,
				},
			}),
		).Return(
			&pbazrsvc.GetPrivateEndpointResponse{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_READY,
				},
				PrivateEndpointIp:     "",
				PrivateEndpointStatus: pbazrsvc.PrivateEndpointStatus_SUCCEEDED,
			},
			nil,
		)

		// create private network policy
		agentWrapper.K8sStub.EXPECT().CreateNetworkPolicy(
			gomock.Any(),
			agent.EqProto(&pbsvck8s.CreateNetworkPolicyRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        fmt.Sprintf("%s-%s", privateLinkPolicyPrefix, privateLinkID),
					Namespace: testNamespace,
				},
				Spec: &pbk8s.NetworkPolicySpec{
					PodSelector: &pbk8s.LabelSelector{},
					Egress: []*pbk8s.NetworkPolicyEgressRule{
						{
							// Allow all egress except PSC subnet
							To: []*pbk8s.NetworkPolicyPeer{
								{
									IpBlock: &pbk8s.IPBlock{
										Cidr: "/32",
									},
								},
								{
									NamespaceSelector: &pbk8s.LabelSelector{},
								},
								{
									PodSelector: &pbk8s.LabelSelector{},
								},
							},
						},
					},
					PolicyTypes: []pbk8s.NetworkPolicyType{pbk8s.NetworkPolicyType_NETWORK_POLICY_EGRESS},
				},
			}),
		).Return(
			&pbsvck8s.CreateNetworkPolicyResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_CREATED,
				},
			},
			nil,
		)
		provider := AZRProvider{
			agent:                           agentWrapper.Agent,
			tenantPrivateEndpointSubnetID:   testTenantPrivateEndpointSubnetID,
			tenantPrivateEndpointSubnetCIDR: testTenantPrivateEndpointCIDR,
			byocClusterUID:                  byocClusterUID,
			clusterName:                     tc.clusterName,
		}

		option := CreatePrivateLinkOption{
			TenantID:      testTenantID,
			Namespace:     testNamespace,
			PrivateLinkID: privateLinkID,
			Target:        testPrivateLinkServiceID,
			Tier:          tc.tier,
		}

		resp, err := provider.CreatePrivateLinkAndWait(context.Background(), option)
		require.NoError(t, err)
		require.Equal(t, resp.ConnectionState, PrivateLinkConnectionAccepted)
	}
}

func TestDeleteAZRPrivateEndpoint(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testPrivateLinkID := uuid.New().String()
	privateLinkName, err := createResourceNameFromPrivateLinkID(testNamespace, testPrivateLinkID)
	require.NoError(t, err)

	agentWrapper := agent.NewMockAgentWrapper(ctrl)

	// delete private link
	agentWrapper.AZRStub.EXPECT().DeletePrivateEndpoint(
		gomock.Any(),
		agent.EqProto(&pbazrsvc.DeletePrivateEndpointRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        privateLinkName,
				Namespace: testNamespace,
			},
		}),
	).Return(
		&pbazrsvc.DeletePrivateEndpointResponse{
			Status: &pbdeletion.Status{
				Code: pbdeletion.StatusCode_SCHEDULED,
			},
		},
		nil,
	)
	agentWrapper.Querier.EXPECT().DeleteTenantCloudResource(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	agentWrapper.AZRStub.EXPECT().GetPrivateEndpoint(
		gomock.Any(),
		agent.EqProto(&pbazrsvc.GetPrivateEndpointRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        privateLinkName,
				Namespace: testNamespace,
			},
		}),
	).Return(
		&pbazrsvc.GetPrivateEndpointResponse{
			Status: &pbresource.Status{
				Code: pbresource.StatusCode_NOT_FOUND,
			},
		},
		nil,
	)

	provider := AZRProvider{
		agent:                           agentWrapper.Agent,
		tenantPrivateEndpointSubnetID:   testTenantPrivateEndpointSubnetID,
		tenantPrivateEndpointSubnetCIDR: testTenantPrivateEndpointCIDR,
	}
	err = provider.DeletePrivateLinkAndWait(context.Background(), testNamespace, testPrivateLinkID)
	require.NoError(t, err)
}

func TestGetAZRPrivateLink(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testNamespace := "rwc-g1h8bikpmjft4bobe659bf95b5-test"
	testID := uuid.New()
	testEndpointDNS := "testDNS"
	privateLinkName, err := createResourceNameFromPrivateLinkID(testNamespace, testID.String())
	assert.NoError(t, err)

	agentWrapper := agent.NewMockAgentWrapper(ctrl)

	agentWrapper.AZRStub.EXPECT().GetPrivateEndpoint(
		gomock.Any(),
		agent.EqProto(&pbazrsvc.GetPrivateEndpointRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        privateLinkName,
				Namespace: testNamespace,
			},
		}),
	).Return(
		&pbazrsvc.GetPrivateEndpointResponse{
			Status: &pbresource.Status{
				Code: pbresource.StatusCode_READY,
			},
			PrivateEndpointIp:     testEndpointDNS,
			PrivateEndpointStatus: pbazrsvc.PrivateEndpointStatus_SUCCEEDED,
		},
		nil,
	)
	agentWrapper.AZRStub.EXPECT().GetPrivateEndpoint(
		gomock.Any(),
		agent.EqProto(&pbazrsvc.GetPrivateEndpointRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        "g1h8bikpmjft4bobe659bf95b5-random",
				Namespace: testNamespace,
			},
		}),
	).Return(
		&pbazrsvc.GetPrivateEndpointResponse{
			Status: &pbresource.Status{
				Code: pbresource.StatusCode_NOT_FOUND,
			},
		},
		nil,
	)

	provider := AZRProvider{
		agent: agentWrapper.Agent,
	}

	res, err := provider.GetPrivateLink(context.Background(), testNamespace, testID.String())
	assert.NoError(t, err)
	assert.Equal(t, testEndpointDNS, res.Endpoint)
	assert.Equal(t, PrivateLinkConnectionAccepted, res.ConnectionState)

	_, err = provider.GetPrivateLink(context.Background(), testNamespace, "random")
	assert.Equal(t, eris.GetCode(err), eris.CodeNotFound)
}

func TestAZRConnectionStateFromSpec(t *testing.T) {
	res := azrConnectionStateFromSpec(pbazrsvc.PrivateEndpointStatus_SUCCEEDED)
	require.Equal(t, PrivateLinkConnectionAccepted, res)

	res = azrConnectionStateFromSpec(pbazrsvc.PrivateEndpointStatus_UPDATING)
	require.Equal(t, PrivateLinkConnectionPending, res)

	res = azrConnectionStateFromSpec(pbazrsvc.PrivateEndpointStatus_FAILED)
	require.Equal(t, PrivateLinkConnectionRejected, res)

	res = azrConnectionStateFromSpec(pbazrsvc.PrivateEndpointStatus_DELETING)
	require.Equal(t, PrivateLinkConnectionClosed, res)

	res = azrConnectionStateFromSpec(pbazrsvc.PrivateEndpointStatus_STATUS_UNSPECIFIED)
	require.Equal(t, PrivateLinkConnectionUnspecified, res)
}
