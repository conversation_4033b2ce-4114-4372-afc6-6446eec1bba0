package network

import (
	"context"
	"fmt"
	"strings"

	"github.com/google/uuid"
	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/risingwave-cloud/internal/agent"
	"github.com/risingwavelabs/risingwave-cloud/internal/managedcluster/settings"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
)

type Factory struct {
	m *model.Model
}

func NewFactory(m *model.Model) *Factory {
	return &Factory{m: m}
}

func (f *Factory) getNetworkProviderConfigFromCluster(ctx context.Context, clusterID uint64, agent *agent.CloudAgent) (Config, error) {
	config, err := f.m.GetClusterSettings(ctx, clusterID)
	if err != nil {
		return nil, eris.Wrap(err, "failed to get managed cluster settings")
	}

	var (
		provider = config[settings.SettingsKeyCloudProvider]
	)

	cluster, err := f.m.GetClusterByID(ctx, clusterID)
	if err != nil {
		return nil, eris.Wrap(err, "failed to get cluster")
	}
	byocClusterUID := uuid.UUID{}
	if byocClusterUUIDString, ok := config[settings.SettingsKeyUUID]; ok {
		byocClusterUID, err = uuid.Parse(byocClusterUUIDString)
		if err != nil {
			return nil, eris.Wrap(err, "failed to parse byoc cluster id")
		}
	}

	switch provider {
	case settings.CloudProviderGCP:
		requiredFields := []string{
			settings.SettingsKeyGcpTenantPscSubnetSelfLink,
			settings.SettingsKeyGcpTenantPscSubnetCidr,
		}
		if err := settings.RequireAllFields(requiredFields, config); err != nil {
			return nil, eris.Wrap(err, "missing required fields in GCP network config")
		}
		return &GCPConfig{
			Agent:                   agent,
			TenantPSCSubnetSelfLink: config[settings.SettingsKeyGcpTenantPscSubnetSelfLink],
			TenantPSCSubnetCIDR:     config[settings.SettingsKeyGcpTenantPscSubnetCidr],
			BYOCClusterUID:          byocClusterUID,
			ClusterName:             cluster.Name,
		}, nil
	case settings.CloudProviderAWS:
		isCloudCluster, err := settings.IsCloudCluster(ctx, f.m, clusterID)
		if err != nil {
			return nil, eris.Wrap(err, "failed to check if the cluser is BYOC")
		}
		cidrKey := settings.SettingsKeyAwsVpcCidr
		if !isCloudCluster {
			cidrKey = settings.SettingsKeyBYOCCIDR
		}
		requiredFields := []string{
			settings.SettingsKeyAwsVpcEndpointSubnetIDs,
			cidrKey,
		}
		if err := settings.RequireAllFields(requiredFields, config); err != nil {
			return nil, eris.Errorf("missing required fields in AWS network config: %v", err)
		}

		subnets := strings.Split(config[settings.SettingsKeyAwsVpcEndpointSubnetIDs], ",")
		return &AWSConfig{
			Agent:             agent,
			TenantVPCESubnets: subnets,
			VPCCIDRRange:      config[cidrKey],
			BYOCClusterUID:    byocClusterUID,
			ClusterName:       cluster.Name,
		}, nil
	case settings.CloudProviderAZR:
		requiredFields := []string{
			settings.SettingsKeyAzrTenantPrivateLinkSubnetID,
			settings.SettingsKeyAzrTenantPrivateLinkSubnetCidr,
		}
		if err := settings.RequireAllFields(requiredFields, config); err != nil {
			return nil, eris.Errorf("missing required fields in AZR network config: %v", err)
		}
		return &AZRConfig{
			Agent:                           agent,
			TenantPrivateEndpointSubnetID:   config[settings.SettingsKeyAzrTenantPrivateLinkSubnetID],
			TenantPrivateEndpointSubnetCIDR: config[settings.SettingsKeyAzrTenantPrivateLinkSubnetCidr],
			BYOCClusterUID:                  byocClusterUID,
			ClusterName:                     cluster.Name,
		}, nil
	case settings.CloudProviderLocal:
		return &LocalConfig{}, nil
	default:
		return nil, fmt.Errorf("unsupported cloud provider: %s", provider)
	}
}

func (f Factory) NewProvider(ctx context.Context, clusterID uint64, agent *agent.CloudAgent) (Provider, error) {
	cfg, err := f.getNetworkProviderConfigFromCluster(ctx, clusterID, agent)
	if err != nil {
		return nil, eris.Wrap(err, "failed to construct network provider cfg")
	}
	return NewProviderFromCfg(cfg)
}

func NewProviderFromCfg(cfg Config) (Provider, error) {
	switch t := cfg.(type) {
	case *AWSConfig:
		return NewAWSProvider(cfg.(*AWSConfig)), nil
	case *GCPConfig:
		return NewGCPProvider(cfg.(*GCPConfig)), nil
	case *AZRConfig:
		return NewAZRProvider(cfg.(*AZRConfig)), nil
	case *LocalConfig:
		return NewLocalProvider(cfg.(*LocalConfig)), nil
	default:
		return nil, eris.Errorf("invalid security group config type %v", t).WithCode(eris.CodeInvalidArgument)
	}
}
