package network

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"

	"github.com/risingwavelabs/risingwave-cloud/internal/agent"
	"github.com/risingwavelabs/risingwave-cloud/internal/managedcluster/settings"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	model_mock "github.com/risingwavelabs/risingwave-cloud/internal/model/mock"
)

func TestNewProvider(t *testing.T) {
	var (
		clusterID      = uint64(2)
		cloudAgent     = &agent.CloudAgent{}
		byocClusterUID = uuid.New()
	)
	tests := map[string]struct {
		cluster  *model.Cluster
		settings []*model.ClusterSetting

		provider Provider
	}{
		"aws_cluster": {
			cluster: &model.Cluster{
				ID:   uint64(clusterID),
				Org:  uuid.MustParse("00000000-0000-0000-0000-000000000000"),
				Name: "testCluster",
			},
			settings: []*model.ClusterSetting{
				{
					Key:   settings.SettingsKeyCloudProvider,
					Value: settings.CloudProviderAWS,
				},
				{
					Key:   settings.SettingsKeyAwsRegion,
					Value: "region",
				},
				{
					Key:   settings.SettingsKeyAwsVpcEndpointSubnetIDs,
					Value: "vpcEndpointSubnet",
				},
				{
					Key:   settings.SettingsKeyAwsVpcCidr,
					Value: "10.0.0.0/8",
				},
				{
					Key:   settings.SettingsKeyUUID,
					Value: byocClusterUID.String(),
				},
			},
			provider: &AWSProvider{
				agent:             cloudAgent,
				tenantVPCESubnets: []string{"vpcEndpointSubnet"},
				vpcCIDRRange:      "10.0.0.0/8",
				byocClusterUID:    byocClusterUID,
				clusterName:       "testCluster",
			},
		},
		"aws_cluster, byoc": {
			cluster: &model.Cluster{
				ID:   uint64(clusterID),
				Org:  uuid.New(),
				Name: "testCluster",
			},
			settings: []*model.ClusterSetting{
				{
					Key:   settings.SettingsKeyCloudProvider,
					Value: settings.CloudProviderAWS,
				},
				{
					Key:   settings.SettingsKeyAwsRegion,
					Value: "region",
				},
				{
					Key:   settings.SettingsKeyAwsVpcEndpointSubnetIDs,
					Value: "vpcEndpointSubnet",
				},
				{
					Key:   settings.SettingsKeyBYOCCIDR,
					Value: "10.0.0.0/8",
				},
				{
					Key:   settings.SettingsKeyUUID,
					Value: byocClusterUID.String(),
				},
			},
			provider: &AWSProvider{
				agent:             cloudAgent,
				tenantVPCESubnets: []string{"vpcEndpointSubnet"},
				vpcCIDRRange:      "10.0.0.0/8",
				byocClusterUID:    byocClusterUID,
				clusterName:       "testCluster",
			},
		},
		"gcp_cluster": {
			cluster: &model.Cluster{
				ID:   uint64(clusterID),
				Org:  uuid.MustParse("00000000-0000-0000-0000-000000000000"),
				Name: "testCluster",
			},
			settings: []*model.ClusterSetting{
				{
					Key:   settings.SettingsKeyCloudProvider,
					Value: settings.CloudProviderGCP,
				},
				{
					Key:   settings.SettingsKeyGcpTenantPscSubnetSelfLink,
					Value: "subnet",
				},
				{
					Key:   settings.SettingsKeyGcpTenantPscSubnetCidr,
					Value: "cidr",
				},
				{
					Key:   settings.SettingsKeyUUID,
					Value: byocClusterUID.String(),
				},
			},
			provider: &GCPProvider{
				agent:                   cloudAgent,
				tenantPSCSubnetSelfLink: "subnet",
				tenantPSCSubnetCIDR:     "cidr",
				byocClusterUID:          byocClusterUID,
				clusterName:             "testCluster",
			},
		},
		"azr_cluster": {
			cluster: &model.Cluster{
				ID:   uint64(clusterID),
				Org:  uuid.MustParse("00000000-0000-0000-0000-000000000000"),
				Name: "testCluster",
			},
			settings: []*model.ClusterSetting{
				{
					Key:   settings.SettingsKeyCloudProvider,
					Value: settings.CloudProviderAZR,
				},
				{
					Key:   settings.SettingsKeyAzrTenantPrivateLinkSubnetID,
					Value: "subnet",
				},
				{
					Key:   settings.SettingsKeyAzrTenantPrivateLinkSubnetCidr,
					Value: "cidr",
				},
				{
					Key:   settings.SettingsKeyUUID,
					Value: byocClusterUID.String(),
				},
			},
			provider: &AZRProvider{
				agent:                           cloudAgent,
				tenantPrivateEndpointSubnetID:   "subnet",
				tenantPrivateEndpointSubnetCIDR: "cidr",
				byocClusterUID:                  byocClusterUID,
				clusterName:                     "testCluster",
			},
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			q := model_mock.NewMockQuerier(ctrl)

			q.EXPECT().GetClusterById(gomock.Any(), clusterID).
				AnyTimes().
				Return(tt.cluster, nil)
			q.EXPECT().
				GetClusterSettings(gomock.Any(), gomock.Eq(clusterID)).
				AnyTimes().
				Return(tt.settings, nil)
			provider, err := NewFactory(model.NewModel(q)).NewProvider(context.Background(), clusterID, cloudAgent)
			require.NoError(t, err)
			assert.Equal(t, provider, tt.provider)
		})
	}
}
