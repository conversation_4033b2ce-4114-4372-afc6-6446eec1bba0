package network

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pbgen/services/gcp"
	pbk8ssvc "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/risingwave-cloud/cli/operations/byoc/utils/namespace"
	"github.com/risingwavelabs/risingwave-cloud/internal/agent"
	"github.com/risingwavelabs/risingwave-cloud/shared/tenant/tier"
)

type GCPProvider struct {
	agent                   *agent.CloudAgent
	tenantPSCSubnetSelfLink string
	tenantPSCSubnetCIDR     string
	clusterName             string
	byocClusterUID          uuid.UUID
}

type GCPConfig struct {
	Agent                   *agent.CloudAgent
	TenantPSCSubnetSelfLink string
	TenantPSCSubnetCIDR     string
	ClusterName             string
	BYOCClusterUID          uuid.UUID
}

func NewGC<PERSON>ider(cfg *GCPConfig) *GCPProvider {
	return &GCPProvider{
		agent:                   cfg.Agent,
		tenantPSCSubnetSelfLink: cfg.TenantPSCSubnetSelfLink,
		tenantPSCSubnetCIDR:     cfg.TenantPSCSubnetCIDR,
		clusterName:             cfg.ClusterName,
		byocClusterUID:          cfg.BYOCClusterUID,
	}
}
func (p *GCPProvider) CreateTenantFirewallAwait(ctx context.Context, option CreateTenantFirewallOption) error {
	req := &pbk8ssvc.CreateNetworkPolicyRequest{
		ResourceMeta: getDefaultNetworkPolicyResourceMeta(option.ResourceNamespace),
		Spec:         p.getDefaultK8sNetworkPolicySpec(option.ResourceNamespace),
	}

	err := p.agent.CreateNetworkPolicy(ctx, req)
	if err != nil {
		return eris.Wrap(err, "failed to create network policy")
	}
	return nil
}

func (p *GCPProvider) ApplyDefaultK8sNetworkPolicy(ctx context.Context, namespace string) error {
	err := p.agent.CreateOrUpdateNetworkPolicy(ctx, &pbk8ssvc.CreateOrUpdateNetworkPolicyRequest{
		ResourceMeta: getDefaultNetworkPolicyResourceMeta(namespace),
		Spec:         p.getDefaultK8sNetworkPolicySpec(namespace),
	})
	if err != nil {
		return eris.Wrap(err, "failed to create k8s network policy")
	}
	return nil
}

func (p *GCPProvider) getDefaultK8sNetworkPolicySpec(namespace string) *pbk8s.NetworkPolicySpec {
	policyTypes := []pbk8s.NetworkPolicyType{pbk8s.NetworkPolicyType_NETWORK_POLICY_EGRESS, pbk8s.NetworkPolicyType_NETWORK_POLICY_INGRESS}
	ingressPolicyPeers := createTenantIngressPolicyPeers(namespace)

	return &pbk8s.NetworkPolicySpec{
		PodSelector: &pbk8s.LabelSelector{},
		Egress: []*pbk8s.NetworkPolicyEgressRule{
			{
				// Allow all egress except PSC subnet
				To: []*pbk8s.NetworkPolicyPeer{
					{
						IpBlock: &pbk8s.IPBlock{
							Cidr:   "0.0.0.0/0",
							Except: []string{p.tenantPSCSubnetCIDR},
						},
					},
				},
			},
		},
		Ingress:     ingressPolicyPeers,
		PolicyTypes: policyTypes,
	}
}

func (p *GCPProvider) DeleteTenantFirewallAwait(context.Context, string, string, uint64) error {
	return nil
}

func (p *GCPProvider) createPrivateLinkNetworkPolicyAWait(ctx context.Context, option CreatePrivateLinkNetworkPolicyOption) error {
	err := p.agent.CreateNetworkPolicy(ctx, &pbk8ssvc.CreateNetworkPolicyRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        fmt.Sprintf("%s-%s", privateLinkPolicyPrefix, option.PrivateLinkID),
			Namespace: option.Namespace,
		},
		Spec: &pbk8s.NetworkPolicySpec{
			PodSelector: &pbk8s.LabelSelector{},
			Egress: []*pbk8s.NetworkPolicyEgressRule{
				{
					// Allow egress only to provisioned ip
					To: []*pbk8s.NetworkPolicyPeer{
						{
							IpBlock: &pbk8s.IPBlock{
								Cidr: fmt.Sprintf("%s/32", option.Endpoint),
							},
						},
					},
				},
			},
			PolicyTypes: []pbk8s.NetworkPolicyType{pbk8s.NetworkPolicyType_NETWORK_POLICY_EGRESS},
		},
	})
	if err != nil {
		return eris.Wrap(err, "failed to create network policy")
	}
	return nil
}

func (p *GCPProvider) deletePrivateLinkNetworkPolicyAWait(ctx context.Context, namespace string, privateLinkID string) error {
	err := p.agent.DeleteNetworkPolicy(ctx, &pbk8ssvc.DeleteNetworkPolicyRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        fmt.Sprintf("%s-%s", privateLinkPolicyPrefix, privateLinkID),
			Namespace: namespace,
		},
	})
	if err != nil {
		return eris.Wrap(err, "failed to delete network policy")
	}
	return nil
}

func (p *GCPProvider) createIPAndWait(ctx context.Context, tenantID uint64, namespace, privateLinkID, subnet string) (*gcp.GetIPAddressResponse, error) {
	ipName, err := createResourceNameFromPrivateLinkID(namespace, privateLinkID)
	if err != nil {
		return nil, err
	}
	req := p.generateCreateIPRequest(namespace, ipName, subnet)
	res, err := p.agent.CreateGCPIPAndWait(ctx, req, tenantID)
	if err != nil {
		return nil, eris.Wrapf(err, "could not create ip address")
	}
	return res, nil
}

func (p *GCPProvider) DeleteIPAndWait(ctx context.Context, namespace, privateLinkID string) error {
	ipName, err := createResourceNameFromPrivateLinkID(namespace, privateLinkID)
	if err != nil {
		return err
	}
	_, err = p.agent.DeleteGCPIPAndWait(ctx, &gcp.DeleteIPAddressRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        ipName,
			Namespace: namespace,
		},
	})
	if err != nil {
		return eris.Wrapf(err, "could not delete ip")
	}
	return nil
}

func (p *GCPProvider) CreatePrivateLinkAndWait(ctx context.Context, option CreatePrivateLinkOption) (*PrivateLinkMeta, error) {
	ns := option.Namespace
	id := option.PrivateLinkID
	tenantID := option.TenantID

	ipRes, err := p.createIPAndWait(ctx, tenantID, ns, option.PrivateLinkID, p.tenantPSCSubnetSelfLink)
	if err != nil {
		return nil, err
	}

	err = p.createPrivateLinkNetworkPolicyAWait(ctx, CreatePrivateLinkNetworkPolicyOption{
		PrivateLinkID: id,
		Namespace:     ns,
		Endpoint:      ipRes.GetIpAddress(),
	})
	if err != nil {
		return nil, err
	}

	req, err := p.generateCreatePrivateLinkRequest(option, ipRes.GetIpSelflink())
	if err != nil {
		return nil, err
	}

	plRes, err := p.agent.CreateGCPPrivateLinkAndWait(ctx, req, tenantID)
	if err != nil {
		return nil, eris.Wrapf(err, "could not create private link")
	}

	connectionState := plRes.GetPscStatus()
	return &PrivateLinkMeta{
		Endpoint:        ipRes.GetIpAddress(),
		ConnectionState: gcpConnectionStateFromSpec(connectionState),
	}, nil
}

func (p *GCPProvider) DeletePrivateLinkAndWait(ctx context.Context, namespace, privateLinkID string) error {
	privateLinkName, err := createResourceNameFromPrivateLinkID(namespace, privateLinkID)
	if err != nil {
		return eris.Wrap(err, "failed to generate private link name")
	}

	_, err = p.agent.DeleteGCPPrivateLinkAndWait(ctx, &gcp.DeletePrivateServiceConnectEndpointRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        privateLinkName,
			Namespace: namespace,
		},
	})
	if err != nil {
		return eris.Wrapf(err, "could not delete private link")
	}

	err = p.deletePrivateLinkNetworkPolicyAWait(ctx, namespace, privateLinkID)
	if err != nil {
		return eris.Wrapf(err, "could not delete network policy for private link")
	}

	err = p.DeleteIPAndWait(ctx, namespace, privateLinkID)
	if err != nil {
		return err
	}

	return nil
}

func (p *GCPProvider) GetPrivateLink(ctx context.Context, namespace, privateLinkID string) (*PrivateLinkMeta, error) {
	resourceName, err := createResourceNameFromPrivateLinkID(namespace, privateLinkID)
	if err != nil {
		return nil, eris.Wrap(err, "failed to generate private link name")
	}

	res, err := p.agent.GetGCPPrivateLink(ctx, &gcp.GetPrivateServiceConnectEndpointRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        resourceName,
			Namespace: namespace,
		},
	})
	if err != nil {
		return nil, eris.Wrapf(err, "could not get private link")
	}
	if res.GetStatus().GetCode() == pbresource.StatusCode_NOT_FOUND {
		return nil, eris.Errorf("privatelink doesn't exist, id: %s, ns: %s", privateLinkID, namespace).WithCode(eris.CodeNotFound)
	}

	ipRes, err := p.agent.GetGCPIP(ctx, &gcp.GetIPAddressRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        resourceName,
			Namespace: namespace,
		},
	})
	if err != nil {
		return nil, eris.Wrapf(err, "could not get ip, id: %s, ns: %s", resourceName, namespace)
	}

	return &PrivateLinkMeta{
		Endpoint:        ipRes.GetIpAddress(),
		ConnectionState: gcpConnectionStateFromSpec(res.GetPscStatus()),
	}, nil
}

func (p *GCPProvider) generateCreateIPRequest(namespace, name, subnet string) *gcp.CreateIPAddressRequest {
	req := &gcp.CreateIPAddressRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        name,
			Namespace: namespace,
		},
		IpSubnet: subnet,
	}
	return req
}

func (p *GCPProvider) generateCreatePrivateLinkRequest(option CreatePrivateLinkOption, ip string) (*gcp.CreatePrivateServiceConnectEndpointRequest, error) {
	name, err := createResourceNameFromPrivateLinkID(option.Namespace, option.PrivateLinkID)
	if err != nil {
		return nil, eris.Wrap(err, "failed to generate private link name")
	}

	labelEnvValue := p.clusterName
	if option.Tier == string(tier.BYOC) {
		labelEnvValue = namespace.ToBase32(p.byocClusterUID)
	}
	req := &gcp.CreatePrivateServiceConnectEndpointRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        name,
			Namespace: option.Namespace,
		},
		PrivateServiceIp: ip,
		Target:           option.Target,
		ExtraLabels: map[string]string{
			TagEnvKey: labelEnvValue,
		},
	}
	return req, nil
}

func gcpConnectionStateFromSpec(status gcp.PscStatus) PrivateLinkConnectionState {
	switch status {
	case gcp.PscStatus_ACCEPTED:
		return PrivateLinkConnectionAccepted
	case gcp.PscStatus_PENDING:
		return PrivateLinkConnectionPending
	case gcp.PscStatus_REJECTED:
		return PrivateLinkConnectionRejected
	case gcp.PscStatus_CLOSED:
		return PrivateLinkConnectionClosed
	case gcp.PscStatus_STATUS_UNSPECIFIED:
		return PrivateLinkConnectionUnspecified
	}
	return PrivateLinkConnectionUnspecified
}
