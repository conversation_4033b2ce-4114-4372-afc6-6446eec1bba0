package network

import (
	"context"
	"fmt"
	"testing"

	"github.com/google/uuid"
	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	"github.com/risingwavelabs/cloudagent/pbgen/services/gcp"
	pbsvck8s "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	"github.com/risingwavelabs/eris"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"

	"github.com/risingwavelabs/risingwave-cloud/cli/operations/byoc/utils/namespace"
	"github.com/risingwavelabs/risingwave-cloud/internal/agent"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/podstrategy"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/shared/tenant/tier"
)

func TestGCPCreateFirewall(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testNamespace := "rwc-g1h8bikpmjft4bobe659bf95b5-test"
	testCIDR := "***********/18"

	agentWrapper := agent.NewMockAgentWrapper(ctrl)

	agentWrapper.K8sStub.EXPECT().CreateNetworkPolicy(
		gomock.Any(),
		agent.EqProto(&pbsvck8s.CreateNetworkPolicyRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        defaultNetworkPolicyName,
				Namespace: testNamespace,
			},
			Spec: &pbk8s.NetworkPolicySpec{
				PodSelector: &pbk8s.LabelSelector{},
				Egress: []*pbk8s.NetworkPolicyEgressRule{
					{
						// Allow all egress except PSC subnet
						To: []*pbk8s.NetworkPolicyPeer{
							{
								IpBlock: &pbk8s.IPBlock{
									Cidr:   "0.0.0.0/0",
									Except: []string{testCIDR},
								},
							},
						},
					},
				},
				Ingress: []*pbk8s.NetworkPolicyIngressRule{
					{
						// Allow only ingress from same namespace and non-tenant namespace
						From: []*pbk8s.NetworkPolicyPeer{
							{
								NamespaceSelector: &pbk8s.LabelSelector{
									MatchLabels: map[string]string{
										podstrategy.TenantResourceNamespace: testNamespace,
									},
								},
							},
							{
								NamespaceSelector: &pbk8s.LabelSelector{
									MatchExpressions: []*pbk8s.LabelSelectorRequirement{
										{
											Key:      podstrategy.TenantResourceNamespace,
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_DOES_NOT_EXIST,
										},
									},
								},
							},
						},
					},
				},
				PolicyTypes: []pbk8s.NetworkPolicyType{
					pbk8s.NetworkPolicyType_NETWORK_POLICY_EGRESS,
					pbk8s.NetworkPolicyType_NETWORK_POLICY_INGRESS,
				},
			},
		}),
	).Return(
		&pbsvck8s.CreateNetworkPolicyResponse{
			Status: &pbcreation.Status{
				Code: pbcreation.StatusCode_CREATED,
			},
		},
		nil,
	)

	provider := GCPProvider{
		agent:               agentWrapper.Agent,
		tenantPSCSubnetCIDR: testCIDR,
	}
	err := provider.CreateTenantFirewallAwait(context.Background(), CreateTenantFirewallOption{
		TenantID:          1,
		TenantName:        testClusterName,
		ResourceNamespace: testNamespace,
		PodLabelSelector:  testLabelSelector,
	})
	require.NoError(t, err)
}

func TestGCPCreatePrivateLinkPolicy(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testNamespace := "rwc-g1h8bikpmjft4bobe659bf95b5-test"
	testCIDR := "***********/32"
	testID := "1234"

	agentWrapper := agent.NewMockAgentWrapper(ctrl)

	agentWrapper.K8sStub.EXPECT().CreateNetworkPolicy(
		gomock.Any(),
		agent.EqProto(&pbsvck8s.CreateNetworkPolicyRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        fmt.Sprintf("%s-%s", privateLinkPolicyPrefix, testID),
				Namespace: testNamespace,
			},
			Spec: &pbk8s.NetworkPolicySpec{
				PodSelector: &pbk8s.LabelSelector{},
				Egress: []*pbk8s.NetworkPolicyEgressRule{
					{
						To: []*pbk8s.NetworkPolicyPeer{
							{
								IpBlock: &pbk8s.IPBlock{
									Cidr: testCIDR,
								},
							},
						},
					},
				},
				PolicyTypes: []pbk8s.NetworkPolicyType{pbk8s.NetworkPolicyType_NETWORK_POLICY_EGRESS},
			},
		}),
	).Return(
		&pbsvck8s.CreateNetworkPolicyResponse{
			Status: &pbcreation.Status{
				Code: pbcreation.StatusCode_CREATED,
			},
		},
		nil,
	)

	provider := GCPProvider{
		agent:               agentWrapper.Agent,
		tenantPSCSubnetCIDR: testCIDR,
	}
	err := provider.createPrivateLinkNetworkPolicyAWait(context.Background(), CreatePrivateLinkNetworkPolicyOption{
		PrivateLinkID: testID,
		Namespace:     testNamespace,
		Endpoint:      "***********",
	})
	require.NoError(t, err)
}

func TestGCPCreatePrivateLink(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testNamespace := "rwc-g1h8bikpmjft4bobe659bf95b5-test"
	testID := uuid.New()
	testTarget := "testTarget"
	testIP := "testIP"
	testCIDR := "***********/18"
	testSubnet := "testsubnet"
	privateLinkName, err := createResourceNameFromPrivateLinkID(testNamespace, testID.String())
	assert.NoError(t, err)
	byocClusterUID := uuid.New()

	type testCase struct {
		tier           string
		byocClusterUID uuid.UUID
		clusterName    string
	}
	testCases := []testCase{
		{
			tier:           string(tier.BYOC),
			byocClusterUID: byocClusterUID,
		},
		{
			tier:        string(tier.Invited),
			clusterName: "testCluster",
		},
	}

	for _, tc := range testCases {
		agentWrapper := agent.NewMockAgentWrapper(ctrl)

		// create ip address
		agentWrapper.GCPStub.EXPECT().CreateIPAddress(
			gomock.Any(),
			agent.EqProto(&gcp.CreateIPAddressRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        privateLinkName,
					Namespace: testNamespace,
				},
				IpSubnet: testSubnet,
			}),
		).Return(
			&gcp.CreateIPAddressResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_SCHEDULED,
				},
			},
			nil,
		)
		agentWrapper.Querier.EXPECT().UpsertTenantCloudResource(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

		agentWrapper.GCPStub.EXPECT().GetIPAddress(
			gomock.Any(),
			agent.EqProto(&gcp.GetIPAddressRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        privateLinkName,
					Namespace: testNamespace,
				},
			}),
		).Return(
			&gcp.GetIPAddressResponse{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_READY,
				},
				IpAddress:  testIP,
				IpSelflink: testIP,
			},
			nil,
		)

		// create networkpolicy
		agentWrapper.K8sStub.EXPECT().CreateNetworkPolicy(
			gomock.Any(),
			agent.EqProto(&pbsvck8s.CreateNetworkPolicyRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        fmt.Sprintf("%s-%s", privateLinkPolicyPrefix, testID),
					Namespace: testNamespace,
				},
				Spec: &pbk8s.NetworkPolicySpec{
					PodSelector: &pbk8s.LabelSelector{},
					Egress: []*pbk8s.NetworkPolicyEgressRule{
						{
							To: []*pbk8s.NetworkPolicyPeer{
								{
									IpBlock: &pbk8s.IPBlock{
										Cidr: fmt.Sprintf("%s/32", testIP),
									},
								},
							},
						},
					},
					PolicyTypes: []pbk8s.NetworkPolicyType{pbk8s.NetworkPolicyType_NETWORK_POLICY_EGRESS},
				},
			}),
		).Return(
			&pbsvck8s.CreateNetworkPolicyResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_CREATED,
				},
			},
			nil,
		)

		// create private service
		tagEnvValue := tc.clusterName
		if tc.tier == string(tier.BYOC) {
			tagEnvValue = namespace.ToBase32(tc.byocClusterUID)
		}
		agentWrapper.GCPStub.EXPECT().CreatePrivateServiceConnectEndpoint(
			gomock.Any(),
			agent.EqProto(&gcp.CreatePrivateServiceConnectEndpointRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        privateLinkName,
					Namespace: testNamespace,
				},
				Target:           testTarget,
				PrivateServiceIp: testIP,
				ExtraLabels:      map[string]string{"envid": tagEnvValue},
			}),
		).Return(
			&gcp.CreatePrivateServiceConnectEndpointResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_SCHEDULED,
				},
			},
			nil,
		)

		agentWrapper.GCPStub.EXPECT().GetPrivateServiceConnectEndpoint(
			gomock.Any(),
			agent.EqProto(&gcp.GetPrivateServiceConnectEndpointRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        privateLinkName,
					Namespace: testNamespace,
				},
			}),
		).Return(
			&gcp.GetPrivateServiceConnectEndpointResponse{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_READY,
				},
				PscStatus: gcp.PscStatus_ACCEPTED,
			},
			nil,
		)

		provider := GCPProvider{
			agent:                   agentWrapper.Agent,
			tenantPSCSubnetCIDR:     testCIDR,
			tenantPSCSubnetSelfLink: testSubnet,
			byocClusterUID:          byocClusterUID,
			clusterName:             tc.clusterName,
		}
		plMeta, err := provider.CreatePrivateLinkAndWait(context.Background(), CreatePrivateLinkOption{
			TenantID:      testTenantID,
			Target:        testTarget,
			PrivateLinkID: testID.String(),
			Namespace:     testNamespace,
			Tier:          tc.tier,
		})
		require.NoError(t, err)
		assert.Equal(t, PrivateLinkConnectionAccepted, plMeta.ConnectionState)
		assert.Equal(t, testIP, plMeta.Endpoint)
	}
}

func TestGCPDeletePrivateLink(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testNamespace := "rwc-g1h8bikpmjft4bobe659bf95b5-test"
	testPrivateLinkID := uuid.New()

	privateLinkName, err := createResourceNameFromPrivateLinkID(testNamespace, testPrivateLinkID.String())
	assert.NoError(t, err)

	agentWrapper := agent.NewMockAgentWrapper(ctrl)

	// delete private link
	agentWrapper.GCPStub.EXPECT().DeletePrivateServiceConnectEndpoint(
		gomock.Any(),
		agent.EqProto(&gcp.DeletePrivateServiceConnectEndpointRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        privateLinkName,
				Namespace: testNamespace,
			},
		}),
	).Return(
		&gcp.DeletePrivateServiceConnectEndpointResponse{
			Status: &pbdeletion.Status{
				Code: pbdeletion.StatusCode_SCHEDULED,
			},
		},
		nil,
	)
	agentWrapper.Querier.EXPECT().DeleteTenantCloudResource(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	agentWrapper.GCPStub.EXPECT().GetPrivateServiceConnectEndpoint(
		gomock.Any(),
		agent.EqProto(&gcp.GetPrivateServiceConnectEndpointRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        privateLinkName,
				Namespace: testNamespace,
			},
		}),
	).Return(
		&gcp.GetPrivateServiceConnectEndpointResponse{
			Status: &pbresource.Status{
				Code: pbresource.StatusCode_NOT_FOUND,
			},
		},
		nil,
	)

	// delete networkpolicy
	agentWrapper.K8sStub.EXPECT().DeleteNetworkPolicy(
		gomock.Any(),
		agent.EqProto(&pbsvck8s.DeleteNetworkPolicyRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        fmt.Sprintf("%s-%s", privateLinkPolicyPrefix, testPrivateLinkID),
				Namespace: testNamespace,
			},
		}),
	).Return(
		&pbsvck8s.DeleteNetworkPolicyResponse{
			Status: &pbdeletion.Status{
				Code: pbdeletion.StatusCode_DELETED,
			},
		},
		nil,
	)

	// delete ip
	agentWrapper.GCPStub.EXPECT().DeleteIPAddress(
		gomock.Any(),
		agent.EqProto(&gcp.DeleteIPAddressRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        privateLinkName,
				Namespace: testNamespace,
			},
		}),
	).Return(
		&gcp.DeleteIPAddressResponse{
			Status: &pbdeletion.Status{
				Code: pbdeletion.StatusCode_SCHEDULED,
			},
		},
		nil,
	)

	agentWrapper.GCPStub.EXPECT().GetIPAddress(
		gomock.Any(),
		agent.EqProto(&gcp.GetIPAddressRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        privateLinkName,
				Namespace: testNamespace,
			},
		}),
	).Return(
		&gcp.GetIPAddressResponse{
			Status: &pbresource.Status{
				Code: pbresource.StatusCode_NOT_FOUND,
			},
		},
		nil,
	)

	provider := GCPProvider{
		agent: agentWrapper.Agent,
	}
	err = provider.DeletePrivateLinkAndWait(context.Background(), testNamespace, testPrivateLinkID.String())
	require.NoError(t, err)
}

func TestGCPGetPrivateLink(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testNamespace := "rwc-g1h8bikpmjft4bobe659bf95b5-test"
	testID := uuid.New()
	testIP := "testIP"
	privateLinkName, err := createResourceNameFromPrivateLinkID(testNamespace, testID.String())
	assert.NoError(t, err)

	agentWrapper := agent.NewMockAgentWrapper(ctrl)

	agentWrapper.GCPStub.EXPECT().GetPrivateServiceConnectEndpoint(
		gomock.Any(),
		agent.EqProto(&gcp.GetPrivateServiceConnectEndpointRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        privateLinkName,
				Namespace: testNamespace,
			},
		}),
	).Return(
		&gcp.GetPrivateServiceConnectEndpointResponse{
			Status: &pbresource.Status{
				Code: pbresource.StatusCode_READY,
			},
			PscStatus: gcp.PscStatus_ACCEPTED,
		},
		nil,
	)
	agentWrapper.GCPStub.EXPECT().GetPrivateServiceConnectEndpoint(
		gomock.Any(),
		agent.EqProto(&gcp.GetPrivateServiceConnectEndpointRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        "g1h8bikpmjft4bobe659bf95b5-random",
				Namespace: testNamespace,
			},
		}),
	).Return(
		&gcp.GetPrivateServiceConnectEndpointResponse{
			Status: &pbresource.Status{
				Code: pbresource.StatusCode_NOT_FOUND,
			},
		},
		nil,
	)
	agentWrapper.GCPStub.EXPECT().GetIPAddress(
		gomock.Any(),
		agent.EqProto(&gcp.GetIPAddressRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        privateLinkName,
				Namespace: testNamespace,
			},
		}),
	).Return(
		&gcp.GetIPAddressResponse{
			Status: &pbresource.Status{
				Code: pbresource.StatusCode_READY,
			},
			IpAddress:  testIP,
			IpSelflink: testIP,
		},
		nil,
	)

	provider := GCPProvider{
		agent: agentWrapper.Agent,
	}

	res, err := provider.GetPrivateLink(context.Background(), testNamespace, testID.String())
	assert.NoError(t, err)
	assert.Equal(t, testIP, res.Endpoint)
	assert.Equal(t, PrivateLinkConnectionAccepted, res.ConnectionState)

	_, err = provider.GetPrivateLink(context.Background(), testNamespace, "random")
	assert.Equal(t, eris.GetCode(err), eris.CodeNotFound)
}

func TestGCPConnectionStateFromSpec(t *testing.T) {
	res := gcpConnectionStateFromSpec(gcp.PscStatus_ACCEPTED)
	assert.Equal(t, PrivateLinkConnectionAccepted, res)

	res = gcpConnectionStateFromSpec(gcp.PscStatus_PENDING)
	assert.Equal(t, PrivateLinkConnectionPending, res)

	res = gcpConnectionStateFromSpec(gcp.PscStatus_REJECTED)
	assert.Equal(t, PrivateLinkConnectionRejected, res)

	res = gcpConnectionStateFromSpec(gcp.PscStatus_CLOSED)
	assert.Equal(t, PrivateLinkConnectionClosed, res)

	res = gcpConnectionStateFromSpec(gcp.PscStatus_STATUS_UNSPECIFIED)
	assert.Equal(t, PrivateLinkConnectionUnspecified, res)
}

func TestGCPStatusFromSpec(t *testing.T) {
	res := statusFromSpec(pbresource.StatusCode_READY)
	assert.Equal(t, model.PrivateLinkStatusCreated, res)

	res = statusFromSpec(pbresource.StatusCode_ERROR)
	assert.Equal(t, model.PrivateLinkStatusError, res)

	res = statusFromSpec(pbresource.StatusCode_UNKNOWN)
	assert.Equal(t, model.PrivateLinkStatusUnknown, res)
}
