package network

import (
	"context"
)

type CreateTenantFirewallOption struct {
	TenantID          uint64
	TenantName        string
	ResourceNamespace string
	PodLabelSelector  map[string]string
}

type CreatePrivateLinkNetworkPolicyOption struct {
	PrivateLinkID string
	Namespace     string
	Endpoint      string
}

type DeleteVPCEndpointsOption struct {
	TagKey   string
	TagValue string
}

type CreatePrivateLinkOption struct {
	TenantID      uint64
	Namespace     string
	Target        string
	PrivateLinkID string
	Tier          string
}

type PrivateLinkConnectionState string

type PrivateLinkMeta struct {
	Endpoint        string
	ConnectionState PrivateLinkConnectionState
}

const (
	// Private link connection status generalised for all cloud providers
	// Reference:
	// [1]https://docs.aws.amazon.com/vpc/latest/privatelink/concepts.html#concepts-service-consumers
	// [2]https://cloud.google.com/config-connector/docs/reference/resource-docs/compute/computeforwardingrule#status
	// [3]https://learn.microsoft.com/en-us/azure/private-link/private-endpoint-overview#private-endpoint-properties
	PrivateLinkConnectionUnspecified = PrivateLinkConnectionState("STATUS_UNSPECIFIED")
	PrivateLinkConnectionPending     = PrivateLinkConnectionState("PENDING")  // privatelink is waiting for acceptance or is initializing right after acceptance
	PrivateLinkConnectionAccepted    = PrivateLinkConnectionState("ACCEPTED") // privatelink is ready to servce
	PrivateLinkConnectionRejected    = PrivateLinkConnectionState("REJECTED") // privatelink is rejected by upstream service
	PrivateLinkConnectionClosed      = PrivateLinkConnectionState("CLOSED")   // privatelink is unable to servce anymore
	TagEnvKey                        = "envid"
)

// Create a pair of security group: sg for RisingWave Pods
// and sg for endpoint.
// The endpoint sg can be used in resources that only allow to
// communicate with the RisingWave pods. (e.g. PrivateLink endpoint).
type Provider interface {
	// create firewall resources for a RisingWave tenant's pods and resources
	// and wait for the firewall resources to be ready.
	CreateTenantFirewallAwait(ctx context.Context, option CreateTenantFirewallOption) error
	// delete security group for RisingWave pods and security group for endpoint
	DeleteTenantFirewallAwait(ctx context.Context, tenantName, resourceNamespace string, tenantID uint64) error

	// create private link
	CreatePrivateLinkAndWait(ctx context.Context, option CreatePrivateLinkOption) (*PrivateLinkMeta, error)
	DeletePrivateLinkAndWait(ctx context.Context, namespace, privateLinkID string) error
	GetPrivateLink(ctx context.Context, namespace, privateLinkID string) (*PrivateLinkMeta, error)

	ApplyDefaultK8sNetworkPolicy(ctx context.Context, namespace string) error
}

// Network provider config.
type Config interface{}
