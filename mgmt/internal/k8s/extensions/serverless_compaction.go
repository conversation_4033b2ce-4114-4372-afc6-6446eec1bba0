package extensions

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/risingwavelabs/eris"
	"go.uber.org/zap"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbrw "github.com/risingwavelabs/cloudagent/pbgen/common/rw"
	pbk8ssvc "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/logger"

	"github.com/risingwavelabs/risingwave-cloud/internal/agent"
	agentprovider "github.com/risingwavelabs/risingwave-cloud/internal/agent/provider"
	"github.com/risingwavelabs/risingwave-cloud/internal/infra/storage"
	k8sutils "github.com/risingwavelabs/risingwave-cloud/internal/k8s"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/podstrategy"

	"github.com/risingwavelabs/risingwave-cloud/internal/config"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/version"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenant/component"
	nsutil "github.com/risingwavelabs/risingwave-cloud/shared/namespace"
)

type JSON = map[string]any

//go:generate mockgen -source=serverless_compaction.go -destination=./mock/serverless_compaction.go -package=mock
type ServerlessCompaction interface {
	// Enable installs the RisingWave serverless compaction extensions
	Enable(ctx context.Context, tenant model.Tenant, extensionsConfig config.RisingWaveExtensionsCompaction) error

	// PostEnable cleans some temporary resources after the RisingWave serverless compaction extensions are ready.
	PostEnable(ctx context.Context, tenant model.Tenant) error

	// Disable disables the RisingWave serverless compaction extensions
	Disable(ctx context.Context, tenant model.Tenant) error

	// PostDisable cleans some temporary resources after the RisingWave serverless compaction extensions are disabled.
	PostDisable(ctx context.Context, tenant model.Tenant) error

	// WaitForReady waits for the RisingWave serverless compaction extensions to be ready
	WaitForReady(ctx context.Context, tenant model.Tenant) error

	// WaitForDeleted waits for the RisingWave serverless compaction extensions to be deleted
	WaitForDeleted(ctx context.Context, tenant model.Tenant) error

	// Update updates the RisingWave serverless compaction extensions
	Update(ctx context.Context, tenant model.Tenant, extensionConfig config.RisingWaveExtensionsCompaction) error

	// PostUpdate cleans some temporary resources after the RisingWave serverless compaction extensions are updated.
	PostUpdate(ctx context.Context, tenant model.Tenant) error

	// ScaleCompactor scales the defined number of compactors
	ScaleCompactor(ctx context.Context, tenant model.Tenant, number uint32) error

	// GetReleaseValues returns the helm release values for the RisingWave serverless compaction extensions
	GetReleaseValues(ctx context.Context, tenant model.Tenant, extensionConfig config.RisingWaveExtensionsCompaction) (JSON, error)

	// SetStorage sets the storage value in the helm release values
	SetStorage(ctx context.Context, releaseValues JSON, tenant *model.Tenant) error
}

func NewServerlessCompaction(cloudAgentProvider agentprovider.CloudAgentProviderInterface, storageFactory storage.FactoryInterface) ServerlessCompaction {
	return &serverlessCompactionImpl{
		agentProvider:  cloudAgentProvider,
		storageFactory: storageFactory,
	}
}

type serverlessCompactionImpl struct {
	storageFactory storage.FactoryInterface
	agentProvider  agentprovider.CloudAgentProviderInterface
}

const (
	ReleaseNameCompaction = "compaction-extension"
)

func (sc *serverlessCompactionImpl) Enable(ctx context.Context, tenant model.Tenant, extensionsConfig config.RisingWaveExtensionsCompaction) (err error) {
	releaseValues, err := sc.GetReleaseValues(ctx, tenant, extensionsConfig)
	if err != nil {
		return
	}

	taskID, err := installExtensionsCompactionTaskID(tenant)
	if err != nil {
		return
	}

	valuesJSONBytes, marshalErr := json.Marshal(releaseValues)
	if marshalErr != nil {
		err = eris.Wrapf(err, "failed to convert release value to JSON: %v", releaseValues)
		return
	}

	agent, err := sc.agentProvider.GetAgent(ctx, tenant.ClusterID)
	if err != nil {
		return eris.Wrap(err, "failed to get cloud agent")
	}
	defer agent.Close()

	_, err = agent.InstallHelmReleaseAwait(ctx, &pbk8ssvc.InstallHelmReleaseRequest{
		ResourceMeta: &pbresource.Meta{
			Id: taskID,
		},
		ReleaseMeta: &pbresource.Meta{
			Id:        ReleaseNameCompaction,
			Namespace: tenant.ResourceNamespace,
		},
		ChartUrl:   getExtensionsServerlessCompactionChartURL(),
		ValuesJson: string(valuesJSONBytes),
	}, tenant.ID)
	if err != nil {
		err = eris.Wrap(err, "failed to install risingwave compaction extensions helm release through cloudagent")
		return
	}
	return nil
}

func (sc *serverlessCompactionImpl) WaitForDeleted(ctx context.Context, tenant model.Tenant) error {
	agent, err := sc.agentProvider.GetAgent(ctx, tenant.ClusterID)
	if err != nil {
		return eris.Wrap(err, "failed to get cloud agent")
	}
	defer agent.Close()

	return k8sutils.WaitForStatefulSetDeleted(ctx, agent, tenant, getCompactionExtensionsStsName())
}

func (sc *serverlessCompactionImpl) GetReleaseValues(ctx context.Context, tenant model.Tenant, extensionsConfig config.RisingWaveExtensionsCompaction) (releaseValues JSON, err error) {
	extensionsImageVersion, err := version.MapRisingwaveCompactionVersionToExtensionVersion(tenant.ImageTag)
	if err != nil {
		return nil, eris.Wrap(err, "failed to map risingwave version to extension serverless compaction version")
	}
	releaseValues = JSON{
		"risingWaveName": k8sutils.DefaultRisingWaveName,
		"namespace":      tenant.ResourceNamespace,
		"image": JSON{
			"repository": config.Conf.Mgmt.RisingWaveExtensions.Compaction.Image.Repository,
			"tag":        extensionsImageVersion,
		},
		"compactor": JSON{
			"replicas":     1,
			"metaEndpoint": extensionsConfig.Compactor.MetaEndpoint,
			"image": JSON{
				"repository": config.Conf.Mgmt.RisingWave.Repository,
				"tag":        tenant.ImageTag,
			},
			"runtime":   "k8s",
			"configMap": extensionsConfig.Compactor.ConfigMap,
			"resources": JSON{
				"requests": JSON{
					"cpu":    extensionsConfig.Compactor.CPURequest,
					"memory": extensionsConfig.Compactor.MemoryRequest,
				},
				"limits": JSON{
					"cpu":    extensionsConfig.Compactor.CPULimit,
					"memory": extensionsConfig.Compactor.MemoryLimit,
				},
			},
		},
		"scaler": JSON{
			"pollingInterval":               extensionsConfig.Scaler.PollingInterval,
			"collectInterval":               extensionsConfig.Scaler.CollectInterval,
			"scaleDownToZeroRequiredTimes":  extensionsConfig.Scaler.ScaleDownToZeroRequiredTimes,
			"scaleDownToNRequiredTimes":     extensionsConfig.Scaler.ScaleDownToNRequiredTimes,
			"coolDownPeriod":                extensionsConfig.Scaler.CoolDownPeriod,
			"minReplicas":                   extensionsConfig.Scaler.MinReplicas,
			"maxReplicas":                   extensionsConfig.Scaler.MaxReplicas,
			"desiredReplicas":               extensionsConfig.Scaler.DesiredReplicas,
			"expirationExpireTime":          extensionsConfig.Scaler.ExpirationExpireTime,
			"defaultCapacityReservedBuffer": extensionsConfig.Scaler.DefaultCapacityReservedBuffer,
		},
		"kubernetes": JSON{
			"defaultParallelism": extensionsConfig.Scaler.DefaultParallelism,
			"logFormat":          "json",
			"logLevel":           "info",
		},
	}

	sc.setAffinityAndTolerationsForRisingwaveCompactionExtensions(releaseValues, &tenant)
	err = sc.SetStorage(ctx, releaseValues, &tenant)
	if err != nil {
		return nil, err
	}

	return releaseValues, nil
}

func getCompactionExtensionsStsName() string {
	return fmt.Sprintf("%s-serverless-compaction-controller", k8sutils.DefaultRisingWaveName)
}

func installExtensionsCompactionTaskID(tenant model.Tenant) (string, error) {
	uuid, err := nsutil.ParseToBase32(tenant.ResourceNamespace)
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%s-install-serverless-compaction", uuid), nil
}

func updatesExtensionsCompactionTaskID(tenant model.Tenant) (string, error) {
	uuid, err := nsutil.ParseToBase32(tenant.ResourceNamespace)
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%s-update-serverless-compaction", uuid), nil
}

func unInstallExtensionsCompactionTaskID(tenant model.Tenant) (string, error) {
	uuid, err := nsutil.ParseToBase32(tenant.ResourceNamespace)
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%s-uninstall-serverless-compaction", uuid), nil
}

func (sc *serverlessCompactionImpl) setAffinityAndTolerationsForRisingwaveCompactionExtensions(releaseValues JSON, tenant *model.Tenant) {
	if tenant.Resources.IsStandalone() { //nolint:staticcheck,revive
		// Currently we don't have release this feature for standalone/free tenant
	} else {
		sc.setAffinityAndTolerationsForRisingwaveCompactionExtensionRegular(releaseValues, tenant)
	}
}

func (sc *serverlessCompactionImpl) setAffinityAndTolerationsForRisingwaveCompactionExtensionRegular(releaseValues JSON, tenant *model.Tenant) {
	releaseValues["compactor"].(map[string]interface{})["tolerations"] = config.Conf.Mgmt.PodManagement.Compactor.GetNodeTolerationJSON()
	releaseValues["compactor"].(map[string]interface{})["affinity"] = config.Conf.Mgmt.PodManagement.Compactor.GetAffinitiesJSONWithExtras(
		// Serverless compaction currently is hard coded to 1c4m machines.
		[]config.MatchExpression{
			{
				Key:      config.NodeAffinityCPUMemoryRatioKey,
				Operator: "In",
				Values:   []string{"1c4m"},
			},
		},
		[]config.MatchExpression{
			{
				Key:      podstrategy.PodLabelKeyResourceNamespace,
				Operator: "In",
				Values:   []string{tenant.ResourceNamespace},
			},
		},
	)
}

func (sc *serverlessCompactionImpl) SetStorage(ctx context.Context, releaseValues JSON, tenant *model.Tenant) error {
	storageProvider, err := sc.storageFactory.GetStorageProviderFromCluster(ctx, tenant.ClusterID, nil)
	if err != nil {
		return err
	}

	values, err := storageProvider.GetExtensionsStateStoreSpecJSON(ctx, tenant.TenantName, tenant.ResourceNamespace)
	if err != nil {
		return nil
	}
	releaseValues["storage"] = values["storage"]

	return nil
}

func (sc *serverlessCompactionImpl) WaitForReady(ctx context.Context, tenant model.Tenant) error {
	agent, err := sc.agentProvider.GetAgent(ctx, tenant.ClusterID)
	if err != nil {
		return eris.Wrap(err, "failed to get cloud agent")
	}
	defer agent.Close()

	return k8sutils.WaitForStatefulSetUpdate(ctx, agent, tenant.ResourceNamespace, getCompactionExtensionsStsName())
}

func (sc *serverlessCompactionImpl) Disable(ctx context.Context, tenant model.Tenant) error {
	taskID, err := unInstallExtensionsCompactionTaskID(tenant)

	if err != nil {
		return err
	}

	agent, err := sc.agentProvider.GetAgent(ctx, tenant.ClusterID)
	if err != nil {
		return eris.Wrap(err, "failed to get cloud agent")
	}
	defer agent.Close()

	_, err = agent.UninstallHelmReleaseAwait(ctx, &pbk8ssvc.UninstallHelmReleaseRequest{
		ResourceMeta: &pbresource.Meta{
			Id: taskID,
		},
		ReleaseMeta: &pbresource.Meta{
			Id:        ReleaseNameCompaction,
			Namespace: tenant.ResourceNamespace,
		},
	}, tenant.ID)
	if err != nil {
		return eris.Wrap(err, "failed to uninstall risingwave compaction extensions helm release through cloudagent")
	}

	logger.FromCtx(ctx).Info("Successfully disabled risingwave compaction extensions compaction for tenant", zap.String("tenant", tenant.TenantName), zap.String("namespace", tenant.ResourceNamespace), zap.String("release name", ReleaseNameCompaction))
	return nil
}

func (sc *serverlessCompactionImpl) ScaleCompactor(ctx context.Context, tenant model.Tenant, number uint32) error {
	_, compactorResources, err := tenant.Resources.GetReplicasAndResources(component.Compactor)
	if err != nil {
		return eris.Wrap(err, "compactor component not found")
	}
	request := &pbk8ssvc.ScaleRisingWaveRequestOneOf{
		ResourceMeta: k8sutils.GetRisingWaveResourceMeta(tenant),
		Mode: &pbk8ssvc.ScaleRisingWaveRequestOneOf_ClusterSpec{
			ClusterSpec: &pbk8ssvc.ClusterScaleSpec{
				CompactorScaleSpec: &pbrw.ScaleSpec{
					Replicas:  uint32(number),
					Resources: compactorResources.ToResourceRequirementProto(),
				},
			},
		},
	}

	agent, err := sc.agentProvider.GetAgent(ctx, tenant.ClusterID)
	if err != nil {
		return eris.Wrap(err, "failed to get cloud agent")
	}
	defer agent.Close()
	err = agent.ScaleRisingWaveOneOf(ctx, request)
	if err != nil {
		return eris.Wrap(err, "failed to scale risingwave pods")
	}

	logger.FromCtx(ctx).Info("Scale RisingWave compactor node resources",
		zap.String("namespace", tenant.ResourceNamespace),
		zap.String("tenant", tenant.TenantName),
	)
	return nil
}

func (sc *serverlessCompactionImpl) getRisingwaveCompactionExtensionsUpdateReleaseValue(_ context.Context, tenant model.Tenant, extensionsConfig config.RisingWaveExtensionsCompaction) (releaseValues JSON, err error) {
	releaseValues = JSON{
		"risingWaveName": k8sutils.DefaultRisingWaveName,
		"namespace":      tenant.ResourceNamespace,
		"compactor": JSON{
			"resources": JSON{
				"requests": JSON{
					"cpu":    extensionsConfig.Compactor.CPURequest,
					"memory": extensionsConfig.Compactor.MemoryRequest,
				},
				"limits": JSON{
					"cpu":    extensionsConfig.Compactor.CPULimit,
					"memory": extensionsConfig.Compactor.MemoryLimit,
				},
			},
		},
		"scaler": JSON{
			"pollingInterval":               extensionsConfig.Scaler.PollingInterval,
			"collectInterval":               extensionsConfig.Scaler.CollectInterval,
			"scaleDownToZeroRequiredTimes":  extensionsConfig.Scaler.ScaleDownToZeroRequiredTimes,
			"scaleDownToNRequiredTimes":     extensionsConfig.Scaler.ScaleDownToNRequiredTimes,
			"coolDownPeriod":                extensionsConfig.Scaler.CoolDownPeriod,
			"minReplicas":                   extensionsConfig.Scaler.MinReplicas,
			"maxReplicas":                   extensionsConfig.Scaler.MaxReplicas,
			"desiredReplicas":               extensionsConfig.Scaler.DesiredReplicas,
			"expirationExpireTime":          extensionsConfig.Scaler.ExpirationExpireTime,
			"defaultCapacityReservedBuffer": extensionsConfig.Scaler.DefaultCapacityReservedBuffer,
		},
		"kubernetes": JSON{
			"defaultParallelism": extensionsConfig.Scaler.DefaultParallelism,
		},
	}

	return releaseValues, nil
}

func (sc *serverlessCompactionImpl) Update(ctx context.Context, tenant model.Tenant, extensionConfig config.RisingWaveExtensionsCompaction) (err error) {
	releaseValues, err := sc.getRisingwaveCompactionExtensionsUpdateReleaseValue(ctx, tenant, extensionConfig)
	if err != nil {
		return
	}

	taskID, err := updatesExtensionsCompactionTaskID(tenant)
	if err != nil {
		err = eris.Wrap(err, "failed to get update extensions compaction task id")
		return
	}

	valuesJSONBytes, marshalErr := json.Marshal(releaseValues)
	if marshalErr != nil {
		err = eris.Wrapf(err, "failed to convert release value to JSON: %v", releaseValues)
		return
	}

	upgradeCtx, cancel := context.WithTimeout(ctx, agent.PollingInstallHelmReleaseQuickTimeout)
	defer cancel()

	agent, err := sc.agentProvider.GetAgent(ctx, tenant.ClusterID)
	if err != nil {
		return eris.Wrap(err, "failed to get cloud agent")
	}
	defer agent.Close()

	_, err = agent.UpgradeHelmReleaseAwait(upgradeCtx, &pbk8ssvc.UpgradeHelmReleaseRequest{
		ResourceMeta: &pbresource.Meta{
			Id: taskID,
		},
		ReleaseMeta: &pbresource.Meta{
			Id:        ReleaseNameCompaction,
			Namespace: tenant.ResourceNamespace,
		},
		ChartUrl:   getExtensionsServerlessCompactionChartURL(),
		ValuesJson: string(valuesJSONBytes),
	}, tenant.ID)
	if err != nil {
		return eris.Wrap(err, "failed to uninstall risingwave compaction extensions helm release through cloudagent")
	}
	return nil
}

func (sc *serverlessCompactionImpl) PostEnable(ctx context.Context, tenant model.Tenant) error {
	taskID, err := installExtensionsCompactionTaskID(tenant)
	if err != nil {
		return err
	}

	agent, err := sc.agentProvider.GetAgent(ctx, tenant.ClusterID)
	if err != nil {
		return eris.Wrap(err, "failed to get cloud agent")
	}
	defer agent.Close()
	return agent.CleanupInstallHelmReleaseTaskAwait(ctx, taskID)
}

func (sc *serverlessCompactionImpl) PostUpdate(ctx context.Context, tenant model.Tenant) error {
	taskID, err := updatesExtensionsCompactionTaskID(tenant)
	if err != nil {
		return err
	}

	agent, err := sc.agentProvider.GetAgent(ctx, tenant.ClusterID)
	if err != nil {
		return eris.Wrap(err, "failed to get cloud agent")
	}
	defer agent.Close()
	return agent.CleanupUpgradeHelmReleaseTaskAwait(ctx, taskID)
}

func (sc *serverlessCompactionImpl) PostDisable(ctx context.Context, tenant model.Tenant) error {
	taskID, err := unInstallExtensionsCompactionTaskID(tenant)
	if err != nil {
		return err
	}

	agent, err := sc.agentProvider.GetAgent(ctx, tenant.ClusterID)
	if err != nil {
		return eris.Wrap(err, "failed to get cloud agent")
	}
	defer agent.Close()
	return agent.CleanupUninstallHelmReleaseTaskAwait(ctx, taskID)
}

// Helm chart url only needed in enable and update. Not need in disable.
// When update the manifest and result unconsistent, tt is not a problem.
// So use default value in config is ok.
// Example:
// "url": "https://risingwavelabs.github.io/risingwave-extensions/charts/",
// "tag": "risingwave-serverless-compaction-0.2.5.tgz"
// TODO: update the manifest after next release rollout.
func getExtensionsServerlessCompactionChartURL() string {
	return fmt.Sprintf("%s%s", config.Conf.Mgmt.RisingWaveExtensions.Compaction.Chart.URL, config.Conf.Mgmt.RisingWaveExtensions.Compaction.Chart.Tag)
}

// Helper function to get original value for compactor replicas.
func GetServerlessCompactionOriginalCompactorReplica(originalCompactorReplica int32, computeReplica int32) int {
	if originalCompactorReplica > 0 {
		return int(originalCompactorReplica)
	}
	return int((computeReplica + 1) / 2)
}
