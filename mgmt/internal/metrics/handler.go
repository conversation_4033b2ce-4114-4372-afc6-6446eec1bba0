package metrics

import (
	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

func NewMetricszHandler() gin.HandlerFunc {
	registry := prometheus.NewRegistry()

	APIServiceGinMetricsSet.RegisterTo(registry)
	APIAdminGinMetricsSet.RegisterTo(registry)
	ClientMetricsSet.RegisterTo(registry)

	registry.MustRegister(WorkflowCreatedTotal)
	registry.MustRegister(WorkflowCompletedTotal)
	registry.MustRegister(WorkflowFailedTotal)
	registry.MustRegister(WorkflowPanicTotal)
	registry.MustRegister(WorkflowTimeoutTotal)
	registry.MustRegister(WorkflowCancelFailedTotal)
	registry.MustRegister(WorkflowDurationSeconds)
	registry.MustRegister(WorkflowPollingDelaySeconds)

	registry.MustRegister(TenantCreatedTotal)

	registry.MustRegister(SnaphostCreationFailedTotal)
	registry.MustRegister(SnapshotDeletionFailedTotal)

	registry.MustRegister(SnapshotGCFailedTotal)

	registry.MustRegister(SnapshotAvailableCount)
	registry.MustRegister(UntrackedSnapshotCount)
	registry.MustRegister(MaxSnapshotPinDuration)
	registry.MustRegister(SnapshotLastMonitoredAt)

	registry.MustRegister(TenantUnhealthStatus)
	registry.MustRegister(TenantUnhealthStatusLastMonitoredAt)

	registry.MustRegister(TenantTestAlert)
	registry.MustRegister(TenantTestAlertMonitoredAt)

	handler := promhttp.HandlerFor(
		registry,
		promhttp.HandlerOpts{
			EnableOpenMetrics: true,
		},
	)
	return func(c *gin.Context) {
		handler.ServeHTTP(c.Writer, c.Request)
	}
}
