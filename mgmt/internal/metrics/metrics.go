package metrics

import (
	"github.com/prometheus/client_golang/prometheus"

	"github.com/risingwavelabs/risingwave-cloud/shared/ginx"
	"github.com/risingwavelabs/risingwave-cloud/shared/http/clientmetrics"
)

type Labels = prometheus.Labels

const (
	namespace = "mgmt"
)

func Namespace() string {
	return namespace
}

var APIServiceGinMetricsSet = ginx.NewMetricsSet(namespace, "apiservice", "")
var APIAdminGinMetricsSet = ginx.NewMetricsSet(namespace, "apiadmin", "")

var ClientMetricsSet = clientmetrics.NewMetricsSet(namespace, "client", "")

// NOTICE: Don't forget register metrics in metrics.go !

var WorkflowCreatedTotal = prometheus.NewCounterVec(prometheus.CounterOpts{
	Namespace: namespace,
	Subsystem: "workflow",
	Name:      "created_total",
	Help:      "The number of workflows created.",
}, []string{"type"})

var WorkflowCompletedTotal = prometheus.NewCounterVec(prometheus.CounterOpts{
	Namespace: namespace,
	Subsystem: "workflow",
	Name:      "completed_total",
	Help:      "The number of workflows completed.",
}, []string{"type"})

var WorkflowFailedTotal = prometheus.NewCounterVec(prometheus.CounterOpts{
	Namespace: namespace,
	Subsystem: "workflow",
	Name:      "failed_total",
	Help:      "The number of workflows failed.",
}, []string{"type"})

var WorkflowPanicTotal = prometheus.NewCounterVec(prometheus.CounterOpts{
	Namespace: namespace,
	Subsystem: "workflow",
	Name:      "panic_total",
	Help:      "The number of workflows panic.",
}, []string{"type", "state"})

var WorkflowTimeoutTotal = prometheus.NewCounterVec(prometheus.CounterOpts{
	Namespace: namespace,
	Subsystem: "workflow",
	Name:      "timeout_total",
	Help:      "The number of workflows timeout.",
}, []string{"type", "state"})

var WorkflowCancelFailedTotal = prometheus.NewCounterVec(prometheus.CounterOpts{
	Namespace: namespace,
	Subsystem: "workflow",
	Name:      "cancel_failed_total",
	Help:      "The number of workflows failed to be canceled.",
}, []string{"type", "state"})

var WorkflowDurationSeconds = prometheus.NewHistogramVec(prometheus.HistogramOpts{
	Namespace: namespace,
	Subsystem: "workflow",
	Name:      "workflow_duration_seconds",
	Help:      "The time workflow to transition.",
	Buckets:   []float64{.25, .5, 1, 2.5, 5, 10, 25, 50, 100, 250, 500, 1000},
}, []string{"type", "state"})

var WorkflowPollingDelaySeconds = prometheus.NewHistogramVec(prometheus.HistogramOpts{
	Namespace: namespace,
	Subsystem: "workflow",
	Name:      "workflow_polling_delay_seconds",
	Help:      "The duration between workflow be able to polling and the actual polling time.",
	Buckets:   []float64{.1, .25, .5, 1, 2.5, 5, 10, 25, 50, 100, 250, 500},
}, []string{"type"})

var TenantCreatedTotal = prometheus.NewCounterVec(prometheus.CounterOpts{
	Namespace: namespace,
	Subsystem: "tenant",
	Name:      "created_total",
	Help:      "The number of tenant created.",
}, []string{})

var SnaphostCreationFailedTotal = prometheus.NewCounterVec(prometheus.CounterOpts{
	Namespace: namespace,
	Subsystem: "tenant",
	Name:      "snapshot_creation_failed_total",
	Help:      "The number of backup failed.",
}, []string{"tenant_id"})

var SnapshotDeletionFailedTotal = prometheus.NewCounterVec(prometheus.CounterOpts{
	Namespace: namespace,
	Subsystem: "tenant",
	Name:      "snapshot_deletion_failed_total",
	Help:      "The number of snapshot deletion failed.",
}, []string{"tenant_id"})

var SnapshotGCFailedTotal = prometheus.NewCounter(prometheus.CounterOpts{
	Namespace: namespace,
	Subsystem: "tenant",
	Name:      "snapshot_gc_failed_total",
	Help:      "The number of snapshot gc failures total.",
})

var SnapshotAvailableCount = prometheus.NewGaugeVec(prometheus.GaugeOpts{
	Namespace: namespace,
	Subsystem: "tenant",
	Name:      "snapshot_available_count",
	Help:      "The number of available snapshots in MSDB. Creator can be 'user' or 'control-plane'.",
}, []string{"tenant_id", "creator"})

var SnapshotDeletableCount = prometheus.NewGaugeVec(prometheus.GaugeOpts{
	Namespace: namespace,
	Subsystem: "tenant",
	Name:      "snapshot_deletable_count",
	Help:      "The number of available snapshots in MSDB. Creator can be 'user' or 'control-plane'.",
}, []string{"tenant_id", "creator"})

var UntrackedSnapshotCount = prometheus.NewGaugeVec(prometheus.GaugeOpts{
	Namespace: namespace,
	Subsystem: "tenant",
	Name:      "untracked_snapshot_count",
	Help:      "The number of snapshots in RW not recorded in MSDB.",
}, []string{"tenant_id"})

var MaxSnapshotPinDuration = prometheus.NewGaugeVec(prometheus.GaugeOpts{
	Namespace: namespace,
	Subsystem: "tenant",
	Name:      "max_snapshot_pin_duration_seconds",
	Help:      "The duration in seconds since the oldest available snapshot was created.",
}, []string{"tenant_id"})

// SnapshotLastMonitoredAt tracks when each tenant's snapshots were last monitored.
var SnapshotLastMonitoredAt = prometheus.NewGaugeVec(
	prometheus.GaugeOpts{
		Namespace: namespace,
		Subsystem: "tenant",
		Name:      "snapshot_last_monitored_at_seconds",
		Help:      "Unix timestamp when the tenant's snapshots were last monitored",
	},
	[]string{"tenant_id"},
)

var TenantUnhealthStatus = prometheus.NewGaugeVec(
	prometheus.GaugeOpts{
		Namespace: namespace,
		Subsystem: "tenant",
		Name:      "unhealth_status",
		Help:      "The tenant health status, 1 for unhealthy, 0 for healthy.",
	}, []string{"tenant_ns_id"})

// TenantHealthStatusLastMonitoredAt tracks when each tenant's health status were last monitored.
var TenantUnhealthStatusLastMonitoredAt = prometheus.NewGaugeVec(
	prometheus.GaugeOpts{
		Namespace: namespace,
		Subsystem: "tenant",
		Name:      "unhealth_status_last_monitored_at_timestamp",
		Help:      "Unix timestamp when the tenant's health status were last monitored",
	},
	[]string{"tenant_ns_id"},
)

var TenantTestAlert = prometheus.NewGaugeVec(
	prometheus.GaugeOpts{
		Namespace: namespace,
		Subsystem: "tenant",
		Name:      "test_alert",
		Help:      "Used for e2e test to trigger the alert notification workflow",
	}, []string{"tenant_ns_id"})

var TenantTestAlertMonitoredAt = prometheus.NewGaugeVec(
	prometheus.GaugeOpts{
		Namespace: namespace,
		Subsystem: "tenant",
		Name:      "test_alert_last_monitored_at_timestamp",
		Help:      "Unix timestamp when test alert on the tenant were last monitored",
	},
	[]string{"tenant_ns_id"},
)
