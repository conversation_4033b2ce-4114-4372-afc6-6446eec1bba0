package metrics

import (
	"fmt"
	"strconv"
	"time"

	"github.com/google/uuid"
	"github.com/prometheus/client_golang/prometheus"
)

func ReportWorkflowCreated(workflowType string) {
	WorkflowCreatedTotal.With(Labels{"type": workflowType}).Inc()
}

func ReportWorkflowCompleted(workflowType string) {
	WorkflowCompletedTotal.With(Labels{"type": workflowType}).Inc()
}

func ReportWorkflowFailed(workflowType string) {
	WorkflowFailedTotal.With(Labels{"type": workflowType}).Inc()
}

func ReportWorkflowPanic(workflowType string, state string) {
	WorkflowPanicTotal.With(Labels{"type": workflowType, "state": state}).Inc()
}

func ReportWorkflowTimeout(workflowType string, state string) {
	WorkflowTimeoutTotal.With(Labels{"type": workflowType, "state": state}).Inc()
}

func ReportWorkflowCancelFailed(workflowType string, state string) {
	WorkflowCancelFailedTotal.With(Labels{"type": workflowType, "state": state}).Inc()
}

func ReportWorkflowDurationSeconds(workflowType string, state string, value float64) {
	WorkflowDurationSeconds.With(prometheus.Labels{
		"type":  workflowType,
		"state": state,
	}).Observe(value)
}

func ReportWorkflowPollingDelaySeconds(workflowType string, value float64) {
	WorkflowPollingDelaySeconds.With(prometheus.Labels{
		"type": workflowType,
	}).Observe(value)
}

func IncSnaphostCreationFailedTotal(tenantID uint64) {
	SnaphostCreationFailedTotal.With(Labels{"tenant_id": fmt.Sprint(tenantID)}).Inc()
}

func IncSnapshotDeletionFailedTotal(tenantID uint64) {
	SnapshotDeletionFailedTotal.With(Labels{"tenant_id": fmt.Sprint(tenantID)}).Inc()
}

func IncSnapshotGCFailedTotal() {
	SnapshotGCFailedTotal.Inc()
}

// SnapshotMetrics contains all the metrics related to snapshots for a tenant.
type SnapshotMetrics struct {
	UserSnapshots             int
	ControlSnapshots          int
	DeletableUserSnapshots    int
	DeletableControlSnapshots int
	UntrackedSnapshots        int
	PinDurationSeconds        float64
}

// ReportSnapshotStats updates all snapshot-related metrics for a tenant.
func ReportSnapshotStats(tenantID uint64, metrics SnapshotMetrics) {
	tenantIDStr := strconv.FormatUint(tenantID, 10)

	// Update snapshot counts
	SnapshotAvailableCount.WithLabelValues(tenantIDStr, "user").Set(float64(metrics.UserSnapshots))
	SnapshotAvailableCount.WithLabelValues(tenantIDStr, "control-plane").Set(float64(metrics.ControlSnapshots))
	SnapshotDeletableCount.WithLabelValues(tenantIDStr, "user").Set(float64(metrics.DeletableUserSnapshots))
	SnapshotDeletableCount.WithLabelValues(tenantIDStr, "control-plane").Set(float64(metrics.DeletableControlSnapshots))
	UntrackedSnapshotCount.WithLabelValues(tenantIDStr).Set(float64(metrics.UntrackedSnapshots))
	MaxSnapshotPinDuration.WithLabelValues(tenantIDStr).Set(metrics.PinDurationSeconds)
}

// ReportSnapshotMonitored updates the timestamp when snapshots were last monitored for a tenant.
func ReportSnapshotMonitored(tenantID uint64) {
	tenantIDStr := strconv.FormatUint(tenantID, 10)
	SnapshotLastMonitoredAt.WithLabelValues(tenantIDStr).Set(float64(time.Now().Unix()))
}

// ReportSnapshotCreationFailed increments the snapshot creation failure counter for a tenant.
func ReportSnapshotCreationFailed(tenantID uint64) {
	tenantIDStr := strconv.FormatUint(tenantID, 10)
	SnaphostCreationFailedTotal.WithLabelValues(tenantIDStr).Inc()
}

// ReportSnapshotDeletionFailed increments the snapshot deletion failure counter for a tenant.
func ReportSnapshotDeletionFailed(tenantID uint64) {
	tenantIDStr := strconv.FormatUint(tenantID, 10)
	SnapshotDeletionFailedTotal.WithLabelValues(tenantIDStr).Inc()
}

// ReportSnapshotGCFailed increments the snapshot GC failure counter.
func ReportSnapshotGCFailed() {
	SnapshotGCFailedTotal.Inc()
}

// ReportTenantUnhealthStatus will report the health status of a tenant to be unhealthy.
// It sets the health status to 0 for healthy and 1 for unhealthy.
func ReportTenantUnhealthStatus(tenantNsID uuid.UUID, isTenantHealth bool) {
	TenantUnhealthStatusLastMonitoredAt.WithLabelValues(tenantNsID.String()).Set(float64(time.Now().Unix()))
	if isTenantHealth {
		TenantUnhealthStatus.WithLabelValues(tenantNsID.String()).Set(0)
	} else {
		TenantUnhealthStatus.WithLabelValues(tenantNsID.String()).Set(1)
	}
}

func ReportTenantTestAlert(tenantNsID uuid.UUID, isTriggered bool) {
	TenantTestAlertMonitoredAt.WithLabelValues(tenantNsID.String()).Set(float64(time.Now().Unix()))
	if isTriggered {
		TenantTestAlert.WithLabelValues(tenantNsID.String()).Set(1)
	} else {
		TenantTestAlert.WithLabelValues(tenantNsID.String()).Set(0)
	}
}
