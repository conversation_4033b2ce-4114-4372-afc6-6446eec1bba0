package model

import (
	"context"
	stderrors "errors"
	"slices"

	"github.com/jackc/pgerrcode"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/risingwavelabs/eris"
	"github.com/samber/lo"

	"github.com/risingwavelabs/risingwave-cloud/internal/apiservice/errors"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/querier"
)

const (
	ExtensionStatusEnabling  = "Enabling"
	ExtensionStatusDisabling = "Disabling"
	ExtensionStatusRunning   = "Running"
	ExtensionStatusUpdating  = "Updating"
	ExtensionStatusDisabled  = "Disabled"
	ExtensionStatusFailed    = "Failed"
	ExtensionStatusUpgrading = "Upgrading"

	ExtensionsResourceTypeCompaction         = "Compaction"
	ExtensionsResourceTypeServerlessBackfill = "ServerlessBackfill"
	ExtensionsResourceTypeIcebergCompaction  = "IcebergCompaction"
)

var TenantExtensionNil = TenantExtension{}

func (model *Model) GetTenantExtensionByTenantIDAndResourceType(ctx context.Context, tenantID uint64, resourceType string) (TenantExtension, error) {
	ctx, cancel := context.WithTimeout(ctx, QueryTimeout())
	defer cancel()

	row, err := model.querier.GetTenantExtensionByTenantIdAndResourceType(ctx, querier.GetTenantExtensionByTenantIdAndResourceTypeParams{
		TenantID:     tenantID,
		ResourceType: resourceType,
	})
	if err == pgx.ErrNoRows {
		return TenantExtensionNil, errors.ErrTenantExtensionNotExist
	}
	if err != nil {
		return TenantExtensionNil, eris.Wrapf(err, "failed to get tenant extensions. TenantId %d", tenantID)
	}
	return *row, nil
}

func (model *Model) UpdateTenantExtensionStatusByTenantIDAndResourceType(ctx context.Context, tenantID uint64, resourceType string, status string) error {
	ctx, cancel := context.WithTimeout(ctx, UpdateTimeout())
	defer cancel()

	res, err := model.querier.UpdateTenantExtensionStatusByTenantIdAndResourceType(ctx, querier.UpdateTenantExtensionStatusByTenantIdAndResourceTypeParams{TenantID: tenantID, ResourceType: resourceType, Status: status})
	if err != nil {
		return eris.Wrapf(err, "failed to update tenant extension status")
	}

	if res.RowsAffected() != 1 {
		return eris.Errorf("update %d rows", res.RowsAffected()).WithCode(eris.CodeNotFound)
	}
	return nil
}

func (model *Model) CompareAndUpdateTenantExtensionStatusByTenantIDAndResourceTypeAndStatuses(ctx context.Context, tenantID uint64, resourceType string, status string, oldStatuses []string) error {
	ctx, cancel := context.WithTimeout(ctx, UpdateTimeout())
	defer cancel()

	res, err := model.querier.CompareAndUpdateTenantExtensionStatusByTenantIdAndResourceTypeAndStatuses(ctx, querier.CompareAndUpdateTenantExtensionStatusByTenantIdAndResourceTypeAndStatusesParams{
		TenantID:         tenantID,
		ResourceType:     resourceType,
		Status:           status,
		ExpectedStatuses: oldStatuses,
	})
	if err != nil {
		return eris.Wrapf(err, "failed to update tenant extension status")
	}

	if res.RowsAffected() != 1 {
		return eris.Errorf("update %d rows", res.RowsAffected())
	}
	return nil
}

func (model *Model) UpdateTenantExtensionByTenantIDAndResourceTypeIfStatusEquals(ctx context.Context, ext TenantExtension) error {
	ctx, cancel := context.WithTimeout(ctx, UpdateTimeout())
	defer cancel()

	res, err := model.querier.UpdateTenantExtensionByTenantIdAndResourceTypeIfStatusEquals(ctx, querier.UpdateTenantExtensionByTenantIdAndResourceTypeIfStatusEqualsParams{
		TenantID:     ext.TenantID,
		ResourceType: ext.ResourceType,
		Status:       ext.Status,
		Config:       ext.Config,
		Version:      ext.Version,
	})
	if err != nil {
		return eris.Wrapf(err, "failed to update tenant extension status")
	}
	if res.RowsAffected() != 1 {
		return eris.Errorf("update %d rows", res.RowsAffected())
	}

	return nil
}

func (model *Model) CreateTenantExtensionRaw(ctx context.Context, ext TenantExtension) error {
	ctx, cancel := context.WithTimeout(ctx, CreateTimeout())
	defer cancel()

	res, err := model.querier.CreateTenantExtension(ctx, querier.CreateTenantExtensionParams{
		TenantID:     ext.TenantID,
		ResourceType: ext.ResourceType,
		Config:       ext.Config,
		Version:      ext.Version,
		Status:       ext.Status,
	})

	var e *pgconn.PgError
	if stderrors.As(err, &e) && e.Code == pgerrcode.UniqueViolation {
		err := eris.Wrapf(err, "tenant extension %d with resource type %s already exists", ext.TenantID, ext.ResourceType)
		return eris.WithCode(err, eris.CodeAlreadyExists)
	}

	if err != nil {
		return eris.Wrapf(err, "failed to update tenant extension status")
	}

	if res.RowsAffected() != 1 {
		return eris.Errorf("failed to create tenant extension %d", ext.TenantID)
	}

	return nil
}

func (model *Model) CreateTenantExtension(ctx context.Context, tenantID uint64, resourceType string, config string, version string, status string) error {
	return model.CreateTenantExtensionRaw(ctx, TenantExtension{
		TenantID:     tenantID,
		ResourceType: resourceType,
		Config:       &config,
		Version:      version,
		Status:       status,
	})
}

func (model *Model) UpdateTenantExtensionConfigByTenantIDAndResourceType(ctx context.Context, tenantID uint64, resourceType string, config *string) error {
	ctx, cancel := context.WithTimeout(ctx, UpdateTimeout())
	defer cancel()

	res, err := model.querier.UpdateTenantExtensionConfigByTenantIdAndResourceType(ctx, querier.UpdateTenantExtensionConfigByTenantIdAndResourceTypeParams{TenantID: tenantID, ResourceType: resourceType, Config: config})
	if err != nil {
		return eris.Wrapf(err, "failed to update tenant extension config")
	}

	if res.RowsAffected() != 1 {
		return eris.Errorf("update %d rows", res.RowsAffected())
	}
	return nil
}

func (model *Model) GetTenantExtensionsByTenantID(ctx context.Context, id uint64) ([]TenantExtension, error) {
	ctx, cancel := context.WithTimeout(ctx, QueryTimeout())
	defer cancel()

	records, err := model.querier.GetTenantExtensionsByTenantId(ctx, id)
	if err != nil {
		return nil, eris.Wrapf(err, "failed to get tenant extensions of tenant %d", id)
	}

	return lo.Map(records, func(item *TenantExtension, _ int) TenantExtension { return *item }), nil
}

func (model *Model) DeleteTenantExtensionByTenantIDAndResourceType(ctx context.Context, tenantID uint64, resourceType string) error {
	ctx, cancel := context.WithTimeout(ctx, DeleteTimeout())
	defer cancel()

	res, err := model.querier.DeleteTenantExtensionByTenantIdAndResourceType(ctx, querier.DeleteTenantExtensionByTenantIdAndResourceTypeParams{TenantID: tenantID, ResourceType: resourceType})
	if err != nil {
		return eris.Wrapf(err, "failed to delete tenant extension")
	}

	if res.RowsAffected() != 1 {
		return eris.Errorf("delete %d rows", res.RowsAffected())
	}
	return nil
}

// IsExtensionEnabled returns true if the extension status is one of the
// enabled statuses: Running, Enabling, Updating, Upgrading.
func IsExtensionEnabled(status string) bool {
	return slices.Contains([]string{
		ExtensionStatusRunning,
		ExtensionStatusEnabling,
		ExtensionStatusUpdating,
		ExtensionStatusUpgrading,
	}, status)
}
