// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/risingwavelabs/risingwave-cloud/internal/model/querier (interfaces: Querier)
//
// Generated by this command:
//
//	mockgen-v0.5.0 -package=mock -destination=internal/model/mock/querier_gen.go github.com/risingwavelabs/risingwave-cloud/internal/model/querier Querier
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"
	time "time"

	uuid "github.com/google/uuid"
	pgconn "github.com/jackc/pgx/v5/pgconn"
	querier "github.com/risingwavelabs/risingwave-cloud/internal/model/querier"
	gomock "go.uber.org/mock/gomock"
)

// MockQuerier is a mock of Querier interface.
type MockQuerier struct {
	ctrl     *gomock.Controller
	recorder *MockQuerierMockRecorder
	isgomock struct{}
}

// MockQuerierMockRecorder is the mock recorder for MockQuerier.
type MockQuerierMockRecorder struct {
	mock *MockQuerier
}

// NewMockQuerier creates a new mock instance.
func NewMockQuerier(ctrl *gomock.Controller) *MockQuerier {
	mock := &MockQuerier{ctrl: ctrl}
	mock.recorder = &MockQuerierMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockQuerier) EXPECT() *MockQuerierMockRecorder {
	return m.recorder
}

// AcquireAdvisoryXactLock mocks base method.
func (m *MockQuerier) AcquireAdvisoryXactLock(ctx context.Context, pgAdvisoryXactLock int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcquireAdvisoryXactLock", ctx, pgAdvisoryXactLock)
	ret0, _ := ret[0].(error)
	return ret0
}

// AcquireAdvisoryXactLock indicates an expected call of AcquireAdvisoryXactLock.
func (mr *MockQuerierMockRecorder) AcquireAdvisoryXactLock(ctx, pgAdvisoryXactLock any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcquireAdvisoryXactLock", reflect.TypeOf((*MockQuerier)(nil).AcquireAdvisoryXactLock), ctx, pgAdvisoryXactLock)
}

// AwakeWorkflow mocks base method.
func (m *MockQuerier) AwakeWorkflow(ctx context.Context, id uuid.UUID) (pgconn.CommandTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AwakeWorkflow", ctx, id)
	ret0, _ := ret[0].(pgconn.CommandTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AwakeWorkflow indicates an expected call of AwakeWorkflow.
func (mr *MockQuerierMockRecorder) AwakeWorkflow(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AwakeWorkflow", reflect.TypeOf((*MockQuerier)(nil).AwakeWorkflow), ctx, id)
}

// CompareAndDelete mocks base method.
func (m *MockQuerier) CompareAndDelete(ctx context.Context, arg querier.CompareAndDeleteParams) (pgconn.CommandTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CompareAndDelete", ctx, arg)
	ret0, _ := ret[0].(pgconn.CommandTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CompareAndDelete indicates an expected call of CompareAndDelete.
func (mr *MockQuerierMockRecorder) CompareAndDelete(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CompareAndDelete", reflect.TypeOf((*MockQuerier)(nil).CompareAndDelete), ctx, arg)
}

// CompareAndRelease mocks base method.
func (m *MockQuerier) CompareAndRelease(ctx context.Context, arg querier.CompareAndReleaseParams) (pgconn.CommandTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CompareAndRelease", ctx, arg)
	ret0, _ := ret[0].(pgconn.CommandTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CompareAndRelease indicates an expected call of CompareAndRelease.
func (mr *MockQuerierMockRecorder) CompareAndRelease(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CompareAndRelease", reflect.TypeOf((*MockQuerier)(nil).CompareAndRelease), ctx, arg)
}

// CompareAndUpdate mocks base method.
func (m *MockQuerier) CompareAndUpdate(ctx context.Context, arg querier.CompareAndUpdateParams) (pgconn.CommandTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CompareAndUpdate", ctx, arg)
	ret0, _ := ret[0].(pgconn.CommandTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CompareAndUpdate indicates an expected call of CompareAndUpdate.
func (mr *MockQuerierMockRecorder) CompareAndUpdate(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CompareAndUpdate", reflect.TypeOf((*MockQuerier)(nil).CompareAndUpdate), ctx, arg)
}

// CompareAndUpdateTenantExtensionStatusByTenantIdAndResourceTypeAndStatuses mocks base method.
func (m *MockQuerier) CompareAndUpdateTenantExtensionStatusByTenantIdAndResourceTypeAndStatuses(ctx context.Context, arg querier.CompareAndUpdateTenantExtensionStatusByTenantIdAndResourceTypeAndStatusesParams) (pgconn.CommandTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CompareAndUpdateTenantExtensionStatusByTenantIdAndResourceTypeAndStatuses", ctx, arg)
	ret0, _ := ret[0].(pgconn.CommandTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CompareAndUpdateTenantExtensionStatusByTenantIdAndResourceTypeAndStatuses indicates an expected call of CompareAndUpdateTenantExtensionStatusByTenantIdAndResourceTypeAndStatuses.
func (mr *MockQuerierMockRecorder) CompareAndUpdateTenantExtensionStatusByTenantIdAndResourceTypeAndStatuses(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CompareAndUpdateTenantExtensionStatusByTenantIdAndResourceTypeAndStatuses", reflect.TypeOf((*MockQuerier)(nil).CompareAndUpdateTenantExtensionStatusByTenantIdAndResourceTypeAndStatuses), ctx, arg)
}

// CompareAndUpdateTenantHealthStatusById mocks base method.
func (m *MockQuerier) CompareAndUpdateTenantHealthStatusById(ctx context.Context, arg querier.CompareAndUpdateTenantHealthStatusByIdParams) (pgconn.CommandTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CompareAndUpdateTenantHealthStatusById", ctx, arg)
	ret0, _ := ret[0].(pgconn.CommandTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CompareAndUpdateTenantHealthStatusById indicates an expected call of CompareAndUpdateTenantHealthStatusById.
func (mr *MockQuerierMockRecorder) CompareAndUpdateTenantHealthStatusById(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CompareAndUpdateTenantHealthStatusById", reflect.TypeOf((*MockQuerier)(nil).CompareAndUpdateTenantHealthStatusById), ctx, arg)
}

// CompareAndUpdateTenantStatusByIdAndStatuses mocks base method.
func (m *MockQuerier) CompareAndUpdateTenantStatusByIdAndStatuses(ctx context.Context, arg querier.CompareAndUpdateTenantStatusByIdAndStatusesParams) (pgconn.CommandTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CompareAndUpdateTenantStatusByIdAndStatuses", ctx, arg)
	ret0, _ := ret[0].(pgconn.CommandTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CompareAndUpdateTenantStatusByIdAndStatuses indicates an expected call of CompareAndUpdateTenantStatusByIdAndStatuses.
func (mr *MockQuerierMockRecorder) CompareAndUpdateTenantStatusByIdAndStatuses(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CompareAndUpdateTenantStatusByIdAndStatuses", reflect.TypeOf((*MockQuerier)(nil).CompareAndUpdateTenantStatusByIdAndStatuses), ctx, arg)
}

// CountSnapshotsByTenantID mocks base method.
func (m *MockQuerier) CountSnapshotsByTenantID(ctx context.Context, tenantID uint64) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountSnapshotsByTenantID", ctx, tenantID)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountSnapshotsByTenantID indicates an expected call of CountSnapshotsByTenantID.
func (mr *MockQuerierMockRecorder) CountSnapshotsByTenantID(ctx, tenantID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountSnapshotsByTenantID", reflect.TypeOf((*MockQuerier)(nil).CountSnapshotsByTenantID), ctx, tenantID)
}

// CreateCluster mocks base method.
func (m *MockQuerier) CreateCluster(ctx context.Context, arg querier.CreateClusterParams) (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateCluster", ctx, arg)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateCluster indicates an expected call of CreateCluster.
func (mr *MockQuerierMockRecorder) CreateCluster(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateCluster", reflect.TypeOf((*MockQuerier)(nil).CreateCluster), ctx, arg)
}

// CreateClusterSetting mocks base method.
func (m *MockQuerier) CreateClusterSetting(ctx context.Context, arg querier.CreateClusterSettingParams) (pgconn.CommandTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateClusterSetting", ctx, arg)
	ret0, _ := ret[0].(pgconn.CommandTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateClusterSetting indicates an expected call of CreateClusterSetting.
func (mr *MockQuerierMockRecorder) CreateClusterSetting(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateClusterSetting", reflect.TypeOf((*MockQuerier)(nil).CreateClusterSetting), ctx, arg)
}

// CreateEvent mocks base method.
func (m *MockQuerier) CreateEvent(ctx context.Context, arg querier.CreateEventParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateEvent", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateEvent indicates an expected call of CreateEvent.
func (mr *MockQuerierMockRecorder) CreateEvent(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateEvent", reflect.TypeOf((*MockQuerier)(nil).CreateEvent), ctx, arg)
}

// CreatePrivateLink mocks base method.
func (m *MockQuerier) CreatePrivateLink(ctx context.Context, arg querier.CreatePrivateLinkParams) (uuid.UUID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePrivateLink", ctx, arg)
	ret0, _ := ret[0].(uuid.UUID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePrivateLink indicates an expected call of CreatePrivateLink.
func (mr *MockQuerierMockRecorder) CreatePrivateLink(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePrivateLink", reflect.TypeOf((*MockQuerier)(nil).CreatePrivateLink), ctx, arg)
}

// CreateSnapshot mocks base method.
func (m *MockQuerier) CreateSnapshot(ctx context.Context, arg querier.CreateSnapshotParams) (*querier.TenantSnapshot, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSnapshot", ctx, arg)
	ret0, _ := ret[0].(*querier.TenantSnapshot)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSnapshot indicates an expected call of CreateSnapshot.
func (mr *MockQuerierMockRecorder) CreateSnapshot(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSnapshot", reflect.TypeOf((*MockQuerier)(nil).CreateSnapshot), ctx, arg)
}

// CreateTenant mocks base method.
func (m *MockQuerier) CreateTenant(ctx context.Context, arg querier.CreateTenantParams) (*querier.Tenant, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTenant", ctx, arg)
	ret0, _ := ret[0].(*querier.Tenant)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTenant indicates an expected call of CreateTenant.
func (mr *MockQuerierMockRecorder) CreateTenant(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTenant", reflect.TypeOf((*MockQuerier)(nil).CreateTenant), ctx, arg)
}

// CreateTenantAlertNofitication mocks base method.
func (m *MockQuerier) CreateTenantAlertNofitication(ctx context.Context, arg querier.CreateTenantAlertNofiticationParams) (uuid.UUID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTenantAlertNofitication", ctx, arg)
	ret0, _ := ret[0].(uuid.UUID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTenantAlertNofitication indicates an expected call of CreateTenantAlertNofitication.
func (mr *MockQuerierMockRecorder) CreateTenantAlertNofitication(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTenantAlertNofitication", reflect.TypeOf((*MockQuerier)(nil).CreateTenantAlertNofitication), ctx, arg)
}

// CreateTenantAlertNofiticationRecord mocks base method.
func (m *MockQuerier) CreateTenantAlertNofiticationRecord(ctx context.Context, arg querier.CreateTenantAlertNofiticationRecordParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTenantAlertNofiticationRecord", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateTenantAlertNofiticationRecord indicates an expected call of CreateTenantAlertNofiticationRecord.
func (mr *MockQuerierMockRecorder) CreateTenantAlertNofiticationRecord(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTenantAlertNofiticationRecord", reflect.TypeOf((*MockQuerier)(nil).CreateTenantAlertNofiticationRecord), ctx, arg)
}

// CreateTenantExtension mocks base method.
func (m *MockQuerier) CreateTenantExtension(ctx context.Context, arg querier.CreateTenantExtensionParams) (pgconn.CommandTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTenantExtension", ctx, arg)
	ret0, _ := ret[0].(pgconn.CommandTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTenantExtension indicates an expected call of CreateTenantExtension.
func (mr *MockQuerierMockRecorder) CreateTenantExtension(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTenantExtension", reflect.TypeOf((*MockQuerier)(nil).CreateTenantExtension), ctx, arg)
}

// CreateWorkflow mocks base method.
func (m *MockQuerier) CreateWorkflow(ctx context.Context, arg querier.CreateWorkflowParams) (uuid.UUID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateWorkflow", ctx, arg)
	ret0, _ := ret[0].(uuid.UUID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateWorkflow indicates an expected call of CreateWorkflow.
func (mr *MockQuerierMockRecorder) CreateWorkflow(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateWorkflow", reflect.TypeOf((*MockQuerier)(nil).CreateWorkflow), ctx, arg)
}

// DeleteAllClusterSettings mocks base method.
func (m *MockQuerier) DeleteAllClusterSettings(ctx context.Context, managedClusterID uint64) (pgconn.CommandTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteAllClusterSettings", ctx, managedClusterID)
	ret0, _ := ret[0].(pgconn.CommandTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteAllClusterSettings indicates an expected call of DeleteAllClusterSettings.
func (mr *MockQuerierMockRecorder) DeleteAllClusterSettings(ctx, managedClusterID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAllClusterSettings", reflect.TypeOf((*MockQuerier)(nil).DeleteAllClusterSettings), ctx, managedClusterID)
}

// DeleteAllTenantConfigs mocks base method.
func (m *MockQuerier) DeleteAllTenantConfigs(ctx context.Context, tenantID uuid.UUID) (pgconn.CommandTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteAllTenantConfigs", ctx, tenantID)
	ret0, _ := ret[0].(pgconn.CommandTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteAllTenantConfigs indicates an expected call of DeleteAllTenantConfigs.
func (mr *MockQuerierMockRecorder) DeleteAllTenantConfigs(ctx, tenantID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAllTenantConfigs", reflect.TypeOf((*MockQuerier)(nil).DeleteAllTenantConfigs), ctx, tenantID)
}

// DeleteClusterCloudResource mocks base method.
func (m *MockQuerier) DeleteClusterCloudResource(ctx context.Context, arg querier.DeleteClusterCloudResourceParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteClusterCloudResource", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteClusterCloudResource indicates an expected call of DeleteClusterCloudResource.
func (mr *MockQuerierMockRecorder) DeleteClusterCloudResource(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteClusterCloudResource", reflect.TypeOf((*MockQuerier)(nil).DeleteClusterCloudResource), ctx, arg)
}

// DeleteClusterSafely mocks base method.
func (m *MockQuerier) DeleteClusterSafely(ctx context.Context, id uint64) (pgconn.CommandTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteClusterSafely", ctx, id)
	ret0, _ := ret[0].(pgconn.CommandTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteClusterSafely indicates an expected call of DeleteClusterSafely.
func (mr *MockQuerierMockRecorder) DeleteClusterSafely(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteClusterSafely", reflect.TypeOf((*MockQuerier)(nil).DeleteClusterSafely), ctx, id)
}

// DeleteEventExceedLimitByWorkflow mocks base method.
func (m *MockQuerier) DeleteEventExceedLimitByWorkflow(ctx context.Context, arg querier.DeleteEventExceedLimitByWorkflowParams) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteEventExceedLimitByWorkflow", ctx, arg)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteEventExceedLimitByWorkflow indicates an expected call of DeleteEventExceedLimitByWorkflow.
func (mr *MockQuerierMockRecorder) DeleteEventExceedLimitByWorkflow(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteEventExceedLimitByWorkflow", reflect.TypeOf((*MockQuerier)(nil).DeleteEventExceedLimitByWorkflow), ctx, arg)
}

// DeleteFinishedWorkflow mocks base method.
func (m *MockQuerier) DeleteFinishedWorkflow(ctx context.Context, id uuid.UUID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteFinishedWorkflow", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteFinishedWorkflow indicates an expected call of DeleteFinishedWorkflow.
func (mr *MockQuerierMockRecorder) DeleteFinishedWorkflow(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteFinishedWorkflow", reflect.TypeOf((*MockQuerier)(nil).DeleteFinishedWorkflow), ctx, id)
}

// DeleteLegacyTenantAlertNotifications mocks base method.
func (m *MockQuerier) DeleteLegacyTenantAlertNotifications(ctx context.Context, timeWindowDays int32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteLegacyTenantAlertNotifications", ctx, timeWindowDays)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteLegacyTenantAlertNotifications indicates an expected call of DeleteLegacyTenantAlertNotifications.
func (mr *MockQuerierMockRecorder) DeleteLegacyTenantAlertNotifications(ctx, timeWindowDays any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteLegacyTenantAlertNotifications", reflect.TypeOf((*MockQuerier)(nil).DeleteLegacyTenantAlertNotifications), ctx, timeWindowDays)
}

// DeletePrivateLink mocks base method.
func (m *MockQuerier) DeletePrivateLink(ctx context.Context, id uuid.UUID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePrivateLink", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeletePrivateLink indicates an expected call of DeletePrivateLink.
func (mr *MockQuerierMockRecorder) DeletePrivateLink(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePrivateLink", reflect.TypeOf((*MockQuerier)(nil).DeletePrivateLink), ctx, id)
}

// DeleteRwDatabasesByName mocks base method.
func (m *MockQuerier) DeleteRwDatabasesByName(ctx context.Context, arg querier.DeleteRwDatabasesByNameParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteRwDatabasesByName", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteRwDatabasesByName indicates an expected call of DeleteRwDatabasesByName.
func (mr *MockQuerierMockRecorder) DeleteRwDatabasesByName(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteRwDatabasesByName", reflect.TypeOf((*MockQuerier)(nil).DeleteRwDatabasesByName), ctx, arg)
}

// DeleteRwUserById mocks base method.
func (m *MockQuerier) DeleteRwUserById(ctx context.Context, arg querier.DeleteRwUserByIdParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteRwUserById", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteRwUserById indicates an expected call of DeleteRwUserById.
func (mr *MockQuerierMockRecorder) DeleteRwUserById(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteRwUserById", reflect.TypeOf((*MockQuerier)(nil).DeleteRwUserById), ctx, arg)
}

// DeleteSnapshotByTenantIDAndID mocks base method.
func (m *MockQuerier) DeleteSnapshotByTenantIDAndID(ctx context.Context, arg querier.DeleteSnapshotByTenantIDAndIDParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteSnapshotByTenantIDAndID", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteSnapshotByTenantIDAndID indicates an expected call of DeleteSnapshotByTenantIDAndID.
func (mr *MockQuerierMockRecorder) DeleteSnapshotByTenantIDAndID(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteSnapshotByTenantIDAndID", reflect.TypeOf((*MockQuerier)(nil).DeleteSnapshotByTenantIDAndID), ctx, arg)
}

// DeleteSnapshotsByTenantID mocks base method.
func (m *MockQuerier) DeleteSnapshotsByTenantID(ctx context.Context, tenantID uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteSnapshotsByTenantID", ctx, tenantID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteSnapshotsByTenantID indicates an expected call of DeleteSnapshotsByTenantID.
func (mr *MockQuerierMockRecorder) DeleteSnapshotsByTenantID(ctx, tenantID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteSnapshotsByTenantID", reflect.TypeOf((*MockQuerier)(nil).DeleteSnapshotsByTenantID), ctx, tenantID)
}

// DeleteSnapshotsByTenantIDAndIDs mocks base method.
func (m *MockQuerier) DeleteSnapshotsByTenantIDAndIDs(ctx context.Context, arg querier.DeleteSnapshotsByTenantIDAndIDsParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteSnapshotsByTenantIDAndIDs", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteSnapshotsByTenantIDAndIDs indicates an expected call of DeleteSnapshotsByTenantIDAndIDs.
func (mr *MockQuerierMockRecorder) DeleteSnapshotsByTenantIDAndIDs(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteSnapshotsByTenantIDAndIDs", reflect.TypeOf((*MockQuerier)(nil).DeleteSnapshotsByTenantIDAndIDs), ctx, arg)
}

// DeleteTenantAlertNotificationRecord mocks base method.
func (m *MockQuerier) DeleteTenantAlertNotificationRecord(ctx context.Context, arg querier.DeleteTenantAlertNotificationRecordParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTenantAlertNotificationRecord", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteTenantAlertNotificationRecord indicates an expected call of DeleteTenantAlertNotificationRecord.
func (mr *MockQuerierMockRecorder) DeleteTenantAlertNotificationRecord(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTenantAlertNotificationRecord", reflect.TypeOf((*MockQuerier)(nil).DeleteTenantAlertNotificationRecord), ctx, arg)
}

// DeleteTenantAlertNotificationRecordsByNotificationId mocks base method.
func (m *MockQuerier) DeleteTenantAlertNotificationRecordsByNotificationId(ctx context.Context, notificationID uuid.UUID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTenantAlertNotificationRecordsByNotificationId", ctx, notificationID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteTenantAlertNotificationRecordsByNotificationId indicates an expected call of DeleteTenantAlertNotificationRecordsByNotificationId.
func (mr *MockQuerierMockRecorder) DeleteTenantAlertNotificationRecordsByNotificationId(ctx, notificationID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTenantAlertNotificationRecordsByNotificationId", reflect.TypeOf((*MockQuerier)(nil).DeleteTenantAlertNotificationRecordsByNotificationId), ctx, notificationID)
}

// DeleteTenantById mocks base method.
func (m *MockQuerier) DeleteTenantById(ctx context.Context, id uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTenantById", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteTenantById indicates an expected call of DeleteTenantById.
func (mr *MockQuerierMockRecorder) DeleteTenantById(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTenantById", reflect.TypeOf((*MockQuerier)(nil).DeleteTenantById), ctx, id)
}

// DeleteTenantCloudResource mocks base method.
func (m *MockQuerier) DeleteTenantCloudResource(ctx context.Context, arg querier.DeleteTenantCloudResourceParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTenantCloudResource", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteTenantCloudResource indicates an expected call of DeleteTenantCloudResource.
func (mr *MockQuerierMockRecorder) DeleteTenantCloudResource(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTenantCloudResource", reflect.TypeOf((*MockQuerier)(nil).DeleteTenantCloudResource), ctx, arg)
}

// DeleteTenantConfig mocks base method.
func (m *MockQuerier) DeleteTenantConfig(ctx context.Context, arg querier.DeleteTenantConfigParams) (pgconn.CommandTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTenantConfig", ctx, arg)
	ret0, _ := ret[0].(pgconn.CommandTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteTenantConfig indicates an expected call of DeleteTenantConfig.
func (mr *MockQuerierMockRecorder) DeleteTenantConfig(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTenantConfig", reflect.TypeOf((*MockQuerier)(nil).DeleteTenantConfig), ctx, arg)
}

// DeleteTenantExtensionByTenantIdAndResourceType mocks base method.
func (m *MockQuerier) DeleteTenantExtensionByTenantIdAndResourceType(ctx context.Context, arg querier.DeleteTenantExtensionByTenantIdAndResourceTypeParams) (pgconn.CommandTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTenantExtensionByTenantIdAndResourceType", ctx, arg)
	ret0, _ := ret[0].(pgconn.CommandTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteTenantExtensionByTenantIdAndResourceType indicates an expected call of DeleteTenantExtensionByTenantIdAndResourceType.
func (mr *MockQuerierMockRecorder) DeleteTenantExtensionByTenantIdAndResourceType(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTenantExtensionByTenantIdAndResourceType", reflect.TypeOf((*MockQuerier)(nil).DeleteTenantExtensionByTenantIdAndResourceType), ctx, arg)
}

// ForceCancel mocks base method.
func (m *MockQuerier) ForceCancel(ctx context.Context, id uuid.UUID) (pgconn.CommandTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ForceCancel", ctx, id)
	ret0, _ := ret[0].(pgconn.CommandTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ForceCancel indicates an expected call of ForceCancel.
func (mr *MockQuerierMockRecorder) ForceCancel(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ForceCancel", reflect.TypeOf((*MockQuerier)(nil).ForceCancel), ctx, id)
}

// ForceLock mocks base method.
func (m *MockQuerier) ForceLock(ctx context.Context, id uuid.UUID) (*time.Time, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ForceLock", ctx, id)
	ret0, _ := ret[0].(*time.Time)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ForceLock indicates an expected call of ForceLock.
func (mr *MockQuerierMockRecorder) ForceLock(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ForceLock", reflect.TypeOf((*MockQuerier)(nil).ForceLock), ctx, id)
}

// ForceResume mocks base method.
func (m *MockQuerier) ForceResume(ctx context.Context, id uuid.UUID) (pgconn.CommandTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ForceResume", ctx, id)
	ret0, _ := ret[0].(pgconn.CommandTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ForceResume indicates an expected call of ForceResume.
func (mr *MockQuerierMockRecorder) ForceResume(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ForceResume", reflect.TypeOf((*MockQuerier)(nil).ForceResume), ctx, id)
}

// GetAllClustersExceptOrg mocks base method.
func (m *MockQuerier) GetAllClustersExceptOrg(ctx context.Context, org uuid.UUID) ([]*querier.ManagedCluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllClustersExceptOrg", ctx, org)
	ret0, _ := ret[0].([]*querier.ManagedCluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllClustersExceptOrg indicates an expected call of GetAllClustersExceptOrg.
func (mr *MockQuerierMockRecorder) GetAllClustersExceptOrg(ctx, org any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllClustersExceptOrg", reflect.TypeOf((*MockQuerier)(nil).GetAllClustersExceptOrg), ctx, org)
}

// GetClusterById mocks base method.
func (m *MockQuerier) GetClusterById(ctx context.Context, id uint64) (*querier.ManagedCluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterById", ctx, id)
	ret0, _ := ret[0].(*querier.ManagedCluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterById indicates an expected call of GetClusterById.
func (mr *MockQuerierMockRecorder) GetClusterById(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterById", reflect.TypeOf((*MockQuerier)(nil).GetClusterById), ctx, id)
}

// GetClusterByOrgName mocks base method.
func (m *MockQuerier) GetClusterByOrgName(ctx context.Context, arg querier.GetClusterByOrgNameParams) (*querier.ManagedCluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterByOrgName", ctx, arg)
	ret0, _ := ret[0].(*querier.ManagedCluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterByOrgName indicates an expected call of GetClusterByOrgName.
func (mr *MockQuerierMockRecorder) GetClusterByOrgName(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterByOrgName", reflect.TypeOf((*MockQuerier)(nil).GetClusterByOrgName), ctx, arg)
}

// GetClusterByUuid mocks base method.
func (m *MockQuerier) GetClusterByUuid(ctx context.Context, value string) (*querier.ManagedCluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterByUuid", ctx, value)
	ret0, _ := ret[0].(*querier.ManagedCluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterByUuid indicates an expected call of GetClusterByUuid.
func (mr *MockQuerierMockRecorder) GetClusterByUuid(ctx, value any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterByUuid", reflect.TypeOf((*MockQuerier)(nil).GetClusterByUuid), ctx, value)
}

// GetClusterSettings mocks base method.
func (m *MockQuerier) GetClusterSettings(ctx context.Context, managedClusterID uint64) ([]*querier.ManagedClusterSetting, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterSettings", ctx, managedClusterID)
	ret0, _ := ret[0].([]*querier.ManagedClusterSetting)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterSettings indicates an expected call of GetClusterSettings.
func (mr *MockQuerierMockRecorder) GetClusterSettings(ctx, managedClusterID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterSettings", reflect.TypeOf((*MockQuerier)(nil).GetClusterSettings), ctx, managedClusterID)
}

// GetClusterSettingsByKey mocks base method.
func (m *MockQuerier) GetClusterSettingsByKey(ctx context.Context, arg querier.GetClusterSettingsByKeyParams) (*querier.GetClusterSettingsByKeyRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterSettingsByKey", ctx, arg)
	ret0, _ := ret[0].(*querier.GetClusterSettingsByKeyRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterSettingsByKey indicates an expected call of GetClusterSettingsByKey.
func (mr *MockQuerierMockRecorder) GetClusterSettingsByKey(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterSettingsByKey", reflect.TypeOf((*MockQuerier)(nil).GetClusterSettingsByKey), ctx, arg)
}

// GetClusterTenantsCount mocks base method.
func (m *MockQuerier) GetClusterTenantsCount(ctx context.Context, clusterID uint64) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterTenantsCount", ctx, clusterID)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterTenantsCount indicates an expected call of GetClusterTenantsCount.
func (mr *MockQuerierMockRecorder) GetClusterTenantsCount(ctx, clusterID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterTenantsCount", reflect.TypeOf((*MockQuerier)(nil).GetClusterTenantsCount), ctx, clusterID)
}

// GetClustersAndSettingsByOrg mocks base method.
func (m *MockQuerier) GetClustersAndSettingsByOrg(ctx context.Context, arg querier.GetClustersAndSettingsByOrgParams) ([]*querier.GetClustersAndSettingsByOrgRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClustersAndSettingsByOrg", ctx, arg)
	ret0, _ := ret[0].([]*querier.GetClustersAndSettingsByOrgRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClustersAndSettingsByOrg indicates an expected call of GetClustersAndSettingsByOrg.
func (mr *MockQuerierMockRecorder) GetClustersAndSettingsByOrg(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClustersAndSettingsByOrg", reflect.TypeOf((*MockQuerier)(nil).GetClustersAndSettingsByOrg), ctx, arg)
}

// GetClustersByOrg mocks base method.
func (m *MockQuerier) GetClustersByOrg(ctx context.Context, org uuid.UUID) ([]*querier.ManagedCluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClustersByOrg", ctx, org)
	ret0, _ := ret[0].([]*querier.ManagedCluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClustersByOrg indicates an expected call of GetClustersByOrg.
func (mr *MockQuerierMockRecorder) GetClustersByOrg(ctx, org any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClustersByOrg", reflect.TypeOf((*MockQuerier)(nil).GetClustersByOrg), ctx, org)
}

// GetClustersCountByOrg mocks base method.
func (m *MockQuerier) GetClustersCountByOrg(ctx context.Context, org uuid.UUID) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClustersCountByOrg", ctx, org)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClustersCountByOrg indicates an expected call of GetClustersCountByOrg.
func (mr *MockQuerierMockRecorder) GetClustersCountByOrg(ctx, org any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClustersCountByOrg", reflect.TypeOf((*MockQuerier)(nil).GetClustersCountByOrg), ctx, org)
}

// GetEventCountByWorkflow mocks base method.
func (m *MockQuerier) GetEventCountByWorkflow(ctx context.Context, workflowID uuid.UUID) (*querier.GetEventCountByWorkflowRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEventCountByWorkflow", ctx, workflowID)
	ret0, _ := ret[0].(*querier.GetEventCountByWorkflowRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEventCountByWorkflow indicates an expected call of GetEventCountByWorkflow.
func (mr *MockQuerierMockRecorder) GetEventCountByWorkflow(ctx, workflowID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEventCountByWorkflow", reflect.TypeOf((*MockQuerier)(nil).GetEventCountByWorkflow), ctx, workflowID)
}

// GetExpiredTenantAlertNotifications mocks base method.
func (m *MockQuerier) GetExpiredTenantAlertNotifications(ctx context.Context, timeWindowMinutes int32) ([]*querier.TenantAlertNotification, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExpiredTenantAlertNotifications", ctx, timeWindowMinutes)
	ret0, _ := ret[0].([]*querier.TenantAlertNotification)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExpiredTenantAlertNotifications indicates an expected call of GetExpiredTenantAlertNotifications.
func (mr *MockQuerierMockRecorder) GetExpiredTenantAlertNotifications(ctx, timeWindowMinutes any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExpiredTenantAlertNotifications", reflect.TypeOf((*MockQuerier)(nil).GetExpiredTenantAlertNotifications), ctx, timeWindowMinutes)
}

// GetLatestUnprocessedTenantAlertNotifications mocks base method.
func (m *MockQuerier) GetLatestUnprocessedTenantAlertNotifications(ctx context.Context, timeWindowMinutes int32) ([]*querier.TenantAlertNotification, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestUnprocessedTenantAlertNotifications", ctx, timeWindowMinutes)
	ret0, _ := ret[0].([]*querier.TenantAlertNotification)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestUnprocessedTenantAlertNotifications indicates an expected call of GetLatestUnprocessedTenantAlertNotifications.
func (mr *MockQuerierMockRecorder) GetLatestUnprocessedTenantAlertNotifications(ctx, timeWindowMinutes any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestUnprocessedTenantAlertNotifications", reflect.TypeOf((*MockQuerier)(nil).GetLatestUnprocessedTenantAlertNotifications), ctx, timeWindowMinutes)
}

// GetMaxEventId mocks base method.
func (m *MockQuerier) GetMaxEventId(ctx context.Context, workflowID uuid.UUID) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMaxEventId", ctx, workflowID)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMaxEventId indicates an expected call of GetMaxEventId.
func (mr *MockQuerierMockRecorder) GetMaxEventId(ctx, workflowID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMaxEventId", reflect.TypeOf((*MockQuerier)(nil).GetMaxEventId), ctx, workflowID)
}

// GetOrgTenantById mocks base method.
func (m *MockQuerier) GetOrgTenantById(ctx context.Context, arg querier.GetOrgTenantByIdParams) (*querier.Tenant, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrgTenantById", ctx, arg)
	ret0, _ := ret[0].(*querier.Tenant)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrgTenantById indicates an expected call of GetOrgTenantById.
func (mr *MockQuerierMockRecorder) GetOrgTenantById(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrgTenantById", reflect.TypeOf((*MockQuerier)(nil).GetOrgTenantById), ctx, arg)
}

// GetOrgTenantByName mocks base method.
func (m *MockQuerier) GetOrgTenantByName(ctx context.Context, arg querier.GetOrgTenantByNameParams) (*querier.Tenant, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrgTenantByName", ctx, arg)
	ret0, _ := ret[0].(*querier.Tenant)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrgTenantByName indicates an expected call of GetOrgTenantByName.
func (mr *MockQuerierMockRecorder) GetOrgTenantByName(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrgTenantByName", reflect.TypeOf((*MockQuerier)(nil).GetOrgTenantByName), ctx, arg)
}

// GetOrgTenantByNsId mocks base method.
func (m *MockQuerier) GetOrgTenantByNsId(ctx context.Context, arg querier.GetOrgTenantByNsIdParams) (*querier.Tenant, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrgTenantByNsId", ctx, arg)
	ret0, _ := ret[0].(*querier.Tenant)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrgTenantByNsId indicates an expected call of GetOrgTenantByNsId.
func (mr *MockQuerierMockRecorder) GetOrgTenantByNsId(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrgTenantByNsId", reflect.TypeOf((*MockQuerier)(nil).GetOrgTenantByNsId), ctx, arg)
}

// GetOrgTenantsCountBySku mocks base method.
func (m *MockQuerier) GetOrgTenantsCountBySku(ctx context.Context, orgID uuid.UUID) ([]*querier.GetOrgTenantsCountBySkuRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrgTenantsCountBySku", ctx, orgID)
	ret0, _ := ret[0].([]*querier.GetOrgTenantsCountBySkuRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrgTenantsCountBySku indicates an expected call of GetOrgTenantsCountBySku.
func (mr *MockQuerierMockRecorder) GetOrgTenantsCountBySku(ctx, orgID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrgTenantsCountBySku", reflect.TypeOf((*MockQuerier)(nil).GetOrgTenantsCountBySku), ctx, orgID)
}

// GetOrgTenantsCountByStatus mocks base method.
func (m *MockQuerier) GetOrgTenantsCountByStatus(ctx context.Context, orgID uuid.UUID) ([]*querier.GetOrgTenantsCountByStatusRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrgTenantsCountByStatus", ctx, orgID)
	ret0, _ := ret[0].([]*querier.GetOrgTenantsCountByStatusRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrgTenantsCountByStatus indicates an expected call of GetOrgTenantsCountByStatus.
func (mr *MockQuerierMockRecorder) GetOrgTenantsCountByStatus(ctx, orgID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrgTenantsCountByStatus", reflect.TypeOf((*MockQuerier)(nil).GetOrgTenantsCountByStatus), ctx, orgID)
}

// GetPrivateLinkByID mocks base method.
func (m *MockQuerier) GetPrivateLinkByID(ctx context.Context, id uuid.UUID) (*querier.TenantPrivateLink, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPrivateLinkByID", ctx, id)
	ret0, _ := ret[0].(*querier.TenantPrivateLink)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPrivateLinkByID indicates an expected call of GetPrivateLinkByID.
func (mr *MockQuerierMockRecorder) GetPrivateLinkByID(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPrivateLinkByID", reflect.TypeOf((*MockQuerier)(nil).GetPrivateLinkByID), ctx, id)
}

// GetPrivateLinkCountByTenantID mocks base method.
func (m *MockQuerier) GetPrivateLinkCountByTenantID(ctx context.Context, tenantID uint64) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPrivateLinkCountByTenantID", ctx, tenantID)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPrivateLinkCountByTenantID indicates an expected call of GetPrivateLinkCountByTenantID.
func (mr *MockQuerierMockRecorder) GetPrivateLinkCountByTenantID(ctx, tenantID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPrivateLinkCountByTenantID", reflect.TypeOf((*MockQuerier)(nil).GetPrivateLinkCountByTenantID), ctx, tenantID)
}

// GetPrivateLinksByTenantID mocks base method.
func (m *MockQuerier) GetPrivateLinksByTenantID(ctx context.Context, tenantID uint64) ([]*querier.TenantPrivateLink, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPrivateLinksByTenantID", ctx, tenantID)
	ret0, _ := ret[0].([]*querier.TenantPrivateLink)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPrivateLinksByTenantID indicates an expected call of GetPrivateLinksByTenantID.
func (mr *MockQuerierMockRecorder) GetPrivateLinksByTenantID(ctx, tenantID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPrivateLinksByTenantID", reflect.TypeOf((*MockQuerier)(nil).GetPrivateLinksByTenantID), ctx, tenantID)
}

// GetRecentEvents mocks base method.
func (m *MockQuerier) GetRecentEvents(ctx context.Context, arg querier.GetRecentEventsParams) ([]*querier.WorkflowEvent, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRecentEvents", ctx, arg)
	ret0, _ := ret[0].([]*querier.WorkflowEvent)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecentEvents indicates an expected call of GetRecentEvents.
func (mr *MockQuerierMockRecorder) GetRecentEvents(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecentEvents", reflect.TypeOf((*MockQuerier)(nil).GetRecentEvents), ctx, arg)
}

// GetRunningWorkflowOfCluster mocks base method.
func (m *MockQuerier) GetRunningWorkflowOfCluster(ctx context.Context, arg querier.GetRunningWorkflowOfClusterParams) (*querier.Workflow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRunningWorkflowOfCluster", ctx, arg)
	ret0, _ := ret[0].(*querier.Workflow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRunningWorkflowOfCluster indicates an expected call of GetRunningWorkflowOfCluster.
func (mr *MockQuerierMockRecorder) GetRunningWorkflowOfCluster(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRunningWorkflowOfCluster", reflect.TypeOf((*MockQuerier)(nil).GetRunningWorkflowOfCluster), ctx, arg)
}

// GetRunningWorkflowOfSnapshot mocks base method.
func (m *MockQuerier) GetRunningWorkflowOfSnapshot(ctx context.Context, arg querier.GetRunningWorkflowOfSnapshotParams) (*querier.Workflow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRunningWorkflowOfSnapshot", ctx, arg)
	ret0, _ := ret[0].(*querier.Workflow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRunningWorkflowOfSnapshot indicates an expected call of GetRunningWorkflowOfSnapshot.
func (mr *MockQuerierMockRecorder) GetRunningWorkflowOfSnapshot(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRunningWorkflowOfSnapshot", reflect.TypeOf((*MockQuerier)(nil).GetRunningWorkflowOfSnapshot), ctx, arg)
}

// GetRunningWorkflowOfTenant mocks base method.
func (m *MockQuerier) GetRunningWorkflowOfTenant(ctx context.Context, arg querier.GetRunningWorkflowOfTenantParams) (*querier.Workflow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRunningWorkflowOfTenant", ctx, arg)
	ret0, _ := ret[0].(*querier.Workflow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRunningWorkflowOfTenant indicates an expected call of GetRunningWorkflowOfTenant.
func (mr *MockQuerierMockRecorder) GetRunningWorkflowOfTenant(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRunningWorkflowOfTenant", reflect.TypeOf((*MockQuerier)(nil).GetRunningWorkflowOfTenant), ctx, arg)
}

// GetRwDatabases mocks base method.
func (m *MockQuerier) GetRwDatabases(ctx context.Context, tenantID uint64) ([]*querier.RwDatabase, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRwDatabases", ctx, tenantID)
	ret0, _ := ret[0].([]*querier.RwDatabase)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRwDatabases indicates an expected call of GetRwDatabases.
func (mr *MockQuerierMockRecorder) GetRwDatabases(ctx, tenantID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRwDatabases", reflect.TypeOf((*MockQuerier)(nil).GetRwDatabases), ctx, tenantID)
}

// GetRwDatabasesCountByTenantId mocks base method.
func (m *MockQuerier) GetRwDatabasesCountByTenantId(ctx context.Context, tenantID uint64) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRwDatabasesCountByTenantId", ctx, tenantID)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRwDatabasesCountByTenantId indicates an expected call of GetRwDatabasesCountByTenantId.
func (mr *MockQuerierMockRecorder) GetRwDatabasesCountByTenantId(ctx, tenantID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRwDatabasesCountByTenantId", reflect.TypeOf((*MockQuerier)(nil).GetRwDatabasesCountByTenantId), ctx, tenantID)
}

// GetRwDatabasesCountGroupByResourceGroup mocks base method.
func (m *MockQuerier) GetRwDatabasesCountGroupByResourceGroup(ctx context.Context, tenantID uint64) ([]*querier.GetRwDatabasesCountGroupByResourceGroupRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRwDatabasesCountGroupByResourceGroup", ctx, tenantID)
	ret0, _ := ret[0].([]*querier.GetRwDatabasesCountGroupByResourceGroupRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRwDatabasesCountGroupByResourceGroup indicates an expected call of GetRwDatabasesCountGroupByResourceGroup.
func (mr *MockQuerierMockRecorder) GetRwDatabasesCountGroupByResourceGroup(ctx, tenantID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRwDatabasesCountGroupByResourceGroup", reflect.TypeOf((*MockQuerier)(nil).GetRwDatabasesCountGroupByResourceGroup), ctx, tenantID)
}

// GetRwDatabasesPaginated mocks base method.
func (m *MockQuerier) GetRwDatabasesPaginated(ctx context.Context, arg querier.GetRwDatabasesPaginatedParams) ([]*querier.RwDatabase, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRwDatabasesPaginated", ctx, arg)
	ret0, _ := ret[0].([]*querier.RwDatabase)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRwDatabasesPaginated indicates an expected call of GetRwDatabasesPaginated.
func (mr *MockQuerierMockRecorder) GetRwDatabasesPaginated(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRwDatabasesPaginated", reflect.TypeOf((*MockQuerier)(nil).GetRwDatabasesPaginated), ctx, arg)
}

// GetRwUsersCountByTenantId mocks base method.
func (m *MockQuerier) GetRwUsersCountByTenantId(ctx context.Context, tenantID uint64) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRwUsersCountByTenantId", ctx, tenantID)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRwUsersCountByTenantId indicates an expected call of GetRwUsersCountByTenantId.
func (mr *MockQuerierMockRecorder) GetRwUsersCountByTenantId(ctx, tenantID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRwUsersCountByTenantId", reflect.TypeOf((*MockQuerier)(nil).GetRwUsersCountByTenantId), ctx, tenantID)
}

// GetSnapshotByID mocks base method.
func (m *MockQuerier) GetSnapshotByID(ctx context.Context, id uuid.UUID) (*querier.TenantSnapshot, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSnapshotByID", ctx, id)
	ret0, _ := ret[0].(*querier.TenantSnapshot)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSnapshotByID indicates an expected call of GetSnapshotByID.
func (mr *MockQuerierMockRecorder) GetSnapshotByID(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSnapshotByID", reflect.TypeOf((*MockQuerier)(nil).GetSnapshotByID), ctx, id)
}

// GetSnapshotByTenantIDAndID mocks base method.
func (m *MockQuerier) GetSnapshotByTenantIDAndID(ctx context.Context, arg querier.GetSnapshotByTenantIDAndIDParams) (*querier.TenantSnapshot, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSnapshotByTenantIDAndID", ctx, arg)
	ret0, _ := ret[0].(*querier.TenantSnapshot)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSnapshotByTenantIDAndID indicates an expected call of GetSnapshotByTenantIDAndID.
func (mr *MockQuerierMockRecorder) GetSnapshotByTenantIDAndID(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSnapshotByTenantIDAndID", reflect.TypeOf((*MockQuerier)(nil).GetSnapshotByTenantIDAndID), ctx, arg)
}

// GetSnapshotByTenantIDAndRWID mocks base method.
func (m *MockQuerier) GetSnapshotByTenantIDAndRWID(ctx context.Context, arg querier.GetSnapshotByTenantIDAndRWIDParams) (*querier.TenantSnapshot, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSnapshotByTenantIDAndRWID", ctx, arg)
	ret0, _ := ret[0].(*querier.TenantSnapshot)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSnapshotByTenantIDAndRWID indicates an expected call of GetSnapshotByTenantIDAndRWID.
func (mr *MockQuerierMockRecorder) GetSnapshotByTenantIDAndRWID(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSnapshotByTenantIDAndRWID", reflect.TypeOf((*MockQuerier)(nil).GetSnapshotByTenantIDAndRWID), ctx, arg)
}

// GetSnapshotsByStatus mocks base method.
func (m *MockQuerier) GetSnapshotsByStatus(ctx context.Context, status string) ([]*querier.TenantSnapshot, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSnapshotsByStatus", ctx, status)
	ret0, _ := ret[0].([]*querier.TenantSnapshot)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSnapshotsByStatus indicates an expected call of GetSnapshotsByStatus.
func (mr *MockQuerierMockRecorder) GetSnapshotsByStatus(ctx, status any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSnapshotsByStatus", reflect.TypeOf((*MockQuerier)(nil).GetSnapshotsByStatus), ctx, status)
}

// GetSnapshotsByTenantID mocks base method.
func (m *MockQuerier) GetSnapshotsByTenantID(ctx context.Context, tenantID uint64) ([]*querier.TenantSnapshot, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSnapshotsByTenantID", ctx, tenantID)
	ret0, _ := ret[0].([]*querier.TenantSnapshot)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSnapshotsByTenantID indicates an expected call of GetSnapshotsByTenantID.
func (mr *MockQuerierMockRecorder) GetSnapshotsByTenantID(ctx, tenantID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSnapshotsByTenantID", reflect.TypeOf((*MockQuerier)(nil).GetSnapshotsByTenantID), ctx, tenantID)
}

// GetSnapshotsByTenantIDAndRW_Version mocks base method.
func (m *MockQuerier) GetSnapshotsByTenantIDAndRW_Version(ctx context.Context, arg querier.GetSnapshotsByTenantIDAndRW_VersionParams) ([]*querier.TenantSnapshot, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSnapshotsByTenantIDAndRW_Version", ctx, arg)
	ret0, _ := ret[0].([]*querier.TenantSnapshot)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSnapshotsByTenantIDAndRW_Version indicates an expected call of GetSnapshotsByTenantIDAndRW_Version.
func (mr *MockQuerierMockRecorder) GetSnapshotsByTenantIDAndRW_Version(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSnapshotsByTenantIDAndRW_Version", reflect.TypeOf((*MockQuerier)(nil).GetSnapshotsByTenantIDAndRW_Version), ctx, arg)
}

// GetSnapshotsWithoutRWIDByTenantID mocks base method.
func (m *MockQuerier) GetSnapshotsWithoutRWIDByTenantID(ctx context.Context, tenantID uint64) ([]*querier.TenantSnapshot, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSnapshotsWithoutRWIDByTenantID", ctx, tenantID)
	ret0, _ := ret[0].([]*querier.TenantSnapshot)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSnapshotsWithoutRWIDByTenantID indicates an expected call of GetSnapshotsWithoutRWIDByTenantID.
func (mr *MockQuerierMockRecorder) GetSnapshotsWithoutRWIDByTenantID(ctx, tenantID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSnapshotsWithoutRWIDByTenantID", reflect.TypeOf((*MockQuerier)(nil).GetSnapshotsWithoutRWIDByTenantID), ctx, tenantID)
}

// GetTenantAlertNotificationByID mocks base method.
func (m *MockQuerier) GetTenantAlertNotificationByID(ctx context.Context, id uuid.UUID) (*querier.TenantAlertNotification, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTenantAlertNotificationByID", ctx, id)
	ret0, _ := ret[0].(*querier.TenantAlertNotification)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantAlertNotificationByID indicates an expected call of GetTenantAlertNotificationByID.
func (mr *MockQuerierMockRecorder) GetTenantAlertNotificationByID(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantAlertNotificationByID", reflect.TypeOf((*MockQuerier)(nil).GetTenantAlertNotificationByID), ctx, id)
}

// GetTenantAlertNotificationRecordCountByNotificationId mocks base method.
func (m *MockQuerier) GetTenantAlertNotificationRecordCountByNotificationId(ctx context.Context, notificationID uuid.UUID) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTenantAlertNotificationRecordCountByNotificationId", ctx, notificationID)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantAlertNotificationRecordCountByNotificationId indicates an expected call of GetTenantAlertNotificationRecordCountByNotificationId.
func (mr *MockQuerierMockRecorder) GetTenantAlertNotificationRecordCountByNotificationId(ctx, notificationID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantAlertNotificationRecordCountByNotificationId", reflect.TypeOf((*MockQuerier)(nil).GetTenantAlertNotificationRecordCountByNotificationId), ctx, notificationID)
}

// GetTenantAlertNotificationRecords mocks base method.
func (m *MockQuerier) GetTenantAlertNotificationRecords(ctx context.Context) ([]*querier.TenantAlertNotificationRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTenantAlertNotificationRecords", ctx)
	ret0, _ := ret[0].([]*querier.TenantAlertNotificationRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantAlertNotificationRecords indicates an expected call of GetTenantAlertNotificationRecords.
func (mr *MockQuerierMockRecorder) GetTenantAlertNotificationRecords(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantAlertNotificationRecords", reflect.TypeOf((*MockQuerier)(nil).GetTenantAlertNotificationRecords), ctx)
}

// GetTenantById mocks base method.
func (m *MockQuerier) GetTenantById(ctx context.Context, id uint64) (*querier.Tenant, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTenantById", ctx, id)
	ret0, _ := ret[0].(*querier.Tenant)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantById indicates an expected call of GetTenantById.
func (mr *MockQuerierMockRecorder) GetTenantById(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantById", reflect.TypeOf((*MockQuerier)(nil).GetTenantById), ctx, id)
}

// GetTenantByNsId mocks base method.
func (m *MockQuerier) GetTenantByNsId(ctx context.Context, nsID uuid.UUID) (*querier.Tenant, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTenantByNsId", ctx, nsID)
	ret0, _ := ret[0].(*querier.Tenant)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantByNsId indicates an expected call of GetTenantByNsId.
func (mr *MockQuerierMockRecorder) GetTenantByNsId(ctx, nsID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantByNsId", reflect.TypeOf((*MockQuerier)(nil).GetTenantByNsId), ctx, nsID)
}

// GetTenantCloudResourcesByTypeAndTenantID mocks base method.
func (m *MockQuerier) GetTenantCloudResourcesByTypeAndTenantID(ctx context.Context, arg querier.GetTenantCloudResourcesByTypeAndTenantIDParams) ([]*querier.TenantCloudResource, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTenantCloudResourcesByTypeAndTenantID", ctx, arg)
	ret0, _ := ret[0].([]*querier.TenantCloudResource)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantCloudResourcesByTypeAndTenantID indicates an expected call of GetTenantCloudResourcesByTypeAndTenantID.
func (mr *MockQuerierMockRecorder) GetTenantCloudResourcesByTypeAndTenantID(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantCloudResourcesByTypeAndTenantID", reflect.TypeOf((*MockQuerier)(nil).GetTenantCloudResourcesByTypeAndTenantID), ctx, arg)
}

// GetTenantExtensionByTenantIdAndResourceType mocks base method.
func (m *MockQuerier) GetTenantExtensionByTenantIdAndResourceType(ctx context.Context, arg querier.GetTenantExtensionByTenantIdAndResourceTypeParams) (*querier.TenantExtension, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTenantExtensionByTenantIdAndResourceType", ctx, arg)
	ret0, _ := ret[0].(*querier.TenantExtension)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantExtensionByTenantIdAndResourceType indicates an expected call of GetTenantExtensionByTenantIdAndResourceType.
func (mr *MockQuerierMockRecorder) GetTenantExtensionByTenantIdAndResourceType(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantExtensionByTenantIdAndResourceType", reflect.TypeOf((*MockQuerier)(nil).GetTenantExtensionByTenantIdAndResourceType), ctx, arg)
}

// GetTenantExtensionsByTenantId mocks base method.
func (m *MockQuerier) GetTenantExtensionsByTenantId(ctx context.Context, tenantID uint64) ([]*querier.TenantExtension, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTenantExtensionsByTenantId", ctx, tenantID)
	ret0, _ := ret[0].([]*querier.TenantExtension)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantExtensionsByTenantId indicates an expected call of GetTenantExtensionsByTenantId.
func (mr *MockQuerierMockRecorder) GetTenantExtensionsByTenantId(ctx, tenantID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantExtensionsByTenantId", reflect.TypeOf((*MockQuerier)(nil).GetTenantExtensionsByTenantId), ctx, tenantID)
}

// GetTenantIDsWithTimedoutSnapshots mocks base method.
func (m *MockQuerier) GetTenantIDsWithTimedoutSnapshots(ctx context.Context, arg querier.GetTenantIDsWithTimedoutSnapshotsParams) ([]uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTenantIDsWithTimedoutSnapshots", ctx, arg)
	ret0, _ := ret[0].([]uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantIDsWithTimedoutSnapshots indicates an expected call of GetTenantIDsWithTimedoutSnapshots.
func (mr *MockQuerierMockRecorder) GetTenantIDsWithTimedoutSnapshots(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantIDsWithTimedoutSnapshots", reflect.TypeOf((*MockQuerier)(nil).GetTenantIDsWithTimedoutSnapshots), ctx, arg)
}

// GetTenants mocks base method.
func (m *MockQuerier) GetTenants(ctx context.Context, arg querier.GetTenantsParams) ([]*querier.Tenant, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTenants", ctx, arg)
	ret0, _ := ret[0].([]*querier.Tenant)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenants indicates an expected call of GetTenants.
func (mr *MockQuerierMockRecorder) GetTenants(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenants", reflect.TypeOf((*MockQuerier)(nil).GetTenants), ctx, arg)
}

// GetTenantsByOrgId mocks base method.
func (m *MockQuerier) GetTenantsByOrgId(ctx context.Context, arg querier.GetTenantsByOrgIdParams) ([]*querier.Tenant, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTenantsByOrgId", ctx, arg)
	ret0, _ := ret[0].([]*querier.Tenant)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsByOrgId indicates an expected call of GetTenantsByOrgId.
func (mr *MockQuerierMockRecorder) GetTenantsByOrgId(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsByOrgId", reflect.TypeOf((*MockQuerier)(nil).GetTenantsByOrgId), ctx, arg)
}

// GetTenantsCount mocks base method.
func (m *MockQuerier) GetTenantsCount(ctx context.Context) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTenantsCount", ctx)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsCount indicates an expected call of GetTenantsCount.
func (mr *MockQuerierMockRecorder) GetTenantsCount(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsCount", reflect.TypeOf((*MockQuerier)(nil).GetTenantsCount), ctx)
}

// GetTenantsCountByOrgId mocks base method.
func (m *MockQuerier) GetTenantsCountByOrgId(ctx context.Context, orgID uuid.UUID) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTenantsCountByOrgId", ctx, orgID)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsCountByOrgId indicates an expected call of GetTenantsCountByOrgId.
func (mr *MockQuerierMockRecorder) GetTenantsCountByOrgId(ctx, orgID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsCountByOrgId", reflect.TypeOf((*MockQuerier)(nil).GetTenantsCountByOrgId), ctx, orgID)
}

// GetTenantsForGarbageCollection mocks base method.
func (m *MockQuerier) GetTenantsForGarbageCollection(ctx context.Context) ([]*querier.Tenant, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTenantsForGarbageCollection", ctx)
	ret0, _ := ret[0].([]*querier.Tenant)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsForGarbageCollection indicates an expected call of GetTenantsForGarbageCollection.
func (mr *MockQuerierMockRecorder) GetTenantsForGarbageCollection(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsForGarbageCollection", reflect.TypeOf((*MockQuerier)(nil).GetTenantsForGarbageCollection), ctx)
}

// GetWorkflow mocks base method.
func (m *MockQuerier) GetWorkflow(ctx context.Context, id uuid.UUID) (*querier.Workflow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorkflow", ctx, id)
	ret0, _ := ret[0].(*querier.Workflow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWorkflow indicates an expected call of GetWorkflow.
func (mr *MockQuerierMockRecorder) GetWorkflow(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkflow", reflect.TypeOf((*MockQuerier)(nil).GetWorkflow), ctx, id)
}

// GetWorkflowByType mocks base method.
func (m *MockQuerier) GetWorkflowByType(ctx context.Context, workflowType string) (*querier.Workflow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorkflowByType", ctx, workflowType)
	ret0, _ := ret[0].(*querier.Workflow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWorkflowByType indicates an expected call of GetWorkflowByType.
func (mr *MockQuerierMockRecorder) GetWorkflowByType(ctx, workflowType any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkflowByType", reflect.TypeOf((*MockQuerier)(nil).GetWorkflowByType), ctx, workflowType)
}

// GetWorkflowFinishedBefore mocks base method.
func (m *MockQuerier) GetWorkflowFinishedBefore(ctx context.Context, updatedAt time.Time) ([]uuid.UUID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorkflowFinishedBefore", ctx, updatedAt)
	ret0, _ := ret[0].([]uuid.UUID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWorkflowFinishedBefore indicates an expected call of GetWorkflowFinishedBefore.
func (mr *MockQuerierMockRecorder) GetWorkflowFinishedBefore(ctx, updatedAt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkflowFinishedBefore", reflect.TypeOf((*MockQuerier)(nil).GetWorkflowFinishedBefore), ctx, updatedAt)
}

// GetWorkflowUpdatedAfter mocks base method.
func (m *MockQuerier) GetWorkflowUpdatedAfter(ctx context.Context, updatedAt time.Time) ([]uuid.UUID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorkflowUpdatedAfter", ctx, updatedAt)
	ret0, _ := ret[0].([]uuid.UUID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWorkflowUpdatedAfter indicates an expected call of GetWorkflowUpdatedAfter.
func (mr *MockQuerierMockRecorder) GetWorkflowUpdatedAfter(ctx, updatedAt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkflowUpdatedAfter", reflect.TypeOf((*MockQuerier)(nil).GetWorkflowUpdatedAfter), ctx, updatedAt)
}

// GetWorkflows mocks base method.
func (m *MockQuerier) GetWorkflows(ctx context.Context, arg querier.GetWorkflowsParams) ([]*querier.Workflow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorkflows", ctx, arg)
	ret0, _ := ret[0].([]*querier.Workflow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWorkflows indicates an expected call of GetWorkflows.
func (mr *MockQuerierMockRecorder) GetWorkflows(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkflows", reflect.TypeOf((*MockQuerier)(nil).GetWorkflows), ctx, arg)
}

// IsPrivateLinkIdExist mocks base method.
func (m *MockQuerier) IsPrivateLinkIdExist(ctx context.Context, id uuid.UUID) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsPrivateLinkIdExist", ctx, id)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsPrivateLinkIdExist indicates an expected call of IsPrivateLinkIdExist.
func (mr *MockQuerierMockRecorder) IsPrivateLinkIdExist(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsPrivateLinkIdExist", reflect.TypeOf((*MockQuerier)(nil).IsPrivateLinkIdExist), ctx, id)
}

// IsPrivateLinkOwnedByTenant mocks base method.
func (m *MockQuerier) IsPrivateLinkOwnedByTenant(ctx context.Context, arg querier.IsPrivateLinkOwnedByTenantParams) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsPrivateLinkOwnedByTenant", ctx, arg)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsPrivateLinkOwnedByTenant indicates an expected call of IsPrivateLinkOwnedByTenant.
func (mr *MockQuerierMockRecorder) IsPrivateLinkOwnedByTenant(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsPrivateLinkOwnedByTenant", reflect.TypeOf((*MockQuerier)(nil).IsPrivateLinkOwnedByTenant), ctx, arg)
}

// IsRwDatabaseExist mocks base method.
func (m *MockQuerier) IsRwDatabaseExist(ctx context.Context, arg querier.IsRwDatabaseExistParams) (int32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsRwDatabaseExist", ctx, arg)
	ret0, _ := ret[0].(int32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsRwDatabaseExist indicates an expected call of IsRwDatabaseExist.
func (mr *MockQuerierMockRecorder) IsRwDatabaseExist(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsRwDatabaseExist", reflect.TypeOf((*MockQuerier)(nil).IsRwDatabaseExist), ctx, arg)
}

// IsRwUserExist mocks base method.
func (m *MockQuerier) IsRwUserExist(ctx context.Context, arg querier.IsRwUserExistParams) (int32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsRwUserExist", ctx, arg)
	ret0, _ := ret[0].(int32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsRwUserExist indicates an expected call of IsRwUserExist.
func (mr *MockQuerierMockRecorder) IsRwUserExist(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsRwUserExist", reflect.TypeOf((*MockQuerier)(nil).IsRwUserExist), ctx, arg)
}

// ListAutoDeletableSnapshotsByTenantIDAndStatus mocks base method.
func (m *MockQuerier) ListAutoDeletableSnapshotsByTenantIDAndStatus(ctx context.Context, arg querier.ListAutoDeletableSnapshotsByTenantIDAndStatusParams) ([]*querier.TenantSnapshot, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAutoDeletableSnapshotsByTenantIDAndStatus", ctx, arg)
	ret0, _ := ret[0].([]*querier.TenantSnapshot)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAutoDeletableSnapshotsByTenantIDAndStatus indicates an expected call of ListAutoDeletableSnapshotsByTenantIDAndStatus.
func (mr *MockQuerierMockRecorder) ListAutoDeletableSnapshotsByTenantIDAndStatus(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAutoDeletableSnapshotsByTenantIDAndStatus", reflect.TypeOf((*MockQuerier)(nil).ListAutoDeletableSnapshotsByTenantIDAndStatus), ctx, arg)
}

// ListRwUsers mocks base method.
func (m *MockQuerier) ListRwUsers(ctx context.Context, tenantID uint64) ([]*querier.RwUser, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListRwUsers", ctx, tenantID)
	ret0, _ := ret[0].([]*querier.RwUser)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListRwUsers indicates an expected call of ListRwUsers.
func (mr *MockQuerierMockRecorder) ListRwUsers(ctx, tenantID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListRwUsers", reflect.TypeOf((*MockQuerier)(nil).ListRwUsers), ctx, tenantID)
}

// ListRwUsersWithPaginated mocks base method.
func (m *MockQuerier) ListRwUsersWithPaginated(ctx context.Context, arg querier.ListRwUsersWithPaginatedParams) ([]*querier.RwUser, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListRwUsersWithPaginated", ctx, arg)
	ret0, _ := ret[0].([]*querier.RwUser)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListRwUsersWithPaginated indicates an expected call of ListRwUsersWithPaginated.
func (mr *MockQuerierMockRecorder) ListRwUsersWithPaginated(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListRwUsersWithPaginated", reflect.TypeOf((*MockQuerier)(nil).ListRwUsersWithPaginated), ctx, arg)
}

// ListSnapshotsByTenantID mocks base method.
func (m *MockQuerier) ListSnapshotsByTenantID(ctx context.Context, arg querier.ListSnapshotsByTenantIDParams) ([]*querier.TenantSnapshot, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListSnapshotsByTenantID", ctx, arg)
	ret0, _ := ret[0].([]*querier.TenantSnapshot)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListSnapshotsByTenantID indicates an expected call of ListSnapshotsByTenantID.
func (mr *MockQuerierMockRecorder) ListSnapshotsByTenantID(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListSnapshotsByTenantID", reflect.TypeOf((*MockQuerier)(nil).ListSnapshotsByTenantID), ctx, arg)
}

// ListSnapshotsByTenantIDAndCreatedBy mocks base method.
func (m *MockQuerier) ListSnapshotsByTenantIDAndCreatedBy(ctx context.Context, arg querier.ListSnapshotsByTenantIDAndCreatedByParams) ([]*querier.TenantSnapshot, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListSnapshotsByTenantIDAndCreatedBy", ctx, arg)
	ret0, _ := ret[0].([]*querier.TenantSnapshot)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListSnapshotsByTenantIDAndCreatedBy indicates an expected call of ListSnapshotsByTenantIDAndCreatedBy.
func (mr *MockQuerierMockRecorder) ListSnapshotsByTenantIDAndCreatedBy(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListSnapshotsByTenantIDAndCreatedBy", reflect.TypeOf((*MockQuerier)(nil).ListSnapshotsByTenantIDAndCreatedBy), ctx, arg)
}

// ListSnapshotsByTenantIDAndStatus mocks base method.
func (m *MockQuerier) ListSnapshotsByTenantIDAndStatus(ctx context.Context, arg querier.ListSnapshotsByTenantIDAndStatusParams) ([]*querier.TenantSnapshot, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListSnapshotsByTenantIDAndStatus", ctx, arg)
	ret0, _ := ret[0].([]*querier.TenantSnapshot)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListSnapshotsByTenantIDAndStatus indicates an expected call of ListSnapshotsByTenantIDAndStatus.
func (mr *MockQuerierMockRecorder) ListSnapshotsByTenantIDAndStatus(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListSnapshotsByTenantIDAndStatus", reflect.TypeOf((*MockQuerier)(nil).ListSnapshotsByTenantIDAndStatus), ctx, arg)
}

// ListTenantConfigs mocks base method.
func (m *MockQuerier) ListTenantConfigs(ctx context.Context, tenantID uuid.UUID) ([]*querier.TenantConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTenantConfigs", ctx, tenantID)
	ret0, _ := ret[0].([]*querier.TenantConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListTenantConfigs indicates an expected call of ListTenantConfigs.
func (mr *MockQuerierMockRecorder) ListTenantConfigs(ctx, tenantID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTenantConfigs", reflect.TypeOf((*MockQuerier)(nil).ListTenantConfigs), ctx, tenantID)
}

// PickCloudClusterByServingType mocks base method.
func (m *MockQuerier) PickCloudClusterByServingType(ctx context.Context, servingType string) (*querier.ManagedCluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PickCloudClusterByServingType", ctx, servingType)
	ret0, _ := ret[0].(*querier.ManagedCluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PickCloudClusterByServingType indicates an expected call of PickCloudClusterByServingType.
func (mr *MockQuerierMockRecorder) PickCloudClusterByServingType(ctx, servingType any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PickCloudClusterByServingType", reflect.TypeOf((*MockQuerier)(nil).PickCloudClusterByServingType), ctx, servingType)
}

// PickupWorkflow mocks base method.
func (m *MockQuerier) PickupWorkflow(ctx context.Context, arg querier.PickupWorkflowParams) (*querier.Workflow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PickupWorkflow", ctx, arg)
	ret0, _ := ret[0].(*querier.Workflow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PickupWorkflow indicates an expected call of PickupWorkflow.
func (mr *MockQuerierMockRecorder) PickupWorkflow(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PickupWorkflow", reflect.TypeOf((*MockQuerier)(nil).PickupWorkflow), ctx, arg)
}

// PollNextWorkflow mocks base method.
func (m *MockQuerier) PollNextWorkflow(ctx context.Context, numMinutes int32) (*querier.Workflow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PollNextWorkflow", ctx, numMinutes)
	ret0, _ := ret[0].(*querier.Workflow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PollNextWorkflow indicates an expected call of PollNextWorkflow.
func (mr *MockQuerierMockRecorder) PollNextWorkflow(ctx, numMinutes any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PollNextWorkflow", reflect.TypeOf((*MockQuerier)(nil).PollNextWorkflow), ctx, numMinutes)
}

// ScheduleWorkflow mocks base method.
func (m *MockQuerier) ScheduleWorkflow(ctx context.Context, arg querier.ScheduleWorkflowParams) (pgconn.CommandTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ScheduleWorkflow", ctx, arg)
	ret0, _ := ret[0].(pgconn.CommandTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ScheduleWorkflow indicates an expected call of ScheduleWorkflow.
func (mr *MockQuerierMockRecorder) ScheduleWorkflow(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScheduleWorkflow", reflect.TypeOf((*MockQuerier)(nil).ScheduleWorkflow), ctx, arg)
}

// SetValueForEmptyTenantSecretStoreDekById mocks base method.
func (m *MockQuerier) SetValueForEmptyTenantSecretStoreDekById(ctx context.Context, arg querier.SetValueForEmptyTenantSecretStoreDekByIdParams) (pgconn.CommandTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetValueForEmptyTenantSecretStoreDekById", ctx, arg)
	ret0, _ := ret[0].(pgconn.CommandTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetValueForEmptyTenantSecretStoreDekById indicates an expected call of SetValueForEmptyTenantSecretStoreDekById.
func (mr *MockQuerierMockRecorder) SetValueForEmptyTenantSecretStoreDekById(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetValueForEmptyTenantSecretStoreDekById", reflect.TypeOf((*MockQuerier)(nil).SetValueForEmptyTenantSecretStoreDekById), ctx, arg)
}

// TryAdvisoryXactLock mocks base method.
func (m *MockQuerier) TryAdvisoryXactLock(ctx context.Context, pgTryAdvisoryXactLock int64) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TryAdvisoryXactLock", ctx, pgTryAdvisoryXactLock)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TryAdvisoryXactLock indicates an expected call of TryAdvisoryXactLock.
func (mr *MockQuerierMockRecorder) TryAdvisoryXactLock(ctx, pgTryAdvisoryXactLock any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TryAdvisoryXactLock", reflect.TypeOf((*MockQuerier)(nil).TryAdvisoryXactLock), ctx, pgTryAdvisoryXactLock)
}

// UpdateCluster mocks base method.
func (m *MockQuerier) UpdateCluster(ctx context.Context, arg querier.UpdateClusterParams) (pgconn.CommandTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCluster", ctx, arg)
	ret0, _ := ret[0].(pgconn.CommandTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateCluster indicates an expected call of UpdateCluster.
func (mr *MockQuerierMockRecorder) UpdateCluster(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCluster", reflect.TypeOf((*MockQuerier)(nil).UpdateCluster), ctx, arg)
}

// UpdateClusterOfStatus mocks base method.
func (m *MockQuerier) UpdateClusterOfStatus(ctx context.Context, arg querier.UpdateClusterOfStatusParams) (pgconn.CommandTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateClusterOfStatus", ctx, arg)
	ret0, _ := ret[0].(pgconn.CommandTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateClusterOfStatus indicates an expected call of UpdateClusterOfStatus.
func (mr *MockQuerierMockRecorder) UpdateClusterOfStatus(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateClusterOfStatus", reflect.TypeOf((*MockQuerier)(nil).UpdateClusterOfStatus), ctx, arg)
}

// UpdatePrivateLinkOfStatus mocks base method.
func (m *MockQuerier) UpdatePrivateLinkOfStatus(ctx context.Context, arg querier.UpdatePrivateLinkOfStatusParams) (pgconn.CommandTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePrivateLinkOfStatus", ctx, arg)
	ret0, _ := ret[0].(pgconn.CommandTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePrivateLinkOfStatus indicates an expected call of UpdatePrivateLinkOfStatus.
func (mr *MockQuerierMockRecorder) UpdatePrivateLinkOfStatus(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePrivateLinkOfStatus", reflect.TypeOf((*MockQuerier)(nil).UpdatePrivateLinkOfStatus), ctx, arg)
}

// UpdateRwSnapshotIDByTenantIDAndID mocks base method.
func (m *MockQuerier) UpdateRwSnapshotIDByTenantIDAndID(ctx context.Context, arg querier.UpdateRwSnapshotIDByTenantIDAndIDParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRwSnapshotIDByTenantIDAndID", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateRwSnapshotIDByTenantIDAndID indicates an expected call of UpdateRwSnapshotIDByTenantIDAndID.
func (mr *MockQuerierMockRecorder) UpdateRwSnapshotIDByTenantIDAndID(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRwSnapshotIDByTenantIDAndID", reflect.TypeOf((*MockQuerier)(nil).UpdateRwSnapshotIDByTenantIDAndID), ctx, arg)
}

// UpdateSnapshotStatusByTenantIDAndID mocks base method.
func (m *MockQuerier) UpdateSnapshotStatusByTenantIDAndID(ctx context.Context, arg querier.UpdateSnapshotStatusByTenantIDAndIDParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSnapshotStatusByTenantIDAndID", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateSnapshotStatusByTenantIDAndID indicates an expected call of UpdateSnapshotStatusByTenantIDAndID.
func (mr *MockQuerierMockRecorder) UpdateSnapshotStatusByTenantIDAndID(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSnapshotStatusByTenantIDAndID", reflect.TypeOf((*MockQuerier)(nil).UpdateSnapshotStatusByTenantIDAndID), ctx, arg)
}

// UpdateSnapshotStatusByTenantIDAndIDAndStatus mocks base method.
func (m *MockQuerier) UpdateSnapshotStatusByTenantIDAndIDAndStatus(ctx context.Context, arg querier.UpdateSnapshotStatusByTenantIDAndIDAndStatusParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSnapshotStatusByTenantIDAndIDAndStatus", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateSnapshotStatusByTenantIDAndIDAndStatus indicates an expected call of UpdateSnapshotStatusByTenantIDAndIDAndStatus.
func (mr *MockQuerierMockRecorder) UpdateSnapshotStatusByTenantIDAndIDAndStatus(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSnapshotStatusByTenantIDAndIDAndStatus", reflect.TypeOf((*MockQuerier)(nil).UpdateSnapshotStatusByTenantIDAndIDAndStatus), ctx, arg)
}

// UpdateTenantAlertNotificationStatusById mocks base method.
func (m *MockQuerier) UpdateTenantAlertNotificationStatusById(ctx context.Context, arg querier.UpdateTenantAlertNotificationStatusByIdParams) (pgconn.CommandTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTenantAlertNotificationStatusById", ctx, arg)
	ret0, _ := ret[0].(pgconn.CommandTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTenantAlertNotificationStatusById indicates an expected call of UpdateTenantAlertNotificationStatusById.
func (mr *MockQuerierMockRecorder) UpdateTenantAlertNotificationStatusById(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTenantAlertNotificationStatusById", reflect.TypeOf((*MockQuerier)(nil).UpdateTenantAlertNotificationStatusById), ctx, arg)
}

// UpdateTenantEtcdConfigById mocks base method.
func (m *MockQuerier) UpdateTenantEtcdConfigById(ctx context.Context, arg querier.UpdateTenantEtcdConfigByIdParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTenantEtcdConfigById", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateTenantEtcdConfigById indicates an expected call of UpdateTenantEtcdConfigById.
func (mr *MockQuerierMockRecorder) UpdateTenantEtcdConfigById(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTenantEtcdConfigById", reflect.TypeOf((*MockQuerier)(nil).UpdateTenantEtcdConfigById), ctx, arg)
}

// UpdateTenantExtensionByTenantIdAndResourceTypeIfStatusEquals mocks base method.
func (m *MockQuerier) UpdateTenantExtensionByTenantIdAndResourceTypeIfStatusEquals(ctx context.Context, arg querier.UpdateTenantExtensionByTenantIdAndResourceTypeIfStatusEqualsParams) (pgconn.CommandTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTenantExtensionByTenantIdAndResourceTypeIfStatusEquals", ctx, arg)
	ret0, _ := ret[0].(pgconn.CommandTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTenantExtensionByTenantIdAndResourceTypeIfStatusEquals indicates an expected call of UpdateTenantExtensionByTenantIdAndResourceTypeIfStatusEquals.
func (mr *MockQuerierMockRecorder) UpdateTenantExtensionByTenantIdAndResourceTypeIfStatusEquals(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTenantExtensionByTenantIdAndResourceTypeIfStatusEquals", reflect.TypeOf((*MockQuerier)(nil).UpdateTenantExtensionByTenantIdAndResourceTypeIfStatusEquals), ctx, arg)
}

// UpdateTenantExtensionConfigByTenantIdAndResourceType mocks base method.
func (m *MockQuerier) UpdateTenantExtensionConfigByTenantIdAndResourceType(ctx context.Context, arg querier.UpdateTenantExtensionConfigByTenantIdAndResourceTypeParams) (pgconn.CommandTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTenantExtensionConfigByTenantIdAndResourceType", ctx, arg)
	ret0, _ := ret[0].(pgconn.CommandTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTenantExtensionConfigByTenantIdAndResourceType indicates an expected call of UpdateTenantExtensionConfigByTenantIdAndResourceType.
func (mr *MockQuerierMockRecorder) UpdateTenantExtensionConfigByTenantIdAndResourceType(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTenantExtensionConfigByTenantIdAndResourceType", reflect.TypeOf((*MockQuerier)(nil).UpdateTenantExtensionConfigByTenantIdAndResourceType), ctx, arg)
}

// UpdateTenantExtensionStatusByTenantIdAndResourceType mocks base method.
func (m *MockQuerier) UpdateTenantExtensionStatusByTenantIdAndResourceType(ctx context.Context, arg querier.UpdateTenantExtensionStatusByTenantIdAndResourceTypeParams) (pgconn.CommandTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTenantExtensionStatusByTenantIdAndResourceType", ctx, arg)
	ret0, _ := ret[0].(pgconn.CommandTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTenantExtensionStatusByTenantIdAndResourceType indicates an expected call of UpdateTenantExtensionStatusByTenantIdAndResourceType.
func (mr *MockQuerierMockRecorder) UpdateTenantExtensionStatusByTenantIdAndResourceType(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTenantExtensionStatusByTenantIdAndResourceType", reflect.TypeOf((*MockQuerier)(nil).UpdateTenantExtensionStatusByTenantIdAndResourceType), ctx, arg)
}

// UpdateTenantImageTagById mocks base method.
func (m *MockQuerier) UpdateTenantImageTagById(ctx context.Context, arg querier.UpdateTenantImageTagByIdParams) (pgconn.CommandTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTenantImageTagById", ctx, arg)
	ret0, _ := ret[0].(pgconn.CommandTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTenantImageTagById indicates an expected call of UpdateTenantImageTagById.
func (mr *MockQuerierMockRecorder) UpdateTenantImageTagById(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTenantImageTagById", reflect.TypeOf((*MockQuerier)(nil).UpdateTenantImageTagById), ctx, arg)
}

// UpdateTenantResourcesById mocks base method.
func (m *MockQuerier) UpdateTenantResourcesById(ctx context.Context, arg querier.UpdateTenantResourcesByIdParams) (pgconn.CommandTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTenantResourcesById", ctx, arg)
	ret0, _ := ret[0].(pgconn.CommandTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTenantResourcesById indicates an expected call of UpdateTenantResourcesById.
func (mr *MockQuerierMockRecorder) UpdateTenantResourcesById(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTenantResourcesById", reflect.TypeOf((*MockQuerier)(nil).UpdateTenantResourcesById), ctx, arg)
}

// UpdateTenantRwConfigById mocks base method.
func (m *MockQuerier) UpdateTenantRwConfigById(ctx context.Context, arg querier.UpdateTenantRwConfigByIdParams) (pgconn.CommandTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTenantRwConfigById", ctx, arg)
	ret0, _ := ret[0].(pgconn.CommandTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTenantRwConfigById indicates an expected call of UpdateTenantRwConfigById.
func (mr *MockQuerierMockRecorder) UpdateTenantRwConfigById(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTenantRwConfigById", reflect.TypeOf((*MockQuerier)(nil).UpdateTenantRwConfigById), ctx, arg)
}

// UpdateTenantRwConfigByNsId mocks base method.
func (m *MockQuerier) UpdateTenantRwConfigByNsId(ctx context.Context, arg querier.UpdateTenantRwConfigByNsIdParams) (pgconn.CommandTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTenantRwConfigByNsId", ctx, arg)
	ret0, _ := ret[0].(pgconn.CommandTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTenantRwConfigByNsId indicates an expected call of UpdateTenantRwConfigByNsId.
func (mr *MockQuerierMockRecorder) UpdateTenantRwConfigByNsId(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTenantRwConfigByNsId", reflect.TypeOf((*MockQuerier)(nil).UpdateTenantRwConfigByNsId), ctx, arg)
}

// UpdateTenantStatusById mocks base method.
func (m *MockQuerier) UpdateTenantStatusById(ctx context.Context, arg querier.UpdateTenantStatusByIdParams) (pgconn.CommandTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTenantStatusById", ctx, arg)
	ret0, _ := ret[0].(pgconn.CommandTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTenantStatusById indicates an expected call of UpdateTenantStatusById.
func (mr *MockQuerierMockRecorder) UpdateTenantStatusById(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTenantStatusById", reflect.TypeOf((*MockQuerier)(nil).UpdateTenantStatusById), ctx, arg)
}

// UpdateTenantTierById mocks base method.
func (m *MockQuerier) UpdateTenantTierById(ctx context.Context, arg querier.UpdateTenantTierByIdParams) (pgconn.CommandTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTenantTierById", ctx, arg)
	ret0, _ := ret[0].(pgconn.CommandTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTenantTierById indicates an expected call of UpdateTenantTierById.
func (mr *MockQuerierMockRecorder) UpdateTenantTierById(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTenantTierById", reflect.TypeOf((*MockQuerier)(nil).UpdateTenantTierById), ctx, arg)
}

// UpsertClusterCloudResource mocks base method.
func (m *MockQuerier) UpsertClusterCloudResource(ctx context.Context, arg querier.UpsertClusterCloudResourceParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertClusterCloudResource", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertClusterCloudResource indicates an expected call of UpsertClusterCloudResource.
func (mr *MockQuerierMockRecorder) UpsertClusterCloudResource(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertClusterCloudResource", reflect.TypeOf((*MockQuerier)(nil).UpsertClusterCloudResource), ctx, arg)
}

// UpsertRwDatabase mocks base method.
func (m *MockQuerier) UpsertRwDatabase(ctx context.Context, arg querier.UpsertRwDatabaseParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertRwDatabase", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertRwDatabase indicates an expected call of UpsertRwDatabase.
func (mr *MockQuerierMockRecorder) UpsertRwDatabase(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertRwDatabase", reflect.TypeOf((*MockQuerier)(nil).UpsertRwDatabase), ctx, arg)
}

// UpsertRwUser mocks base method.
func (m *MockQuerier) UpsertRwUser(ctx context.Context, arg querier.UpsertRwUserParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertRwUser", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertRwUser indicates an expected call of UpsertRwUser.
func (mr *MockQuerierMockRecorder) UpsertRwUser(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertRwUser", reflect.TypeOf((*MockQuerier)(nil).UpsertRwUser), ctx, arg)
}

// UpsertTenantCloudResource mocks base method.
func (m *MockQuerier) UpsertTenantCloudResource(ctx context.Context, arg querier.UpsertTenantCloudResourceParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertTenantCloudResource", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertTenantCloudResource indicates an expected call of UpsertTenantCloudResource.
func (mr *MockQuerierMockRecorder) UpsertTenantCloudResource(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertTenantCloudResource", reflect.TypeOf((*MockQuerier)(nil).UpsertTenantCloudResource), ctx, arg)
}

// UpsertTenantConfig mocks base method.
func (m *MockQuerier) UpsertTenantConfig(ctx context.Context, arg querier.UpsertTenantConfigParams) (pgconn.CommandTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertTenantConfig", ctx, arg)
	ret0, _ := ret[0].(pgconn.CommandTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertTenantConfig indicates an expected call of UpsertTenantConfig.
func (mr *MockQuerierMockRecorder) UpsertTenantConfig(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertTenantConfig", reflect.TypeOf((*MockQuerier)(nil).UpsertTenantConfig), ctx, arg)
}
