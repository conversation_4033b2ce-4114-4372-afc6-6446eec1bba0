// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.28.0
// source: extensions.sql

package querier

import (
	"context"

	"github.com/jackc/pgx/v5/pgconn"
)

const compareAndUpdateTenantExtensionStatusByTenantIdAndResourceTypeAndStatuses = `-- name: CompareAndUpdateTenantExtensionStatusByTenantIdAndResourceTypeAndStatuses :execresult
UPDATE
    tenant_extensions
SET
    status = $3,
    updated_at = CURRENT_TIMESTAMP
WHERE
    tenant_id = $1
    AND resource_type = $2
    AND status = ANY($4 :: text [])
`

type CompareAndUpdateTenantExtensionStatusByTenantIdAndResourceTypeAndStatusesParams struct {
	TenantID         uint64
	ResourceType     string
	Status           string
	ExpectedStatuses []string
}

func (q *Queries) CompareAndUpdateTenantExtensionStatusByTenantIdAndResourceTypeAndStatuses(ctx context.Context, arg CompareAndUpdateTenantExtensionStatusByTenantIdAndResourceTypeAndStatusesParams) (pgconn.CommandTag, error) {
	return q.db.Exec(ctx, compareAndUpdateTenantExtensionStatusByTenantIdAndResourceTypeAndStatuses,
		arg.TenantID,
		arg.ResourceType,
		arg.Status,
		arg.ExpectedStatuses,
	)
}

const createTenantExtension = `-- name: CreateTenantExtension :execresult
INSERT INTO
    tenant_extensions (
        tenant_id,
        resource_type,
        config,
        version,
        status
    )
VALUES
    (
        $1,
        $2,
        $3,
        $4,
        $5
    )
`

type CreateTenantExtensionParams struct {
	TenantID     uint64
	ResourceType string
	Config       *string
	Version      string
	Status       string
}

func (q *Queries) CreateTenantExtension(ctx context.Context, arg CreateTenantExtensionParams) (pgconn.CommandTag, error) {
	return q.db.Exec(ctx, createTenantExtension,
		arg.TenantID,
		arg.ResourceType,
		arg.Config,
		arg.Version,
		arg.Status,
	)
}

const deleteTenantExtensionByTenantIdAndResourceType = `-- name: DeleteTenantExtensionByTenantIdAndResourceType :execresult
DELETE FROM
    tenant_extensions
WHERE
    tenant_id = $1
    AND resource_type = $2
`

type DeleteTenantExtensionByTenantIdAndResourceTypeParams struct {
	TenantID     uint64
	ResourceType string
}

func (q *Queries) DeleteTenantExtensionByTenantIdAndResourceType(ctx context.Context, arg DeleteTenantExtensionByTenantIdAndResourceTypeParams) (pgconn.CommandTag, error) {
	return q.db.Exec(ctx, deleteTenantExtensionByTenantIdAndResourceType, arg.TenantID, arg.ResourceType)
}

const getTenantExtensionByTenantIdAndResourceType = `-- name: GetTenantExtensionByTenantIdAndResourceType :one
SELECT
    tenant_id, resource_type, config, version, status, created_at, updated_at
FROM
    tenant_extensions
WHERE
    tenant_id = $1
    AND resource_type = $2
LIMIT
    1
`

type GetTenantExtensionByTenantIdAndResourceTypeParams struct {
	TenantID     uint64
	ResourceType string
}

func (q *Queries) GetTenantExtensionByTenantIdAndResourceType(ctx context.Context, arg GetTenantExtensionByTenantIdAndResourceTypeParams) (*TenantExtension, error) {
	row := q.db.QueryRow(ctx, getTenantExtensionByTenantIdAndResourceType, arg.TenantID, arg.ResourceType)
	var i TenantExtension
	err := row.Scan(
		&i.TenantID,
		&i.ResourceType,
		&i.Config,
		&i.Version,
		&i.Status,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const getTenantExtensionsByTenantId = `-- name: GetTenantExtensionsByTenantId :many
SELECT
    tenant_id, resource_type, config, version, status, created_at, updated_at
FROM
    tenant_extensions
WHERE
    tenant_id = $1
`

func (q *Queries) GetTenantExtensionsByTenantId(ctx context.Context, tenantID uint64) ([]*TenantExtension, error) {
	rows, err := q.db.Query(ctx, getTenantExtensionsByTenantId, tenantID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*TenantExtension
	for rows.Next() {
		var i TenantExtension
		if err := rows.Scan(
			&i.TenantID,
			&i.ResourceType,
			&i.Config,
			&i.Version,
			&i.Status,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateTenantExtensionByTenantIdAndResourceTypeIfStatusEquals = `-- name: UpdateTenantExtensionByTenantIdAndResourceTypeIfStatusEquals :execresult
UPDATE
    tenant_extensions
SET
    config = $4,
    version = $5,
    updated_at = CURRENT_TIMESTAMP
WHERE
    tenant_id = $1
    AND resource_type = $2
    AND status = $3
`

type UpdateTenantExtensionByTenantIdAndResourceTypeIfStatusEqualsParams struct {
	TenantID     uint64
	ResourceType string
	Status       string
	Config       *string
	Version      string
}

func (q *Queries) UpdateTenantExtensionByTenantIdAndResourceTypeIfStatusEquals(ctx context.Context, arg UpdateTenantExtensionByTenantIdAndResourceTypeIfStatusEqualsParams) (pgconn.CommandTag, error) {
	return q.db.Exec(ctx, updateTenantExtensionByTenantIdAndResourceTypeIfStatusEquals,
		arg.TenantID,
		arg.ResourceType,
		arg.Status,
		arg.Config,
		arg.Version,
	)
}

const updateTenantExtensionConfigByTenantIdAndResourceType = `-- name: UpdateTenantExtensionConfigByTenantIdAndResourceType :execresult
UPDATE
    tenant_extensions
SET
    config = $3,
    updated_at = CURRENT_TIMESTAMP
WHERE
    tenant_id = $1 AND resource_type = $2
`

type UpdateTenantExtensionConfigByTenantIdAndResourceTypeParams struct {
	TenantID     uint64
	ResourceType string
	Config       *string
}

func (q *Queries) UpdateTenantExtensionConfigByTenantIdAndResourceType(ctx context.Context, arg UpdateTenantExtensionConfigByTenantIdAndResourceTypeParams) (pgconn.CommandTag, error) {
	return q.db.Exec(ctx, updateTenantExtensionConfigByTenantIdAndResourceType, arg.TenantID, arg.ResourceType, arg.Config)
}

const updateTenantExtensionStatusByTenantIdAndResourceType = `-- name: UpdateTenantExtensionStatusByTenantIdAndResourceType :execresult
UPDATE
    tenant_extensions
SET
    status = $3,
    updated_at = CURRENT_TIMESTAMP
WHERE
    tenant_id = $1 AND resource_type=$2
`

type UpdateTenantExtensionStatusByTenantIdAndResourceTypeParams struct {
	TenantID     uint64
	ResourceType string
	Status       string
}

func (q *Queries) UpdateTenantExtensionStatusByTenantIdAndResourceType(ctx context.Context, arg UpdateTenantExtensionStatusByTenantIdAndResourceTypeParams) (pgconn.CommandTag, error) {
	return q.db.Exec(ctx, updateTenantExtensionStatusByTenantIdAndResourceType, arg.TenantID, arg.ResourceType, arg.Status)
}
