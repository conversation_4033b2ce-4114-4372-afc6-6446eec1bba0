// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.28.0

package querier

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgconn"
)

type Querier interface {
	AcquireAdvisoryXactLock(ctx context.Context, pgAdvisoryXactLock int64) error
	AwakeWorkflow(ctx context.Context, id uuid.UUID) (pgconn.CommandTag, error)
	CompareAndDelete(ctx context.Context, arg CompareAndDeleteParams) (pgconn.CommandTag, error)
	CompareAndRelease(ctx context.Context, arg CompareAndReleaseParams) (pgconn.CommandTag, error)
	CompareAndUpdate(ctx context.Context, arg CompareAndUpdateParams) (pgconn.CommandTag, error)
	CompareAndUpdateTenantExtensionStatusByTenantIdAndResourceTypeAndStatuses(ctx context.Context, arg CompareAndUpdateTenantExtensionStatusByTenantIdAndResourceTypeAndStatusesParams) (pgconn.CommandTag, error)
	CompareAndUpdateTenantHealthStatusById(ctx context.Context, arg CompareAndUpdateTenantHealthStatusByIdParams) (pgconn.CommandTag, error)
	CompareAndUpdateTenantStatusByIdAndStatuses(ctx context.Context, arg CompareAndUpdateTenantStatusByIdAndStatusesParams) (pgconn.CommandTag, error)
	CountSnapshotsByTenantID(ctx context.Context, tenantID uint64) (int64, error)
	CreateCluster(ctx context.Context, arg CreateClusterParams) (uint64, error)
	CreateClusterSetting(ctx context.Context, arg CreateClusterSettingParams) (pgconn.CommandTag, error)
	CreateEvent(ctx context.Context, arg CreateEventParams) error
	CreatePrivateLink(ctx context.Context, arg CreatePrivateLinkParams) (uuid.UUID, error)
	CreateSnapshot(ctx context.Context, arg CreateSnapshotParams) (*TenantSnapshot, error)
	CreateTenant(ctx context.Context, arg CreateTenantParams) (*Tenant, error)
	CreateTenantAlertNofitication(ctx context.Context, arg CreateTenantAlertNofiticationParams) (uuid.UUID, error)
	CreateTenantAlertNofiticationRecord(ctx context.Context, arg CreateTenantAlertNofiticationRecordParams) error
	CreateTenantExtension(ctx context.Context, arg CreateTenantExtensionParams) (pgconn.CommandTag, error)
	CreateWorkflow(ctx context.Context, arg CreateWorkflowParams) (uuid.UUID, error)
	DeleteAllClusterSettings(ctx context.Context, managedClusterID uint64) (pgconn.CommandTag, error)
	DeleteAllTenantConfigs(ctx context.Context, tenantID uuid.UUID) (pgconn.CommandTag, error)
	DeleteClusterCloudResource(ctx context.Context, arg DeleteClusterCloudResourceParams) error
	DeleteClusterSafely(ctx context.Context, id uint64) (pgconn.CommandTag, error)
	DeleteEventExceedLimitByWorkflow(ctx context.Context, arg DeleteEventExceedLimitByWorkflowParams) (int64, error)
	DeleteFinishedWorkflow(ctx context.Context, id uuid.UUID) error
	DeleteLegacyTenantAlertNotifications(ctx context.Context, timeWindowDays int32) error
	DeletePrivateLink(ctx context.Context, id uuid.UUID) error
	DeleteRwDatabasesByName(ctx context.Context, arg DeleteRwDatabasesByNameParams) error
	DeleteRwUserById(ctx context.Context, arg DeleteRwUserByIdParams) error
	DeleteSnapshotByTenantIDAndID(ctx context.Context, arg DeleteSnapshotByTenantIDAndIDParams) error
	DeleteSnapshotsByTenantID(ctx context.Context, tenantID uint64) error
	DeleteSnapshotsByTenantIDAndIDs(ctx context.Context, arg DeleteSnapshotsByTenantIDAndIDsParams) error
	DeleteTenantAlertNotificationRecord(ctx context.Context, arg DeleteTenantAlertNotificationRecordParams) error
	DeleteTenantAlertNotificationRecordsByNotificationId(ctx context.Context, notificationID uuid.UUID) error
	DeleteTenantById(ctx context.Context, id uint64) error
	DeleteTenantCloudResource(ctx context.Context, arg DeleteTenantCloudResourceParams) error
	DeleteTenantConfig(ctx context.Context, arg DeleteTenantConfigParams) (pgconn.CommandTag, error)
	DeleteTenantExtensionByTenantIdAndResourceType(ctx context.Context, arg DeleteTenantExtensionByTenantIdAndResourceTypeParams) (pgconn.CommandTag, error)
	ForceCancel(ctx context.Context, id uuid.UUID) (pgconn.CommandTag, error)
	ForceLock(ctx context.Context, id uuid.UUID) (*time.Time, error)
	ForceResume(ctx context.Context, id uuid.UUID) (pgconn.CommandTag, error)
	GetAllClustersExceptOrg(ctx context.Context, org uuid.UUID) ([]*ManagedCluster, error)
	GetClusterById(ctx context.Context, id uint64) (*ManagedCluster, error)
	GetClusterByOrgName(ctx context.Context, arg GetClusterByOrgNameParams) (*ManagedCluster, error)
	GetClusterByUuid(ctx context.Context, value string) (*ManagedCluster, error)
	GetClusterSettings(ctx context.Context, managedClusterID uint64) ([]*ManagedClusterSetting, error)
	GetClusterSettingsByKey(ctx context.Context, arg GetClusterSettingsByKeyParams) (*GetClusterSettingsByKeyRow, error)
	GetClusterTenantsCount(ctx context.Context, clusterID uint64) (int64, error)
	GetClustersAndSettingsByOrg(ctx context.Context, arg GetClustersAndSettingsByOrgParams) ([]*GetClustersAndSettingsByOrgRow, error)
	GetClustersByOrg(ctx context.Context, org uuid.UUID) ([]*ManagedCluster, error)
	GetClustersCountByOrg(ctx context.Context, org uuid.UUID) (int64, error)
	GetEventCountByWorkflow(ctx context.Context, workflowID uuid.UUID) (*GetEventCountByWorkflowRow, error)
	GetExpiredTenantAlertNotifications(ctx context.Context, timeWindowMinutes int32) ([]*TenantAlertNotification, error)
	GetLatestUnprocessedTenantAlertNotifications(ctx context.Context, timeWindowMinutes int32) ([]*TenantAlertNotification, error)
	GetMaxEventId(ctx context.Context, workflowID uuid.UUID) (int64, error)
	GetOrgTenantById(ctx context.Context, arg GetOrgTenantByIdParams) (*Tenant, error)
	GetOrgTenantByName(ctx context.Context, arg GetOrgTenantByNameParams) (*Tenant, error)
	GetOrgTenantByNsId(ctx context.Context, arg GetOrgTenantByNsIdParams) (*Tenant, error)
	GetOrgTenantsCountBySku(ctx context.Context, orgID uuid.UUID) ([]*GetOrgTenantsCountBySkuRow, error)
	GetOrgTenantsCountByStatus(ctx context.Context, orgID uuid.UUID) ([]*GetOrgTenantsCountByStatusRow, error)
	GetPrivateLinkByID(ctx context.Context, id uuid.UUID) (*TenantPrivateLink, error)
	GetPrivateLinkCountByTenantID(ctx context.Context, tenantID uint64) (int64, error)
	GetPrivateLinksByTenantID(ctx context.Context, tenantID uint64) ([]*TenantPrivateLink, error)
	GetRecentEvents(ctx context.Context, arg GetRecentEventsParams) ([]*WorkflowEvent, error)
	GetRunningWorkflowOfCluster(ctx context.Context, arg GetRunningWorkflowOfClusterParams) (*Workflow, error)
	GetRunningWorkflowOfSnapshot(ctx context.Context, arg GetRunningWorkflowOfSnapshotParams) (*Workflow, error)
	GetRunningWorkflowOfTenant(ctx context.Context, arg GetRunningWorkflowOfTenantParams) (*Workflow, error)
	GetRwDatabases(ctx context.Context, tenantID uint64) ([]*RwDatabase, error)
	GetRwDatabasesCountByTenantId(ctx context.Context, tenantID uint64) (int64, error)
	GetRwDatabasesCountGroupByResourceGroup(ctx context.Context, tenantID uint64) ([]*GetRwDatabasesCountGroupByResourceGroupRow, error)
	GetRwDatabasesPaginated(ctx context.Context, arg GetRwDatabasesPaginatedParams) ([]*RwDatabase, error)
	GetRwUsersCountByTenantId(ctx context.Context, tenantID uint64) (int64, error)
	GetSnapshotByID(ctx context.Context, id uuid.UUID) (*TenantSnapshot, error)
	GetSnapshotByTenantIDAndID(ctx context.Context, arg GetSnapshotByTenantIDAndIDParams) (*TenantSnapshot, error)
	GetSnapshotByTenantIDAndRWID(ctx context.Context, arg GetSnapshotByTenantIDAndRWIDParams) (*TenantSnapshot, error)
	GetSnapshotsByStatus(ctx context.Context, status string) ([]*TenantSnapshot, error)
	GetSnapshotsByTenantID(ctx context.Context, tenantID uint64) ([]*TenantSnapshot, error)
	GetSnapshotsByTenantIDAndRW_Version(ctx context.Context, arg GetSnapshotsByTenantIDAndRW_VersionParams) ([]*TenantSnapshot, error)
	GetSnapshotsWithoutRWIDByTenantID(ctx context.Context, tenantID uint64) ([]*TenantSnapshot, error)
	GetTenantAlertNotificationByID(ctx context.Context, id uuid.UUID) (*TenantAlertNotification, error)
	GetTenantAlertNotificationRecordCountByNotificationId(ctx context.Context, notificationID uuid.UUID) (int64, error)
	GetTenantAlertNotificationRecords(ctx context.Context) ([]*TenantAlertNotificationRecord, error)
	GetTenantById(ctx context.Context, id uint64) (*Tenant, error)
	GetTenantByNsId(ctx context.Context, nsID uuid.UUID) (*Tenant, error)
	GetTenantCloudResourcesByTypeAndTenantID(ctx context.Context, arg GetTenantCloudResourcesByTypeAndTenantIDParams) ([]*TenantCloudResource, error)
	GetTenantExtensionByTenantIdAndResourceType(ctx context.Context, arg GetTenantExtensionByTenantIdAndResourceTypeParams) (*TenantExtension, error)
	GetTenantExtensionsByTenantId(ctx context.Context, tenantID uint64) ([]*TenantExtension, error)
	GetTenantIDsWithTimedoutSnapshots(ctx context.Context, arg GetTenantIDsWithTimedoutSnapshotsParams) ([]uint64, error)
	GetTenants(ctx context.Context, arg GetTenantsParams) ([]*Tenant, error)
	GetTenantsByOrgId(ctx context.Context, arg GetTenantsByOrgIdParams) ([]*Tenant, error)
	GetTenantsCount(ctx context.Context) (int64, error)
	GetTenantsCountByOrgId(ctx context.Context, orgID uuid.UUID) (int64, error)
	GetTenantsForGarbageCollection(ctx context.Context) ([]*Tenant, error)
	GetWorkflow(ctx context.Context, id uuid.UUID) (*Workflow, error)
	GetWorkflowByType(ctx context.Context, workflowType string) (*Workflow, error)
	GetWorkflowFinishedBefore(ctx context.Context, updatedAt time.Time) ([]uuid.UUID, error)
	GetWorkflowUpdatedAfter(ctx context.Context, updatedAt time.Time) ([]uuid.UUID, error)
	GetWorkflows(ctx context.Context, arg GetWorkflowsParams) ([]*Workflow, error)
	IsPrivateLinkIdExist(ctx context.Context, id uuid.UUID) (int64, error)
	IsPrivateLinkOwnedByTenant(ctx context.Context, arg IsPrivateLinkOwnedByTenantParams) (int64, error)
	IsRwDatabaseExist(ctx context.Context, arg IsRwDatabaseExistParams) (int32, error)
	IsRwUserExist(ctx context.Context, arg IsRwUserExistParams) (int32, error)
	ListAutoDeletableSnapshotsByTenantIDAndStatus(ctx context.Context, arg ListAutoDeletableSnapshotsByTenantIDAndStatusParams) ([]*TenantSnapshot, error)
	ListRwUsers(ctx context.Context, tenantID uint64) ([]*RwUser, error)
	ListRwUsersWithPaginated(ctx context.Context, arg ListRwUsersWithPaginatedParams) ([]*RwUser, error)
	ListSnapshotsByTenantID(ctx context.Context, arg ListSnapshotsByTenantIDParams) ([]*TenantSnapshot, error)
	ListSnapshotsByTenantIDAndCreatedBy(ctx context.Context, arg ListSnapshotsByTenantIDAndCreatedByParams) ([]*TenantSnapshot, error)
	ListSnapshotsByTenantIDAndStatus(ctx context.Context, arg ListSnapshotsByTenantIDAndStatusParams) ([]*TenantSnapshot, error)
	ListTenantConfigs(ctx context.Context, tenantID uuid.UUID) ([]*TenantConfig, error)
	PickCloudClusterByServingType(ctx context.Context, servingType string) (*ManagedCluster, error)
	PickupWorkflow(ctx context.Context, arg PickupWorkflowParams) (*Workflow, error)
	PollNextWorkflow(ctx context.Context, numMinutes int32) (*Workflow, error)
	ScheduleWorkflow(ctx context.Context, arg ScheduleWorkflowParams) (pgconn.CommandTag, error)
	SetValueForEmptyTenantSecretStoreDekById(ctx context.Context, arg SetValueForEmptyTenantSecretStoreDekByIdParams) (pgconn.CommandTag, error)
	TryAdvisoryXactLock(ctx context.Context, pgTryAdvisoryXactLock int64) (bool, error)
	UpdateCluster(ctx context.Context, arg UpdateClusterParams) (pgconn.CommandTag, error)
	UpdateClusterOfStatus(ctx context.Context, arg UpdateClusterOfStatusParams) (pgconn.CommandTag, error)
	UpdatePrivateLinkOfStatus(ctx context.Context, arg UpdatePrivateLinkOfStatusParams) (pgconn.CommandTag, error)
	UpdateRwSnapshotIDByTenantIDAndID(ctx context.Context, arg UpdateRwSnapshotIDByTenantIDAndIDParams) error
	UpdateSnapshotStatusByTenantIDAndID(ctx context.Context, arg UpdateSnapshotStatusByTenantIDAndIDParams) error
	UpdateSnapshotStatusByTenantIDAndIDAndStatus(ctx context.Context, arg UpdateSnapshotStatusByTenantIDAndIDAndStatusParams) error
	UpdateTenantAlertNotificationStatusById(ctx context.Context, arg UpdateTenantAlertNotificationStatusByIdParams) (pgconn.CommandTag, error)
	UpdateTenantEtcdConfigById(ctx context.Context, arg UpdateTenantEtcdConfigByIdParams) error
	UpdateTenantExtensionByTenantIdAndResourceTypeIfStatusEquals(ctx context.Context, arg UpdateTenantExtensionByTenantIdAndResourceTypeIfStatusEqualsParams) (pgconn.CommandTag, error)
	UpdateTenantExtensionConfigByTenantIdAndResourceType(ctx context.Context, arg UpdateTenantExtensionConfigByTenantIdAndResourceTypeParams) (pgconn.CommandTag, error)
	UpdateTenantExtensionStatusByTenantIdAndResourceType(ctx context.Context, arg UpdateTenantExtensionStatusByTenantIdAndResourceTypeParams) (pgconn.CommandTag, error)
	UpdateTenantImageTagById(ctx context.Context, arg UpdateTenantImageTagByIdParams) (pgconn.CommandTag, error)
	UpdateTenantResourcesById(ctx context.Context, arg UpdateTenantResourcesByIdParams) (pgconn.CommandTag, error)
	UpdateTenantRwConfigById(ctx context.Context, arg UpdateTenantRwConfigByIdParams) (pgconn.CommandTag, error)
	UpdateTenantRwConfigByNsId(ctx context.Context, arg UpdateTenantRwConfigByNsIdParams) (pgconn.CommandTag, error)
	UpdateTenantStatusById(ctx context.Context, arg UpdateTenantStatusByIdParams) (pgconn.CommandTag, error)
	UpdateTenantTierById(ctx context.Context, arg UpdateTenantTierByIdParams) (pgconn.CommandTag, error)
	UpsertClusterCloudResource(ctx context.Context, arg UpsertClusterCloudResourceParams) error
	UpsertRwDatabase(ctx context.Context, arg UpsertRwDatabaseParams) error
	UpsertRwUser(ctx context.Context, arg UpsertRwUserParams) error
	UpsertTenantCloudResource(ctx context.Context, arg UpsertTenantCloudResourceParams) error
	UpsertTenantConfig(ctx context.Context, arg UpsertTenantConfigParams) (pgconn.CommandTag, error)
}

var _ Querier = (*Queries)(nil)
