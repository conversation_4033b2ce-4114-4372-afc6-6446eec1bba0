// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.28.0
// source: rw_databases.sql

package querier

import (
	"context"
)

const deleteRwDatabasesByName = `-- name: DeleteRwDatabasesByName :exec
DELETE FROM rw_databases WHERE tenant_id = $1 AND name = $2
`

type DeleteRwDatabasesByNameParams struct {
	TenantID uint64
	Name     string
}

func (q *Queries) DeleteRwDatabasesByName(ctx context.Context, arg DeleteRwDatabasesByNameParams) error {
	_, err := q.db.Exec(ctx, deleteRwDatabasesByName, arg.TenantID, arg.Name)
	return err
}

const getRwDatabases = `-- name: GetRwDatabases :many
SELECT id, created_at, updated_at, tenant_id, name, resource_group FROM rw_databases WHERE tenant_id = $1 ORDER BY name
`

func (q *Queries) GetRwDatabases(ctx context.Context, tenantID uint64) ([]*RwDatabase, error) {
	rows, err := q.db.Query(ctx, getRwDatabases, tenantID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*RwDatabase
	for rows.Next() {
		var i RwDatabase
		if err := rows.Scan(
			&i.ID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.TenantID,
			&i.Name,
			&i.ResourceGroup,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getRwDatabasesCountByTenantId = `-- name: GetRwDatabasesCountByTenantId :one
SELECT COUNT(*) FROM rw_databases WHERE tenant_id = $1
`

func (q *Queries) GetRwDatabasesCountByTenantId(ctx context.Context, tenantID uint64) (int64, error) {
	row := q.db.QueryRow(ctx, getRwDatabasesCountByTenantId, tenantID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const getRwDatabasesCountGroupByResourceGroup = `-- name: GetRwDatabasesCountGroupByResourceGroup :many
SELECT resource_group, COUNT(*) AS cnt FROM rw_databases
WHERE tenant_id = $1
GROUP BY resource_group
`

type GetRwDatabasesCountGroupByResourceGroupRow struct {
	ResourceGroup string
	Cnt           int64
}

func (q *Queries) GetRwDatabasesCountGroupByResourceGroup(ctx context.Context, tenantID uint64) ([]*GetRwDatabasesCountGroupByResourceGroupRow, error) {
	rows, err := q.db.Query(ctx, getRwDatabasesCountGroupByResourceGroup, tenantID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*GetRwDatabasesCountGroupByResourceGroupRow
	for rows.Next() {
		var i GetRwDatabasesCountGroupByResourceGroupRow
		if err := rows.Scan(&i.ResourceGroup, &i.Cnt); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getRwDatabasesPaginated = `-- name: GetRwDatabasesPaginated :many
SELECT id, created_at, updated_at, tenant_id, name, resource_group FROM rw_databases WHERE tenant_id = $1 ORDER BY name OFFSET $2 LIMIT $3
`

type GetRwDatabasesPaginatedParams struct {
	TenantID uint64
	Offset   int32
	Limit    int32
}

func (q *Queries) GetRwDatabasesPaginated(ctx context.Context, arg GetRwDatabasesPaginatedParams) ([]*RwDatabase, error) {
	rows, err := q.db.Query(ctx, getRwDatabasesPaginated, arg.TenantID, arg.Offset, arg.Limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*RwDatabase
	for rows.Next() {
		var i RwDatabase
		if err := rows.Scan(
			&i.ID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.TenantID,
			&i.Name,
			&i.ResourceGroup,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const isRwDatabaseExist = `-- name: IsRwDatabaseExist :one
SELECT 1 FROM rw_databases WHERE tenant_id = $1 AND name = $2
`

type IsRwDatabaseExistParams struct {
	TenantID uint64
	Name     string
}

func (q *Queries) IsRwDatabaseExist(ctx context.Context, arg IsRwDatabaseExistParams) (int32, error) {
	row := q.db.QueryRow(ctx, isRwDatabaseExist, arg.TenantID, arg.Name)
	var column_1 int32
	err := row.Scan(&column_1)
	return column_1, err
}

const upsertRwDatabase = `-- name: UpsertRwDatabase :exec
INSERT INTO rw_databases (name, tenant_id, resource_group)
VALUES ($1, $2, $3)
ON CONFLICT(tenant_id, name) DO UPDATE SET resource_group = excluded.resource_group,
                                           updated_at     = CURRENT_TIMESTAMP
`

type UpsertRwDatabaseParams struct {
	Name          string
	TenantID      uint64
	ResourceGroup string
}

func (q *Queries) UpsertRwDatabase(ctx context.Context, arg UpsertRwDatabaseParams) error {
	_, err := q.db.Exec(ctx, upsertRwDatabase, arg.Name, arg.TenantID, arg.ResourceGroup)
	return err
}
