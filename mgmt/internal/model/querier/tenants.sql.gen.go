// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.28.0
// source: tenants.sql

package querier

import (
	"context"
	"encoding/json"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgconn"
)

const compareAndUpdateTenantHealthStatusById = `-- name: CompareAndUpdateTenantHealthStatusById :execresult
UPDATE tenants SET health_status = $2, updated_at = CURRENT_TIMESTAMP WHERE id = $1 AND health_status = $3 AND deactivated_at IS NULL
`

type CompareAndUpdateTenantHealthStatusByIdParams struct {
	ID        uint64
	NewStatus string
	OldStatus string
}

func (q *Queries) CompareAndUpdateTenantHealthStatusById(ctx context.Context, arg CompareAndUpdateTenantHealthStatusByIdParams) (pgconn.CommandTag, error) {
	return q.db.Exec(ctx, compareAndUpdateTenantHealthStatusById, arg.ID, arg.NewStatus, arg.OldStatus)
}

const compareAndUpdateTenantStatusByIdAndStatuses = `-- name: CompareAndUpdateTenantStatusByIdAndStatuses :execresult
UPDATE tenants SET status = $2, health_status = 'Unknown', updated_at = CURRENT_TIMESTAMP WHERE id = $1 AND status = ANY($3 :: text []) AND deactivated_at IS NULL
`

type CompareAndUpdateTenantStatusByIdAndStatusesParams struct {
	ID          uint64
	NewStatus   string
	OldStatuses []string
}

func (q *Queries) CompareAndUpdateTenantStatusByIdAndStatuses(ctx context.Context, arg CompareAndUpdateTenantStatusByIdAndStatusesParams) (pgconn.CommandTag, error) {
	return q.db.Exec(ctx, compareAndUpdateTenantStatusByIdAndStatuses, arg.ID, arg.NewStatus, arg.OldStatuses)
}

const createTenant = `-- name: CreateTenant :one
INSERT INTO tenants (tenant_name, resource_namespace, ns_id, status, sku, image_tag, region, cluster_id, replica, rw_config, etcd_config, tier_id, retention_days, validity_days, resource_version, resources, org_id, secret_store_data_encryption_key, usage_type)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19)
RETURNING id, created_at, updated_at, user_id, tenant_name, region, status, sku, resource_namespace, image_tag, cluster_id, replica, rw_config, health_status, etcd_config, tier_id, retention_days, validity_days, resource_version, resources, org_id, deactivated_at, meta_store_version, secret_store_data_encryption_key, ns_id, usage_type
`

type CreateTenantParams struct {
	TenantName                   string
	ResourceNamespace            string
	NsID                         uuid.NullUUID
	Status                       string
	Sku                          string
	ImageTag                     string
	Region                       string
	ClusterID                    uint64
	Replica                      []byte
	RwConfig                     string
	EtcdConfig                   string
	TierID                       string
	RetentionDays                int32
	ValidityDays                 int32
	ResourceVersion              string
	Resources                    json.RawMessage
	OrgID                        uuid.UUID
	SecretStoreDataEncryptionKey string
	UsageType                    string
}

func (q *Queries) CreateTenant(ctx context.Context, arg CreateTenantParams) (*Tenant, error) {
	row := q.db.QueryRow(ctx, createTenant,
		arg.TenantName,
		arg.ResourceNamespace,
		arg.NsID,
		arg.Status,
		arg.Sku,
		arg.ImageTag,
		arg.Region,
		arg.ClusterID,
		arg.Replica,
		arg.RwConfig,
		arg.EtcdConfig,
		arg.TierID,
		arg.RetentionDays,
		arg.ValidityDays,
		arg.ResourceVersion,
		arg.Resources,
		arg.OrgID,
		arg.SecretStoreDataEncryptionKey,
		arg.UsageType,
	)
	var i Tenant
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.UserID,
		&i.TenantName,
		&i.Region,
		&i.Status,
		&i.Sku,
		&i.ResourceNamespace,
		&i.ImageTag,
		&i.ClusterID,
		&i.Replica,
		&i.RwConfig,
		&i.HealthStatus,
		&i.EtcdConfig,
		&i.TierID,
		&i.RetentionDays,
		&i.ValidityDays,
		&i.ResourceVersion,
		&i.Resources,
		&i.OrgID,
		&i.DeactivatedAt,
		&i.MetaStoreVersion,
		&i.SecretStoreDataEncryptionKey,
		&i.NsID,
		&i.UsageType,
	)
	return &i, err
}

const deleteTenantById = `-- name: DeleteTenantById :exec
UPDATE tenants SET status='Deleted', deactivated_at = CURRENT_TIMESTAMP WHERE id = $1 AND deactivated_at IS NULL
`

func (q *Queries) DeleteTenantById(ctx context.Context, id uint64) error {
	_, err := q.db.Exec(ctx, deleteTenantById, id)
	return err
}

const getOrgTenantById = `-- name: GetOrgTenantById :one
SELECT id, created_at, updated_at, user_id, tenant_name, region, status, sku, resource_namespace, image_tag, cluster_id, replica, rw_config, health_status, etcd_config, tier_id, retention_days, validity_days, resource_version, resources, org_id, deactivated_at, meta_store_version, secret_store_data_encryption_key, ns_id, usage_type FROM tenants WHERE id = $1 AND org_id = $2 AND deactivated_at IS NULL
`

type GetOrgTenantByIdParams struct {
	ID    uint64
	OrgID uuid.UUID
}

func (q *Queries) GetOrgTenantById(ctx context.Context, arg GetOrgTenantByIdParams) (*Tenant, error) {
	row := q.db.QueryRow(ctx, getOrgTenantById, arg.ID, arg.OrgID)
	var i Tenant
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.UserID,
		&i.TenantName,
		&i.Region,
		&i.Status,
		&i.Sku,
		&i.ResourceNamespace,
		&i.ImageTag,
		&i.ClusterID,
		&i.Replica,
		&i.RwConfig,
		&i.HealthStatus,
		&i.EtcdConfig,
		&i.TierID,
		&i.RetentionDays,
		&i.ValidityDays,
		&i.ResourceVersion,
		&i.Resources,
		&i.OrgID,
		&i.DeactivatedAt,
		&i.MetaStoreVersion,
		&i.SecretStoreDataEncryptionKey,
		&i.NsID,
		&i.UsageType,
	)
	return &i, err
}

const getOrgTenantByName = `-- name: GetOrgTenantByName :one
SELECT id, created_at, updated_at, user_id, tenant_name, region, status, sku, resource_namespace, image_tag, cluster_id, replica, rw_config, health_status, etcd_config, tier_id, retention_days, validity_days, resource_version, resources, org_id, deactivated_at, meta_store_version, secret_store_data_encryption_key, ns_id, usage_type FROM tenants WHERE tenant_name = $1 AND org_id = $2 AND deactivated_at IS NULL
`

type GetOrgTenantByNameParams struct {
	TenantName string
	OrgID      uuid.UUID
}

func (q *Queries) GetOrgTenantByName(ctx context.Context, arg GetOrgTenantByNameParams) (*Tenant, error) {
	row := q.db.QueryRow(ctx, getOrgTenantByName, arg.TenantName, arg.OrgID)
	var i Tenant
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.UserID,
		&i.TenantName,
		&i.Region,
		&i.Status,
		&i.Sku,
		&i.ResourceNamespace,
		&i.ImageTag,
		&i.ClusterID,
		&i.Replica,
		&i.RwConfig,
		&i.HealthStatus,
		&i.EtcdConfig,
		&i.TierID,
		&i.RetentionDays,
		&i.ValidityDays,
		&i.ResourceVersion,
		&i.Resources,
		&i.OrgID,
		&i.DeactivatedAt,
		&i.MetaStoreVersion,
		&i.SecretStoreDataEncryptionKey,
		&i.NsID,
		&i.UsageType,
	)
	return &i, err
}

const getOrgTenantByNsId = `-- name: GetOrgTenantByNsId :one
SELECT id, created_at, updated_at, user_id, tenant_name, region, status, sku, resource_namespace, image_tag, cluster_id, replica, rw_config, health_status, etcd_config, tier_id, retention_days, validity_days, resource_version, resources, org_id, deactivated_at, meta_store_version, secret_store_data_encryption_key, ns_id, usage_type FROM tenants WHERE ns_id = $2::uuid AND org_id = $1 AND deactivated_at IS NULL
`

type GetOrgTenantByNsIdParams struct {
	OrgID uuid.UUID
	NsID  uuid.UUID
}

func (q *Queries) GetOrgTenantByNsId(ctx context.Context, arg GetOrgTenantByNsIdParams) (*Tenant, error) {
	row := q.db.QueryRow(ctx, getOrgTenantByNsId, arg.OrgID, arg.NsID)
	var i Tenant
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.UserID,
		&i.TenantName,
		&i.Region,
		&i.Status,
		&i.Sku,
		&i.ResourceNamespace,
		&i.ImageTag,
		&i.ClusterID,
		&i.Replica,
		&i.RwConfig,
		&i.HealthStatus,
		&i.EtcdConfig,
		&i.TierID,
		&i.RetentionDays,
		&i.ValidityDays,
		&i.ResourceVersion,
		&i.Resources,
		&i.OrgID,
		&i.DeactivatedAt,
		&i.MetaStoreVersion,
		&i.SecretStoreDataEncryptionKey,
		&i.NsID,
		&i.UsageType,
	)
	return &i, err
}

const getOrgTenantsCountBySku = `-- name: GetOrgTenantsCountBySku :many
SELECT tenants.sku, COUNT(*) FROM tenants WHERE org_id = $1 AND deactivated_at IS NULL GROUP BY sku
`

type GetOrgTenantsCountBySkuRow struct {
	Sku   string
	Count int64
}

func (q *Queries) GetOrgTenantsCountBySku(ctx context.Context, orgID uuid.UUID) ([]*GetOrgTenantsCountBySkuRow, error) {
	rows, err := q.db.Query(ctx, getOrgTenantsCountBySku, orgID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*GetOrgTenantsCountBySkuRow
	for rows.Next() {
		var i GetOrgTenantsCountBySkuRow
		if err := rows.Scan(&i.Sku, &i.Count); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getOrgTenantsCountByStatus = `-- name: GetOrgTenantsCountByStatus :many
SELECT tenants.status, COUNT(*) FROM tenants WHERE org_id = $1 AND deactivated_at IS NULL GROUP BY status
`

type GetOrgTenantsCountByStatusRow struct {
	Status string
	Count  int64
}

func (q *Queries) GetOrgTenantsCountByStatus(ctx context.Context, orgID uuid.UUID) ([]*GetOrgTenantsCountByStatusRow, error) {
	rows, err := q.db.Query(ctx, getOrgTenantsCountByStatus, orgID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*GetOrgTenantsCountByStatusRow
	for rows.Next() {
		var i GetOrgTenantsCountByStatusRow
		if err := rows.Scan(&i.Status, &i.Count); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getTenantById = `-- name: GetTenantById :one
SELECT id, created_at, updated_at, user_id, tenant_name, region, status, sku, resource_namespace, image_tag, cluster_id, replica, rw_config, health_status, etcd_config, tier_id, retention_days, validity_days, resource_version, resources, org_id, deactivated_at, meta_store_version, secret_store_data_encryption_key, ns_id, usage_type FROM tenants WHERE id = $1 AND deactivated_at IS NULL
`

func (q *Queries) GetTenantById(ctx context.Context, id uint64) (*Tenant, error) {
	row := q.db.QueryRow(ctx, getTenantById, id)
	var i Tenant
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.UserID,
		&i.TenantName,
		&i.Region,
		&i.Status,
		&i.Sku,
		&i.ResourceNamespace,
		&i.ImageTag,
		&i.ClusterID,
		&i.Replica,
		&i.RwConfig,
		&i.HealthStatus,
		&i.EtcdConfig,
		&i.TierID,
		&i.RetentionDays,
		&i.ValidityDays,
		&i.ResourceVersion,
		&i.Resources,
		&i.OrgID,
		&i.DeactivatedAt,
		&i.MetaStoreVersion,
		&i.SecretStoreDataEncryptionKey,
		&i.NsID,
		&i.UsageType,
	)
	return &i, err
}

const getTenantByNsId = `-- name: GetTenantByNsId :one
SELECT id, created_at, updated_at, user_id, tenant_name, region, status, sku, resource_namespace, image_tag, cluster_id, replica, rw_config, health_status, etcd_config, tier_id, retention_days, validity_days, resource_version, resources, org_id, deactivated_at, meta_store_version, secret_store_data_encryption_key, ns_id, usage_type FROM tenants WHERE ns_id = $1::uuid AND deactivated_at IS NULL
`

func (q *Queries) GetTenantByNsId(ctx context.Context, nsID uuid.UUID) (*Tenant, error) {
	row := q.db.QueryRow(ctx, getTenantByNsId, nsID)
	var i Tenant
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.UserID,
		&i.TenantName,
		&i.Region,
		&i.Status,
		&i.Sku,
		&i.ResourceNamespace,
		&i.ImageTag,
		&i.ClusterID,
		&i.Replica,
		&i.RwConfig,
		&i.HealthStatus,
		&i.EtcdConfig,
		&i.TierID,
		&i.RetentionDays,
		&i.ValidityDays,
		&i.ResourceVersion,
		&i.Resources,
		&i.OrgID,
		&i.DeactivatedAt,
		&i.MetaStoreVersion,
		&i.SecretStoreDataEncryptionKey,
		&i.NsID,
		&i.UsageType,
	)
	return &i, err
}

const getTenants = `-- name: GetTenants :many
SELECT id, created_at, updated_at, user_id, tenant_name, region, status, sku, resource_namespace, image_tag, cluster_id, replica, rw_config, health_status, etcd_config, tier_id, retention_days, validity_days, resource_version, resources, org_id, deactivated_at, meta_store_version, secret_store_data_encryption_key, ns_id, usage_type FROM tenants WHERE deactivated_at IS NULL ORDER BY id OFFSET $1 LIMIT $2
`

type GetTenantsParams struct {
	Offset int32
	Limit  int32
}

func (q *Queries) GetTenants(ctx context.Context, arg GetTenantsParams) ([]*Tenant, error) {
	rows, err := q.db.Query(ctx, getTenants, arg.Offset, arg.Limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*Tenant
	for rows.Next() {
		var i Tenant
		if err := rows.Scan(
			&i.ID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.UserID,
			&i.TenantName,
			&i.Region,
			&i.Status,
			&i.Sku,
			&i.ResourceNamespace,
			&i.ImageTag,
			&i.ClusterID,
			&i.Replica,
			&i.RwConfig,
			&i.HealthStatus,
			&i.EtcdConfig,
			&i.TierID,
			&i.RetentionDays,
			&i.ValidityDays,
			&i.ResourceVersion,
			&i.Resources,
			&i.OrgID,
			&i.DeactivatedAt,
			&i.MetaStoreVersion,
			&i.SecretStoreDataEncryptionKey,
			&i.NsID,
			&i.UsageType,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getTenantsByOrgId = `-- name: GetTenantsByOrgId :many
SELECT id, created_at, updated_at, user_id, tenant_name, region, status, sku, resource_namespace, image_tag, cluster_id, replica, rw_config, health_status, etcd_config, tier_id, retention_days, validity_days, resource_version, resources, org_id, deactivated_at, meta_store_version, secret_store_data_encryption_key, ns_id, usage_type FROM tenants WHERE org_id = $1 AND deactivated_at IS NULL OFFSET $2 LIMIT $3
`

type GetTenantsByOrgIdParams struct {
	OrgID  uuid.UUID
	Offset int32
	Limit  int32
}

func (q *Queries) GetTenantsByOrgId(ctx context.Context, arg GetTenantsByOrgIdParams) ([]*Tenant, error) {
	rows, err := q.db.Query(ctx, getTenantsByOrgId, arg.OrgID, arg.Offset, arg.Limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*Tenant
	for rows.Next() {
		var i Tenant
		if err := rows.Scan(
			&i.ID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.UserID,
			&i.TenantName,
			&i.Region,
			&i.Status,
			&i.Sku,
			&i.ResourceNamespace,
			&i.ImageTag,
			&i.ClusterID,
			&i.Replica,
			&i.RwConfig,
			&i.HealthStatus,
			&i.EtcdConfig,
			&i.TierID,
			&i.RetentionDays,
			&i.ValidityDays,
			&i.ResourceVersion,
			&i.Resources,
			&i.OrgID,
			&i.DeactivatedAt,
			&i.MetaStoreVersion,
			&i.SecretStoreDataEncryptionKey,
			&i.NsID,
			&i.UsageType,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getTenantsCount = `-- name: GetTenantsCount :one
SELECT COUNT(*) FROM tenants WHERE deactivated_at IS NULL
`

func (q *Queries) GetTenantsCount(ctx context.Context) (int64, error) {
	row := q.db.QueryRow(ctx, getTenantsCount)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const getTenantsCountByOrgId = `-- name: GetTenantsCountByOrgId :one
SELECT COUNT(*) FROM tenants WHERE org_id = $1 AND deactivated_at IS NULL
`

func (q *Queries) GetTenantsCountByOrgId(ctx context.Context, orgID uuid.UUID) (int64, error) {
	row := q.db.QueryRow(ctx, getTenantsCountByOrgId, orgID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const getTenantsForGarbageCollection = `-- name: GetTenantsForGarbageCollection :many
SELECT id, created_at, updated_at, user_id, tenant_name, region, status, sku, resource_namespace, image_tag, cluster_id, replica, rw_config, health_status, etcd_config, tier_id, retention_days, validity_days, resource_version, resources, org_id, deactivated_at, meta_store_version, secret_store_data_encryption_key, ns_id, usage_type FROM tenants
WHERE (status = 'Deleting' AND deactivated_at IS NULL)
   OR ((status = 'Creating' OR status = 'Failed')
    AND updated_at < CURRENT_TIMESTAMP - '3 DAYS'::INTERVAL
    AND deactivated_at IS NULL)
`

func (q *Queries) GetTenantsForGarbageCollection(ctx context.Context) ([]*Tenant, error) {
	rows, err := q.db.Query(ctx, getTenantsForGarbageCollection)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*Tenant
	for rows.Next() {
		var i Tenant
		if err := rows.Scan(
			&i.ID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.UserID,
			&i.TenantName,
			&i.Region,
			&i.Status,
			&i.Sku,
			&i.ResourceNamespace,
			&i.ImageTag,
			&i.ClusterID,
			&i.Replica,
			&i.RwConfig,
			&i.HealthStatus,
			&i.EtcdConfig,
			&i.TierID,
			&i.RetentionDays,
			&i.ValidityDays,
			&i.ResourceVersion,
			&i.Resources,
			&i.OrgID,
			&i.DeactivatedAt,
			&i.MetaStoreVersion,
			&i.SecretStoreDataEncryptionKey,
			&i.NsID,
			&i.UsageType,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const setValueForEmptyTenantSecretStoreDekById = `-- name: SetValueForEmptyTenantSecretStoreDekById :execresult
UPDATE tenants SET secret_store_data_encryption_key= $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2 AND deactivated_at IS NULL AND secret_store_data_encryption_key=''
`

type SetValueForEmptyTenantSecretStoreDekByIdParams struct {
	SecretStoreDataEncryptionKey string
	ID                           uint64
}

func (q *Queries) SetValueForEmptyTenantSecretStoreDekById(ctx context.Context, arg SetValueForEmptyTenantSecretStoreDekByIdParams) (pgconn.CommandTag, error) {
	return q.db.Exec(ctx, setValueForEmptyTenantSecretStoreDekById, arg.SecretStoreDataEncryptionKey, arg.ID)
}

const updateTenantEtcdConfigById = `-- name: UpdateTenantEtcdConfigById :exec
UPDATE tenants SET etcd_config = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2 AND deactivated_at IS NULL
`

type UpdateTenantEtcdConfigByIdParams struct {
	EtcdConfig string
	ID         uint64
}

func (q *Queries) UpdateTenantEtcdConfigById(ctx context.Context, arg UpdateTenantEtcdConfigByIdParams) error {
	_, err := q.db.Exec(ctx, updateTenantEtcdConfigById, arg.EtcdConfig, arg.ID)
	return err
}

const updateTenantImageTagById = `-- name: UpdateTenantImageTagById :execresult
UPDATE tenants SET image_tag= $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2 AND deactivated_at IS NULL
`

type UpdateTenantImageTagByIdParams struct {
	ImageTag string
	ID       uint64
}

func (q *Queries) UpdateTenantImageTagById(ctx context.Context, arg UpdateTenantImageTagByIdParams) (pgconn.CommandTag, error) {
	return q.db.Exec(ctx, updateTenantImageTagById, arg.ImageTag, arg.ID)
}

const updateTenantResourcesById = `-- name: UpdateTenantResourcesById :execresult
UPDATE tenants SET resources = $2, updated_at = CURRENT_TIMESTAMP WHERE id = $1 AND deactivated_at IS NULL
`

type UpdateTenantResourcesByIdParams struct {
	ID        uint64
	Resources json.RawMessage
}

func (q *Queries) UpdateTenantResourcesById(ctx context.Context, arg UpdateTenantResourcesByIdParams) (pgconn.CommandTag, error) {
	return q.db.Exec(ctx, updateTenantResourcesById, arg.ID, arg.Resources)
}

const updateTenantRwConfigById = `-- name: UpdateTenantRwConfigById :execresult
UPDATE tenants SET rw_config = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2 AND deactivated_at IS NULL
`

type UpdateTenantRwConfigByIdParams struct {
	RwConfig string
	ID       uint64
}

func (q *Queries) UpdateTenantRwConfigById(ctx context.Context, arg UpdateTenantRwConfigByIdParams) (pgconn.CommandTag, error) {
	return q.db.Exec(ctx, updateTenantRwConfigById, arg.RwConfig, arg.ID)
}

const updateTenantRwConfigByNsId = `-- name: UpdateTenantRwConfigByNsId :execresult
UPDATE tenants SET rw_config = $1, updated_at = CURRENT_TIMESTAMP WHERE ns_id = $2 AND deactivated_at IS NULL
`

type UpdateTenantRwConfigByNsIdParams struct {
	RwConfig string
	NsID     uuid.NullUUID
}

func (q *Queries) UpdateTenantRwConfigByNsId(ctx context.Context, arg UpdateTenantRwConfigByNsIdParams) (pgconn.CommandTag, error) {
	return q.db.Exec(ctx, updateTenantRwConfigByNsId, arg.RwConfig, arg.NsID)
}

const updateTenantStatusById = `-- name: UpdateTenantStatusById :execresult
UPDATE tenants SET status = $1, health_status = 'Unknown', updated_at = CURRENT_TIMESTAMP WHERE id = $2 AND deactivated_at IS NULL
`

type UpdateTenantStatusByIdParams struct {
	Status string
	ID     uint64
}

func (q *Queries) UpdateTenantStatusById(ctx context.Context, arg UpdateTenantStatusByIdParams) (pgconn.CommandTag, error) {
	return q.db.Exec(ctx, updateTenantStatusById, arg.Status, arg.ID)
}

const updateTenantTierById = `-- name: UpdateTenantTierById :execresult
UPDATE tenants SET tier_id = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2 AND deactivated_at IS NULL
`

type UpdateTenantTierByIdParams struct {
	TierID string
	ID     uint64
}

func (q *Queries) UpdateTenantTierById(ctx context.Context, arg UpdateTenantTierByIdParams) (pgconn.CommandTag, error) {
	return q.db.Exec(ctx, updateTenantTierById, arg.TierID, arg.ID)
}
