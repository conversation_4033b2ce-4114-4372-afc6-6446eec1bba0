package model

import (
	"context"

	"github.com/jackc/pgx/v5"
	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/risingwave-cloud/internal/model/querier"
)

func (model *Model) GetRwDatabases(ctx context.Context, tenantID uint64) ([]*RwDatabase, error) {
	ctx, cancel := context.WithTimeout(ctx, QueryTimeout())
	defer cancel()

	databases, err := model.querier.GetRwDatabases(ctx, tenantID)
	if err != nil && err != pgx.ErrNoRows {
		return nil, eris.Wrapf(err, "failed to get rw_database")
	}

	return databases, nil
}

func (model *Model) GetRwDatabasesCount(ctx context.Context, tenantID uint64) (uint64, error) {
	ctx, cancel := context.WithTimeout(ctx, QueryTimeout())
	defer cancel()

	count, err := model.querier.GetRwDatabasesCountByTenantId(ctx, tenantID)

	return uint64(count), err
}

func (model *Model) GetRwDatabasesCountGroupByResourceGroup(ctx context.Context, tenantID uint64) (map[string]int, error) {
	ctx, cancel := context.WithTimeout(ctx, QueryTimeout())
	defer cancel()

	counts, err := model.querier.GetRwDatabasesCountGroupByResourceGroup(ctx, tenantID)
	if err != nil && err != pgx.ErrNoRows {
		return nil, eris.Wrapf(err, "failed to get rw_databases count group by resource group")
	}
	result := make(map[string]int)
	for _, c := range counts {
		result[c.ResourceGroup] = int(c.Cnt)
	}

	return result, nil
}

func (model *Model) GetRwDatabasesPaginated(ctx context.Context, tenantID uint64, offset uint64, limit uint64) ([]*RwDatabase, error) {
	ctx, cancel := context.WithTimeout(ctx, QueryTimeout())
	defer cancel()

	databases, err := model.querier.GetRwDatabasesPaginated(ctx, querier.GetRwDatabasesPaginatedParams{
		TenantID: tenantID,
		Offset:   int32(offset),
		Limit:    int32(limit),
	})
	if err != nil && err != pgx.ErrNoRows {
		return nil, eris.Wrapf(err, "failed to get rw_database")
	}

	return databases, nil
}

func (model *Model) IsRwDatabaseExist(ctx context.Context, tenantID uint64, database string) (bool, error) {
	ctx, cancel := context.WithTimeout(ctx, QueryTimeout())
	defer cancel()

	_, err := model.querier.IsRwDatabaseExist(ctx, querier.IsRwDatabaseExistParams{
		TenantID: tenantID,
		Name:     database,
	})
	if err != nil && err != pgx.ErrNoRows {
		return false, eris.Wrapf(err, "failed to check if rw_database exists by clusterId %d and database %s", tenantID, database)
	}
	return err != pgx.ErrNoRows, nil
}

func (model *Model) DeleteRwDatabasesByName(ctx context.Context, tenantID uint64, database string) error {
	err := model.querier.DeleteRwDatabasesByName(ctx, querier.DeleteRwDatabasesByNameParams{
		TenantID: tenantID,
		Name:     database,
	})
	if err != nil {
		return eris.Wrapf(err, "failed to delete rw_database")
	}
	return nil
}

func (model *Model) UpsertRwDatabase(ctx context.Context, tenantID uint64, database string, resourceGroup string) error {
	err := model.querier.UpsertRwDatabase(ctx, querier.UpsertRwDatabaseParams{
		TenantID:      tenantID,
		Name:          database,
		ResourceGroup: resourceGroup,
	})
	if err != nil {
		return eris.Wrapf(err, "failed to create rw_database")
	}
	return nil
}
