package component

import (
	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbrw "github.com/risingwavelabs/cloudagent/pbgen/common/rw"
)

type ComponentType string //nolint:revive

const (
	Etcd       ComponentType = "etcd"
	Frontend   ComponentType = "frontend"
	Compute    ComponentType = "compute"
	Compactor  ComponentType = "compactor"
	Meta       ComponentType = "meta"
	Standalone ComponentType = "standalone"
)

func (comp ComponentType) ToPbComponentType() pbrw.ComponentType {
	switch comp {
	case Compute:
		return pbrw.ComponentType_COMPUTE
	case Compactor:
		return pbrw.ComponentType_COMPACTOR
	case Meta:
		return pbrw.ComponentType_META
	case Frontend:
		return pbrw.ComponentType_FRONTEND
	case Etcd, Standalone:
		return pbrw.ComponentType_UNKNOWN_COMPONENT
	}
	return pbrw.ComponentType_UNKNOWN_COMPONENT
}

// ResourceSpec represents the resource definition of a node group in RisingWave cluster.
// Note that the `id` is just for validating the request. The single source of truth are the actual
// CPU and memory spec. The `id` is only used in the API layer to retrieve the actual values of the
// spec. This design can allow us to minimize the attack surface of the resource request. Users can
// only use the preset resource spec.
type ResourceSpec struct {
	ID            string `json:"id"`
	CPURequest    string `json:"cpu_request"`
	CPULimit      string `json:"cpu_limit"`
	MemoryRequest string `json:"memory_request"`
	MemoryLimit   string `json:"memory_limit"`
}

func (spec *ResourceSpec) ToResourceRequirementProto() *pbk8s.ResourceRequirements {
	if spec == nil {
		return nil
	}
	return &pbk8s.ResourceRequirements{
		CpuRequest:    spec.CPURequest,
		CpuLimit:      spec.CPULimit,
		MemoryRequest: spec.MemoryRequest,
		MemoryLimit:   spec.MemoryLimit,
	}
}
