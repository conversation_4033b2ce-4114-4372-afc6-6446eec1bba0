package resourcereq

import (
	"fmt"
	"reflect"
	"slices"

	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext/compaction"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/provision"
	spec "github.com/risingwavelabs/risingwave-cloud/shared/spec/mgmt_spec_v2"

	"github.com/risingwavelabs/eris"
	"github.com/samber/lo"

	"github.com/risingwavelabs/risingwave-cloud/internal/config"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenant/component"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenant/metastore"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenant/resourcespec"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/update"
)

type ValidationResult struct {
	IsValid     bool
	AllowsTrial bool
	Message     string
}

type ParseTenantResourceResult struct {
	ValidationResult
	Result resourcespec.TenantResourceSpec
}

func ParseTenantResource(tierID string, request TenantResourceRequest) (ParseTenantResourceResult, error) {
	fail := func(message string, a ...any) ParseTenantResourceResult {
		return ParseTenantResourceResult{ValidationResult: ValidationResult{
			IsValid:     false,
			AllowsTrial: false,
			Message:     fmt.Sprintf(message, a...),
		}}
	}

	allowsTrial := true

	rc := config.GetResourceDef()
	tier := rc.GetTierByID(tierID)
	if tier == nil {
		return fail("no such tier: %s", tierID), nil
	}

	// Assert. Either standalone or meta/compute/frontend/compactor must exist.
	isStandalone := request.Components.Standalone != nil
	isCluster := request.Components.Meta != nil && request.Components.Compute != nil &&
		request.Components.Frontend != nil && request.Components.Compactor != nil
	if !isStandalone && !isCluster {
		return fail("invalid resource request: either standalone or distributed resources must be provided"), nil
	}
	if isStandalone && isCluster {
		return fail("invalid resource request: provide at most one of standalone or distributed resources"), nil
	}

	type componentValidation struct {
		req   *ComponentResourceRequest
		limit []*config.AvailableComponentType
		spec  component.ResourceSpec
	}
	components := map[string]*componentValidation{
		model.StandaloneComponentName: {req: request.Components.Standalone, limit: tier.AvailableStandaloneTypeList},
		model.ComputeComponentName:    {req: request.Components.Compute, limit: tier.AvailableComputeTypeList},
		model.CompactorComponentName:  {req: request.Components.Compactor, limit: tier.AvailableCompactorTypeList},
		model.FrontendComponentName:   {req: request.Components.Frontend, limit: tier.AvailableFrontendTypeList},
		model.MetaComponentName:       {req: request.Components.Meta, limit: tier.AvailableMetaTypeList},
	}
	for k, comp := range components {
		req, limit := comp.req, comp.limit
		if req == nil {
			continue
		}

		availableIDs := toComponentsID(limit)
		if !slices.Contains(availableIDs, req.ComponentTypeID) {
			return fail("component type %s is not available for %s. Valid types are %s", req.ComponentTypeID, k, availableIDs), nil
		}

		paidValid, trialValid := isReplicaValid(req.ComponentTypeID, int32(req.Replica), limit)
		if !paidValid {
			return fail("request %d replica(s) not valid for %s. Exceeds the maximum allowed value or less than or equal to 0", req.Replica, k), nil
		}
		allowsTrial = allowsTrial && trialValid

		comp.spec = ComponentTypeToComponentResourceSpec(rc.GetComponentTypeByID(req.ComponentTypeID))
	}

	metaStoreSpec, err := parseMetaStore(rc, tier, request)
	if err != nil && eris.GetPropertyP[string](err, "message") != (*string)(nil) {
		return fail(*eris.GetPropertyP[string](err, "message")), nil
	}

	if request.ComputeFileCacheSizeGiB < 0 || request.ComputeFileCacheSizeGiB > int(tier.MaximumComputeFileCacheGB) {
		return fail("request %d GB for compute file cache, which exceeds the maximum allowed value or less than 0", request.ComputeFileCacheSizeGiB), nil
	}

	getComponentSpec := func(s string) *component.ResourceSpec {
		val := components[s]
		if val == nil || val.req == nil {
			return nil
		}
		return &val.spec
	}

	getComponentReplicas := func(r *ComponentResourceRequest) int32 {
		if r == nil {
			return 0
		}
		return int32(r.Replica)
	}

	return ParseTenantResourceResult{
		ValidationResult{IsValid: true, AllowsTrial: allowsTrial},
		lo.IfF(request.Components.Standalone != nil, func() resourcespec.TenantResourceSpec {
			return resourcespec.FromValidTenantResourceSpec(&resourcespec.TenantResourceSpecV1Standalone{
				TenantResourceSpecV1Common: resourcespec.TenantResourceSpecV1Common{
					ComputeCacheSpec: resourcespec.ComputeCacheSpec{
						SizeGb: int32(request.ComputeFileCacheSizeGiB),
					},
					MetaStoreSpec: metaStoreSpec,
				},
				StandaloneResourceSpec: *getComponentSpec(model.StandaloneComponentName),
				StandaloneReplica:      getComponentReplicas(request.Components.Standalone),
			})
		}).ElseF(func() resourcespec.TenantResourceSpec {
			return resourcespec.FromValidTenantResourceSpec(&resourcespec.TenantResourceSpecV1Regular{
				TenantResourceSpecV1Common: resourcespec.TenantResourceSpecV1Common{
					ComputeCacheSpec: resourcespec.ComputeCacheSpec{
						SizeGb: int32(request.ComputeFileCacheSizeGiB),
					},
					MetaStoreSpec: metaStoreSpec,
				},
				ComputeResourceSpec:   *getComponentSpec(model.ComputeComponentName),
				CompactorResourceSpec: *getComponentSpec(model.CompactorComponentName),
				FrontendResourceSpec:  *getComponentSpec(model.FrontendComponentName),
				MetaResourceSpec:      *getComponentSpec(model.MetaComponentName),
				ComputeReplica:        getComponentReplicas(request.Components.Compute),
				CompactorReplica:      getComponentReplicas(request.Components.Compactor),
				FrontendReplica:       getComponentReplicas(request.Components.Frontend),
				MetaReplica:           getComponentReplicas(request.Components.Meta),
			})
		}),
	}, nil
}

func parseMetaStore(resourceDef *config.ResourcesDef, tier *config.Tier, request TenantResourceRequest) (*metastore.Spec, error) {
	fail := func(message string, a ...any) (*metastore.Spec, error) {
		return nil, eris.New("failed to parse meta store").WithProperty("message", fmt.Sprintf(message, a...)).WithCode(eris.CodeInvalidArgument)
	}
	metaStoreReq := request.MetaStore
	// no meta store defined in request, decide by backend later
	if metaStoreReq == nil && request.Components.Etcd == nil {
		return nil, nil
	}
	if metaStoreReq == nil {
		// legacy ETCD request
		componentEtcd := request.Components.Etcd
		if request.EtcdVolumeSizeGiB == nil {
			return fail("no etcd volume size specified")
		}
		metaStoreReq = &TenantResourceRequestMetaStore{
			Type: metastore.Etcd,
			Etcd: &TenantResourceRequestMetaStoreEtcd{
				ComponentTypeID: componentEtcd.ComponentTypeID,
				Replica:         componentEtcd.Replica,
				SizeGb:          *request.EtcdVolumeSizeGiB,
			},
		}
	}

	switch metaStoreReq.Type {
	case metastore.Etcd:
		reqEtcd := metaStoreReq.Etcd
		tierEtcd := tier.AvailableMetaStore.Etcd
		if tierEtcd == nil {
			return fail("Etcd meta store not supported")
		}

		availableIDs := toComponentsID(tierEtcd.TypeList)
		if !slices.Contains(availableIDs, reqEtcd.ComponentTypeID) {
			return fail("no such component type '%s' for Etcd meta store. Valid types are %s", reqEtcd.ComponentTypeID, availableIDs)
		}

		isValid, _ := isReplicaValid(reqEtcd.ComponentTypeID, int32(reqEtcd.Replica), tierEtcd.TypeList)
		if !isValid {
			return fail("request %d replica(s) not valid for Etcd meta store. Exceeds the maximum allowed value or less than or equal to 0", reqEtcd.Replica)
		}

		if reqEtcd.SizeGb < 0 || reqEtcd.SizeGb > int(tierEtcd.MaximumSizeGB) {
			return fail("request %d GB for etcd storage, which exceeds the maximum allowed value or less than 0", reqEtcd.SizeGb)
		}
		return &metastore.Spec{
			Type: metaStoreReq.Type,
			Etcd: &metastore.EtcdSpec{
				ResourceSpec: ComponentTypeToComponentResourceSpec(resourceDef.GetComponentTypeByID(reqEtcd.ComponentTypeID)),
				Replica:      int32(reqEtcd.Replica),
				SizeGb:       int32(reqEtcd.SizeGb),
			},
		}, nil

	case metastore.Postgresql:
		reqPg := metaStoreReq.Postgresql
		tierPg := tier.AvailableMetaStore.PostgreSQL
		if tierPg == nil {
			return fail("PostgreSQL meta store not supported")
		}
		availableIDs := toComponentsID(tierPg.TypeList)
		if !slices.Contains(availableIDs, reqPg.ComponentTypeID) {
			return fail("no such component type '%s' for PostgreSQL meta store. Valid types are %s", reqPg.ComponentTypeID, availableIDs)
		}

		isValid, _ := isReplicaValid(reqPg.ComponentTypeID, int32(reqPg.Replica), tierPg.TypeList)
		if !isValid {
			return fail("request %d replica(s) not valid for PostgreSQL meta store. Exceeds the maximum allowed value or less than or equal to 0", reqPg.Replica)
		}

		if reqPg.SizeGb < 0 || reqPg.SizeGb > int(tierPg.MaximumSizeGB) {
			return fail("request %d GB for postgresql storage, which exceeds the maximum allowed value or less than 0", reqPg.SizeGb)
		}
		return &metastore.Spec{
			Type: metaStoreReq.Type,
			PostgreSQL: &metastore.PostgreSQLSpec{
				ResourceSpec: ComponentTypeToComponentResourceSpec(resourceDef.GetComponentTypeByID(reqPg.ComponentTypeID)),
				Replica:      int32(reqPg.Replica),
				SizeGb:       int32(reqPg.SizeGb),
			},
		}, nil
	case metastore.AwsRds:
		return fail("don't allow specify AwsRds meta store in request")
	case metastore.GcpCloudSQL:
		return fail("don't allow specify GcpCloudSQL meta store in request")
	case metastore.AzrPostgres:
		return fail("don't allow specify AzrPostgres meta store in request")
	case metastore.SharingPg:
		return fail("don't allow specify SharingPg meta store in request")
	default:
		return fail("meta store type '%s' isn't supported", metaStoreReq.Type)
	}
}

type ParseResourceUpdateResult struct {
	ValidationResult
	Result update.TenantResourcesRequest
}

func (res ParseResourceUpdateResult) WillUpdate(src resourcespec.TenantResourceSpec) bool {
	dst := res.Result.ApplyTo(src)
	return !reflect.DeepEqual(src, dst)
}

func (res ParseResourceUpdateResult) WillUpdateExtension(
	tenant *model.Tenant,
	tenantExt model.TenantExtension,
	request *spec.TenantExtensionsRequest,
) (*provision.ServerlessCompactionOptions, error) {
	var newMaxConcurrency uint32
	if request == nil || request.ServerlessCompaction == nil {
		newMaxConcurrency = 0
	} else {
		newMaxConcurrency = uint32(request.ServerlessCompaction.MaximumCompactionConcurrency)
	}

	// Use ServerlessCompactionOptions.Allowed field from the Tier struct
	rc := config.GetResourceDef()
	tier := rc.GetTierByID(tenant.TierID)
	if newMaxConcurrency > 0 {
		if tier == nil || !tier.ServerlessCompactionOptions.Allowed {
			return nil, eris.New("serverless compaction operation is not allowed for tier " + tenant.TierID).
				WithCode(eris.CodePermissionDenied)
		}
	}

	cfg, err := compaction.GetConfig(tenantExt)
	if err != nil {
		return nil, eris.Wrapf(err, "failed to get compaction config for tenant %d", tenantExt.TenantID)
	}

	maxConcurrency, err := cfg.GetMaxConcurrency()
	if err != nil {
		return nil, eris.Wrapf(err, "failed to get the current max concurrency for tenant %d", tenantExt.TenantID)
	}

	// If there's any change in the max concurrency, we need to update the compaction options.
	if maxConcurrency != newMaxConcurrency {
		return &provision.ServerlessCompactionOptions{
			MaximumCPUSize: int(newMaxConcurrency),
		}, nil
	}

	return nil, nil
}

func ParseResourceUpdate(tierID string, request TenantResourceRequestComponents) (ParseResourceUpdateResult, error) {
	fail := func(message string, a ...any) ParseResourceUpdateResult {
		return ParseResourceUpdateResult{ValidationResult: ValidationResult{
			IsValid:     false,
			AllowsTrial: false,
			Message:     fmt.Sprintf(message, a...),
		}}
	}

	allowsTrial := true

	isStandalone := request.Standalone != nil
	isCluster := request.Meta != nil || request.Compute != nil ||
		request.Frontend != nil || request.Compactor != nil
	if !isStandalone && !isCluster {
		return fail("invalid resource request: either standalone or distributed resources must be provided"), nil
	}
	if isStandalone && isCluster {
		return fail("invalid resource request: provide at most one of standalone or distributed resources"), nil
	}

	rc := config.GetResourceDef()
	tier := rc.GetTierByID(tierID)
	if tier == nil {
		return fail(""), eris.Errorf("No tier %s found", tierID)
	}

	type componentValidation struct {
		req   *ComponentResourceRequest
		limit []*config.AvailableComponentType
		res   *update.ComponentResourcesRequest
	}
	components := map[string]*componentValidation{
		model.ComputeComponentName:    {req: request.Compute, limit: tier.AvailableComputeTypeList},
		model.CompactorComponentName:  {req: request.Compactor, limit: tier.AvailableCompactorTypeList},
		model.FrontendComponentName:   {req: request.Frontend, limit: tier.AvailableFrontendTypeList},
		model.MetaComponentName:       {req: request.Meta, limit: tier.AvailableMetaTypeList},
		model.StandaloneComponentName: {req: request.Standalone, limit: tier.AvailableStandaloneTypeList},
	}
	for k, comp := range components {
		req, limit := comp.req, comp.limit
		if req == nil {
			continue
		}
		availableIDs := toComponentsID(limit)
		if !slices.Contains(availableIDs, req.ComponentTypeID) {
			return fail("no such component type %s for %s. Valid types are %s", req.ComponentTypeID, k, availableIDs), nil
		}

		paidValid, trialValid := isReplicaValid(req.ComponentTypeID, int32(req.Replica), limit)
		if !paidValid {
			return fail("request %d replica(s) not valid for %s. Exceeds the maximum allowed value or <= 0", req.Replica, k), nil
		}
		allowsTrial = allowsTrial && trialValid

		comp.res = &update.ComponentResourcesRequest{
			Replicas:  int32(req.Replica),
			Resources: ComponentTypeToComponentResourceSpec(rc.GetComponentTypeByID(req.ComponentTypeID)),
		}
	}

	return ParseResourceUpdateResult{
		ValidationResult{IsValid: true, AllowsTrial: allowsTrial},
		update.TenantResourcesRequest{
			Compute:    components[model.ComputeComponentName].res,
			Compactor:  components[model.CompactorComponentName].res,
			Frontend:   components[model.FrontendComponentName].res,
			Meta:       components[model.MetaComponentName].res,
			Standalone: components[model.StandaloneComponentName].res,
		}}, nil
}

func toComponentsID(availableList []*config.AvailableComponentType) []string {
	var availableID []string
	for _, item := range availableList {
		availableID = append(availableID, item.ComponentType.ID)
	}
	return availableID
}

func isReplicaValid(compID string, replica int32, availableList []*config.AvailableComponentType) (isValid, allowsTrial bool) {
	if replica <= 0 {
		return false, false
	}

	for _, item := range availableList {
		if compID != item.ComponentType.ID {
			continue
		}
		allowsTrial = allowsTrial || replica <= item.MaximumTrialReplica
		isValid = isValid || replica <= item.MaximumReplica
	}

	return
}

func ComponentTypeToComponentResourceSpec(c *config.ComponentType) component.ResourceSpec {
	return component.ResourceSpec{
		ID:            c.ID,
		CPURequest:    c.CPURequest.String(),
		CPULimit:      c.CPULimit.String(),
		MemoryRequest: c.MemoryRequest.String(),
		MemoryLimit:   c.MemoryLimit.String(),
	}
}
