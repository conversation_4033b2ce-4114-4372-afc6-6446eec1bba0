package resourcereq

import (
	"testing"

	"github.com/stretchr/testify/require"
	"k8s.io/apimachinery/pkg/api/resource"

	"github.com/risingwavelabs/risingwave-cloud/internal/config"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenant/component"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenant/metastore"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenant/resourcespec"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/update"
	"github.com/risingwavelabs/risingwave-cloud/shared/ptr"
	spec "github.com/risingwavelabs/risingwave-cloud/shared/spec/mgmt_spec_v2"
	"github.com/risingwavelabs/risingwave-cloud/shared/tenant/tier"
)

const TestTierID = "ValidateUt"
const TestTierPreferEtcd = "TestTierPreferEtcd"
const TestTierPreferEtcdDeveloper = "Developer-TestTierPreferEtcdDeveloper"
const TestTierPreferPostgreSQL = "TestTierPreferPostgreSql"
const TestTierPreferPostgreSQLDeveloper = "Developer-TestTierPreferPostgreSqlDeveloper"
const TestTierPreferAwsRds = "TestTierPreferAwsRds"
const TestTierPreferGcpCloudSQL = "TestTierPreferGcpCloudSql"
const TestTierPreferAzrPostgres = "TestTierPreferAzrPostgres"

const TestComponentType05c05gID = "ut05c05g"
const TestComponentType1c1gID = "ut1c1g"
const TestComponentType1c2gID = "ut1c2g"
const TestComponentType2c4gID = "ut2c4g"
const TestComponentType4c8gID = "ut4c8g"
const TestComponentType8c16gID = "ut8c16g"
const TestComponentType16c32gID = "ut16c32g"

var (
	res1c1g = component.ResourceSpec{
		ID:            TestComponentType1c1gID,
		CPURequest:    "1",
		CPULimit:      "1",
		MemoryRequest: "1Gi",
		MemoryLimit:   "1Gi",
	}
	res1c2g = component.ResourceSpec{
		ID:            TestComponentType1c2gID,
		CPURequest:    "1",
		CPULimit:      "1",
		MemoryRequest: "2Gi",
		MemoryLimit:   "2Gi",
	}
	ct05c05g = config.ComponentType{
		ID:            TestComponentType05c05gID,
		CPURequest:    resource.MustParse("500m"),
		CPULimit:      resource.MustParse("500m"),
		MemoryRequest: resource.MustParse("512Mi"),
		MemoryLimit:   resource.MustParse("512Mi"),
	}
	ct1c1g = config.ComponentType{
		ID:            TestComponentType1c1gID,
		CPURequest:    resource.MustParse("1"),
		CPULimit:      resource.MustParse("1"),
		MemoryRequest: resource.MustParse("1Gi"),
		MemoryLimit:   resource.MustParse("1Gi"),
	}
	ct1c2g = config.ComponentType{
		ID:            TestComponentType1c2gID,
		CPURequest:    resource.MustParse("1"),
		CPULimit:      resource.MustParse("1"),
		MemoryRequest: resource.MustParse("2Gi"),
		MemoryLimit:   resource.MustParse("2Gi"),
	}
	ct2c4g = config.ComponentType{
		ID:            TestComponentType2c4gID,
		CPURequest:    resource.MustParse("2"),
		CPULimit:      resource.MustParse("2"),
		MemoryRequest: resource.MustParse("4Gi"),
		MemoryLimit:   resource.MustParse("4Gi"),
	}
	ct4c8g = config.ComponentType{
		ID:            TestComponentType4c8gID,
		CPURequest:    resource.MustParse("4"),
		CPULimit:      resource.MustParse("4"),
		MemoryRequest: resource.MustParse("8Gi"),
		MemoryLimit:   resource.MustParse("8Gi"),
	}
	ct8c16g = config.ComponentType{
		ID:            TestComponentType8c16gID,
		CPURequest:    resource.MustParse("8"),
		CPULimit:      resource.MustParse("8"),
		MemoryRequest: resource.MustParse("16Gi"),
		MemoryLimit:   resource.MustParse("16Gi"),
	}
	ct16c32g = config.ComponentType{
		ID:            TestComponentType16c32gID,
		CPURequest:    resource.MustParse("16"),
		CPULimit:      resource.MustParse("16"),
		MemoryRequest: resource.MustParse("32Gi"),
		MemoryLimit:   resource.MustParse("32Gi"),
	}

	testResource = resourcespec.FromValidTenantResourceSpec(&resourcespec.TenantResourceSpecV1Regular{
		ComputeResourceSpec:   res1c1g,
		ComputeReplica:        1,
		CompactorResourceSpec: res1c1g,
		CompactorReplica:      1,
		FrontendResourceSpec:  res1c1g,
		FrontendReplica:       1,
		MetaResourceSpec:      res1c1g,
		MetaReplica:           1,
		TenantResourceSpecV1Common: resourcespec.TenantResourceSpecV1Common{
			EtcdResourceSpec:       &res1c1g,
			EtcdReplica:            1,
			EtcdSizeGb:             1,
			ComputeFileCacheSizeGb: 1,
		},
	})
	component1X1c1g = ComponentResourceRequest{ComponentTypeID: TestComponentType1c1gID, Replica: 1}
	component1X1c2g = ComponentResourceRequest{ComponentTypeID: TestComponentType1c2gID, Replica: 1}
	component3X1c1g = ComponentResourceRequest{ComponentTypeID: TestComponentType1c1gID, Replica: 3}
	component5X1c1g = ComponentResourceRequest{ComponentTypeID: TestComponentType1c1gID, Replica: 5}
	component0X1c1g = ComponentResourceRequest{ComponentTypeID: TestComponentType1c1gID}
	component1X2c1g = ComponentResourceRequest{ComponentTypeID: "ut2c1g", Replica: 1}
)

func TestWillUpdate(t *testing.T) {
	base := testResource
	type testcase struct {
		req         update.TenantResourcesRequest
		expectEqual bool
	}
	testcases := map[string]testcase{
		"No Update": {
			req: update.TenantResourcesRequest{
				Compute: &update.ComponentResourcesRequest{
					Replicas:  1,
					Resources: res1c1g,
				},
			},
			expectEqual: true,
		},
		"Update Resource": {
			req: update.TenantResourcesRequest{
				Compute: &update.ComponentResourcesRequest{
					Replicas:  1,
					Resources: res1c2g,
				},
			},
			expectEqual: false,
		},
		"Update Replica": {
			req: update.TenantResourcesRequest{
				Compute: &update.ComponentResourcesRequest{
					Replicas:  2,
					Resources: res1c1g,
				},
			},
			expectEqual: false,
		},
		"Empty": {
			req:         update.TenantResourcesRequest{},
			expectEqual: true,
		},
	}
	for name, tc := range testcases {
		t.Run(name, func(t *testing.T) {
			validation := ParseResourceUpdateResult{
				Result: tc.req,
			}
			result := validation.WillUpdate(base)
			if tc.expectEqual {
				require.False(t, result)
			} else {
				require.True(t, result)
			}
		})
	}
}

func TestParseTenantResource(t *testing.T) {
	InitTestConfig()
	type testcase struct {
		tierID      string
		req         TenantResourceRequest
		expectPass  bool
		expectTrial bool
	}

	testcases := map[string]testcase{
		"Normal case 1X1c1g": {
			tierID: TestTierID,
			req: TenantResourceRequest{
				Components: TenantResourceRequestComponents{
					Compute:   &component1X1c1g,
					Compactor: &component1X1c1g,
					Frontend:  &component1X1c1g,
					Meta:      &component1X1c1g,
					Etcd:      &component1X1c1g,
				},
				ComputeFileCacheSizeGiB: 1,
				EtcdVolumeSizeGiB:       ptr.Ptr(1),
			},
			expectPass:  true,
			expectTrial: true,
		},
		"Normal case 1X1c2g": {
			tierID: TestTierID,
			req: TenantResourceRequest{
				Components: TenantResourceRequestComponents{
					Compute:   &component1X1c2g, // 1 * 1c2g
					Compactor: &component1X1c1g,
					Frontend:  &component1X1c1g,
					Meta:      &component1X1c1g,
					Etcd:      &component1X1c1g,
				},
				ComputeFileCacheSizeGiB: 1,
				EtcdVolumeSizeGiB:       ptr.Ptr(1),
			},
			expectPass:  true,
			expectTrial: false,
		},
		"Normal case 3X1c1g": {
			tierID: TestTierID,
			req: TenantResourceRequest{
				Components: TenantResourceRequestComponents{
					Compute:   &component3X1c1g, // 3 * 1c1g
					Compactor: &component1X1c1g,
					Frontend:  &component1X1c1g,
					Meta:      &component1X1c1g,
					Etcd:      &component1X1c1g,
				},
				ComputeFileCacheSizeGiB: 1,
				EtcdVolumeSizeGiB:       ptr.Ptr(1),
			},
			expectPass:  true,
			expectTrial: false,
		},
		"ComponentType not exist": {
			tierID: TestTierID,
			req: TenantResourceRequest{
				Components: TenantResourceRequestComponents{
					Compute:   &component1X2c1g, // Not exist 2c1g
					Compactor: &component1X1c1g,
					Frontend:  &component1X1c1g,
					Meta:      &component1X1c1g,
					Etcd:      &component1X1c1g,
				},
				ComputeFileCacheSizeGiB: 1,
				EtcdVolumeSizeGiB:       ptr.Ptr(1),
			},
			expectPass: false,
		},
		"ComponentType not allow": {
			tierID: TestTierID,
			req: TenantResourceRequest{
				Components: TenantResourceRequestComponents{
					Compute:   &component1X1c1g,
					Compactor: &component1X1c1g,
					Frontend:  &component1X1c1g,
					Meta:      &component1X2c1g, // Meta not allow 2c1g
					Etcd:      &component1X1c1g,
				},
				ComputeFileCacheSizeGiB: 1,
				EtcdVolumeSizeGiB:       ptr.Ptr(1),
			},
			expectPass: false,
		},
		"Exceed replica": {
			tierID: TestTierID,
			req: TenantResourceRequest{
				Components: TenantResourceRequestComponents{
					Compute:   &component5X1c1g, // Not allow 5 replica
					Compactor: &component1X1c1g,
					Frontend:  &component1X1c1g,
					Meta:      &component1X1c1g,
					Etcd:      &component1X1c1g,
				},
				ComputeFileCacheSizeGiB: 1,
				EtcdVolumeSizeGiB:       ptr.Ptr(1),
			},
			expectPass: false,
		},
		"Zero replica": {
			tierID: TestTierID,
			req: TenantResourceRequest{
				Components: TenantResourceRequestComponents{
					Compute:   &component0X1c1g, // Not allow 0 replica
					Compactor: &component1X1c1g,
					Frontend:  &component1X1c1g,
					Meta:      &component1X1c1g,
					Etcd:      &component1X1c1g,
				},
				ComputeFileCacheSizeGiB: 1,
				EtcdVolumeSizeGiB:       ptr.Ptr(1),
			},
			expectPass: false,
		},
		"Tier not exist": {
			tierID: "NotExistTier",
			req: TenantResourceRequest{
				Components: TenantResourceRequestComponents{
					Compute:   &component1X1c1g,
					Compactor: &component1X1c1g,
					Frontend:  &component1X1c1g,
					Meta:      &component1X1c1g,
					Etcd:      &component1X1c1g,
				},
				ComputeFileCacheSizeGiB: 1,
				EtcdVolumeSizeGiB:       ptr.Ptr(1),
			},
			expectPass: false,
		},
		"Exceed compute cache": {
			tierID: TestTierID,
			req: TenantResourceRequest{
				Components: TenantResourceRequestComponents{
					Compute:   &component1X1c1g,
					Compactor: &component1X1c1g,
					Frontend:  &component1X1c1g,
					Meta:      &component1X1c1g,
					Etcd:      &component1X1c1g,
				},
				ComputeFileCacheSizeGiB: 1000, // Not allow 1000G
				EtcdVolumeSizeGiB:       ptr.Ptr(1),
			},
			expectPass: false,
		},
		"Allow standalone mode": {
			tierID: TestTierID,
			req: TenantResourceRequest{
				Components: TenantResourceRequestComponents{
					Standalone: &component1X1c1g,
					Etcd:       &component1X1c1g,
				},
				ComputeFileCacheSizeGiB: 1,
				EtcdVolumeSizeGiB:       ptr.Ptr(1),
			},
			expectPass:  true,
			expectTrial: true,
		},
		"Allow standalone mode (but not trial)": {
			tierID: TestTierID,
			req: TenantResourceRequest{
				Components: TenantResourceRequestComponents{
					Standalone: &component3X1c1g,
					Etcd:       &component1X1c1g,
				},
				ComputeFileCacheSizeGiB: 1,
				EtcdVolumeSizeGiB:       ptr.Ptr(1),
			},
			expectPass:  true,
			expectTrial: false,
		},
		"Either standalone or cluster": {
			tierID: TestTierID,
			req: TenantResourceRequest{
				Components: TenantResourceRequestComponents{
					Etcd: &component1X1c1g,
				},
				ComputeFileCacheSizeGiB: 1,
				EtcdVolumeSizeGiB:       ptr.Ptr(1),
			},
			expectPass: false,
		},
		"Legacy no etcd size": {
			tierID: TestTierPreferEtcd,
			req: TenantResourceRequest{
				Components: TenantResourceRequestComponents{
					Standalone: &component1X1c1g,
					Etcd:       &component1X1c1g,
				},
			},
			expectPass: false,
		},
		"Meta Store Etcd": {
			tierID: TestTierPreferEtcd,
			req: TenantResourceRequest{
				Components: TenantResourceRequestComponents{
					Standalone: &component1X1c1g,
				},
				MetaStore: &TenantResourceRequestMetaStore{
					Type: metastore.Etcd,
					Etcd: &TenantResourceRequestMetaStoreEtcd{
						ComponentTypeID: TestComponentType1c1gID,
						Replica:         1,
						SizeGb:          1,
					},
				},
			},
			expectPass:  true,
			expectTrial: true,
		},
		"Meta Store PostgreSQL": {
			tierID: TestTierPreferEtcd,
			req: TenantResourceRequest{
				Components: TenantResourceRequestComponents{
					Standalone: &component1X1c1g,
				},
				MetaStore: &TenantResourceRequestMetaStore{
					Type: metastore.Postgresql,
					Postgresql: &TenantResourceRequestMetaStorePostgreSQL{
						ComponentTypeID: TestComponentType1c1gID,
						Replica:         1,
						SizeGb:          1,
					},
				},
			},
			expectPass:  true,
			expectTrial: true,
		},
		"Meta Store AwsRds": {
			tierID: TestTierPreferAwsRds,
			req: TenantResourceRequest{
				Components: TenantResourceRequestComponents{
					Standalone: &component1X1c1g,
				},
			},
			expectPass:  true,
			expectTrial: true,
		},
		"Meta Store GcpCloudSQL": {
			tierID: TestTierPreferGcpCloudSQL,
			req: TenantResourceRequest{
				Components: TenantResourceRequestComponents{
					Standalone: &component1X1c1g,
				},
			},
			expectPass:  true,
			expectTrial: true,
		},
		"Meta Store AzrPostgres": {
			tierID: TestTierPreferAzrPostgres,
			req: TenantResourceRequest{
				Components: TenantResourceRequestComponents{
					Standalone: &component1X1c1g,
				},
			},
			expectPass:  true,
			expectTrial: true,
		},
		"Meta Store dont support": {
			tierID: TestTierID,
			req: TenantResourceRequest{
				Components: TenantResourceRequestComponents{
					Standalone: &component1X1c1g,
				},
				MetaStore: &TenantResourceRequestMetaStore{
					Type: metastore.Postgresql,
					Postgresql: &TenantResourceRequestMetaStorePostgreSQL{
						ComponentTypeID: TestComponentType1c1gID,
						Replica:         1,
						SizeGb:          1,
					},
				},
			},
			expectPass: false,
		},
	}

	for name, tc := range testcases {
		t.Run(name, func(t *testing.T) {
			validation, err := ParseTenantResource(tc.tierID, tc.req)
			require.NoError(t, err)
			require.EqualValues(t, tc.expectPass, validation.IsValid)
			if tc.expectPass {
				require.EqualValues(t, tc.expectTrial, validation.AllowsTrial)
			}
		})
	}
}

func TestParseResourceUpdate(t *testing.T) {
	InitTestConfig()
	type testcase struct {
		tierID      string
		req         TenantResourceRequestComponents
		expectError bool
		expectPass  bool
		expectTrial bool
	}

	testcases := map[string]testcase{
		"Normal case 1X1c1g": {
			tierID: TestTierID,
			req: TenantResourceRequestComponents{
				Compute: &component1X1c1g,
			},
			expectPass:  true,
			expectTrial: true,
		},
		"Normal case 1X1c2g": {
			tierID: TestTierID,
			req: TenantResourceRequestComponents{
				Compute:   &component1X1c2g,
				Compactor: &component1X1c1g,
				Frontend:  &component1X1c1g,
				Meta:      &component1X1c1g,
			},
			expectPass:  true,
			expectTrial: false,
		},
		"Normal case 3X1c1g": {
			tierID: TestTierID,
			req: TenantResourceRequestComponents{
				Compute: &component3X1c1g,
			},
			expectPass:  true,
			expectTrial: false,
		},
		"ComponentType not exist": {
			tierID: TestTierID,
			req: TenantResourceRequestComponents{
				Compute: &component1X2c1g,
			},
			expectPass: false,
		},
		"ComponentType not allow": {
			tierID: TestTierID,
			req: TenantResourceRequestComponents{
				Meta: &component1X2c1g,
			},
			expectPass: false,
		},
		"Exceed replica": {
			tierID: TestTierID,
			req: TenantResourceRequestComponents{
				Compute: &component5X1c1g,
			},
			expectPass: false,
		},
		"Zero replica": {
			tierID: TestTierID,
			req: TenantResourceRequestComponents{
				Compute: &component0X1c1g,
			},
			expectPass: false,
		},
		"Tier not exist": {
			tierID: "NotExistTier",
			req: TenantResourceRequestComponents{
				Compute: &component0X1c1g,
			},
			expectError: true,
			expectTrial: true,
		},
		"Standalone update": {
			tierID: TestTierID,
			req: TenantResourceRequestComponents{
				Standalone: &component1X1c1g,
			},
			expectPass:  true,
			expectTrial: true,
		},
		"Both Standalone and cluster update": {
			tierID: TestTierID,
			req: TenantResourceRequestComponents{
				Compute:    &component1X1c1g,
				Standalone: &component1X1c1g,
			},
			expectPass: false,
		},
		"None of Standalone or cluster update": {
			tierID:     TestTierID,
			req:        TenantResourceRequestComponents{},
			expectPass: false,
		},
	}

	for name, tc := range testcases {
		t.Run(name, func(t *testing.T) {
			validation, err := ParseResourceUpdate(tc.tierID, tc.req)
			if tc.expectError {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			require.EqualValues(t, tc.expectPass, validation.IsValid)
			if tc.expectPass {
				require.EqualValues(t, tc.expectTrial, validation.AllowsTrial)
			}
		})
	}
}

func InitTestConfig() {
	def := config.ResourcesDefinitions
	_, ok := def.Tiers[TestTierID]
	if ok {
		return
	}
	computeList := []*config.AvailableComponentType{
		{ComponentType: &ct1c1g, MaximumReplica: 3, MaximumTrialReplica: 1},
		{ComponentType: &ct1c2g, MaximumReplica: 3},
	}
	otherList := []*config.AvailableComponentType{
		{ComponentType: &ct1c1g, MaximumReplica: 3, MaximumTrialReplica: 1},
	}
	metaList := []*config.AvailableComponentType{
		{ComponentType: &ct05c05g, MaximumReplica: 3, MaximumTrialReplica: 1},
		{ComponentType: &ct1c1g, MaximumReplica: 3, MaximumTrialReplica: 1},
		{ComponentType: &ct1c2g, MaximumReplica: 3},
		{ComponentType: &ct2c4g, MaximumReplica: 3},
		{ComponentType: &ct4c8g, MaximumReplica: 3},
		{ComponentType: &ct8c16g, MaximumReplica: 3},
		{ComponentType: &ct16c32g, MaximumReplica: 3},
	}
	metaStoreList := []*config.AvailableComponentType{
		{ComponentType: &ct05c05g, MaximumReplica: 3},
		{ComponentType: &ct1c1g, MaximumReplica: 3},
		{ComponentType: &ct1c2g, MaximumReplica: 3},
		{ComponentType: &ct2c4g, MaximumReplica: 3},
		{ComponentType: &ct4c8g, MaximumReplica: 3},
	}
	etcdMetaStore := &config.AvailableMetaStoreEtcd{
		TypeList:      metaStoreList,
		MaximumSizeGB: 10,
	}
	postgresqlMetaStore := &config.AvailableMetaStorePostgreSQL{
		TypeList:      metaStoreList,
		MaximumSizeGB: 10,
	}
	awsRdsMetaStore := &config.AvailableMetaStoreAwsRds{
		MachineTypeList: []*config.MachineType{
			{Name: "db.t4g.large", CPU: resource.MustParse("2")},
			{Name: "db.t4g.xlarge", CPU: resource.MustParse("4")},
		},
		MaximumSizeGB: 10,
	}
	gcpCloudSQLMetaStore := &config.AvailableMetaStoreGcpCloudSQL{
		MachineTypeList: []*config.MachineType{
			{Name: "db-custom-2-7680", CPU: resource.MustParse("2")},
			{Name: "db-custom-4-15360", CPU: resource.MustParse("4")},
		},
		MaximumSizeGB: 10,
	}
	azrPostgresMetaStore := &config.AvailableMetaStoreAzrPostgres{
		MachineTypeList: []*config.MachineType{
			{Name: "Standard_B2ms 2c8g", CPU: resource.MustParse("2")},
			{Name: "Standard_B4ms 4c16g", CPU: resource.MustParse("4")},
		},
		MaximumSizeGB: 10,
	}
	genTier := func(id string, modifiers ...func(*config.Tier)) config.Tier {
		tier := config.Tier{
			ID:                          id,
			MaximumComputeFileCacheGB:   10,
			ExpirationMechanism:         "date",
			DefaultRetentionPeriod:      1,
			DefaultValidityPeriod:       1,
			AvailableStandaloneTypeList: otherList,
			AvailableComputeTypeList:    computeList,
			AvailableCompactorTypeList:  otherList,
			AvailableFrontendTypeList:   otherList,
			AvailableMetaTypeList:       metaList,
			AvailableMetaStore: &config.AvailableMetaStore{
				Prefer: config.PreferMetaStoreEtcd,
				Etcd:   etcdMetaStore,
			},
		}
		for _, m := range modifiers {
			m(&tier)
		}
		return tier
	}
	def.Tiers[TestTierID] = genTier(TestTierID)
	def.Tiers[TestTierPreferEtcd] = genTier(TestTierPreferEtcd, func(tier *config.Tier) {
		tier.AvailableMetaStore = &config.AvailableMetaStore{
			Etcd:       etcdMetaStore,
			PostgreSQL: postgresqlMetaStore,
			Prefer:     config.PreferMetaStoreEtcd,
		}
	})
	def.Tiers[TestTierPreferEtcdDeveloper] = genTier(TestTierPreferEtcdDeveloper, func(tier *config.Tier) {
		tier.AvailableMetaStore = &config.AvailableMetaStore{
			Etcd:       etcdMetaStore,
			PostgreSQL: postgresqlMetaStore,
			Prefer:     config.PreferMetaStoreEtcd,
		}
	})
	def.Tiers[TestTierPreferPostgreSQL] = genTier(TestTierPreferPostgreSQL, func(tier *config.Tier) {
		tier.AvailableMetaStore = &config.AvailableMetaStore{
			Etcd:       etcdMetaStore,
			PostgreSQL: postgresqlMetaStore,
			Prefer:     config.PreferMetaStorePostgreSQL,
		}
	})
	def.Tiers[TestTierPreferPostgreSQLDeveloper] = genTier(TestTierPreferPostgreSQLDeveloper, func(tier *config.Tier) {
		tier.AvailableMetaStore = &config.AvailableMetaStore{
			Etcd:       etcdMetaStore,
			PostgreSQL: postgresqlMetaStore,
			Prefer:     config.PreferMetaStorePostgreSQL,
		}
	})
	def.Tiers[TestTierPreferAwsRds] = genTier(TestTierPreferAwsRds, func(tier *config.Tier) {
		tier.AvailableMetaStore = &config.AvailableMetaStore{
			Etcd:   etcdMetaStore,
			AwsRds: awsRdsMetaStore,
			Prefer: config.PreferMetaStoreAwsRds,
		}
	})
	def.Tiers[TestTierPreferGcpCloudSQL] = genTier(TestTierPreferGcpCloudSQL, func(tier *config.Tier) {
		tier.AvailableMetaStore = &config.AvailableMetaStore{
			Etcd:        etcdMetaStore,
			GcpCloudSQL: gcpCloudSQLMetaStore,
			Prefer:      config.PreferMetaStoreGcpCloudSQL,
		}
	})
	def.Tiers[TestTierPreferAzrPostgres] = genTier(TestTierPreferAzrPostgres, func(tier *config.Tier) {
		tier.AvailableMetaStore = &config.AvailableMetaStore{
			Etcd:        etcdMetaStore,
			AzrPostgres: azrPostgresMetaStore,
			Prefer:      config.PreferMetaStoreAzrPostgres,
		}
	})
	def.Tiers[string(tier.Invited)] = genTier(string(tier.Invited), func(tier *config.Tier) {
		tier.ServerlessCompactionOptions = config.ServerlessCompactionOptions{Allowed: true}
	})

	def.ComponentTypes[TestComponentType05c05gID] = ct05c05g
	def.ComponentTypes[TestComponentType1c1gID] = ct1c1g
	def.ComponentTypes[TestComponentType1c2gID] = ct1c2g
	def.ComponentTypes[TestComponentType2c4gID] = ct2c4g
	def.ComponentTypes[TestComponentType4c8gID] = ct4c8g
	def.ComponentTypes[TestComponentType8c16gID] = ct8c16g
	def.ComponentTypes[TestComponentType16c32gID] = ct16c32g
	// Set the compaction extension config to ensure it is not nil
	config.Conf.Mgmt.RisingWaveExtensions.Compaction.Compactor.CPULimit = "1000m"
}

func TestWillUpdateExtension_ServerlessCompactionAllowed(t *testing.T) {
	InitTestConfig()

	// Set the compaction extension config to ensure it is not nil
	config.Conf.Mgmt.RisingWaveExtensions.Compaction.Compactor.CPULimit = "1000m"

	// Setup: create a tenant with a tier that allows serverless compaction
	tenant := &model.Tenant{
		ID:        1,
		TierID:    string(tier.Invited),
		Resources: testResource,
	}

	// Extension request to enable serverless compaction
	newExtensionRequest := &spec.TenantExtensionsRequest{
		ServerlessCompaction: &spec.TenantExtensionServerlessCompactionRequest{
			MaximumCompactionConcurrency: 2,
		},
	}
	// Old extension (can be nil or a dummy)
	oldExtension := model.TenantExtension{}

	// Should succeed (no error) for allowed tier
	res := ParseResourceUpdateResult{}
	opts, err := res.WillUpdateExtension(tenant, oldExtension, newExtensionRequest)
	require.NoError(t, err)
	require.NotNil(t, opts)
	require.Equal(t, 2, opts.MaximumCPUSize)
}

func TestWillUpdateExtension_ServerlessCompactionNotAllowed(t *testing.T) {
	InitTestConfig()

	// Setup: create a tenant with a tier that does NOT allow serverless compaction
	tenant := &model.Tenant{
		ID:        2,
		TierID:    TestTierID, // This tier does not have AllowServerlessCompaction
		Resources: testResource,
	}

	// Extension request to enable serverless compaction
	newExtensionRequest := &spec.TenantExtensionsRequest{
		ServerlessCompaction: &spec.TenantExtensionServerlessCompactionRequest{
			MaximumCompactionConcurrency: 2,
		},
	}
	oldExtension := model.TenantExtension{}

	// Should return a permission error
	res := ParseResourceUpdateResult{}
	opts, err := res.WillUpdateExtension(tenant, oldExtension, newExtensionRequest)
	require.Error(t, err)
	require.Nil(t, opts)
	require.Contains(t, err.Error(), "serverless compaction operation is not allowed for tier")
}
