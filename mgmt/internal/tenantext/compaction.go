package tenantext

import (
	"context"

	"github.com/risingwavelabs/eris"
	"k8s.io/utils/ptr"

	agentprovider "github.com/risingwavelabs/risingwave-cloud/internal/agent/provider"
	"github.com/risingwavelabs/risingwave-cloud/internal/infra/storage"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/modeltx"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext/compaction"
)

// ServerlessCompactionParams defines the parameters for serverless compaction operations.
type ServerlessCompactionParams struct {
	// MaxConcurrency specifies the maximum number of concurrent compaction operations.
	MaxConcurrency *int `json:"max_concurrency,omitempty"`

	// Version specifies the version of the compaction proxy. The version will be
	// automatically set to the latest version if not provided.
	Version *string `json:"version,omitempty"`

	// LogLevel specifies the log level for the compaction operations.
	// Defaults to "info" if not provided.
	LogLevel *string `json:"log_level,omitempty"`
}

// ToTransforms converts the ServerlessCompactionParams to a slice of compaction.Transforms.
// It uses the provided factory to create the transforms, and if init is true, it adds the default transform.
func (params *ServerlessCompactionParams) ToTransforms(factory compaction.TransformFactory) []compaction.Transform {
	if params == nil {
		return nil
	}

	var transforms []compaction.Transform

	// Always initialise the config with the default transform if it's empty.
	transforms = append(transforms, factory.DefaultOnEmpty())

	// If MaxConcurrency is not set, use the default value from the tenant extension.
	if params.MaxConcurrency != nil {
		transforms = append(transforms, factory.SetMaximumConcurrency(*params.MaxConcurrency))
	}

	if params.Version != nil {
		// If Version is set, add the version transform.
		transforms = append(transforms, factory.SetVersion(*params.Version))
	}

	if params.LogLevel != nil {
		transforms = append(transforms, factory.SetLogLevel(*params.LogLevel))
	}

	return transforms
}

// ServerlessCompactionValidationService defines the interface for serverless compaction validation services.
type ServerlessCompactionValidationService = ValidationService[ServerlessCompactionParams]

var _ ServerlessCompactionValidationService = (*ServerlessCompactionValidationServiceImpl)(nil)

type ServerlessCompactionValidationServiceImpl struct {
	tenant    *model.Tenant
	tenantExt *model.TenantExtension
}

// Validate checks if the provided parameters for serverless compaction are valid.
func (s *ServerlessCompactionValidationServiceImpl) Validate(params ServerlessCompactionParams) error {
	tenantExt := s.tenantExt
	if tenantExt == nil {
		tenantExt = ptr.To(compaction.DefaultTenantExtension(s.tenant.ID, s.tenant.ImageTag))
	}

	return compaction.ValidateTransforms(*tenantExt, func(factory compaction.TransformFactory) ([]compaction.Transform, error) {
		return params.ToTransforms(factory), nil
	})
}

// NewServerlessCompactionValidationService creates a new instance of ServerlessCompactionValidationServiceImpl.
func NewServerlessCompactionValidationService(
	tenant *model.Tenant,
	tenantExt *model.TenantExtension,
) *ServerlessCompactionValidationServiceImpl {
	return &ServerlessCompactionValidationServiceImpl{
		tenant:    tenant,
		tenantExt: tenantExt,
	}
}

// serverlessCompactionExecutionService defines the interface for serverless compaction services.
type serverlessCompactionExecutionService = executionService[ServerlessCompactionParams]

var _ serverlessCompactionExecutionService = (*serverlessCompactionExecutionServiceImpl)(nil)

// serverlessCompactionExecutionServiceImpl is an implementation of the serverlessCompactionExecutionService interface.
type serverlessCompactionExecutionServiceImpl struct {
	m              *model.Model
	txP            modeltx.TxProvider
	caProvider     agentprovider.CloudAgentProviderInterface
	storageFactory storage.FactoryInterface

	tenantID  uint64
	tenant    *model.Tenant
	tenantExt model.TenantExtension
}

// Execute runs the serverless compaction workflow with the provided parameters.
func (s *serverlessCompactionExecutionServiceImpl) Execute(ctx context.Context, params *ServerlessCompactionParams, forceUpdate bool) error {
	// Create a new workflow execution for serverless compaction.
	exec, factory, err := compaction.NewWorkflowExec(
		s.tenant,
		&s.tenantExt,
		s.txP,
		s.caProvider,
		s.storageFactory,
		forceUpdate,
	)
	if err != nil {
		return eris.Wrap(err, "failed to create workflow execution for serverless compaction")
	}

	// Build the transforms based on the provided parameters if it's a create or update operation.
	var transforms []compaction.Transform
	if params != nil {
		// If the version is not set, retrieve the version based on the tenant's external version and image tag.
		if params.Version == nil {
			extVer, err := compaction.GetVersionByExtVersionAndTenantVersion(s.tenantExt.Version, s.tenant.ImageTag)
			if err != nil {
				return eris.Wrapf(err, "failed to get compaction version for tenant %d", s.tenantID)
			}
			params.Version = ptr.To(extVer)
		}
		transforms = append(transforms, params.ToTransforms(factory)...)
	}

	// Execute the workflow with the provided context, tenant ID, and transforms.
	return exec.Execute(ctx, transforms)
}

// ServerlessCompactionWorkflowService defines the interface for serverless compaction workflow services.
type ServerlessCompactionWorkflowService = ExtensionWorkflowService[ServerlessCompactionParams]

// NewServerlessCompactionWorkflowService creates a new instance of ExtensionWorkflowService with the provided tenant extension.
func NewServerlessCompactionWorkflowService(
	ctx context.Context,
	m *model.Model,
	txP modeltx.TxProvider,
	caProvider agentprovider.CloudAgentProviderInterface,
	storageFactory storage.FactoryInterface,
	tenantID uint64,
	force bool,
) (ServerlessCompactionWorkflowService, error) {
	return newExtensionWorkflowServiceImpl(
		ctx,
		m,
		tenantID,
		model.ExtensionsResourceTypeCompaction,
		func(tenant *model.Tenant, tenantExt model.TenantExtension) executionService[ServerlessCompactionParams] {
			return &serverlessCompactionExecutionServiceImpl{
				m:              m,
				txP:            txP,
				caProvider:     caProvider,
				storageFactory: storageFactory,
				tenantID:       tenantID,
				tenant:         tenant,
				tenantExt:      tenantExt,
			}
		},
		func(tenant *model.Tenant) (model.TenantExtension, error) {
			return compaction.DefaultTenantExtension(tenant.ID, tenant.ImageTag), nil
		},
		force)
}
