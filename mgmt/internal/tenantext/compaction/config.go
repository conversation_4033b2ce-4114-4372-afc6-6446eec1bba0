package compaction

import (
	"github.com/risingwavelabs/eris"
	"k8s.io/apimachinery/pkg/api/resource"

	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	v0 "github.com/risingwavelabs/risingwave-cloud/internal/tenantext/compaction/v0"
	v1 "github.com/risingwavelabs/risingwave-cloud/internal/tenantext/compaction/v1"
)

// Config is an interface that defines the configuration for the serverless compaction extension.
type Config interface {
	// GetMaxConcurrency returns the maximum amount of concurrency for the compaction process.
	GetMaxConcurrency() (uint32, error)

	// GetVersion returns the version of the serverless compaction extension.
	GetVersion() string
}

var _ Config = (*configV0Impl)(nil)

type configV0Impl v0.Config

func (c *configV0Impl) GetMaxConcurrency() (uint32, error) {
	if c == nil {
		return 0, nil
	}

	cpuQuantity, err := resource.ParseQuantity(c.Compactor.CPULimit)
	if err != nil {
		return 0, err
	}
	maxConcurrency := cpuQuantity.MilliValue() / 1000 * c.Scaler.MaxReplicas
	if maxConcurrency < 1 {
		return 0, nil // If the max concurrency is less than 1, return 0
	}
	return uint32(maxConcurrency), nil
}

func (c *configV0Impl) GetVersion() string {
	if c == nil {
		return "" // Return empty string if config is nil
	}

	return c.Image.Tag
}

var _ Config = (*configV1Impl)(nil)

type configV1Impl v1.Config

func (c *configV1Impl) GetMaxConcurrency() (uint32, error) {
	if c == nil {
		return 0, nil // Return 0 if config is nil
	}

	cpuQuantity, err := resource.ParseQuantity(c.NodeGroup.Resources.Limits.Cpu().String())
	if err != nil {
		return 0, err
	}
	maxConcurrency := cpuQuantity.MilliValue() / 1000 * int64(c.MaxReplicas)
	if maxConcurrency < 1 {
		return 0, nil // If the max concurrency is less than 1, return 0
	}
	return uint32(maxConcurrency), nil
}

func (c *configV1Impl) GetVersion() string {
	if c == nil {
		return "" // Return empty string if config is nil
	}

	return c.Version
}

// GetConfig retrieves the configuration for the serverless compaction extension based on the tenant extension version.
func GetConfig(tenantExt model.TenantExtension) (Config, error) {
	switch tenantExt.Version {
	case v0.Version:
		obj := v0.NewObject(tenantExt)
		cfg, err := obj.Config()
		if err != nil {
			return nil, err
		}
		return (*configV0Impl)(cfg), nil
	case v1.Version:
		obj := v1.NewObject(tenantExt)
		cfg, err := obj.Config()
		if err != nil {
			return nil, err
		}
		return (*configV1Impl)(cfg), nil
	}
	return nil, eris.New("version not supported")
}
