package compaction

import (
	"context"

	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext/internal"
)

// ExtObjectID is a type that represents the unique identifier for a serverless compaction object.
type ExtObjectID = internal.ExtObjectID

// Transform is an interface that defines a method to apply a transformation to a given configuration.
type Transform interface {
	Apply(any) error
}

// TransformFactory is an interface that defines a method to create a new Transform instance.
type TransformFactory interface {
	// Default returns a Transform instance with default settings.
	Default() Transform

	// DefaultOnEmpty returns a Transform instance with default settings if the provided configuration is nil.
	DefaultOnEmpty() Transform

	// SetLogLevel returns a Transform instance that sets the log level.
	SetLogLevel(logLevel string) Transform

	// SetVersion returns a Transform instance that sets the version.
	SetVersion(version string) Transform

	// SetMaximumConcurrency returns a Transform instance that sets the maximum concurrency.
	SetMaximumConcurrency(maximumConcurrency int) Transform
}

// WorkflowExec is an interface that defines the execution of a workflow with a specific configuration.
type WorkflowExec interface {
	// Execute runs the workflow with the provided configuration.
	Execute(ctx context.Context, transforms []Transform) error
}
