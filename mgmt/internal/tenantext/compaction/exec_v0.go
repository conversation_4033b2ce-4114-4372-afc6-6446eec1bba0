package compaction

import (
	"context"

	"github.com/risingwavelabs/eris"

	agentprovider "github.com/risingwavelabs/risingwave-cloud/internal/agent/provider"
	"github.com/risingwavelabs/risingwave-cloud/internal/infra/storage"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/modeltx"
	v0 "github.com/risingwavelabs/risingwave-cloud/internal/tenantext/compaction/v0"
)

var _ WorkflowExec = (*workflowExecV0)(nil)

type workflowExecV0 struct {
	inner *v0.WorkflowExec
}

// Execute implements the WorkflowExec interface for serverless compaction v0.
func (w *workflowExecV0) Execute(ctx context.Context, transforms []Transform) error {
	v0Transforms := make([]v0.Transform, len(transforms))
	for i, transform := range transforms {
		t, ok := transform.(V0Transform)
		if !ok {
			return eris.New("transform is not a valid V0Transform")
		}
		v0Transforms[i] = v0.Transform(t)
	}

	return w.inner.Execute(ctx, v0Transforms)
}

// NewV0TransformFactory creates a new instance of V0TransformFactory.
func NewV0TransformFactory() TransformFactory {
	return &V0TransformFactory{}
}

// NewV0WorkflowExec creates a new instance of WorkflowExec for serverless compaction v0.
func NewV0WorkflowExec(
	tenant *model.Tenant,
	tenantExt *model.TenantExtension,
	txP modeltx.TxProvider,
	caProvider agentprovider.CloudAgentProviderInterface,
	storageFactory storage.FactoryInterface,
	forceUpdate bool,
) WorkflowExec {
	return &workflowExecV0{inner: v0.NewWorkflowExec(
		tenant,
		v0.NewObject(*tenantExt),
		txP,
		caProvider,
		storageFactory,
		forceUpdate,
	)}
}
