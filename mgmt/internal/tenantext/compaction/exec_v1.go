package compaction

import (
	"context"

	"github.com/risingwavelabs/eris"

	agentprovider "github.com/risingwavelabs/risingwave-cloud/internal/agent/provider"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/modeltx"
	v1 "github.com/risingwavelabs/risingwave-cloud/internal/tenantext/compaction/v1"
)

var _ WorkflowExec = (*workflowExecV1)(nil)

type workflowExecV1 struct {
	inner *v1.WorkflowExec
}

// Execute implements the WorkflowExec interface for serverless compaction v1.
func (w *workflowExecV1) Execute(ctx context.Context, transforms []Transform) error {
	v1Transforms := make([]v1.Transform, len(transforms))
	for i, transform := range transforms {
		t, ok := transform.(V1Transform)
		if !ok {
			return eris.New("transform is not a valid V1Transform")
		}
		v1Transforms[i] = v1.Transform(t)
	}

	return w.inner.Execute(ctx, v1Transforms)
}

// NewV1TransformFactory creates a new instance of V1TransformFactory.
func NewV1TransformFactory() TransformFactory {
	return &V1TransformFactory{}
}

// NewV1WorkflowExec creates a new instance of WorkflowExec for serverless compaction v1.
func NewV1WorkflowExec(
	tenant *model.Tenant,
	tenantExt *model.TenantExtension,
	txP modeltx.TxProvider,
	caProvider agentprovider.CloudAgentProviderInterface,
	forceUpdate bool,
) WorkflowExec {
	return &workflowExecV1{inner: v1.NewWorkflowExec(
		tenant,
		v1.NewObject(*tenantExt),
		txP,
		caProvider,
		forceUpdate,
	)}
}
