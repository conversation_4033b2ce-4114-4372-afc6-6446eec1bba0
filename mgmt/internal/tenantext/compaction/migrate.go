package compaction

import (
	"context"

	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	v0 "github.com/risingwavelabs/risingwave-cloud/internal/tenantext/compaction/v0"
	v1 "github.com/risingwavelabs/risingwave-cloud/internal/tenantext/compaction/v1"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext/status"
	"github.com/risingwavelabs/risingwave-cloud/shared/ptr"
)

// IsV1Supported checks if the tenant version supports v1 of the compaction extension.
func IsV1Supported(tenantVersion string) bool {
	return isSemverGE(tenantVersion, "v2.4.0")
}

// DefaultTenantExtension returns the default tenant extension for serverless compaction.
func DefaultTenantExtension(tenantID uint64, tenantVersion string) model.TenantExtension {
	extVer := v1.Version
	if !IsV1Supported(tenantVersion) {
		extVer = v0.Version
	}

	return model.TenantExtension{
		Version:      extVer,
		ResourceType: model.ExtensionsResourceTypeCompaction,
		TenantID:     tenantID,
		Config:       nil,
		Status:       "",
	}
}

// IsMigratable checks if the tenant extension can be migrated to the latest version.
func IsMigratable(tenantVersion string, tenantExt model.TenantExtension) bool {
	// Check if the tenant extension is already at the latest version.
	if tenantExt.Version == v1.Version {
		return false
	}
	// If the tenant extension is at v0, we need to migrate it to v1 if supported.
	if !IsV1Supported(tenantVersion) {
		return false
	}
	// Migration is possible.
	return true
}

// MigrateTenantExtension performs the migration of a tenant extension for compaction.
func MigrateTenantExtension(tenantVersion string, tenantExt model.TenantExtension) (*model.TenantExtension, error) {
	cfgV0, err := v0.NewObject(tenantExt).Config()
	if err != nil {
		return nil, err
	}

	// Convert the v0 configuration to v1 format.
	var cfgV1 *v1.Config
	if cfgV0 != nil {
		cfgV1 = &v1.Config{}
		cfgV1.Default()

		// Migrate the configuration from v0 to v1.
		cfgV1.MinReplicas = int32(cfgV0.Scaler.MinReplicas)
		cfgV1.MaxReplicas = int32(cfgV0.Scaler.MaxReplicas)

		ngResources, err := cfgV0.Compactor.ToResourceRequirements()
		if err != nil {
			return nil, err
		}
		cfgV1.NodeGroup.Resources = ngResources

		cfgV1.Version, err = GetVersionByExtVersionAndTenantVersion(v1.Version, tenantVersion)
		if err != nil {
			return nil, err
		}
	}

	// Convert the tenant extension to v1 format.
	tenantExt.Version = v1.Version
	tenantExt.Config = nil
	obj := v1.NewObject(tenantExt)
	err = obj.SetConfig(cfgV1)
	if err != nil {
		return nil, err
	}

	return ptr.Ptr(obj.Record()), nil
}

// TryMigrateAndPersistTenantExtension attempts to migrate the tenant extension
// to the latest version and persist it in the database.
// It returns the original tenant extension if migration is not needed,
// or migrated tenant extension or an error if the migration fails.
func TryMigrateAndPersistTenantExtension(
	ctx context.Context,
	tenantVersion string,
	tenantExt model.TenantExtension,
	m *model.Model,
) (*model.TenantExtension, error) {
	if !IsMigratable(tenantVersion, tenantExt) {
		return &tenantExt, nil
	}

	// The migration can only be performed if the tenant extension is in an ineffective status.
	if !status.IsStatusIneffective(tenantExt.Status) {
		return &tenantExt, nil
	}

	migratedExt, err := MigrateTenantExtension(tenantVersion, tenantExt)
	if err != nil {
		return nil, err
	}

	// Persist the migrated tenant extension in the database.
	err = m.UpdateTenantExtensionByTenantIDAndResourceTypeIfStatusEquals(ctx, *migratedExt)
	if err != nil {
		return nil, err
	}

	return migratedExt, nil
}
