package compaction

import (
	v0 "github.com/risingwavelabs/risingwave-cloud/internal/tenantext/compaction/v0"
	v1 "github.com/risingwavelabs/risingwave-cloud/internal/tenantext/compaction/v1"

	agentprovider "github.com/risingwavelabs/risingwave-cloud/internal/agent/provider"
	"github.com/risingwavelabs/risingwave-cloud/internal/infra/storage"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/modeltx"

	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext/internal"
)

// NewWorkflowExec creates a new instance of WorkflowExec for serverless compaction v1.
func NewWorkflowExec(
	tenant *model.Tenant,
	tenantExt *model.TenantExtension,
	txP modeltx.TxProvider,
	caProvider agentprovider.CloudAgentProviderInterface,
	storageFactory storage.FactoryInterface,
	forceUpdate bool,
) (WorkflowExec, TransformFactory, error) {
	switch tenantExt.Version {
	case v1.Version:
		return NewV1WorkflowExec(tenant, tenantExt, txP, caProvider, forceUpdate), NewV1TransformFactory(), nil
	default:
		return NewV0WorkflowExec(tenant, tenantExt, txP, caProvider, storageFactory, forceUpdate), NewV0TransformFactory(), nil
	}
}

// NewTransformFactory creates a new instance of TransformFactory for serverless compaction.
func NewTransformFactory(
	tenantExt model.TenantExtension,
) TransformFactory {
	switch tenantExt.Version {
	case v1.Version:
		return NewV1TransformFactory()
	default:
		return NewV0TransformFactory()
	}
}

// ObjectProps is an alias for internal.ExtObjectProps, which represents the properties of an object in serverless compaction.
type ObjectProps = internal.ExtObjectProps

// NewObject creates a new instance of ExtObjectProps for serverless compaction based on the tenant extension version.
func NewObject(
	tenantExt model.TenantExtension,
) ObjectProps {
	switch tenantExt.Version {
	case v1.Version:
		return v1.NewObject(tenantExt)
	default:
		return v0.NewObject(tenantExt)
	}
}

// ValidateTransforms checks if the provided transform is valid for the given tenant extension version.
func ValidateTransforms(
	tenantExt model.TenantExtension,
	f func(TransformFactory) ([]Transform, error),
) error {
	obj := NewObject(tenantExt)
	factory := NewTransformFactory(tenantExt)

	transforms, err := f(factory)
	if err != nil {
		return err
	}
	for _, transform := range transforms {
		if err := transform.Apply(obj); err != nil {
			return err
		}
	}

	return obj.ValidateConfig()
}
