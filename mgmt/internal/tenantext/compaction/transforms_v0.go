package compaction

import (
	"github.com/risingwavelabs/eris"

	v0 "github.com/risingwavelabs/risingwave-cloud/internal/tenantext/compaction/v0"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext/compaction/v0/transform"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext/internal"
)

// V0Transform is a type that represents a transformation operation specifically for serverless compaction v0 configurations.
type V0Transform v0.Transform

// Apply applies the transformation to the provided configuration object.
func (t V0Transform) Apply(config any) error {
	obj, ok := config.(*v0.Object)
	if !ok {
		return eris.New("failed to convert config to v0.Object")
	}
	return t(obj)
}

// V0Default returns a default transformation for serverless compaction v0 configurations.
func V0Default() Transform {
	return V0Transform(transform.Default().ToTransform())
}

// V0DefaultOnEmpty returns a transformation that applies default settings if the provided configuration is nil.
func V0DefaultOnEmpty() Transform {
	return V0Transform(transform.DefaultOnEmpty().ToTransform())
}

// V0SetLogLevel sets the version for the serverless compaction v0 configuration.
func V0SetLogLevel(_ string) Transform {
	// No-op for v0, as it does not have a log level setting.
	return V0Transform(func(_ *internal.ExtObject[v0.Config, *v0.Config]) error {
		return nil
	})
}

// V0SetVersion sets the version for the serverless compaction v0 configuration.
func V0SetVersion(version string) Transform {
	return V0Transform(transform.SetVersion(version).ToTransform())
}

// V0SetMaximumConcurrency sets the maximum concurrency for the serverless compaction v0 configuration.
func V0SetMaximumConcurrency(maximumConcurrency int) Transform {
	return V0Transform(transform.SetMaximumConcurrency(maximumConcurrency).ToTransform())
}

// V0SetStatus sets the status of the serverless compaction v0 object.
func V0SetStatus(status string) Transform {
	return V0Transform(func(obj *v0.Object) error {
		if obj == nil {
			return eris.New("object cannot be nil")
		}
		obj.SetStatus(status)
		return nil
	})
}

var _ TransformFactory = (*V0TransformFactory)(nil)

// V0TransformFactory is a factory for creating transformations for serverless compaction v0 configurations.
type V0TransformFactory struct{}

func (f *V0TransformFactory) Default() Transform {
	return V0Default()
}

func (f *V0TransformFactory) DefaultOnEmpty() Transform {
	return V0DefaultOnEmpty()
}

func (f *V0TransformFactory) SetLogLevel(logLevel string) Transform {
	return V0SetLogLevel(logLevel)
}

func (f *V0TransformFactory) SetVersion(version string) Transform {
	return V0SetVersion(version)
}

func (f *V0TransformFactory) SetMaximumConcurrency(maximumConcurrency int) Transform {
	return V0SetMaximumConcurrency(maximumConcurrency)
}

func (f *V0TransformFactory) SetStatus(status string) Transform {
	return V0SetStatus(status)
}
