package compaction

import (
	"github.com/risingwavelabs/eris"

	v1 "github.com/risingwavelabs/risingwave-cloud/internal/tenantext/compaction/v1"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext/compaction/v1/transform"
)

// V1Transform is a type that represents a transformation operation specifically for serverless compaction v1 configurations.
type V1Transform v1.Transform

// Apply applies the transformation to the provided configuration object.
func (t V1Transform) Apply(config any) error {
	obj, ok := config.(*v1.Object)
	if !ok {
		return eris.New("failed to convert config to v1.Object")
	}
	return t(obj)
}

// V1Default returns a default transformation for serverless compaction v1 configurations.
func V1Default() Transform {
	return V1Transform(transform.Default().ToTransform())
}

// V1DefaultOnEmpty returns a transformation that applies default settings if the provided configuration is nil.
func V1DefaultOnEmpty() Transform {
	return V1Transform(transform.DefaultOnEmpty().ToTransform())
}

// V1SetLogLevel sets the version for the serverless compaction v1 configuration.
func V1SetLogLevel(logLevel string) Transform {
	return V1Transform(transform.SetLogLevel(logLevel).ToTransform())
}

// V1SetVersion sets the version for the serverless compaction v1 configuration.
func V1SetVersion(version string) Transform {
	return V1Transform(transform.SetVersion(version).ToTransform())
}

// V1SetMaximumConcurrency sets the maximum concurrency for the serverless compaction v1 configuration.
func V1SetMaximumConcurrency(maximumConcurrency int) Transform {
	return V1Transform(transform.SetMaximumConcurrency(maximumConcurrency).ToTransform())
}

// V1SetStatus sets the status of the serverless compaction v1 object.
func V1SetStatus(status string) Transform {
	return V1Transform(func(obj *v1.Object) error {
		if obj == nil {
			return eris.New("object cannot be nil")
		}
		obj.SetStatus(status)
		return nil
	})
}

var _ TransformFactory = (*V1TransformFactory)(nil)

// V1TransformFactory is a factory for creating transformations for serverless compaction v1 configurations.
type V1TransformFactory struct{}

func (f *V1TransformFactory) Default() Transform {
	return V1Default()
}

func (f *V1TransformFactory) DefaultOnEmpty() Transform {
	return V1DefaultOnEmpty()
}

func (f *V1TransformFactory) SetLogLevel(logLevel string) Transform {
	return V1SetLogLevel(logLevel)
}

func (f *V1TransformFactory) SetVersion(version string) Transform {
	return V1SetVersion(version)
}

func (f *V1TransformFactory) SetMaximumConcurrency(maximumConcurrency int) Transform {
	return V1SetMaximumConcurrency(maximumConcurrency)
}

func (f *V1TransformFactory) SetStatus(status string) Transform {
	return V1SetStatus(status)
}
