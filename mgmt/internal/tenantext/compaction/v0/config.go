package v0

import (
	"encoding/json"

	"github.com/risingwavelabs/risingwave-cloud/internal/config"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext/internal"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext/internal/transform"
)

// Version is the version of the serverless compaction extension.
const Version = ""

// Transform is a type that represents a transformation function for the serverless compaction extension configuration.
type Transform = internal.Transform[Config, *Config]

// ConfigTransform is a type that represents a transformation function for the serverless compaction extension configuration.
type ConfigTransform func(config *Config) (*Config, error)

// ToTransform converts a ConfigTransform to an internal.Transform that operates on Config objects.
func (f ConfigTransform) ToTransform() internal.Transform[Config, *Config] {
	return transform.SetConfigFunc(f)
}

// Config holds the configuration for the serverless compaction extension.
type Config struct {
	config.RisingWaveExtensionsCompactionWithRWConfig `json:",inline"`
}

// Default initializes the Config with default values.
func (c *Config) Default() {
	*c = Config{
		RisingWaveExtensionsCompactionWithRWConfig: *config.NewRisingWaveExtensionsCompactionWithRWConfig(),
	}
}

// MarshalJSON implements the json.Marshaler interface for Config.
func (c *Config) MarshalJSON() ([]byte, error) {
	type alias Config
	data, err := json.Marshal((*alias)(c))
	if err != nil {
		return nil, err
	}
	return data, nil
}

// UnmarshalJSON implements the json.Unmarshaler interface for Config.
func (c *Config) UnmarshalJSON(bytes []byte) error {
	type alias Config
	if err := json.Unmarshal(bytes, (*alias)(c)); err != nil {
		return err
	}
	return nil
}

// Validate validates the configuration properties of the serverless compaction extension.
func (c *Config) Validate() error {
	// FIXME: probably we should validate the config properties here.
	return nil
}
