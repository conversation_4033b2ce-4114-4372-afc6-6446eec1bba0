package v0

import (
	agentprovider "github.com/risingwavelabs/risingwave-cloud/internal/agent/provider"
	"github.com/risingwavelabs/risingwave-cloud/internal/infra/storage"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/extensions"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/modeltx"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext/internal"
)

// WorkflowExec is a type that represents the execution of a serverless compaction v0 workflow.
type WorkflowExec = internal.ExtWorkflowExec[Config, *Config]

// NewWorkflowExec creates a new instance of WorkflowExec for serverless compaction v0.
func NewWorkflowExec(
	tenant *model.Tenant,
	obj *Object,
	txP modeltx.TxProvider,
	caProvider agentprovider.CloudAgentProviderInterface,
	storageFactory storage.FactoryInterface,
	forceUpdate bool,
) *WorkflowExec {
	return internal.NewExtWorkflowExecWithActions[Config, *Config](
		tenant,
		obj,
		txP,
		newStatusBasedReconciler(extensions.NewServerlessCompaction(caProvider, storageFactory)),
		forceUpdate,
	)
}
