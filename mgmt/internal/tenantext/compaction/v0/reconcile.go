package v0

import (
	"context"

	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/risingwave-cloud/internal/config"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/extensions"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext/internal"
)

// Reconciler is an interface for reconciling serverless compaction v0 objects.
type Reconciler = internal.ExtReconciler[Config, *Config]

var _ internal.ReconcileActions[Config, *Config] = (*statusBasedReconciler)(nil)

// statusBasedReconciler is a type that implements the ReconcileActions interface for serverless compaction v0 objects.
type statusBasedReconciler struct {
	sc extensions.ServerlessCompaction
}

func (r *statusBasedReconciler) OnCreation(ctx context.Context, tenant *model.Tenant, ebi *Object) error {
	cfg, err := ebi.Config()
	if err != nil {
		return eris.Wrapf(err, "failed to get config for extension %s of tenant %s", ebi.ResourceType(), tenant.ResourceNamespace)
	}

	// Install or upgrade the Helm release for serverless compaction.
	err = r.sc.Enable(ctx, *tenant, *config.ToRisingWaveExtensionsCompaction(&cfg.RisingWaveExtensionsCompactionWithRWConfig))
	if err != nil {
		return eris.Wrapf(err, "failed to enable serverless compaction for tenant %s", tenant.ResourceNamespace)
	}
	// Wait until the Helm release is ready.
	err = r.sc.WaitForReady(ctx, *tenant)
	if err != nil {
		return eris.Wrapf(err, "failed to wait for serverless compaction Helm release to be ready for tenant %s", tenant.ResourceNamespace)
	}
	return nil
}

func (r *statusBasedReconciler) PostCreation(ctx context.Context, tenant *model.Tenant, _ *Object) error {
	// Post-enable the Helm release for serverless compaction. (cleanup)
	err := r.sc.PostEnable(ctx, *tenant)
	if err != nil {
		return eris.Wrapf(err, "failed to post-enable serverless compaction for tenant %s", tenant.ResourceNamespace)
	}
	return nil
}

func (r *statusBasedReconciler) OnUpdate(ctx context.Context, tenant *model.Tenant, ebi *Object) error {
	cfg, err := ebi.Config()
	if err != nil {
		return eris.Wrapf(err, "failed to get config for extension %s of tenant %s", ebi.ResourceType(), tenant.ResourceNamespace)
	}

	// Update the Helm release for serverless compaction.
	err = r.sc.Update(ctx, *tenant, *config.ToRisingWaveExtensionsCompaction(&cfg.RisingWaveExtensionsCompactionWithRWConfig))
	if err != nil {
		return eris.Wrapf(err, "failed to update serverless compaction for tenant %s", tenant.ResourceNamespace)
	}
	// Wait until the Helm release is ready.
	err = r.sc.WaitForReady(ctx, *tenant)
	if err != nil {
		return eris.Wrapf(err, "failed to wait for serverless compaction Helm release to be ready for tenant %s", tenant.ResourceNamespace)
	}
	return nil
}

func (r *statusBasedReconciler) PostUpdate(ctx context.Context, tenant *model.Tenant, _ *Object) error {
	err := r.sc.PostUpdate(ctx, *tenant)
	if err != nil {
		return eris.Wrapf(err, "failed to post-update serverless compaction for tenant %s", tenant.ResourceNamespace)
	}
	return nil
}

func (r *statusBasedReconciler) OnDeletion(ctx context.Context, tenant *model.Tenant, _ internal.ExtObjectID) error {
	err := r.sc.Disable(ctx, *tenant)
	if err != nil {
		return eris.Wrapf(err, "failed to disable serverless compaction for tenant %s", tenant.ResourceNamespace)
	}
	err = r.sc.WaitForDeleted(ctx, *tenant)
	if err != nil {
		return eris.Wrapf(err, "failed to wait for serverless compaction Helm release to be deleted for tenant %s", tenant.ResourceNamespace)
	}
	return nil
}

func (r *statusBasedReconciler) PostDeletion(ctx context.Context, tenant *model.Tenant, _ internal.ExtObjectID) error {
	// Post-delete the Helm release for serverless compaction. (cleanup)
	err := r.sc.PostDisable(ctx, *tenant)
	if err != nil {
		return eris.Wrapf(err, "failed to post-disable serverless compaction for tenant %s", tenant.ResourceNamespace)
	}
	return nil
}

func newStatusBasedReconciler(sc extensions.ServerlessCompaction) *statusBasedReconciler {
	return &statusBasedReconciler{
		sc: sc,
	}
}
