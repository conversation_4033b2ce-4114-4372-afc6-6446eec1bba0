package transform

import (
	"github.com/risingwavelabs/eris"
	"k8s.io/apimachinery/pkg/api/resource"

	v0 "github.com/risingwavelabs/risingwave-cloud/internal/tenantext/compaction/v0"
)

// Default returns a default configuration for serverless compaction v0.
func Default() v0.ConfigTransform {
	return func(config *v0.Config) (*v0.Config, error) {
		if config == nil {
			config = &v0.Config{}
		}
		config.Default()
		return config, nil
	}
}

// DefaultOnEmpty returns a default configuration for serverless compaction v0 if the provided config is nil.
func DefaultOnEmpty() v0.ConfigTransform {
	return func(config *v0.Config) (*v0.Config, error) {
		if config != nil {
			return config, nil
		}
		config = &v0.Config{}
		config.Default()
		return config, nil
	}
}

// SetVersion applies the version to the configuration.
func SetVersion(version string) v0.ConfigTransform {
	return func(config *v0.Config) (*v0.Config, error) {
		if config == nil {
			return nil, eris.New("config cannot be nil")
		}

		if version == "" {
			return nil, eris.New("version cannot be empty")
		}

		config.Image.Tag = version
		return config, nil
	}
}

// SetMaximumConcurrency applies the maximum concurrency setting to the configuration.
func SetMaximumConcurrency(maximumConcurrency int) v0.ConfigTransform {
	return func(config *v0.Config) (*v0.Config, error) {
		if maximumConcurrency < 1 {
			return nil, eris.New("maximum concurrency must be at least 1")
		}

		if config == nil {
			return nil, eris.New("config cannot be nil")
		}

		cpuLimit, err := resource.ParseQuantity(config.Compactor.CPULimit)
		if err != nil {
			return nil, eris.Wrapf(err, "failed to parse CPU limit: %s", config.Compactor.CPULimit)
		}

		cpuLimitMilli := cpuLimit.MilliValue()
		if cpuLimitMilli == 0 {
			cpuLimitMilli = 1000 // Default to 1 RWU if not set
		}

		// Calculate the maximum number of replicas based on the maximum concurrency and CPU limit.
		// The formula is: maxReplicas = ceil(maximumConcurrency * 1000 / cpuLimitMilli)
		maxReplicas := (int64(maximumConcurrency)*1000 + cpuLimitMilli - 1) / cpuLimitMilli

		// Set the max replicas in the config.
		config.Scaler.MaxReplicas = maxReplicas
		return config, nil
	}
}
