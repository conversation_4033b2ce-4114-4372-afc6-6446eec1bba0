package v1

import (
	"encoding/json"
	"slices"
	"strings"

	"github.com/BurntSushi/toml"
	"github.com/risingwavelabs/eris"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"

	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext/internal"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext/internal/transform"
)

// DefaultHelmChartVersion is the default version of the Helm chart used for serverless compaction.
// TODO: make this configurable in the config file.
const DefaultHelmChartVersion = "0.3.2"

// Version defines the version of the serverless compaction extension.
const Version = "v1"

// HelmChartConfig defines the configuration for the Helm chart used in the serverless compaction extension.
type HelmChartConfig struct {
	ChartVersion string `json:"chart_version,omitempty"` // Version of the Helm chart to be used for the serverless compaction extension.
}

// NodeGroupConfig defines the configuration for a node group in the serverless compaction extension.
type NodeGroupConfig struct {
	Resources corev1.ResourceRequirements `json:"resources,omitempty"` // Resource requirements for the node group, such as CPU and memory.
}

// Transform is a type that represents a transformation function for the serverless compaction extension configuration.
type Transform = internal.Transform[Config, *Config]

// ConfigTransform is a type that represents a transformation function for the serverless compaction extension configuration.
type ConfigTransform func(config *Config) (*Config, error)

// ToTransform converts a ConfigTransform to an internal.Transform that operates on Config objects.
func (f ConfigTransform) ToTransform() internal.Transform[Config, *Config] {
	return transform.SetConfigFunc(f)
}

// Config holds the configuration for the serverless compaction extension.
type Config struct {
	Helm        HelmChartConfig             `json:"helm,omitempty"`         // Configuration for the Helm chart used in serverless compaction.
	LogLevel    string                      `json:"log_level,omitempty"`    // Log level for the extension, e.g. "info", "debug", "error".
	Version     string                      `json:"version,omitempty"`      // Version of the serverless compaction extension, e.g. "v2.4.1".
	Resources   corev1.ResourceRequirements `json:"resources,omitempty"`    // Resource requirements for the serverless compaction extension, such as CPU and memory.
	NodeGroup   NodeGroupConfig             `json:"node_group,omitempty"`   // Configuration for the node group used in serverless compaction.
	MinReplicas int32                       `json:"min_replicas,omitempty"` // Minimum number of replicas for the node group.
	MaxReplicas int32                       `json:"max_replicas,omitempty"` // Maximum number of replicas for the node group.
	Overrides   []string                    `json:"overrides,omitempty"`    // List of overrides for the extension configuration in TOML.
}

// Default initializes the Config with default values.
func (c *Config) Default() {
	*c = Config{
		Helm: HelmChartConfig{
			ChartVersion: DefaultHelmChartVersion,
		},
		LogLevel: "info",
		// TODO: make the resources configurable in the config file.
		Resources: corev1.ResourceRequirements{
			Limits: corev1.ResourceList{
				corev1.ResourceCPU:    resource.MustParse("0.5"),
				corev1.ResourceMemory: resource.MustParse("64Mi"),
			},
			Requests: corev1.ResourceList{
				corev1.ResourceCPU:    resource.MustParse("0.1"),
				corev1.ResourceMemory: resource.MustParse("64Mi"),
			},
		},
		NodeGroup: NodeGroupConfig{
			// TODO: make the resources configurable in the config file.
			Resources: corev1.ResourceRequirements{
				Limits: corev1.ResourceList{
					corev1.ResourceCPU:    resource.MustParse("1"),
					corev1.ResourceMemory: resource.MustParse("4Gi"),
				},
				Requests: corev1.ResourceList{
					corev1.ResourceCPU:    resource.MustParse("0.5"),
					corev1.ResourceMemory: resource.MustParse("2Gi"),
				},
			},
		},
		MinReplicas: 0,
		MaxReplicas: 16,
	}
}

// MarshalJSON implements the json.Marshaler interface for Config.
func (c *Config) MarshalJSON() ([]byte, error) {
	type alias Config
	return json.Marshal((*alias)(c))
}

// UnmarshalJSON implements the json.Unmarshaler interface for Config.
func (c *Config) UnmarshalJSON(data []byte) error {
	type alias Config
	return json.Unmarshal(data, (*alias)(c))
}

// Validate checks the configuration for the serverless compaction extension.
func (c *Config) Validate() error {
	if c == nil {
		return eris.New("config cannot be nil")
	}

	if c.Helm.ChartVersion == "" {
		return eris.New("chart version is required")
	}

	if !slices.Contains([]string{"info", "debug", "error"}, strings.ToLower(c.LogLevel)) {
		return eris.New("log level must be one of: info, debug, error")
	}

	if c.Version == "" {
		return eris.New("version is required")
	}

	if c.MinReplicas < 0 || c.MaxReplicas < 0 {
		return eris.New("min replicas or max replicas are required")
	}

	if c.MinReplicas > c.MaxReplicas {
		return eris.New("min replicas cannot be greater than max replicas")
	}

	for i, override := range c.Overrides {
		if override == "" {
			return eris.Errorf("override at index %d cannot be empty", i)
		}
		var tmp any
		_, err := toml.Decode(override, &tmp)
		if err != nil {
			return eris.Wrapf(err, "override at index %d is not valid TOML", i)
		}
	}

	return nil
}
