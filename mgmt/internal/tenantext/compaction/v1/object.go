package v1

import (
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext/internal"
)

// Object is a type that represents a serverless compaction v1 object.
type Object = internal.ExtObject[Config, *Config]

// NewObject creates a new instance of Object with the given id and config.
func NewObject(ext model.TenantExtension) *Object {
	return internal.NewExtObject[Config, *Config](ext)
}

// ObjectID is a type that represents the unique identifier for a serverless compaction v1 object.
type ObjectID = internal.ExtObjectID
