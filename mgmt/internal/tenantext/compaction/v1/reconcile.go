package v1

import (
	"context"
	"encoding/json"
	"slices"
	"strings"

	"github.com/risingwavelabs/eris"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbrw "github.com/risingwavelabs/cloudagent/pbgen/common/rw"
	pbk8ssvc "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/conversion"
	agentk8s "github.com/risingwavelabs/cloudagent/pkg/providers/k8s"
	"github.com/risingwavelabs/risingwave-cloud/internal/agent"
	agentprovider "github.com/risingwavelabs/risingwave-cloud/internal/agent/provider"
	"github.com/risingwavelabs/risingwave-cloud/internal/config"
	k8sutils "github.com/risingwavelabs/risingwave-cloud/internal/k8s"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/podstrategy"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext/internal"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext/internal/utils"
	"github.com/risingwavelabs/risingwave-cloud/shared/logger"
)

const (
	TaskInstall   = "ext-compaction-install"
	TaskUpgrade   = "ext-compaction-upgrade"
	TaskUninstall = "ext-compaction-uninstall"
)

// ChartURLTemplate is a constant that represents the URL template for the Helm chart of serverless compaction v1 objects.
// TODO: use a variable for the chart URL template to allow for different versions or configurations.
const ChartURLTemplate = "https://risingwavelabs.github.io/risingwave-extensions/risingwave-extensions-%s.tgz"

// getChartURL is a function that returns the URL for the Helm chart of serverless compaction v1 objects.
func getChartURL(chartVersion string) string {
	// Replace the chartVersion in the template with the provided chartVersion.
	return strings.ReplaceAll(ChartURLTemplate, "%s", chartVersion)
}

// ServerlessNodeGroup is a constant that represents the serverless node group for serverless compaction v1 objects.
const ServerlessNodeGroup = "serverless"

// HelmReleaseName is a constant that represents the name of the Helm release for serverless compaction v1 objects.
const HelmReleaseName = "risingwave-serverless-compaction"

// HelmReleaseFullName is a constant that represents the full name of the Helm release for serverless compaction v1 objects.
const HelmReleaseFullName = "risingwave-serverless-compaction"

// getHelmReleaseValues is a function that returns the values for the Helm release of serverless compaction v1 objects.
func getHelmReleaseValues(tenant *model.Tenant, cfg *Config) map[string]any {
	tolerations := config.Conf.Mgmt.PodManagement.Compactor.GetNodeTolerationJSON()
	affinity := config.Conf.Mgmt.PodManagement.Compactor.GetAffinitiesJSONWithExtras(
		// Serverless compaction currently is hard coded to 1c4m machines.
		[]config.MatchExpression{
			{
				Key:      config.NodeAffinityCPUMemoryRatioKey,
				Operator: "In",
				Values:   []string{"1c4m"},
			},
		},
		[]config.MatchExpression{
			{
				Key:      podstrategy.PodLabelKeyResourceNamespace,
				Operator: "In",
				Values:   []string{tenant.ResourceNamespace},
			},
		},
	)

	serverlessCompaction := map[string]any{
		"enabled":       true, // Enable serverless compaction by default.
		"risingwaveRef": k8sutils.DefaultRisingWaveName,
		"logLevel":      strings.ToLower(cfg.LogLevel),
		"logFormat":     "json", // Use JSON log format by default.
		"deployment": map[string]any{
			"nodeGroupRef": ServerlessNodeGroup, // Reference the serverless node group.
		},
		"autoscaling": map[string]any{
			"taskBufferSize":      4, // Buffer size for tasks in the autoscaling configuration.
			"taskCapacityPerNode": 2, // Capacity per node for tasks in the autoscaling configuration.
			"minNodes":            cfg.MinReplicas,
			"maxNodes":            cfg.MaxReplicas,
		},
	}

	if len(cfg.Overrides) > 0 {
		serverlessCompaction["config"] = map[string]any{
			"overrides": cfg.Overrides, // Add overrides if provided.
		}
	}

	// TODO: review if we need to add more configurations to the Helm release values.
	return map[string]any{
		"fullnameOverride": HelmReleaseFullName,

		"commonLabels": map[string]any{
			"risingwave/name":      k8sutils.DefaultRisingWaveName,
			"risingwave/component": "extension-serverless-compaction",
		},

		"image": map[string]any{
			"tag": cfg.Version,
		},

		"statefulSet": map[string]any{
			"replicas":    1, // Use a single replica for serverless compaction proxy.
			"resources":   cfg.Resources,
			"affinity":    affinity,
			"tolerations": tolerations,
		},

		"serverlessCompaction": serverlessCompaction,
	}
}

// Reconciler is an interface for reconciling serverless compaction v1 objects.
type Reconciler = internal.ExtReconciler[Config, *Config]

var _ internal.ReconcileActions[Config, *Config] = (*statusBasedReconciler)(nil)

// statusBasedReconciler is a type that implements the ReconcileActions interface for serverless compaction v1 objects.
type statusBasedReconciler struct {
	caProvider agentprovider.CloudAgentProviderInterface
}

// TODO: allow for more customisation, e.g. config, affinity, tolerations, etc.
func (r *statusBasedReconciler) createRisingWaveNodeGroup(
	ctx context.Context,
	ca *agent.CloudAgent,
	tenant *model.Tenant,
	config *Config,
	rw *pbrw.RisingWaveSpec,
) error {
	compactorSpec := rw.GetComponents().GetCompactorSpec()
	defaultNgIdx := slices.IndexFunc(compactorSpec.GetNodeGroups(), func(spec *pbrw.NodeGroupSpec) bool {
		return spec.GetName() == agentk8s.DefaultNodeGroup
	})
	if defaultNgIdx < 0 {
		return eris.New("default node group not found in compactor spec")
	}
	ng := compactorSpec.GetNodeGroups()[defaultNgIdx]

	// Create a new node group spec based on the default node group spec.
	err := func(ng *pbrw.NodeGroupSpec) error {
		if ng.GetNodePodSpec() == nil {
			return eris.New("malformed node group spec: node pod spec is nil")
		}
		if ng.GetNodePodSpec().GetContainerSpec() == nil {
			return eris.New("malformed node group spec: container spec is nil")
		}

		// Set replicas.
		ng.Replicas = 0

		// Set the node group name.
		ng.Name = ServerlessNodeGroup

		// Set the resources.
		resources, err := conversion.ToResourceRequirementsProto(&config.NodeGroup.Resources)
		if err != nil {
			return eris.Wrap(err, "failed to convert resource requirements")
		}
		ng.NodePodSpec.ContainerSpec.Resources = resources

		// Reset the node group config.
		ng.NodeConfig = nil

		return nil
	}(ng)
	if err != nil {
		return eris.Wrapf(err, "failed to create node group spec for serverless compaction")
	}

	err = ca.CreateRisingWaveNodeGroup(
		ctx,
		k8sutils.DefaultRisingWaveName,
		tenant.ResourceNamespace,
		pbrw.ComponentType_COMPACTOR,
		ng,
	)
	if err != nil {
		return eris.Wrapf(err, "failed to create node group %s for tenant %s", ServerlessNodeGroup, tenant.ResourceNamespace)
	}
	return nil
}

func (r *statusBasedReconciler) createOrUpdateRisingWaveNodeGroup(
	ctx context.Context,
	ca *agent.CloudAgent,
	tenant *model.Tenant,
	config *Config,
) error {
	log := logger.FromCtx(ctx).With(
		zap.String("namespace", tenant.ResourceNamespace),
		zap.String("node_group", ServerlessNodeGroup),
	)

	// Principle: conflict means retrying, so we do not need to handle conflict here.
	rw, err := ca.GetRisingWave(ctx, k8sutils.DefaultRisingWaveName, tenant.ResourceNamespace)
	if err != nil {
		return eris.Wrapf(err, "failed to get risingwave for tenant %s", tenant.ResourceNamespace)
	}

	if rw.GetRisingwaveSpec().GetEnableStandaloneMode() {
		return eris.New("serverless compaction does not support standalone mode")
	}

	compactorSpec := rw.GetRisingwaveSpec().GetComponents().GetCompactorSpec()
	ngIdx := slices.IndexFunc(compactorSpec.GetNodeGroups(), func(spec *pbrw.NodeGroupSpec) bool {
		return spec.GetName() == ServerlessNodeGroup
	})
	if ngIdx < 0 {
		log.Info("Node group for serverless compaction not found, creating a new one")

		err = r.createRisingWaveNodeGroup(ctx, ca, tenant, config, rw.GetRisingwaveSpec())
		if err != nil {
			log.Error("Failed to create node group for serverless compaction", zap.Error(err))

			return eris.Wrapf(err, "failed to create node group %s for tenant %s", ServerlessNodeGroup, tenant.ResourceNamespace)
		}

		log.Info("Successfully created node group for serverless compaction")

		return nil
	}

	ng := compactorSpec.GetNodeGroups()[ngIdx]
	newNg := proto.Clone(ng).(*pbrw.NodeGroupSpec)
	err = func(ng *pbrw.NodeGroupSpec) error {
		if ng.GetNodePodSpec() == nil {
			return eris.New("malformed node group spec: node pod spec is nil")
		}
		if ng.GetNodePodSpec().GetContainerSpec() == nil {
			return eris.New("malformed node group spec: container spec is nil")
		}
		resources, err := conversion.ToResourceRequirementsProto(&config.NodeGroup.Resources)
		if err != nil {
			return eris.Wrap(err, "failed to convert resource requirements")
		}
		ng.NodePodSpec.ContainerSpec.Resources = resources
		return nil
	}(newNg)
	if err != nil {
		return eris.Wrapf(err, "failed to patch node group spec for serverless compaction")
	}

	if proto.Equal(newNg, rw) {
		log.Info("Node group spec for serverless compaction is already up-to-date, no changes needed")
		// No changes needed, return early.
		return nil
	}

	// Update the node group spec in the risingwave spec.
	log.Info("Updating node group spec for serverless compaction")
	err = ca.UpdateRisingWaveNodeGroup(
		ctx,
		k8sutils.DefaultRisingWaveName,
		tenant.ResourceNamespace,
		pbrw.ComponentType_COMPACTOR,
		newNg,
	)
	if err != nil {
		log.Error("Failed to update node group for serverless compaction", zap.Error(err))

		return eris.Wrapf(err, "failed to update node group %s for tenant %s", ServerlessNodeGroup, tenant.ResourceNamespace)
	}

	log.Info("Successfully updated node group for serverless compaction")
	return nil
}

func (r *statusBasedReconciler) createHelmRelease(
	ctx context.Context,
	ca *agent.CloudAgent,
	tenant *model.Tenant,
	config *Config,
) error {
	taskID := utils.GetTaskID(tenant.NsID, TaskInstall)
	values := getHelmReleaseValues(tenant, config)
	valuesJSON, err := json.Marshal(values)
	if err != nil {
		return eris.Wrapf(err, "failed to marshal values for helm release %s for tenant %s", HelmReleaseName, tenant.ResourceNamespace)
	}

	_, err = ca.InstallHelmReleaseAwait(ctx, &pbk8ssvc.InstallHelmReleaseRequest{
		ResourceMeta: &pbresource.Meta{
			Id: taskID,
		},
		ReleaseMeta: &pbresource.Meta{
			Id:        HelmReleaseName,
			Namespace: tenant.ResourceNamespace,
		},
		ChartUrl:   getChartURL(config.Helm.ChartVersion),
		ValuesJson: string(valuesJSON),
	}, tenant.ID)
	if err != nil {
		return eris.Wrapf(err, "failed to install helm release %s for tenant %s", HelmReleaseName, tenant.ResourceNamespace)
	}

	return nil
}

func (r *statusBasedReconciler) upgradeHelmRelease(
	ctx context.Context,
	ca *agent.CloudAgent,
	tenant *model.Tenant,
	config *Config,
) error {
	taskID := utils.GetTaskID(tenant.NsID, TaskUpgrade)
	values := getHelmReleaseValues(tenant, config)
	valuesJSON, err := json.Marshal(values)
	if err != nil {
		return eris.Wrapf(err, "failed to marshal values for helm release %s for tenant %s", HelmReleaseName, tenant.ResourceNamespace)
	}

	_, err = ca.UpgradeHelmReleaseAwait(ctx, &pbk8ssvc.UpgradeHelmReleaseRequest{
		ResourceMeta: &pbresource.Meta{
			Id: taskID,
		},
		ReleaseMeta: &pbresource.Meta{
			Id:        HelmReleaseName,
			Namespace: tenant.ResourceNamespace,
		},
		ChartUrl:   getChartURL(config.Helm.ChartVersion),
		ValuesJson: string(valuesJSON),
	}, tenant.ID)
	if err != nil {
		return eris.Wrapf(err, "failed to upgrade helm release %s for tenant %s", HelmReleaseName, tenant.ResourceNamespace)
	}

	return nil
}

func (r *statusBasedReconciler) waitUntilHelmReleaseReady(
	ctx context.Context,
	ca *agent.CloudAgent,
	tenant *model.Tenant,
) error {
	log := logger.FromCtx(ctx)

	log.Info("Waiting for helm release to be ready", zap.String("release", HelmReleaseName), zap.String("namespace", tenant.ResourceNamespace))

	err := k8sutils.WaitForStatefulSetUpdate(ctx, ca, tenant.ResourceNamespace, HelmReleaseFullName)
	if err != nil {
		return eris.Wrapf(err, "failed to wait for helm release %s to be ready for tenant %s", HelmReleaseName, tenant.ResourceNamespace)
	}

	log.Info("Helm release is ready", zap.String("release", HelmReleaseName), zap.String("namespace", tenant.ResourceNamespace))

	return nil
}

func (r *statusBasedReconciler) OnCreation(ctx context.Context, tenant *model.Tenant, ebi *Object) error {
	cfg, err := ebi.Config()
	if err != nil {
		return eris.Wrapf(err, "failed to get config for extension %s of tenant %s", ebi.ResourceType(), tenant.ResourceNamespace)
	}

	ca, err := r.caProvider.GetAgent(ctx, tenant.ClusterID)
	if err != nil {
		return eris.Wrapf(err, "failed to get cloud agent for tenant %s", tenant.ResourceNamespace)
	}
	defer ca.Close()

	// Create or update the RisingWave node group for serverless compaction.
	err = r.createOrUpdateRisingWaveNodeGroup(ctx, ca, tenant, cfg)
	if err != nil {
		return eris.Wrapf(err, "failed to create or update risingwave node group for serverless compaction for tenant %s", tenant.ResourceNamespace)
	}

	// Create the Helm release for serverless compaction.
	err = r.createHelmRelease(ctx, ca, tenant, cfg)
	if err != nil {
		return eris.Wrapf(err, "failed to create or update helm release for serverless compaction for tenant %s", tenant.ResourceNamespace)
	}

	// Wait until the Helm release is ready.
	return r.waitUntilHelmReleaseReady(ctx, ca, tenant)
}

func (r *statusBasedReconciler) PostCreation(ctx context.Context, tenant *model.Tenant, _ *Object) error {
	ca, err := r.caProvider.GetAgent(ctx, tenant.ClusterID)
	if err != nil {
		return eris.Wrapf(err, "failed to get cloud agent for tenant %s", tenant.ResourceNamespace)
	}
	defer ca.Close()

	taskID := utils.GetTaskID(tenant.NsID, TaskInstall)
	return ca.CleanupInstallHelmReleaseTaskAwait(ctx, taskID)
}

func (r *statusBasedReconciler) OnUpdate(ctx context.Context, tenant *model.Tenant, ebi *Object) error {
	cfg, err := ebi.Config()
	if err != nil {
		return eris.Wrapf(err, "failed to get config for extension %s of tenant %s", ebi.ResourceType(), tenant.ResourceNamespace)
	}

	ca, err := r.caProvider.GetAgent(ctx, tenant.ClusterID)
	if err != nil {
		return eris.Wrapf(err, "failed to get cloud agent for tenant %s", tenant.ResourceNamespace)
	}
	defer ca.Close()

	// Create or update the RisingWave node group for serverless compaction.
	err = r.createOrUpdateRisingWaveNodeGroup(ctx, ca, tenant, cfg)
	if err != nil {
		return eris.Wrapf(err, "failed to create or update risingwave node group for serverless compaction for tenant %s", tenant.ResourceNamespace)
	}

	// Upgrade the Helm release for serverless compaction.
	err = r.upgradeHelmRelease(ctx, ca, tenant, cfg)
	if err != nil {
		return eris.Wrapf(err, "failed to create or update helm release for serverless compaction for tenant %s", tenant.ResourceNamespace)
	}

	// Wait until the Helm release is ready.
	return r.waitUntilHelmReleaseReady(ctx, ca, tenant)
}

func (r *statusBasedReconciler) PostUpdate(ctx context.Context, tenant *model.Tenant, _ *Object) error {
	ca, err := r.caProvider.GetAgent(ctx, tenant.ClusterID)
	if err != nil {
		return eris.Wrapf(err, "failed to get cloud agent for tenant %s", tenant.ResourceNamespace)
	}
	defer ca.Close()

	taskID := utils.GetTaskID(tenant.NsID, TaskUpgrade)
	return ca.CleanupUpgradeHelmReleaseTaskAwait(ctx, taskID)
}

func (r *statusBasedReconciler) deleteRisingWaveNodeGroupIfExists(
	ctx context.Context,
	ca *agent.CloudAgent,
	tenant *model.Tenant,
) error {
	log := logger.FromCtx(ctx)

	log.Info("Deleting node group for serverless compaction if it exists")

	err := ca.DeleteRisingWaveNodeGroup(
		ctx,
		k8sutils.DefaultRisingWaveName,
		tenant.ResourceNamespace,
		pbrw.ComponentType_COMPACTOR,
		ServerlessNodeGroup,
	)
	if err != nil {
		if status.Code(err) == codes.NotFound {
			log.Info("Node group for serverless compaction does not exist, skipping deletion")

			return nil
		}

		log.Error("Failed to delete node group for serverless compaction", zap.Error(err))

		return eris.Wrapf(err, "failed to delete node group %s for tenant %s", ServerlessNodeGroup, tenant.ResourceNamespace)
	}

	log.Info("Successfully deleted node group for serverless compaction")

	return nil
}

func (r *statusBasedReconciler) uninstallHelmRelease(
	ctx context.Context,
	ca *agent.CloudAgent,
	tenant *model.Tenant,
) (err error) {
	log := logger.FromCtx(ctx).With(
		zap.String("release", HelmReleaseName),
		zap.String("namespace", tenant.ResourceNamespace),
	)

	taskID := utils.GetTaskID(tenant.NsID, TaskUninstall)

	log.Info("Uninstalling helm release for serverless compaction", zap.String("release", HelmReleaseName), zap.String("namespace", tenant.ResourceNamespace))

	_, err = ca.UninstallHelmReleaseAwait(ctx, &pbk8ssvc.UninstallHelmReleaseRequest{
		ResourceMeta: &pbresource.Meta{
			Id: taskID,
		},
		ReleaseMeta: &pbresource.Meta{
			Id:        HelmReleaseName,
			Namespace: tenant.ResourceNamespace,
		},
	}, tenant.ID)
	if err != nil {
		log.Error("Failed to uninstall helm release for serverless compaction", zap.Error(err))

		return eris.Wrapf(err, "failed to delete helm release %s for tenant %s", HelmReleaseName, tenant.ResourceNamespace)
	}

	log.Info("Successfully uninstalled helm release for serverless compaction")

	return nil
}

// OnDeletion implements the OnDeletion method of the ReconcileActions interface.
func (r *statusBasedReconciler) OnDeletion(ctx context.Context, tenant *model.Tenant, _ ObjectID) error {
	ca, err := r.caProvider.GetAgent(ctx, tenant.ClusterID)
	if err != nil {
		return eris.Wrapf(err, "failed to get cloud agent for tenant %s", tenant.ResourceNamespace)
	}
	defer ca.Close()

	// Uninstall the Helm release for serverless compaction.
	err = r.uninstallHelmRelease(ctx, ca, tenant)
	if err != nil {
		return eris.Wrapf(err, "failed to uninstall helm release for serverless compaction for tenant %s", tenant.ResourceNamespace)
	}

	// Delete the RisingWave node group for serverless compaction if it exists.
	err = r.deleteRisingWaveNodeGroupIfExists(ctx, ca, tenant)
	if err != nil {
		return eris.Wrapf(err, "failed to delete risingwave node group for serverless compaction for tenant %s", tenant.ResourceNamespace)
	}

	return nil
}

func (r *statusBasedReconciler) PostDeletion(ctx context.Context, tenant *model.Tenant, _ ObjectID) error {
	ca, err := r.caProvider.GetAgent(ctx, tenant.ClusterID)
	if err != nil {
		return eris.Wrapf(err, "failed to get cloud agent for tenant %s", tenant.ResourceNamespace)
	}
	defer ca.Close()

	taskID := utils.GetTaskID(tenant.NsID, TaskUninstall)
	return ca.CleanupUninstallHelmReleaseTaskAwait(ctx, taskID)
}

func newStatusBasedReconciler(caProvider agentprovider.CloudAgentProviderInterface) *statusBasedReconciler {
	return &statusBasedReconciler{
		caProvider: caProvider,
	}
}
