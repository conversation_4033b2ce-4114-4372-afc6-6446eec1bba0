package transform

import (
	"github.com/risingwavelabs/eris"

	v1 "github.com/risingwavelabs/risingwave-cloud/internal/tenantext/compaction/v1"
)

// Default returns a default configuration for serverless compaction v1.
func Default() v1.ConfigTransform {
	return func(config *v1.Config) (*v1.Config, error) {
		if config == nil {
			config = &v1.Config{}
		}
		config.Default()
		return config, nil
	}
}

// DefaultOnEmpty returns a default configuration for serverless compaction v1 if the provided config is nil.
func DefaultOnEmpty() v1.ConfigTransform {
	return func(config *v1.Config) (*v1.Config, error) {
		if config != nil {
			return config, nil
		}
		config = &v1.Config{}
		config.Default()
		return config, nil
	}
}

// SetLogLevel applies the log level to the configuration.
func SetLogLevel(logLevel string) v1.ConfigTransform {
	return func(config *v1.Config) (*v1.Config, error) {
		if config == nil {
			return nil, eris.New("config cannot be nil")
		}

		if logLevel == "" {
			return nil, eris.New("log level cannot be empty")
		}

		config.LogLevel = logLevel
		return config, nil
	}
}

// SetVersion applies the version to the configuration.
func SetVersion(version string) v1.ConfigTransform {
	return func(config *v1.Config) (*v1.Config, error) {
		if config == nil {
			return nil, eris.New("config cannot be nil")
		}

		if version == "" {
			return nil, eris.New("version cannot be empty")
		}

		config.Version = version
		return config, nil
	}
}

// SetMaximumConcurrency applies the maximum concurrency setting to the configuration.
func SetMaximumConcurrency(maximumConcurrency int) v1.ConfigTransform {
	return func(config *v1.Config) (*v1.Config, error) {
		if maximumConcurrency < 1 {
			return nil, eris.New("maximum concurrency must be at least 1")
		}

		if config == nil {
			return nil, eris.New("config cannot be nil")
		}

		cpuLimitMilli := config.NodeGroup.Resources.Limits.Cpu().MilliValue()
		if cpuLimitMilli == 0 {
			cpuLimitMilli = 1000 // Default to 1 RWU if not set
		}

		// Calculate the maximum number of replicas based on the maximum concurrency and CPU limit.
		// The formula is: maxReplicas = ceil(maximumConcurrency * 1000 / cpuLimitMilli)
		maxReplicas := (int64(maximumConcurrency)*1000 + cpuLimitMilli - 1) / cpuLimitMilli

		// Set the max replicas in the config.
		config.MaxReplicas = int32(maxReplicas)
		return config, nil
	}
}
