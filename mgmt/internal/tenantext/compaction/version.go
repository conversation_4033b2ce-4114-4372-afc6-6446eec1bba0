package compaction

import (
	"strings"

	"github.com/blang/semver/v4"
	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	v0 "github.com/risingwavelabs/risingwave-cloud/internal/tenantext/compaction/v0"

	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/version"
	v1 "github.com/risingwavelabs/risingwave-cloud/internal/tenantext/compaction/v1"
)

// isReleaseTestVersion checks if the given version string is a release test version.
func isReleaseTestVersion(s string) bool {
	s = strings.TrimPrefix(s, "v")
	v, err := semver.Parse(s)
	if err != nil {
		return false
	}
	if len(v.Pre) > 0 && v.Pre[0].String() == "rt" {
		return true
	}
	return false
}

// isReleaseCandidateVersion checks if the given version string is a release candidate version.
func isReleaseCandidateVersion(s string) bool {
	s = strings.TrimPrefix(s, "v")
	v, err := semver.Parse(s)
	if err != nil {
		return false
	}
	if len(v.Pre) > 0 && v.Pre[0].String() == "rc" {
		return true
	}
	return false
}

// isSemverGE checks if version a is greater than or equal to version b.
func isSemverGE(a, b string) bool {
	a, b = strings.TrimPrefix(a, "v"), strings.TrimPrefix(b, "v")
	v, err := semver.Parse(a)
	if err != nil {
		return false
	}
	vb := semver.MustParse(b)
	return v.GE(vb)
}

type versionMapEntry struct {
	eval func(tenantVersion string) (string, bool)
}

// FIXME: make this configurable in the future.
var v1VersionMap = []versionMapEntry{
	{
		eval: func(tenantVersion string) (string, bool) {
			if isReleaseTestVersion(tenantVersion) {
				return tenantVersion, true
			}
			return "", false
		},
	},
	{
		eval: func(tenantVersion string) (string, bool) {
			if isReleaseCandidateVersion(tenantVersion) {
				return tenantVersion, true
			}
			return "", false
		},
	},
	{
		eval: func(tenantVersion string) (string, bool) {
			if isSemverGE(tenantVersion, "v2.6.0") {
				return "", false
			}
			if isSemverGE(tenantVersion, "v2.5.0") {
				return "v2.5.0", true
			}
			if isSemverGE(tenantVersion, "v2.4.0") {
				return "v2.4.1", true
			}
			return "", false
		},
	},
}

// GetVersionByExtVersionAndTenantVersion maps the external version and tenant version to the compaction version.
func GetVersionByExtVersionAndTenantVersion(
	extVersion string,
	tenantVersion string,
) (string, error) {
	if extVersion == v1.Version {
		for _, entry := range v1VersionMap {
			v, ok := entry.eval(tenantVersion)
			if ok {
				return v, nil
			}
		}
		return "", eris.New("unsupported tenant version for compaction extension")
	}

	return version.MapRisingwaveCompactionVersionToExtensionVersion(tenantVersion)
}

// NewTenantExtension creates a new tenant extension for compaction based on the tenant version.
func NewTenantExtension(tenantID uint64, tenantVersion string, status string) model.TenantExtension {
	extVer := v1.Version
	if !isSemverGE(tenantVersion, "v2.4.0") {
		extVer = v0.Version
	}
	return model.TenantExtension{
		Version:      extVer,
		ResourceType: model.ExtensionsResourceTypeCompaction,
		TenantID:     tenantID,
		Config:       nil,
		Status:       status,
	}
}
