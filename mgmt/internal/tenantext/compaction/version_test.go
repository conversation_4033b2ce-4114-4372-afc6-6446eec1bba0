package compaction

import (
	"testing"

	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	v0 "github.com/risingwavelabs/risingwave-cloud/internal/tenantext/compaction/v0"
	v1 "github.com/risingwavelabs/risingwave-cloud/internal/tenantext/compaction/v1"
)

func TestNewTenantExtension(t *testing.T) {
	// Test cases for NewTenantExtension
	tests := []struct {
		name    string
		version string
		status  string
		want    model.TenantExtension
	}{
		{
			name:    "v2.3.0",
			version: "v2.3.0",
			status:  "a",
			want: model.TenantExtension{
				ResourceType: model.ExtensionsResourceTypeCompaction,
				Version:      v0.Version,
				Status:       "a",
			},
		},
		{
			name:    "v2.4.0",
			version: "v2.4.0",
			status:  "b",
			want: model.TenantExtension{
				ResourceType: model.ExtensionsResourceTypeCompaction,
				Version:      v1.Version,
				Status:       "b",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := NewTenantExtension(0, tt.version, tt.status)
			if got.ResourceType != tt.want.ResourceType || got.Version != tt.want.Version || got.Status != tt.want.Status {
				t.Errorf("NewTenantExtension() = %v, want %v", got, tt.want)
			}
		})
	}
}
