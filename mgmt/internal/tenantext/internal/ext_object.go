package internal

import (
	"encoding/json"

	"github.com/jinzhu/copier"
	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/shared/ptr"
)

// ExtObjectID is a struct that represents the unique identifier for an extension service.
type ExtObjectID struct {
	// TenantID is the ID of the tenant that owns the extension service.
	TenantID uint64 `json:"tenant_id"`

	// ResourceType is the type of the extension service, e.g. "Compaction", "ServerlessBackfill", etc.
	ResourceType string `json:"resource_type"`
}

// JSONSerialization is an interface that requires types to implement
// both MarshalJSON and UnmarshalJSON methods for JSON serialization.
type JSONSerialization[T any] interface {
	*T
	json.Marshaler
	json.Unmarshaler
}

// Validator is an interface that defines a method for validating the configuration.
type Validator interface {
	// Validate validates the configuration properties of the extension service.
	Validate() error
}

// Defaulter is an interface that defines a method for applying default values.
type Defaulter interface {
	// Default applies default values to the configuration properties of the extension service.
	Default()
}

// ExtObjectConfigProperties is an interface that defines the methods required for
// managing the configuration properties of an extension service.
type ExtObjectConfigProperties[T any] interface {
	JSONSerialization[T]

	Validator

	Defaulter
}

// ExtObjectProps is an interface that defines the common methods required for
// managing the properties of an extension service, including validation of configuration.
type ExtObjectProps interface {
	// ValidateConfig validates the configuration properties of the extension service.
	ValidateConfig() error

	// Record returns the underlying model.TenantExtension record.
	Record() model.TenantExtension
}

// ExtObject is a concrete implementation of the ExtObject interface.
type ExtObject[T any, TP ExtObjectConfigProperties[T]] struct {
	record model.TenantExtension
}

// NewExtObject creates a new instance of ExtObject with the provided record.
func NewExtObject[T any, TP ExtObjectConfigProperties[T]](record model.TenantExtension) *ExtObject[T, TP] {
	return &ExtObject[T, TP]{
		record: record,
	}
}

// Record returns the underlying model.TenantExtension record of the extension service.
func (ebi *ExtObject[T, TP]) Record() model.TenantExtension {
	return ebi.record
}

// ValidateConfig validates the configuration properties of the extension service.
func (ebi *ExtObject[T, TP]) ValidateConfig() error {
	cfg, err := ebi.Config()
	if err != nil {
		return eris.Wrap(err, "failed to get config")
	}
	return TP(cfg).Validate()
}

// TenantID returns the tenant ID associated with the extension service.
func (ebi *ExtObject[T, TP]) TenantID() uint64 {
	return ebi.record.TenantID
}

// ResourceType returns the resource type of the extension service, e.g. "Compaction", "ServerlessBackfill", etc.
func (ebi *ExtObject[T, TP]) ResourceType() string {
	return ebi.record.ResourceType
}

// ID returns the unique identifier for the extension service as an ExtObjectID.
func (ebi *ExtObject[T, TP]) ID() ExtObjectID {
	return ExtObjectID{
		TenantID:     ebi.record.TenantID,
		ResourceType: ebi.record.ResourceType,
	}
}

// ConfigRaw returns the raw configuration of the extension service as a pointer to a string.
func (ebi *ExtObject[T, TP]) ConfigRaw() *string {
	return ebi.record.Config
}

// Config returns the configuration of the extension service as a JSONSerialization type.
func (ebi *ExtObject[T, TP]) Config() (*T, error) {
	if ebi.record.Config == nil {
		return nil, nil
	}
	var config T
	if err := json.Unmarshal([]byte(*ebi.record.Config), &config); err != nil {
		return nil, eris.Wrap(err, "failed to unmarshal tenant extension config")
	}
	return &config, nil
}

// SetConfig sets the configuration of the extension service to the provided config.
func (ebi *ExtObject[T, TP]) SetConfig(config *T) error {
	if config == nil {
		ebi.record.Config = nil
		return nil
	}

	configBytes, err := json.Marshal(config)
	if err != nil {
		return eris.Wrap(err, "failed to marshal config")
	}
	ebi.record.Config = ptr.Ptr(string(configBytes))
	return nil
}

// SetStatus sets the status of the extension service to the provided status.
func (ebi *ExtObject[T, TP]) SetStatus(status string) {
	ebi.record.Status = status
}

// Status returns the status of the extension service as a string.
func (ebi *ExtObject[T, TP]) Status() string {
	return ebi.record.Status
}

// DeepCopy creates a deep copy of the ExtObject instance.
func (ebi *ExtObject[T, TP]) DeepCopy() *ExtObject[T, TP] {
	dst := &ExtObject[T, TP]{}
	if err := copier.Copy(dst, ebi); err != nil {
		panic(eris.Wrap(err, "failed to copy ExtObject"))
	}
	return dst
}

// Transform is a function type that takes an ExtObject and returns a modified
// ExtObject, allowing for transformations on the object. It can return an error
// if the transformation fails.
type Transform[T any, TP ExtObjectConfigProperties[T]] func(obj *ExtObject[T, TP]) error
