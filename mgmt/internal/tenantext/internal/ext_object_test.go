package internal_test

import (
	"encoding/json"
	"errors"
	"testing"

	"github.com/samber/lo"
	"github.com/stretchr/testify/require"

	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext/internal"
	"github.com/risingwavelabs/risingwave-cloud/shared/ptr"
)

type DummyConfig struct {
	Positive int  `json:"positive,omitempty"`
	Negative int  `json:"negative,omitempty"`
	True     bool `json:"true,omitempty"`
}

func (d *DummyConfig) MarshalJSON() ([]byte, error) {
	type alias DummyConfig // Use alias to avoid recursion in MarshalJSON
	return json.Marshal((*alias)(d))
}

func (d *DummyConfig) UnmarshalJSON(bytes []byte) error {
	type alias DummyConfig
	return json.Unmarshal(bytes, (*alias)(d))
}

func (d *DummyConfig) Validate() error {
	if d.Positive <= 0 {
		return errors.New("negative positive")
	}
	if d.Negative >= 0 {
		return errors.New("positive negative")
	}
	if !d.True {
		return errors.New("true is false")
	}
	return nil
}

func (d *DummyConfig) Default() {
	*d = DummyConfig{
		Positive: 1,
		Negative: -1,
		True:     true,
	}
}

type Object = internal.ExtObject[DummyConfig, *DummyConfig]

func newObject(t *testing.T, config *DummyConfig, status string) *Object {
	t.Helper()
	var configStr *string
	if config != nil {
		configStr = ptr.Ptr(string(lo.Must(json.Marshal(&config))))
	}
	tenantExt := &model.TenantExtension{
		Version:      "1.0.0",
		ResourceType: model.ExtensionsResourceTypeCompaction,
		TenantID:     1,
		Config:       configStr,
		Status:       status,
	}
	return internal.NewExtObject[DummyConfig, *DummyConfig](*tenantExt)
}

type Transform = internal.Transform[DummyConfig, *DummyConfig]

func TestExtObject_ReadWriteConfig(t *testing.T) {
	t.Parallel()

	obj := newObject(t, &DummyConfig{
		Positive: 10,
		Negative: -5,
		True:     true,
	}, model.ExtensionStatusRunning)

	// Read the config
	config, err := obj.Config()
	require.NoError(t, err, "failed to get config")

	require.Equal(t, 10, config.Positive, "expected positive to be 10")
	require.Equal(t, -5, config.Negative, "expected negative to be -5")
	require.True(t, config.True, "expected true to be true")

	// Update the config
	err = obj.SetConfig(&DummyConfig{
		Positive: 20,
		Negative: -10,
		True:     false,
	})
	require.NoError(t, err, "failed to set config")

	updatedConfig, err := obj.Config()
	require.NoError(t, err, "failed to get updated config")
	require.Equal(t, 20, updatedConfig.Positive, "expected updated positive to be 20")
	require.Equal(t, -10, updatedConfig.Negative, "expected updated negative to be -10")
	require.False(t, updatedConfig.True, "expected updated true to be false")
}

func TestExtObject_ValidateConfig(t *testing.T) {
	testcases := []struct {
		name    string
		config  DummyConfig
		wantErr bool
	}{
		{
			name: "valid config",
			config: DummyConfig{
				Positive: 10,
				Negative: -5,
				True:     true,
			},
			wantErr: false,
		},
		{
			name: "invalid positive",
			config: DummyConfig{
				Positive: -1,
				Negative: 10,
				True:     false,
			},
			wantErr: true,
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()

			obj := newObject(t, &tc.config, model.ExtensionStatusRunning)

			err := obj.ValidateConfig()
			if tc.wantErr {
				require.Error(t, err, "expected validation to fail")
			} else {
				require.NoError(t, err, "expected validation to pass")
			}
		})
	}
}

func TestExtObject_Transforms(t *testing.T) {
	config := DummyConfig{
		Positive: 10,
		Negative: -5,
		True:     true,
	}

	testcases := map[string]struct {
		transforms []Transform
		expected   *Object
	}{
		"set config": {
			transforms: []Transform{
				func(obj *internal.ExtObject[DummyConfig, *DummyConfig]) error {
					return obj.SetConfig(&DummyConfig{
						Positive: 10,
						Negative: -5,
						True:     false,
					})
				},
			},
			expected: newObject(t, &DummyConfig{
				Positive: 10,
				Negative: -5,
				True:     false,
			}, model.ExtensionStatusRunning),
		},
		"default config": {
			transforms: []Transform{
				func(obj *internal.ExtObject[DummyConfig, *DummyConfig]) error {
					var cfg DummyConfig
					cfg.Default()
					return obj.SetConfig(&cfg)
				},
			},
			expected: newObject(t, &DummyConfig{
				Positive: 1,
				Negative: -1,
				True:     true,
			}, model.ExtensionStatusRunning),
		},
		"set config func": {
			transforms: []Transform{
				func(obj *internal.ExtObject[DummyConfig, *DummyConfig]) error {
					cfg, err := obj.Config()
					if err != nil {
						return err
					}
					cfg.Positive = 100
					cfg.Negative = -50
					cfg.True = true
					return obj.SetConfig(cfg)
				},
			},
			expected: newObject(t, &DummyConfig{
				Positive: 100,
				Negative: -50,
				True:     true,
			}, model.ExtensionStatusRunning),
		},
		"set status": {
			transforms: []Transform{
				func(obj *internal.ExtObject[DummyConfig, *DummyConfig]) error {
					obj.SetStatus(model.ExtensionStatusEnabling)
					return nil
				},
			},
			expected: newObject(t, &config, model.ExtensionStatusEnabling),
		},
	}

	for name, tc := range testcases {
		t.Run(name, func(t *testing.T) {
			t.Parallel()

			obj := newObject(t, &config, model.ExtensionStatusRunning)

			for _, transform := range tc.transforms {
				err := transform(obj)
				require.NoError(t, err, "transform failed")
			}

			expected := tc.expected
			require.Equal(t, expected, obj)
		})
	}
}
