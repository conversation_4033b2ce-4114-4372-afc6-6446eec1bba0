package internal

import (
	"context"

	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext/status"
)

// ExtReconciler is an interface for reconciling cloud resources with a given type T.
//
// Process of reconciliation:
// 1. defer PostReconcile,
// 2. if committed, return nil,
// 3. if not committed, call Reconcile,
// 4. if <PERSON>con<PERSON><PERSON> returns an error, return the error,
// 5. if <PERSON><PERSON><PERSON><PERSON> succeeds, commit.
type ExtReconciler[T any, TP ExtObjectConfigProperties[T]] interface {
	// Reconcile reconciles the cloud resources with the provided type T.
	// It should handle both creation and deletion based on the config of the object.
	// It should never touch the internal state of the object.
	Reconcile(ctx context.Context, tenant *model.Tenant, ebi *ExtObject[T, TP]) error

	// PostReconcile is called after the reconciliation process.
	// It will be called after the Reconcile method and any DB transaction is committed.
	// It will always be called even if the reconciliation is re-entered.
	// It can be used to perform any additional actions after the reconciliation
	// is complete, such as performing clean-up tasks.
	PostReconcile(ctx context.Context, tenant *model.Tenant, ebi *ExtObject[T, TP]) error
}

// ReconcileActions is an interface that defines the actions to be taken during reconciliation.
type ReconcileActions[T any, TP ExtObjectConfigProperties[T]] interface {
	// OnCreation is called when a resource is created.
	OnCreation(ctx context.Context, tenant *model.Tenant, ebi *ExtObject[T, TP]) error

	// PostCreation is called after a resource is created.
	PostCreation(ctx context.Context, tenant *model.Tenant, ebi *ExtObject[T, TP]) error

	// OnUpdate is called when a resource is updated.
	OnUpdate(ctx context.Context, tenant *model.Tenant, ebi *ExtObject[T, TP]) error

	// PostUpdate is called after a resource is updated.
	PostUpdate(ctx context.Context, tenant *model.Tenant, ebi *ExtObject[T, TP]) error

	// OnDeletion is called when a resource is deleted.
	OnDeletion(ctx context.Context, tenant *model.Tenant, id ExtObjectID) error

	// PostDeletion is called after a resource is deleted.
	PostDeletion(ctx context.Context, tenant *model.Tenant, id ExtObjectID) error
}

// StatusDrivenExtReconciler is an interface for reconciling cloud resources with a given type T
// driven by the object status.
type StatusDrivenExtReconciler[T any, TP ExtObjectConfigProperties[T]] struct {
	ReconcileActions[T, TP]
}

// Reconcile reconciles the cloud resources with the provided type T based on the status of the object.
func (r *StatusDrivenExtReconciler[T, TP]) Reconcile(ctx context.Context, tenant *model.Tenant, ebi *ExtObject[T, TP]) (err error) {
	switch {
	case status.IsStatusForCreate(ebi.Status()):
		return r.OnCreation(ctx, tenant, ebi)
	case status.IsStatusForUpdate(ebi.Status()):
		return r.OnUpdate(ctx, tenant, ebi)
	case status.IsStatusForDelete(ebi.Status()):
		return r.OnDeletion(ctx, tenant, ebi.ID())
	}
	// Ignore other statuses
	return nil
}

// PostReconcile is called after the reconciliation process.
func (r *StatusDrivenExtReconciler[T, TP]) PostReconcile(ctx context.Context, tenant *model.Tenant, ebi *ExtObject[T, TP]) (err error) {
	switch {
	case status.IsStatusForCreate(ebi.Status()):
		return r.PostCreation(ctx, tenant, ebi)
	case status.IsStatusForUpdate(ebi.Status()):
		return r.PostUpdate(ctx, tenant, ebi)
	case status.IsStatusForDelete(ebi.Status()):
		return r.PostDeletion(ctx, tenant, ebi.ID())
	}
	// Ignore other statuses
	return nil
}

// NewStatusDrivenExtReconciler creates a new StatusDrivenExtReconciler with the provided reconcile actions.
func NewStatusDrivenExtReconciler[T any, TP ExtObjectConfigProperties[T]](
	reconcileActions ReconcileActions[T, TP],
) *StatusDrivenExtReconciler[T, TP] {
	r := &StatusDrivenExtReconciler[T, TP]{ReconcileActions: reconcileActions}
	var _ ExtReconciler[T, TP] = r // Ensure it implements the ExtReconciler interface
	return r
}
