package internal

import (
	"context"
	"reflect"

	"github.com/risingwavelabs/eris"
	"go.uber.org/zap"

	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/modeltx"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext/status"
	"github.com/risingwavelabs/risingwave-cloud/shared/logger"
)

// ExtWorkflowExec is an interface for executing transactional workflows for extension services.
type ExtWorkflowExec[T any, TP ExtObjectConfigProperties[T]] struct {
	txP          modeltx.TxProvider
	r            ExtReconciler[T, TP]
	tenant       *model.Tenant
	tenantExtObj *ExtObject[T, TP]
	forceUpdate  bool // forceUpdate indicates whether to force update the object even if it is already in a commit status.
}

// NewExtWorkflowExec creates a new instance of ExtWorkflowExec.
func NewExtWorkflowExec[T any, TP ExtObjectConfigProperties[T]](
	tenant *model.Tenant,
	tenantExtObj *ExtObject[T, TP],
	txP modeltx.TxProvider,
	r ExtReconciler[T, TP],
	forceUpdate bool,
) *ExtWorkflowExec[T, TP] {
	if tenant == nil {
		panic("tenant cannot be nil")
	}
	if tenantExtObj == nil {
		panic("tenantExtObj cannot be nil")
	}
	if tenant.ID != tenantExtObj.TenantID() {
		panic("tenant ID does not match tenantExtObj tenant ID")
	}
	return &ExtWorkflowExec[T, TP]{
		txP:          txP,
		r:            r,
		tenant:       tenant,
		tenantExtObj: tenantExtObj,
		forceUpdate:  forceUpdate,
	}
}

// NewExtWorkflowExecWithActions creates a new instance of ExtWorkflowExec with the provided actions.
func NewExtWorkflowExecWithActions[T any, TP ExtObjectConfigProperties[T]](
	tenant *model.Tenant,
	tenantExtObj *ExtObject[T, TP],
	txP modeltx.TxProvider,
	a ReconcileActions[T, TP],
	forceUpdate bool,
) *ExtWorkflowExec[T, TP] {
	return NewExtWorkflowExec(
		tenant,
		tenantExtObj,
		txP,
		NewStatusDrivenExtReconciler[T, TP](a),
		forceUpdate)
}

func (exec *ExtWorkflowExec[T, TP]) updateObject(
	ctx context.Context,
	curStatus string,
	obj *ExtObject[T, TP],
	updateConfig bool,
) error {
	tx, err := exec.txP.Begin(ctx)
	if err != nil {
		return eris.Wrapf(err, "failed to begin transaction for tenant %d and resource type %s", obj.TenantID(), obj.ResourceType())
	}

	m := model.NewModel(exec.txP.GetQuerier(tx))

	if exec.forceUpdate {
		err = m.UpdateTenantExtensionStatusByTenantIDAndResourceType(
			ctx,
			obj.TenantID(),
			obj.ResourceType(),
			obj.Status())
		if err != nil {
			_ = tx.Rollback(ctx)
			return eris.Wrapf(err, "failed to update tenant extension status for tenant %d and resource type %s", obj.TenantID(), obj.ResourceType())
		}
	} else {
		// CAS from current status to the new status.
		err = m.CompareAndUpdateTenantExtensionStatusByTenantIDAndResourceTypeAndStatuses(
			ctx,
			obj.TenantID(),
			obj.ResourceType(),
			obj.Status(),
			[]string{
				curStatus,
			})
		if err != nil {
			_ = tx.Rollback(ctx)
			return eris.Wrapf(err, "failed to update tenant extension status for tenant %d and resource type %s", obj.TenantID(), obj.ResourceType())
		}
	}

	// Update the config of the object.
	if updateConfig || exec.forceUpdate {
		err = m.UpdateTenantExtensionConfigByTenantIDAndResourceType(ctx, obj.TenantID(), obj.ResourceType(), obj.ConfigRaw())
		if err != nil {
			_ = tx.Rollback(ctx)
			return eris.Wrapf(err, "failed to update tenant extension config for tenant %d and resource type %s", obj.TenantID(), obj.ResourceType())
		}
	}

	// Commit the transaction.
	err = tx.Commit(ctx)
	if err != nil {
		return eris.Wrapf(err, "failed to commit transaction for tenant %d and resource type %s", obj.TenantID(), obj.ResourceType())
	}

	return nil
}

// Execute executes the transactional workflow for the given extension object.
// It takes a context, an ExtObjectID, and a slice of Transform functions to apply to the object.
// When executed, it applies the transforms in a transactional manner to the underlying database.
func (exec *ExtWorkflowExec[T, TP]) Execute(ctx context.Context, transforms []Transform[T, TP]) (err error) {
	id := exec.tenantExtObj.ID()

	log := logger.FromCtx(ctx).
		With(
			zap.Uint64("tenant_id", id.TenantID),
			zap.String("resource_type", id.ResourceType),
		)

	tenant, obj := exec.tenant, exec.tenantExtObj

	oldObj := obj.DeepCopy()
	oldCfg, err := oldObj.Config()
	if err != nil {
		return eris.Wrapf(err, "failed to get config for extension object for tenant %d and resource type %s", id.TenantID, id.ResourceType)
	}

	// Apply the transforms to the object.
	for _, transform := range transforms {
		err := transform(obj)
		if err != nil {
			return eris.Wrapf(err, "failed to apply transform to extension object for tenant %d and resource type %s", id.TenantID, id.ResourceType)
		}
	}

	objSnapshot := obj.DeepCopy()
	defer func() {
		// Run the post-reconciliation if no error occurred during the reconciliation process,
		// i.e.if the object was successfully reconciled and updated.
		if err == nil {
			err1 := exec.r.PostReconcile(ctx, tenant, objSnapshot)
			if err1 != nil {
				log.Error("failed to post reconcile extension object", zap.Error(err1))
				err = err1
			}
		}
	}()

	// If the object is already in a commit status, we skip the execution.
	// There's chances that the object's config will be changed by the transforms,
	// but if the status is already in a commit status, there's something wrong with the workflow,
	// and we should not proceed with the execution.
	if status.IsCommitStatus(obj.Status()) {
		log.Warn("extension object is already in a commit status, skipping execution")
		return nil
	}

	log.Info("reconciling extension object")

	// Validate the configuration of the object after applying the transforms.
	cfg, err := obj.Config()
	if err != nil {
		return eris.Wrapf(err, "failed to get config for extension object for tenant %d and resource type %s", id.TenantID, id.ResourceType)
	} else if cfg != nil {
		// If the object is not in a delete status, validate the config.
		if !status.IsStatusForDelete(obj.Status()) {
			if err := TP(cfg).Validate(); err != nil {
				return eris.Wrapf(err, "invalid config for extension object for tenant %d and resource type %s", id.TenantID, id.ResourceType)
			}
		}
	}

	// Run the reconciler to reconcile the object with the cloud resources.
	err = exec.r.Reconcile(ctx, tenant, obj)
	if err != nil {
		return eris.Wrapf(err, "failed to reconcile extension object for tenant %d and resource type %s", id.TenantID, id.ResourceType)
	}

	// Update the status of the object based on the current status.
	obj.SetStatus(status.GetCommitStatus(obj.Status()))

	// Commit the changes to the database.
	err = exec.updateObject(ctx, oldObj.Status(), obj, !reflect.DeepEqual(oldCfg, cfg))
	if err != nil {
		return eris.Wrapf(err, "failed to update extension object for tenant %d and resource type %s", id.TenantID, id.ResourceType)
	}

	return nil
}
