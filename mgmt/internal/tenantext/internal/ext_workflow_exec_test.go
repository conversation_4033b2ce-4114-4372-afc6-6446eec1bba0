package internal_test

import (
	"context"
	"errors"
	"testing"

	"github.com/jackc/pgx/v5/pgconn"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/modeltx"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/querier"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext/internal"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext/status"
	"github.com/risingwavelabs/risingwave-cloud/shared/ptr"
)

type dummyReconciler struct {
	cnt     int
	postCnt int
	success bool
}

func (d *dummyReconciler) Reconcile(_ context.Context, _ *model.Tenant, _ *internal.ExtObject[DummyConfig, *DummyConfig]) error {
	if d == nil {
		panic("unexpected call to nil dummyReconciler")
	}

	d.cnt++

	if !d.success {
		return errors.New("dummyR<PERSON>onciler failed")
	}
	return nil
}

func (d *dummyReconciler) PostReconcile(_ context.Context, _ *model.Tenant, _ *internal.ExtObject[DummyConfig, *DummyConfig]) error {
	if d != nil {
		d.postCnt++
	}
	return nil
}

func newExtWorkflowExecForTest(t *testing.T,
	config *DummyConfig,
	status string,
	forceUpdate bool,
	txP modeltx.TxProvider,
	r *dummyReconciler,
) *internal.ExtWorkflowExec[DummyConfig, *DummyConfig] {
	t.Helper()

	tenant := &model.Tenant{
		ID: 1,
	}
	tenantExtObj := newObject(t, config, status)

	return internal.NewExtWorkflowExec[DummyConfig, *DummyConfig](
		tenant,
		tenantExtObj,
		txP,
		r,
		forceUpdate,
	)
}

func TestExeWorkflowExec_Execute(t *testing.T) {
	testcases := []struct {
		name        string
		config      *DummyConfig
		status      string
		forceUpdate bool
		tx          func(txP *modeltx.MockTxProvider)
		reconciler  *dummyReconciler
		transforms  []Transform
		expectError bool
	}{
		{
			name:        "noop-empty-status",
			status:      "",
			forceUpdate: false,
			transforms:  []Transform{},
			expectError: false,
		},
		{
			name:        "noop-running-status",
			status:      model.ExtensionStatusRunning,
			expectError: false,
		},
		{
			name:        "noop-disabled-status",
			status:      model.ExtensionStatusDisabled,
			expectError: false,
		},
		{
			name:        "noop-failed-status",
			status:      model.ExtensionStatusFailed,
			expectError: false,
		},
		{
			name:   "post-reconcile-running-status",
			status: model.ExtensionStatusRunning,
			reconciler: &dummyReconciler{
				success: false,
			},
			expectError: false,
		},
		{
			name:   "enabling-with-valid-config",
			config: &DummyConfig{Positive: 10, Negative: -5, True: true},
			status: model.ExtensionStatusEnabling,
			tx: func(txP *modeltx.MockTxProvider) {
				txP.Transaction.EXPECT().Commit(gomock.Any()).Times(1)
				txP.Querier.EXPECT().CompareAndUpdateTenantExtensionStatusByTenantIdAndResourceTypeAndStatuses(
					gomock.Any(),
					querier.CompareAndUpdateTenantExtensionStatusByTenantIdAndResourceTypeAndStatusesParams{
						TenantID:         1,
						ResourceType:     model.ExtensionsResourceTypeCompaction,
						Status:           model.ExtensionStatusRunning,
						ExpectedStatuses: []string{model.ExtensionStatusEnabling},
					}).Return(pgconn.NewCommandTag("UPDATE 1"), nil)
			},
			reconciler: &dummyReconciler{
				success: true,
			},
			expectError: false,
		},
		{
			name:   "updating-with-valid-config",
			config: &DummyConfig{Positive: 10, Negative: -5, True: true},
			status: model.ExtensionStatusUpdating,
			tx: func(txP *modeltx.MockTxProvider) {
				txP.Transaction.EXPECT().Commit(gomock.Any()).Times(1)
				txP.Querier.EXPECT().CompareAndUpdateTenantExtensionStatusByTenantIdAndResourceTypeAndStatuses(
					gomock.Any(),
					querier.CompareAndUpdateTenantExtensionStatusByTenantIdAndResourceTypeAndStatusesParams{
						TenantID:         1,
						ResourceType:     model.ExtensionsResourceTypeCompaction,
						Status:           model.ExtensionStatusRunning,
						ExpectedStatuses: []string{model.ExtensionStatusUpdating},
					}).Return(pgconn.NewCommandTag("UPDATE 1"), nil)
			},
			reconciler: &dummyReconciler{
				success: true,
			},
			expectError: false,
		},
		{
			name:   "upgrading-with-valid-config",
			config: &DummyConfig{Positive: 10, Negative: -5, True: true},
			tx: func(txP *modeltx.MockTxProvider) {
				txP.Transaction.EXPECT().Commit(gomock.Any()).Times(1)
				txP.Querier.EXPECT().CompareAndUpdateTenantExtensionStatusByTenantIdAndResourceTypeAndStatuses(
					gomock.Any(),
					querier.CompareAndUpdateTenantExtensionStatusByTenantIdAndResourceTypeAndStatusesParams{
						TenantID:         1,
						ResourceType:     model.ExtensionsResourceTypeCompaction,
						Status:           model.ExtensionStatusRunning,
						ExpectedStatuses: []string{model.ExtensionStatusUpgrading},
					}).Return(pgconn.NewCommandTag("UPDATE 1"), nil)
			},
			status: model.ExtensionStatusUpgrading,
			reconciler: &dummyReconciler{
				success: true,
			},
			expectError: false,
		},
		{
			name:   "disabling-with-valid-config",
			config: &DummyConfig{Positive: 10, Negative: -5, True: true},
			status: model.ExtensionStatusDisabling,
			tx: func(txP *modeltx.MockTxProvider) {
				txP.Transaction.EXPECT().Commit(gomock.Any()).Times(1)
				txP.Querier.EXPECT().CompareAndUpdateTenantExtensionStatusByTenantIdAndResourceTypeAndStatuses(
					gomock.Any(),
					querier.CompareAndUpdateTenantExtensionStatusByTenantIdAndResourceTypeAndStatusesParams{
						TenantID:         1,
						ResourceType:     model.ExtensionsResourceTypeCompaction,
						Status:           model.ExtensionStatusDisabled,
						ExpectedStatuses: []string{model.ExtensionStatusDisabling},
					}).Return(pgconn.NewCommandTag("UPDATE 1"), nil)
			},
			reconciler: &dummyReconciler{
				success: true,
			},
			expectError: false,
		},
		{
			name:   "disabling-with-invalid-config",
			config: &DummyConfig{Positive: 10, Negative: -5, True: false},
			status: model.ExtensionStatusDisabling,
			tx: func(txP *modeltx.MockTxProvider) {
				txP.Transaction.EXPECT().Commit(gomock.Any()).Times(1)
				txP.Querier.EXPECT().CompareAndUpdateTenantExtensionStatusByTenantIdAndResourceTypeAndStatuses(
					gomock.Any(),
					querier.CompareAndUpdateTenantExtensionStatusByTenantIdAndResourceTypeAndStatusesParams{
						TenantID:         1,
						ResourceType:     model.ExtensionsResourceTypeCompaction,
						Status:           model.ExtensionStatusDisabled,
						ExpectedStatuses: []string{model.ExtensionStatusDisabling},
					}).Return(pgconn.NewCommandTag("UPDATE 1"), nil)
			},
			reconciler: &dummyReconciler{
				success: true,
			},
			expectError: false,
		},
		{
			name:   "enabling-with-valid-config-force-update",
			config: &DummyConfig{Positive: 10, Negative: -5, True: true},
			status: model.ExtensionStatusEnabling,
			tx: func(txP *modeltx.MockTxProvider) {
				txP.Transaction.EXPECT().Commit(gomock.Any()).Times(1)
				txP.Querier.EXPECT().UpdateTenantExtensionConfigByTenantIdAndResourceType(
					gomock.Any(),
					querier.UpdateTenantExtensionConfigByTenantIdAndResourceTypeParams{
						TenantID:     1,
						ResourceType: model.ExtensionsResourceTypeCompaction,
						Config:       ptr.Ptr(`{"positive":10,"negative":-5,"true":true}`),
					},
				).Return(pgconn.NewCommandTag("UPDATE 1"), nil)
				txP.Querier.EXPECT().UpdateTenantExtensionStatusByTenantIdAndResourceType(
					gomock.Any(),
					querier.UpdateTenantExtensionStatusByTenantIdAndResourceTypeParams{
						TenantID:     1,
						ResourceType: model.ExtensionsResourceTypeCompaction,
						Status:       model.ExtensionStatusRunning,
					}).Return(pgconn.NewCommandTag("UPDATE 1"), nil)
			},
			reconciler: &dummyReconciler{
				success: true,
			},
			forceUpdate: true,
			expectError: false,
		},
		{
			name:        "enabling-with-invalid-config",
			config:      &DummyConfig{Positive: 10, Negative: -5, True: false},
			status:      model.ExtensionStatusEnabling,
			expectError: true,
		},
		{
			name:        "updating-with-invalid-config",
			config:      &DummyConfig{Positive: 10, Negative: -5, True: false},
			status:      model.ExtensionStatusUpdating,
			expectError: true,
		},
		{
			name:        "upgrading-with-invalid-config",
			config:      &DummyConfig{Positive: 10, Negative: -5, True: false},
			status:      model.ExtensionStatusUpdating,
			expectError: true,
		},
		{
			name:   "updating-with-one-transform",
			config: &DummyConfig{Positive: 10, Negative: -5, True: true},
			status: model.ExtensionStatusUpdating,
			tx: func(txP *modeltx.MockTxProvider) {
				txP.Transaction.EXPECT().Commit(gomock.Any()).Times(1)
				txP.Querier.EXPECT().UpdateTenantExtensionConfigByTenantIdAndResourceType(
					gomock.Any(),
					querier.UpdateTenantExtensionConfigByTenantIdAndResourceTypeParams{
						TenantID:     1,
						ResourceType: model.ExtensionsResourceTypeCompaction,
						Config:       ptr.Ptr(`{"positive":20,"negative":-5,"true":true}`),
					},
				).Return(pgconn.NewCommandTag("UPDATE 1"), nil)
				txP.Querier.EXPECT().CompareAndUpdateTenantExtensionStatusByTenantIdAndResourceTypeAndStatuses(
					gomock.Any(),
					querier.CompareAndUpdateTenantExtensionStatusByTenantIdAndResourceTypeAndStatusesParams{
						TenantID:         1,
						ResourceType:     model.ExtensionsResourceTypeCompaction,
						Status:           model.ExtensionStatusRunning,
						ExpectedStatuses: []string{model.ExtensionStatusUpdating},
					}).Return(pgconn.NewCommandTag("UPDATE 1"), nil)
			},
			transforms: []Transform{
				func(obj *internal.ExtObject[DummyConfig, *DummyConfig]) error {
					config, err := obj.Config()
					if err != nil {
						return err
					}
					config.Positive = 20 // Update the positive value
					return obj.SetConfig(config)
				},
			},
			reconciler: &dummyReconciler{
				success: true,
			},
			expectError: false,
		},
		{
			name:   "updating-with-multiple-transforms",
			config: &DummyConfig{Positive: 10, Negative: -5, True: true},
			status: model.ExtensionStatusUpdating,
			tx: func(txP *modeltx.MockTxProvider) {
				txP.Transaction.EXPECT().Commit(gomock.Any()).Times(1)
				txP.Querier.EXPECT().UpdateTenantExtensionConfigByTenantIdAndResourceType(
					gomock.Any(),
					querier.UpdateTenantExtensionConfigByTenantIdAndResourceTypeParams{
						TenantID:     1,
						ResourceType: model.ExtensionsResourceTypeCompaction,
						Config:       ptr.Ptr(`{"positive":3,"negative":-5,"true":true}`),
					},
				).Return(pgconn.NewCommandTag("UPDATE 1"), nil)
				txP.Querier.EXPECT().CompareAndUpdateTenantExtensionStatusByTenantIdAndResourceTypeAndStatuses(
					gomock.Any(),
					querier.CompareAndUpdateTenantExtensionStatusByTenantIdAndResourceTypeAndStatusesParams{
						TenantID:         1,
						ResourceType:     model.ExtensionsResourceTypeCompaction,
						Status:           model.ExtensionStatusRunning,
						ExpectedStatuses: []string{model.ExtensionStatusUpdating},
					}).Return(pgconn.NewCommandTag("UPDATE 1"), nil)
			},
			transforms: []Transform{
				func(obj *internal.ExtObject[DummyConfig, *DummyConfig]) error {
					config, err := obj.Config()
					if err != nil {
						return err
					}
					config.Positive = 1
					return obj.SetConfig(config)
				},
				func(obj *internal.ExtObject[DummyConfig, *DummyConfig]) error {
					config, err := obj.Config()
					if err != nil {
						return err
					}
					if config.Positive == 1 {
						config.Positive = 2
					}
					return obj.SetConfig(config)
				},
				func(obj *internal.ExtObject[DummyConfig, *DummyConfig]) error {
					config, err := obj.Config()
					if err != nil {
						return err
					}
					if config.Positive == 2 {
						config.Positive = 3
					}
					return obj.SetConfig(config)
				},
			},
			reconciler: &dummyReconciler{
				success: true,
			},
			expectError: false,
		},
		{
			name:   "enabling-reconcile-failure",
			config: &DummyConfig{Positive: 10, Negative: -5, True: true},
			status: model.ExtensionStatusEnabling,
			reconciler: &dummyReconciler{
				success: false,
			},
			expectError: true,
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()

			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			txP := modeltx.NewMockTxProvider(ctrl)
			if tc.tx != nil {
				tc.tx(txP)
			}

			exec := newExtWorkflowExecForTest(t, tc.config, tc.status, tc.forceUpdate, txP, tc.reconciler)

			err := exec.Execute(context.Background(), tc.transforms)
			if tc.expectError {
				assert.Error(t, err, "expected error but got none")
			} else {
				assert.NoError(t, err, "expected no error but got one")
			}

			if tc.reconciler != nil {
				if !status.IsCommitStatus(tc.status) {
					assert.Equal(t, 1, tc.reconciler.cnt, "reconciler should be called once")
				}
				// If the status is a commit status, the reconciler's post method should be called.
				// If the status is not a commit status, the post method should not be called when the reconciler is successful.
				//
				// NOTE:
				// There's another case where the reconciler succeeds but the DB operation fails,
				// in which case the post method should not be called either. But we don't test that here.
				if tc.reconciler.success || status.IsCommitStatus(tc.status) {
					assert.Equal(t, 1, tc.reconciler.postCnt, "reconciler should be called once")
				} else {
					assert.Equal(t, 0, tc.reconciler.postCnt, "reconciler post should not be called on failure")
				}
			}
		})
	}
}
