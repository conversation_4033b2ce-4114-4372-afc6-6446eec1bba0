package transform

import (
	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext/internal"
)

// SetConfig is a Transform function that sets the configuration of the
// ExtObject to the provided config. It returns the modified object or an error
// if the operation fails.
func SetConfig[T any, TP internal.ExtObjectConfigProperties[T]](config *T) internal.Transform[T, TP] {
	return func(obj *internal.ExtObject[T, TP]) error {
		return obj.SetConfig(config)
	}
}

// DefaultConfig is a Transform function that sets the configuration of the
// ExtObject to the default configuration. It retrieves the current configuration,
// applies the default values, and then sets the modified configuration back to
// the ExtObject. It returns an error if any step fails.
func DefaultConfig[T any, TP internal.ExtObjectConfigProperties[T]]() internal.Transform[T, TP] {
	return func(obj *internal.ExtObject[T, TP]) error {
		config, err := obj.Config()
		if err != nil {
			return eris.Wrap(err, "failed to get config")
		}
		if config == nil {
			config = new(T)
		}
		TP(config).Default()
		if err := obj.SetConfig(config); err != nil {
			return eris.Wrap(err, "failed to set config")
		}
		return nil
	}
}

// SetConfigFunc is a Transform function that applies a provided function to the
// configuration of the ExtObject. It retrieves the current configuration, applies
// the function to it, and then sets the modified configuration back to the
// ExtObject. It returns an error if any step fails.
func SetConfigFunc[T any, TP internal.ExtObjectConfigProperties[T]](configFunc func(*T) (*T, error)) internal.Transform[T, TP] {
	return func(obj *internal.ExtObject[T, TP]) error {
		config, err := obj.Config()
		if err != nil {
			return eris.Wrap(err, "failed to get config")
		}
		config, err = configFunc(config)
		if err != nil {
			return eris.Wrap(err, "failed to apply config function")
		}
		if err := obj.SetConfig(config); err != nil {
			return eris.Wrap(err, "failed to set config")
		}
		return nil
	}
}

// SetStatus is a Transform function that sets the status of the ExtObject to
// the provided status. It returns the modified object.
func SetStatus[T any, TP internal.ExtObjectConfigProperties[T]](status string) internal.Transform[T, TP] {
	return func(obj *internal.ExtObject[T, TP]) error {
		obj.SetStatus(status)
		return nil
	}
}
