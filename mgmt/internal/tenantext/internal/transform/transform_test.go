package transform

import (
	"encoding/json"
	"errors"
	"testing"

	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"

	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext/internal"
	"github.com/risingwavelabs/risingwave-cloud/shared/ptr"
)

type DummyConfig struct {
	Positive int  `json:"positive,omitempty"`
	Negative int  `json:"negative,omitempty"`
	True     bool `json:"true,omitempty"`
}

func (d *DummyConfig) MarshalJSON() ([]byte, error) {
	type alias DummyConfig // Use alias to avoid recursion in MarshalJSON
	return json.Marshal((*alias)(d))
}

func (d *DummyConfig) UnmarshalJSON(bytes []byte) error {
	type alias DummyConfig
	return json.Unmarshal(bytes, (*alias)(d))
}

func (d *DummyConfig) Validate() error {
	if d.Positive <= 0 {
		return errors.New("negative positive")
	}
	if d.Negative >= 0 {
		return errors.New("positive negative")
	}
	if !d.True {
		return errors.New("true is false")
	}
	return nil
}

func (d *DummyConfig) Default() {
	*d = DummyConfig{
		Positive: 1,
		Negative: -1,
		True:     true,
	}
}

type Object = internal.ExtObject[DummyConfig, *DummyConfig]

func newObject(t *testing.T, config *DummyConfig, status string) *Object {
	t.Helper()
	var configStr *string
	if config != nil {
		configStr = ptr.Ptr(string(lo.Must(json.Marshal(&config))))
	}
	tenantExt := &model.TenantExtension{
		Version:      "1.0.0",
		ResourceType: model.ExtensionsResourceTypeCompaction,
		TenantID:     1,
		Config:       configStr,
		Status:       status,
	}
	return internal.NewExtObject[DummyConfig, *DummyConfig](*tenantExt)
}

type Transform = internal.Transform[DummyConfig, *DummyConfig]

func TestDefaultConfig(t *testing.T) {
	cfg := &DummyConfig{}
	transform := DefaultConfig[DummyConfig, *DummyConfig]()

	obj := newObject(t, cfg, "")
	err := transform(obj)
	assert.NoError(t, err, "expected no error from DefaultConfig transform")

	newCfg, err := obj.Config()
	assert.NoError(t, err, "expected no error retrieving config after DefaultConfig transform")
	assert.NotNil(t, newCfg, "expected config to be set after DefaultConfig transform")

	assert.Equal(t, 1, newCfg.Positive, "expected Positive to be set to default value")
	assert.Equal(t, -1, newCfg.Negative, "expected Negative to be set to default value")
	assert.True(t, newCfg.True, "expected True to be set to default value")
}

func TestSetConfig(t *testing.T) {
	cfg := &DummyConfig{
		Positive: 10,
		Negative: -5,
		True:     true,
	}
	transform := SetConfig[DummyConfig, *DummyConfig](cfg)

	obj := newObject(t, nil, "")
	err := transform(obj)
	assert.NoError(t, err, "expected no error from SetConfig transform")

	newCfg, err := obj.Config()
	assert.NoError(t, err, "expected no error retrieving config after SetConfig transform")
	assert.NotNil(t, newCfg, "expected config to be set after SetConfig transform")

	assert.Equal(t, 10, newCfg.Positive, "expected Positive to be set to 10")
	assert.Equal(t, -5, newCfg.Negative, "expected Negative to be set to -5")
	assert.True(t, newCfg.True, "expected True to be true")
}

func TestSetConfigFunc(t *testing.T) {
	cfg := &DummyConfig{
		Positive: 10,
		Negative: -5,
		True:     true,
	}
	transform := SetConfigFunc[DummyConfig, *DummyConfig](func(c *DummyConfig) (*DummyConfig, error) {
		c.Positive += 5
		return c, nil
	})

	obj := newObject(t, cfg, "")
	err := transform(obj)
	assert.NoError(t, err, "expected no error from SetConfigFunc transform")

	newCfg, err := obj.Config()
	assert.NoError(t, err, "expected no error retrieving config after SetConfigFunc transform")
	assert.NotNil(t, newCfg, "expected config to be set after SetConfigFunc transform")

	assert.Equal(t, 15, newCfg.Positive, "expected Positive to be incremented by 5")
	assert.Equal(t, -5, newCfg.Negative, "expected Negative to remain -5")
	assert.True(t, newCfg.True, "expected True to remain true")
}

func TestSetStatus(t *testing.T) {
	cfg := &DummyConfig{
		Positive: 10,
		Negative: -5,
		True:     true,
	}
	transform := SetStatus[DummyConfig, *DummyConfig]("running")

	obj := newObject(t, cfg, "stopped")
	err := transform(obj)
	assert.NoError(t, err, "expected no error from SetStatus transform")

	newStatus := obj.Record().Status
	assert.Equal(t, "running", newStatus, "expected status to be set to 'running'")
}
