package utils_test

import (
	"fmt"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext/internal/utils"
	"github.com/risingwavelabs/risingwave-cloud/shared/namespace"
)

func TestGetTaskID(t *testing.T) {
	t.Parallel()

	ns := uuid.New()
	task := "example-task"
	expected := fmt.Sprintf("%s-%s", namespace.ToBase32(ns), task)

	result := utils.GetTaskID(ns, task)
	assert.Equal(t, expected, result, "GetTaskID should return the expected task ID format")
}
