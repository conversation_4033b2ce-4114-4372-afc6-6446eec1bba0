package tenantext

import (
	"context"
	"slices"

	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext/status"

	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/risingwave-cloud/internal/apiservice/errors"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
)

// ValidationService defines the interface for validating parameters for tenant extensions.
type ValidationService[T any] interface {
	// Validate checks if the provided parameters for the workflow are valid.
	Validate(params T) error
}

// executionService defines the interface for executing workflows for tenant extensions.
type executionService[T any] interface {
	// Execute runs the workflow with the provided parameters.
	//
	// The params must be non-nil if the execution is for creation or update.
	// The params must be nil if the execution is for deletion.
	//
	// If forceUpdate is true, the DB record update will happen no matter the
	// current status of the tenant extension.
	Execute(ctx context.Context, params *T, forceUpdate bool) error
}

// ExtensionWorkflowService defines the interface for performing workflow steps for tenant extensions.
type ExtensionWorkflowService[T any] interface {
	// Exists checks if the tenant extension exists.
	Exists(ctx context.Context) (bool, error)

	// Reconcile runs the reconciliation workflow step for a tenant extension.
	Reconcile(ctx context.Context, params T) error

	// ReconcileIfExists runs the reconciliation workflow step for a tenant extension if it exists.
	ReconcileIfExists(ctx context.Context, params T) error

	EnableOnAbsenceOrUpdateIfExists(ctx context.Context, params T) error

	// Enable runs the enabling workflow step for a tenant extension.
	Enable(ctx context.Context, params T) error

	// Update runs the updating workflow step for a tenant extension.
	Update(ctx context.Context, params T) error

	// UpdateIfExists runs the updating workflow step for a tenant extension if it exists.
	UpdateIfExists(ctx context.Context, params T) error

	// Start runs the enabling workflow step for a tenant extension without parameters.
	Start(ctx context.Context) error

	// StartIfExists runs the enabling workflow step for a tenant extension if it exists.
	StartIfExists(ctx context.Context) error

	// Stop runs the disabling workflow step for a tenant extension.
	Stop(ctx context.Context) error

	// StopIfExists runs the disabling workflow step for a tenant extension if it exists.
	StopIfExists(ctx context.Context) error

	// Disable runs the disabling workflow step for a tenant extension and deletes it.
	Disable(ctx context.Context) error

	// DisableIfExists runs the disabling workflow step for a tenant extension if it exists and deletes it.
	DisableIfExists(ctx context.Context) error
}

// ExtensionWorkflowServiceImpl provides a generic service for managing tenant extension workflows.
type ExtensionWorkflowServiceImpl[T any] struct {
	m           *model.Model
	tenant      *model.Tenant
	tenantExt   *model.TenantExtension
	extType     string
	execFactory func(tenant *model.Tenant, tenantExt model.TenantExtension) executionService[T]
	extFactory  func(tenant *model.Tenant) (model.TenantExtension, error)

	force bool
}

func (s *ExtensionWorkflowServiceImpl[T]) exec() executionService[T] {
	return s.execFactory(s.tenant, *s.tenantExt)
}

func (s *ExtensionWorkflowServiceImpl[T]) enforceStatus(ctx context.Context, statuses []string) error {
	if s.tenantExt == nil {
		return eris.New("tenant extension isn't found")
	}
	if slices.Contains(statuses, s.tenantExt.Status) {
		return nil
	}
	err := s.m.UpdateTenantExtensionStatusByTenantIDAndResourceType(ctx, s.tenantExt.TenantID, s.tenantExt.ResourceType, statuses[0])
	if err != nil {
		return err
	}
	s.tenantExt.Status = statuses[0]
	return nil
}

// execute runs the workflow with the provided parameters.
func (s *ExtensionWorkflowServiceImpl[T]) execute(
	ctx context.Context,
	terminateStatus string,
	inProgressStatuses []string,
	params *T) error {
	if s.tenantExt == nil {
		return eris.New("tenant extension isn't found")
	}

	if s.force {
		err := s.enforceStatus(ctx, inProgressStatuses)
		if err != nil {
			return eris.Wrapf(err, "failed to enforce status %s for tenant extension %s", inProgressStatuses[0], s.tenantExt.ResourceType)
		}
	}

	if !slices.Contains(inProgressStatuses, s.tenantExt.Status) &&
		s.tenantExt.Status != terminateStatus {
		// If the tenant extension is not in one of the expected statuses, we return an error.
		expectedStatus := make([]string, len(inProgressStatuses)+1)
		copy(expectedStatus, inProgressStatuses)
		expectedStatus[len(inProgressStatuses)] = terminateStatus

		return eris.Errorf("tenant extension is not in one of the expected statuses: %v", expectedStatus)
	}

	// Let the execution happen even if the tenant extension is in the termination status
	// to allow the post-execution clean-up to run.
	return s.exec().Execute(ctx, params, s.force)
}

func (s *ExtensionWorkflowServiceImpl[T]) Reconcile(ctx context.Context, params T) error {
	return s.execute(ctx, status.GetCommitStatus(s.tenantExt.Status), []string{s.tenantExt.Status}, &params)
}

func (s *ExtensionWorkflowServiceImpl[T]) ReconcileIfExists(ctx context.Context, params T) error {
	if s.tenantExt != nil {
		return s.Reconcile(ctx, params)
	}
	return nil
}

// Enable runs the enabling workflow for a tenant extension.
func (s *ExtensionWorkflowServiceImpl[T]) Enable(ctx context.Context, params T) error {
	return s.execute(
		ctx,
		model.ExtensionStatusRunning,
		[]string{model.ExtensionStatusEnabling},
		&params,
	)
}

// Update runs the updating workflow for a tenant extension.
func (s *ExtensionWorkflowServiceImpl[T]) Update(ctx context.Context, params T) error {
	return s.execute(
		ctx,
		model.ExtensionStatusRunning,
		[]string{model.ExtensionStatusUpdating, model.ExtensionStatusUpgrading},
		&params,
	)
}

func (s *ExtensionWorkflowServiceImpl[T]) UpdateIfExists(ctx context.Context, params T) error {
	if s.tenantExt != nil {
		return s.Update(ctx, params)
	}
	return nil
}

func (s *ExtensionWorkflowServiceImpl[T]) Start(ctx context.Context) error {
	var empty T
	return s.Enable(ctx, empty)
}

func (s *ExtensionWorkflowServiceImpl[T]) Stop(ctx context.Context) error {
	return s.execute(
		ctx,
		model.ExtensionStatusDisabled,
		[]string{model.ExtensionStatusDisabling},
		nil,
	)
}

func (s *ExtensionWorkflowServiceImpl[T]) StartIfExists(ctx context.Context) error {
	if s.tenantExt != nil {
		return s.Start(ctx)
	}
	return nil
}

func (s *ExtensionWorkflowServiceImpl[T]) StopIfExists(ctx context.Context) error {
	if s.tenantExt != nil {
		return s.Stop(ctx)
	}
	return nil
}

func (s *ExtensionWorkflowServiceImpl[T]) Disable(ctx context.Context) error {
	err := s.Stop(ctx)
	if err != nil {
		return err
	}

	err = s.m.DeleteTenantExtensionByTenantIDAndResourceType(ctx, s.tenantExt.TenantID, s.tenantExt.ResourceType)
	if err != nil {
		return eris.Wrapf(err, "failed to delete tenant extension %s for tenant %d", s.tenantExt.ResourceType, s.tenantExt.TenantID)
	}
	return nil
}

func (s *ExtensionWorkflowServiceImpl[T]) DisableIfExists(ctx context.Context) error {
	if s.tenantExt != nil {
		return s.Disable(ctx)
	}
	return nil
}

func (s *ExtensionWorkflowServiceImpl[T]) Exists(_ context.Context) (bool, error) {
	return s.tenantExt != nil, nil
}

func (s *ExtensionWorkflowServiceImpl[T]) EnableOnAbsenceOrUpdateIfExists(ctx context.Context, params T) error {
	if s.tenantExt == nil {
		// If the tenant extension does not exist, we create it and enable it.
		tenantExt, err := s.extFactory(s.tenant)
		if err != nil {
			return eris.Wrapf(err, "failed to create tenant extension %s for tenant %d", s.extType, s.tenant.ID)
		}
		err = s.m.CreateTenantExtensionRaw(ctx, tenantExt)
		if err != nil {
			return eris.Wrapf(err, "failed to create tenant extension %s for tenant %d", s.extType, s.tenant.ID)
		}
		s.tenantExt = &tenantExt

		return s.Enable(ctx, params)
	}

	return s.Update(ctx, params)
}

func newExtensionWorkflowServiceImpl[T any](
	ctx context.Context,
	m *model.Model,
	tenantID uint64,
	extType string,
	execFactory func(tenant *model.Tenant, tenantExt model.TenantExtension) executionService[T],
	extFactory func(tenant *model.Tenant) (model.TenantExtension, error),
	force bool,
) (ExtensionWorkflowService[T], error) {
	tenant, err := m.GetTenantByID(ctx, tenantID)
	if err != nil {
		return nil, eris.Wrapf(err, "failed to get tenant by ID %d", tenantID)
	}
	tenantExt, err := m.GetTenantExtensionByTenantIDAndResourceType(ctx, tenantID, extType)
	if err != nil && !eris.Is(err, errors.ErrTenantExtensionNotExist) {
		return nil, eris.Wrapf(err, "failed to get tenant extension of %s for tenant %d", extType, tenantID)
	}
	var tenantExtPtr *model.TenantExtension
	if tenantExt != model.TenantExtensionNil {
		tenantExtPtr = &tenantExt
	}

	return &ExtensionWorkflowServiceImpl[T]{
		m:           m,
		tenant:      &tenant,
		tenantExt:   tenantExtPtr,
		execFactory: execFactory,
		extType:     extType,
		extFactory:  extFactory,
		force:       force,
	}, nil
}
