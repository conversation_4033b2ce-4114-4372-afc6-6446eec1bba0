package status

import "github.com/risingwavelabs/risingwave-cloud/internal/model"

// GetCommitStatus returns the commit status based on the provided status string.
func GetCommitStatus(status string) string {
	switch status {
	case model.ExtensionStatusEnabling,
		model.ExtensionStatusUpdating,
		model.ExtensionStatusUpgrading:
		return model.ExtensionStatusRunning
	case model.ExtensionStatusDisabling:
		return model.ExtensionStatusDisabled
	default:
		return status // For other statuses, return as is
	}
}

// IsStatusIneffective checks if the provided status indicates that the extension is not effective or not in a usable state.
func IsStatusIneffective(status string) bool {
	return status == model.ExtensionStatusDisabled ||
		status == model.ExtensionStatusFailed ||
		status == ""
}

// IsStatusEffective checks if the provided status indicates that the extension is effective or in a usable state.
func IsStatusEffective(status string) bool {
	return !IsStatusIneffective(status)
}

// IsCommitStatus checks if the provided status is a commit status.
func IsCommitStatus(status string) bool {
	return status == GetCommitStatus(status)
}

// IsStatusInProgress checks if the provided status indicates an in-progress state.
func IsStatusInProgress(status string) bool {
	return status == model.ExtensionStatusEnabling ||
		status == model.ExtensionStatusDisabling ||
		status == model.ExtensionStatusUpdating ||
		status == model.ExtensionStatusUpgrading
}

// IsStatusForCreate checks if the provided status is suitable for creating an extension.
func IsStatusForCreate(status string) bool {
	return status == model.ExtensionStatusEnabling
}

// IsStatusForUpdate checks if the provided status is suitable for updating an extension.
func IsStatusForUpdate(status string) bool {
	return status == model.ExtensionStatusUpdating ||
		status == model.ExtensionStatusUpgrading
}

// IsStatusForCreateOrUpdate checks if the provided status is suitable for creating or updating an extension.
func IsStatusForCreateOrUpdate(status string) bool {
	return IsStatusForCreate(status) || IsStatusForUpdate(status)
}

// IsStatusForDelete checks if the provided status is suitable for enabling an extension.
func IsStatusForDelete(status string) bool {
	return status == model.ExtensionStatusDisabling
}
