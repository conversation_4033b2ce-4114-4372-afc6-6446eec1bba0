package status_test

import (
	"testing"

	"github.com/stretchr/testify/require"

	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext/status"
)

func TestIsStatusForCreateOrUpdate(t *testing.T) {
	testcases := map[string]struct {
		status   string
		expected bool
	}{
		"enabling":  {status: model.ExtensionStatusEnabling, expected: true},
		"updating":  {status: model.ExtensionStatusUpdating, expected: true},
		"upgrading": {status: model.ExtensionStatusUpgrading, expected: true},
		"disabling": {status: model.ExtensionStatusDisabling, expected: false},
		"disabled":  {status: model.ExtensionStatusDisabled, expected: false},
		"failed":    {status: model.ExtensionStatusFailed, expected: false},
		"running":   {status: model.ExtensionStatusRunning, expected: false},
		"empty":     {status: "", expected: false},
	}
	for name, tc := range testcases {
		t.Run(name, func(t *testing.T) {
			t.<PERSON>llel()

			result := status.IsStatusForCreateOrUpdate(tc.status)
			require.Equal(t, tc.expected, result)
		})
	}
}

func TestIsStatusForDelete(t *testing.T) {
	testcases := map[string]struct {
		status   string
		expected bool
	}{
		"enabling":  {status: model.ExtensionStatusEnabling, expected: false},
		"updating":  {status: model.ExtensionStatusUpdating, expected: false},
		"upgrading": {status: model.ExtensionStatusUpgrading, expected: false},
		"disabling": {status: model.ExtensionStatusDisabling, expected: true},
		"disabled":  {status: model.ExtensionStatusDisabled, expected: false},
		"failed":    {status: model.ExtensionStatusFailed, expected: false},
		"running":   {status: model.ExtensionStatusRunning, expected: false},
		"empty":     {status: "", expected: false},
	}
	for name, tc := range testcases {
		t.Run(name, func(t *testing.T) {
			t.Parallel()

			result := status.IsStatusForDelete(tc.status)
			require.Equal(t, tc.expected, result)
		})
	}
}

func TestIsCommitStatus(t *testing.T) {
	testcases := map[string]struct {
		status   string
		expected bool
	}{
		"enabling":  {status: model.ExtensionStatusEnabling, expected: false},
		"updating":  {status: model.ExtensionStatusUpdating, expected: false},
		"upgrading": {status: model.ExtensionStatusUpgrading, expected: false},
		"disabling": {status: model.ExtensionStatusDisabling, expected: false},
		"disabled":  {status: model.ExtensionStatusDisabled, expected: true},
		"failed":    {status: model.ExtensionStatusFailed, expected: true},
		"running":   {status: model.ExtensionStatusRunning, expected: true},
		"empty":     {status: "", expected: true},
	}
	for name, tc := range testcases {
		t.Run(name, func(t *testing.T) {
			t.Parallel()

			result := status.IsCommitStatus(tc.status)
			require.Equal(t, tc.expected, result)
		})
	}
}

func TestGetCommitStatus(t *testing.T) {
	testcases := map[string]struct {
		status   string
		expected string
	}{
		"enabling":  {status: model.ExtensionStatusEnabling, expected: model.ExtensionStatusRunning},
		"updating":  {status: model.ExtensionStatusUpdating, expected: model.ExtensionStatusRunning},
		"upgrading": {status: model.ExtensionStatusUpgrading, expected: model.ExtensionStatusRunning},
		"disabling": {status: model.ExtensionStatusDisabling, expected: model.ExtensionStatusDisabled},
		"disabled":  {status: model.ExtensionStatusDisabled, expected: model.ExtensionStatusDisabled},
		"failed":    {status: model.ExtensionStatusFailed, expected: model.ExtensionStatusFailed},
		"running":   {status: model.ExtensionStatusRunning, expected: model.ExtensionStatusRunning},
		"empty":     {status: "", expected: ""},
	}
	for name, tc := range testcases {
		t.Run(name, func(t *testing.T) {
			t.Parallel()

			result := status.GetCommitStatus(tc.status)
			require.Equal(t, tc.expected, result)
		})
	}
}
