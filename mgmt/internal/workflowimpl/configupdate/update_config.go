package configupdate

import (
	"context"
	"strconv"
	"time"

	"github.com/google/uuid"
	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/risingwave-cloud/internal/account"
	agentprovider "github.com/risingwavelabs/risingwave-cloud/internal/agent/provider"
	"github.com/risingwavelabs/risingwave-cloud/internal/config"
	"github.com/risingwavelabs/risingwave-cloud/internal/infra/storage"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/extensions"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/rwconfig"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/rwcrd"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/rwworkload"
	"github.com/risingwavelabs/risingwave-cloud/internal/metrics"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/modeltx"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/configset"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenant/component"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow/activity"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow/builder"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl"
	"github.com/risingwavelabs/risingwave-cloud/shared/utils"
)

const UpdateWorkflowType = "ConfigUpdate"

const (
	UpdateConfigState         = "UpdateConfiguration"
	PreprocessingRequestState = "PreprocessingRequest"
	RestartRisingWavePods     = "RestartRisingWavePods"
	ValidationState           = "Validating"

	StopRisingWaveState         = "StopRisingWaveState"
	StartRisingWaveState        = "StartRisingWaveState"
	PostStopRisingWaveState     = "PostStopRisingWave"
	PostStartRisingWaveState    = "PostStartRisingWave"
	IntermediateValidationState = "IntermediateValidating"
)

type ConfigPatch struct {
	RawConfig string  `json:"RawConfig"`
	Component *string `json:"Component"`
	NodeGroup *string `json:"NodeGroup"`
}

type UpdateContext struct {
	workflowimpl.TenantContextImpl
	RwConfigRaw string `json:"RwConfigRaw,omitempty"` // deprecated
	NoRestart   bool   `json:"NoRestart"`

	ConfigPatch    ConfigPatch          `json:"ConfigPatch"`
	PreviousConfig *configset.ConfigSet `json:"PreviousConfig"`
}

func (ctx *UpdateContext) Labels() map[string]string {
	return map[string]string{
		"tenantId": strconv.FormatUint(ctx.TenantID, 10),
	}
}

type UpdateWorkflowService interface {
	NewConfigUpdateWorkflow(tenantID uint64, patch ConfigPatch, previous *configset.ConfigSet, noRestart bool) *workflow.Workflow
	UpdateConfigWithWorkflow(ctx context.Context, tenantID uint64, patch ConfigPatch, noRestart bool) (uuid.UUID, error)
}

type UpdateWorkflowServiceImpl struct {
	builder             *builder.WorkflowBuilder
	repository          *workflow.Repository
	rwWorkload          rwworkload.RisingWaveWorkloadManager
	account             account.Agent
	tenantModel         *model.Model
	txProvider          modeltx.TxProvider
	compaction          extensions.ServerlessCompaction
	cloudAgentProvider  agentprovider.CloudAgentProviderInterface
	rwConfigFactory     rwconfig.Factory
	configSetRepository configset.Repository
	rwBuilder           rwcrd.RwBuilder
	storageFactory      storage.FactoryInterface
}

func NewUpdateWorkflowService(
	repository *workflow.Repository,
	rwWorkload rwworkload.RisingWaveWorkloadManager,
	account account.Agent,
	tenantModel *model.Model,
	txProvider modeltx.TxProvider,
	compaction extensions.ServerlessCompaction,
	cloudAgentProvider agentprovider.CloudAgentProviderInterface,
	rwConfigFactory rwconfig.Factory,
	configSetRepository configset.Repository,
	rwBuilder rwcrd.RwBuilder,
	storageFactory storage.FactoryInterface,
) UpdateWorkflowService {
	s := &UpdateWorkflowServiceImpl{
		repository:          repository,
		rwWorkload:          rwWorkload,
		account:             account,
		tenantModel:         tenantModel,
		txProvider:          txProvider,
		compaction:          compaction,
		cloudAgentProvider:  cloudAgentProvider,
		rwConfigFactory:     rwConfigFactory,
		configSetRepository: configSetRepository,
		rwBuilder:           rwBuilder,
		storageFactory:      storageFactory,
	}
	var (
		PreprocessRequestActivity         = activity.WrapDoError(s.preprocessRequest)
		UpdateConfigActivity              = activity.WrapDoError(s.updateConfig)
		RestartRisingWavePodsActivity     = activity.WrapDoError(s.RestartRisingWavePods)
		ValidateRisingWaveStartedActivity = activity.WrapDoError(s.validateRisingWaveStarted)
	)

	retry := activity.RetryWrapper(repository, 3)
	s.builder = builder.NewBuilder(UpdateWorkflowType).
		Next(PreprocessingRequestState).Activity(PreprocessRequestActivity).Wrap(retry).Wrap(activity.FailWorkflowWrapper).
		Next(UpdateConfigState).Activity(UpdateConfigActivity).Wrap(retry).Wrap(activity.FailWorkflowWrapper).
		Next(RestartRisingWavePods).Activity(RestartRisingWavePodsActivity).Wrap(retry).Wrap(activity.FailWorkflowWrapper).
		Next(ValidationState).Activity(ValidateRisingWaveStartedActivity).Wrap(retry).Wrap(activity.FailWorkflowWrapper).
		Succeed().
		// deprecated status
		From(StopRisingWaveState).Activity(workflow.EmptyActivity).
		Defer(PostStopRisingWaveState, activity.WrapDefer(workflow.EmptyActivity, repository)).
		Next(IntermediateValidationState).Activity(workflow.EmptyActivity).
		Next(UpdateConfigState).
		// deprecated status
		From(StartRisingWaveState).Activity(workflow.EmptyActivity).
		Defer(PostStartRisingWaveState, activity.WrapDefer(workflow.EmptyActivity, repository)).
		Next(RestartRisingWavePods)

	contextProvider := func() any { return &UpdateContext{} }
	repository.Registry.Register(UpdateWorkflowType, s.builder.WorkflowConstructor(contextProvider))

	return s
}

func (s *UpdateWorkflowServiceImpl) NewConfigUpdateWorkflow(tenantID uint64, patch ConfigPatch, previous *configset.ConfigSet, noRestart bool) *workflow.Workflow {
	ctx := &UpdateContext{
		TenantContextImpl: workflowimpl.NewTenantContext(tenantID),
		NoRestart:         noRestart,
		ConfigPatch:       patch,
		PreviousConfig:    previous,
	}
	var w = s.builder.Build()
	w.Context = ctx

	return w
}

func (s *UpdateWorkflowServiceImpl) UpdateConfigWithWorkflow(ctx context.Context, tenantID uint64, patch ConfigPatch, noRestart bool) (uuid.UUID, error) {
	ctx, cancel := context.WithTimeout(ctx, 5*model.TransactionTimeout())
	defer cancel()

	workflowID, err := modeltx.Query(ctx, s.txProvider, func(ctx context.Context, txModel *model.Model) (uuid.UUID, error) {
		tenant, err := s.tenantModel.GetTenantByID(ctx, tenantID)
		if err != nil {
			return uuid.Nil, err
		}

		previous, err := s.configSetRepository.GetConfigSet(ctx, tenant.NsID)
		if err != nil {
			return uuid.Nil, err
		}

		err = txModel.CompareAndUpdateTenantStatusByIDAndStatuses(ctx,
			tenantID, model.TenantStatusConfigUpdating, []string{model.TenantStatusRunning, model.TenantStatusConfigUpdating},
		)
		if err != nil {
			return uuid.UUID{}, eris.Wrap(err, "failed to update tenant status")
		}

		id, err := s.repository.CreateInTx(ctx, txModel, s.NewConfigUpdateWorkflow(tenantID, patch, previous, noRestart))
		if err != nil {
			return uuid.UUID{}, eris.Wrap(err, "failed to create workflow")
		}
		return id, nil
	})
	if err != nil {
		return uuid.UUID{}, err
	}
	metrics.ReportWorkflowCreated(UpdateWorkflowType)
	return workflowID, nil
}

func (s *UpdateWorkflowServiceImpl) preprocessRequest(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*UpdateContext)
	var tenantModel = s.tenantModel

	tenant, err := tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}

	if tenant.Status != model.TenantStatusConfigUpdating {
		return eris.Errorf("illegal state, tenant %d not in %s status", tenant.ID, model.TenantStatusConfigUpdating)
	}

	return nil
}

func (s *UpdateWorkflowServiceImpl) updateConfig(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*UpdateContext)

	tenant, err := s.tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}

	cloudAgent, err := s.cloudAgentProvider.GetAgent(c, tenant.ClusterID)
	if err != nil {
		return err
	}
	defer cloudAgent.Close()

	rwConfigManager, err := s.rwConfigFactory.GetRwConfigManager(c, tenant.ClusterID, cloudAgent)
	if err != nil {
		return err
	}

	configSet, err := s.configSetRepository.GetConfigSet(c, tenant.NsID)
	if err != nil {
		return err
	}

	// migrate to independent configs
	patch := ctx.ConfigPatch
	isGlobalUpdate := patch.Component == nil && patch.NodeGroup == nil

	mustMigrate := configSet.IsLegacy && !isGlobalUpdate && !ctx.NoRestart
	preferMigrate := configSet.IsLegacy && config.Conf.Features.ConfigSet && !ctx.NoRestart
	if mustMigrate || preferMigrate {
		err = rwConfigManager.MigrateToIndependentConfigs(c, tenant, s.rwBuilder)
		if err != nil {
			return eris.Wrap(err, "failed to migrate config set to independent")
		}

		// get the migrated config set
		configSet, err = s.configSetRepository.GetConfigSet(c, tenant.NsID)
		if err != nil {
			return err
		}
	}

	// update config map
	if isGlobalUpdate {
		err = configSet.UpdateAllConfigs(patch.RawConfig)
		if err != nil {
			return err
		}
		err = rwConfigManager.UpdateAllConfigMap(c, tenant, configSet)
		if err != nil {
			return eris.Wrap(err, "failed to update all config map")
		}
	} else {
		key := configset.ConfigKey{
			Component: *patch.Component,
			NodeGroup: *patch.NodeGroup,
		}
		err = configSet.UpdateConfigs([]configset.ConfigKey{key}, patch.RawConfig)
		if err != nil {
			return err
		}
		err = rwConfigManager.UpdateNodeGroupConfigMap(c, tenant, key, configSet)
		if err != nil {
			return eris.Wrap(err, "failed to update config map for node group")
		}
	}

	// update msdb
	err = s.configSetRepository.UpdateConfigSet(c, tenant.NsID, configSet)
	if err != nil {
		return err
	}

	return nil
}

func (s *UpdateWorkflowServiceImpl) RestartRisingWavePods(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*UpdateContext)
	if ctx.NoRestart {
		return nil
	}

	tenant, err := s.tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}

	cloudAgent, err := s.cloudAgentProvider.GetAgent(c, tenant.ClusterID)
	if err != nil {
		return err
	}
	defer cloudAgent.Close()

	// TODO standalone support

	patch := ctx.ConfigPatch
	isGlobalUpdate := patch.Component == nil && patch.NodeGroup == nil

	affectedNodeGroups := []configset.ConfigKey{}
	if isGlobalUpdate {
		nodeGroups, err := rwconfig.GetNodeGroups(tenant)
		if err != nil {
			return err
		}
		for _, nodeGroup := range nodeGroups {
			affectedNodeGroups = append(affectedNodeGroups, configset.ConfigKey{
				Component: nodeGroup.Component,
				NodeGroup: nodeGroup.NodeGroup,
			})
		}
	} else {
		affectedNodeGroups = append(affectedNodeGroups, configset.ConfigKey{
			Component: *patch.Component,
			NodeGroup: *patch.NodeGroup,
		})
	}

	restartAt := time.Now()
	for _, nodeGroup := range affectedNodeGroups {
		const duration = 10 * time.Second
		const interval = 2 * time.Second
		err = utils.PollingImmediate(c, duration, interval, func(ctx context.Context) (bool, error) {
			err := cloudAgent.UpdateRisingWaveNodeGroupRestartAt(
				ctx, k8s.DefaultRisingWaveName, tenant.ResourceNamespace,
				component.ComponentType(nodeGroup.Component).ToPbComponentType(),
				nodeGroup.NodeGroup, &restartAt,
			)
			if err != nil && eris.GetCode(err) == eris.CodeAborted {
				return false, nil
			}
			if err != nil {
				return false, err
			}
			return true, nil
		})
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *UpdateWorkflowServiceImpl) validateRisingWaveStarted(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*UpdateContext)

	err := s.tenantModel.UpdateTenantStatusByID(c, ctx.TenantID, model.TenantStatusRunning)
	if err != nil {
		return eris.Wrap(err, "failed to update tenant status")
	}
	return nil
}
