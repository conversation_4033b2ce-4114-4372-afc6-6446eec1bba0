package delete

import (
	"context"
	"strconv"
	"sync"
	"time"

	"github.com/risingwavelabs/risingwave-cloud/internal/infra/storage"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext"

	"github.com/risingwavelabs/risingwave-cloud/internal/workflow/builder"

	"github.com/google/uuid"
	"github.com/risingwavelabs/eris"
	"go.uber.org/zap"

	"github.com/risingwavelabs/risingwave-cloud/internal/account"
	agentprovider "github.com/risingwavelabs/risingwave-cloud/internal/agent/provider"
	apierr "github.com/risingwavelabs/risingwave-cloud/internal/apiservice/errors"
	"github.com/risingwavelabs/risingwave-cloud/internal/infra/network"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/extensions"
	"github.com/risingwavelabs/risingwave-cloud/internal/metrics"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/modeltx"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow/activity"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/notifications"
	"github.com/risingwavelabs/risingwave-cloud/shared/logger"
)

const DeleteTenantWorkflowType = "DeleteTenant"

const (
	PreprocessRequestState          = "PreprocessRequest"
	DeleteExtensionsCompactionState = "DeleteExtensionsCompaction"
	DeletePrivateLinksState         = "DeletePrivateLinks"
	DeleteRisingWaveResourceState   = "DeleteRisingWaveResource"
	ValidateState                   = "Validate"
	DeleteStorage                   = "DeleteStorage"
	CleanupTaskInitial              = "CleanupTaskInitial"
	CleanupTaskFinal                = "CleanupTaskFinal"
	TransmitNotificationState       = "TransmitNotification"
	DeleteAccountServiceState       = "DeleteAccountService"
	DeleteMsdbRecordState           = "DeleteMsdbRecord"
)

type DeleteTenantContext struct { //nolint:revive
	workflowimpl.TenantContextImpl
}

func (ctx *DeleteTenantContext) Labels() map[string]string {
	return map[string]string{
		"tenantId": strconv.FormatUint(ctx.TenantID, 10),
	}
}

type DeleteTenantWorkflowService struct { //nolint:revive
	builder         *builder.WorkflowBuilder
	repository      *workflow.Repository
	clusterProvider k8s.IKubernetesClusterProvider
	account         account.Agent

	model      *model.Model
	txProvider modeltx.TxProvider

	storageFactory         storage.FactoryInterface
	cloudAgentProvider     agentprovider.CloudAgentProviderInterface
	networkProviderFactory *network.Factory
	compaction             extensions.ServerlessCompaction
}

func NewDeleteTenantWorkflowService(
	repository *workflow.Repository,
	clusterProvider k8s.IKubernetesClusterProvider,
	account account.Agent,
	tenantModel *model.Model,
	txProvider modeltx.TxProvider,
	cloudAgentProvider agentprovider.CloudAgentProviderInterface,
	networkProviderFactory *network.Factory,
	storageFactory storage.FactoryInterface,
	compaction extensions.ServerlessCompaction,
) *DeleteTenantWorkflowService {
	s := &DeleteTenantWorkflowService{
		repository:             repository,
		clusterProvider:        clusterProvider,
		account:                account,
		model:                  tenantModel,
		txProvider:             txProvider,
		cloudAgentProvider:     cloudAgentProvider,
		networkProviderFactory: networkProviderFactory,
		storageFactory:         storageFactory,
		compaction:             compaction,
	}

	var (
		PreprocessRequestActivity           = activity.WrapDoError(s.preprocessRequest)
		DeleteExtensionsCompactionActivity  = activity.WrapDoError(s.deleteExtensionsCompaction)
		DeletePrivateLinksActivity          = activity.WrapDoError(s.deletePrivateLinks)
		DeleteRisingWaveResourceActivity    = activity.WrapDoError(s.deleteRisingWaveResource)
		ValidateTenantActivity              = activity.WrapDoError(s.validateTenant)
		DeleteStorageActivity               = activity.WrapDoError(s.deleteStorage)
		CleanupStorageDeletionTaskActivity  = activity.WrapDoError(s.cleanupTask)
		TransmitSuccessNotificationActivity = s.newNotificationActivity(notifications.DeleteSuccess)
		DeleteAccountServiceActivity        = activity.WrapDoError(s.deleteAccountService)
		DeleteMsdbRecordActivity            = activity.WrapDoError(s.deleteMsdbRecord)
	)

	retryWithDelay := activity.RetryWrapperLinDelay(repository, 3, time.Second*20)
	s.builder = builder.NewBuilder(DeleteTenantWorkflowType).
		Next(PreprocessRequestState).Activity(PreprocessRequestActivity).Wrap(retryWithDelay).Wrap(activity.FailWorkflowWrapper).
		Next(DeleteExtensionsCompactionState).Activity(DeleteExtensionsCompactionActivity).Wrap(retryWithDelay).Wrap(activity.FailWorkflowWrapper).
		Next(DeletePrivateLinksState).Activity(DeletePrivateLinksActivity).Wrap(retryWithDelay).Wrap(activity.FailWorkflowWrapper).
		Next(DeleteRisingWaveResourceState).Activity(DeleteRisingWaveResourceActivity).Wrap(retryWithDelay).Wrap(activity.FailWorkflowWrapper).
		Defer(CleanupTaskInitial, activity.WrapDefer(retryWithDelay(CleanupStorageDeletionTaskActivity), repository)).
		Next(ValidateState).Activity(ValidateTenantActivity).Wrap(retryWithDelay).Wrap(activity.FailWorkflowWrapper).
		Next(DeleteStorage).Activity(DeleteStorageActivity).Wrap(retryWithDelay).Wrap(activity.FailWorkflowWrapper).
		Defer(CleanupTaskFinal, activity.WrapDefer(retryWithDelay(CleanupStorageDeletionTaskActivity), repository)).
		Next(TransmitNotificationState).Activity(TransmitSuccessNotificationActivity).Wrap(retryWithDelay).Wrap(activity.FailWorkflowWrapper).
		Next(DeleteAccountServiceState).Activity(DeleteAccountServiceActivity).Wrap(retryWithDelay).Wrap(activity.FailWorkflowWrapper).
		Next(DeleteMsdbRecordState).Activity(DeleteMsdbRecordActivity).Wrap(retryWithDelay).Wrap(activity.FailWorkflowWrapper).
		Succeed()

	contextProvider := func() any { return &DeleteTenantContext{} }
	repository.Registry.Register(DeleteTenantWorkflowType, s.builder.WorkflowConstructor(contextProvider))

	return s
}

func (s *DeleteTenantWorkflowService) NewDeleteTenantWorkflow(tenantID uint64) *workflow.Workflow {
	ctx := &DeleteTenantContext{
		TenantContextImpl: workflowimpl.NewTenantContext(tenantID),
	}
	var w = s.builder.Build()
	w.Context = ctx

	return w
}

var (
	DeleteTenantInCreatingError = eris.New("cannot delete tenant in creating")
)

func (s *DeleteTenantWorkflowService) DeleteTenantWithWorkflow(ctx context.Context, tenantID uint64) (uuid.UUID, error) {
	ctx, cancel := context.WithTimeout(ctx, 5*model.TransactionTimeout())
	defer cancel()

	workflowID, err := modeltx.Query(ctx, s.txProvider, func(ctx context.Context, txModel *model.Model) (uuid.UUID, error) {
		tenant, err := txModel.GetTenantByID(ctx, tenantID)
		if err != nil {
			return uuid.UUID{}, err
		}
		if tenant.Status == model.TenantStatusCreating {
			return uuid.UUID{}, DeleteTenantInCreatingError
		}

		err = txModel.UpdateTenantStatusByID(ctx, tenantID, model.TenantStatusDeleting)
		if err != nil {
			return uuid.UUID{}, eris.Wrap(err, "failed to update tenant status")
		}

		id, err := s.repository.CreateInTx(ctx, txModel, s.NewDeleteTenantWorkflow(tenantID))
		if err != nil {
			return uuid.UUID{}, eris.Wrap(err, "failed to create workflow")
		}
		return id, nil
	})
	if err != nil {
		return uuid.UUID{}, err
	}
	metrics.ReportWorkflowCreated(DeleteTenantWorkflowType)
	return workflowID, nil
}

func (s *DeleteTenantWorkflowService) preprocessRequest(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*DeleteTenantContext)
	var tenantModel = s.model

	tenant, err := tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}

	if tenant.Status != model.TenantStatusDeleting {
		return eris.Errorf("illegal state, tenant %d is not in deleting status", ctx.TenantID)
	}

	return nil
}

func (s *DeleteTenantWorkflowService) deleteExtensionsCompaction(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*DeleteTenantContext)

	ws, err := tenantext.NewServerlessCompactionWorkflowService(c,
		s.model,
		s.txProvider,
		s.cloudAgentProvider,
		s.storageFactory,
		ctx.TenantID,
		true, // delete
	)
	if err != nil {
		return eris.Wrapf(err, "failed to create serverless compaction workflow service for tenant %d", ctx.TenantID)
	}

	return ws.DisableIfExists(c)
}

func (s *DeleteTenantWorkflowService) deletePrivateLinks(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*DeleteTenantContext)

	var tenantModel = s.model
	tenant, err := tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return eris.Wrapf(err, "failed to retrieve tenant, tenantID: %v", ctx.GetTenantID())
	}

	privateLinks, err := tenantModel.GetPrivateLinksByTenantID(c, ctx.TenantID)
	if err != nil {
		return eris.Wrapf(err, "could not retrieve private links")
	}

	agent, err := s.cloudAgentProvider.GetAgent(c, tenant.ClusterID)
	if err != nil {
		return eris.Wrapf(err, "failed to retrieve cloud agent, clusterID: %v", tenant.ClusterID)
	}
	defer agent.Close()

	networkProvider, err := s.networkProviderFactory.NewProvider(c, tenant.ClusterID, agent)
	if err != nil {
		return eris.Wrapf(err, "failed to retrieve network provider, clusterID: %v", tenant.ClusterID)
	}

	var wg sync.WaitGroup
	errorsChan := make(chan error, len(privateLinks))
	for _, pl := range privateLinks {
		wg.Add(1)
		go func(privateLink *model.PrivateLink) {
			defer wg.Done()

			prevStatus := model.PrivateLinkStatus(privateLink.Status)
			privateLink.Status = string(model.PrivateLinkStatusDeleting)
			err = tenantModel.UpdatePrivateLink(c, *privateLink, prevStatus)
			if err != nil {
				errorsChan <- eris.Wrapf(err, "could not update private link status")
				return
			}

			err := networkProvider.DeletePrivateLinkAndWait(c, tenant.ResourceNamespace, privateLink.ID.String())
			if err != nil {
				errorsChan <- eris.Wrapf(err, "failed to delete private link, privatelinkID %v", privateLink.ID.String())
				return
			}

			err = s.account.DeletePrivateLinkInAccountService(c, privateLink.ID)
			if err != nil && eris.GetCode(err) != eris.CodeNotFound {
				errorsChan <- eris.Wrapf(err, "could not delete private link for account service")
				return
			}

			err = s.model.DeletePrivateLink(c, privateLink.ID)
			if err != nil {
				errorsChan <- eris.Wrapf(err, "could not delete private link")
				return
			}
		}(&pl)
	}

	wg.Wait()
	close(errorsChan)

	var plErrors error
	for err := range errorsChan {
		plErrors = eris.Join(err, plErrors)
	}

	return plErrors
}

func (s *DeleteTenantWorkflowService) deleteRisingWaveResource(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*DeleteTenantContext)
	var tenantModel = s.model

	tenant, err := tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}

	cluster, err := s.clusterProvider.GetCluster(c, tenant.ClusterID)
	if err != nil {
		return err
	}
	defer cluster.Close()

	err = cluster.DeleteRisingWaveResource(c, tenant)

	if err != nil {
		return eris.Wrap(err, "failed to delete tenant")
	}

	return nil
}

func (s *DeleteTenantWorkflowService) validateTenant(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*DeleteTenantContext)
	var tenantModel = s.model

	tenant, err := tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}

	cluster, err := s.clusterProvider.GetCluster(c, tenant.ClusterID)
	if err != nil {
		return err
	}
	defer cluster.Close()

	err = cluster.WaitForRisingWaveDeleted(c, tenant)
	if err != nil {
		return err
	}

	return nil
}

func (s *DeleteTenantWorkflowService) deleteStorage(c context.Context, w *workflow.Workflow) error {
	// delete the storage again
	// currently we delete storage twice, the first is in the `cluster.DeleteRisingWaveResource`
	// however, the pod may still be alive at that time, new s3 files may be created
	// so we need to delete it again after all pods are terminated
	var ctx = w.Context.(*DeleteTenantContext)
	var tenantModel = s.model

	tenant, err := tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}

	cluster, err := s.clusterProvider.GetCluster(c, tenant.ClusterID)
	if err != nil {
		return err
	}
	defer cluster.Close()

	err = cluster.DeleteStorage(c, tenant.ResourceNamespace, tenant.ID)
	if err != nil {
		return err
	}

	err = tenantModel.DeleteSnapshotsByTenantID(c, ctx.TenantID)
	if err != nil {
		return err
	}

	return nil
}

func (s *DeleteTenantWorkflowService) cleanupTask(c context.Context, w *workflow.Workflow) error {
	// delete storage deletion task created by cloudagent
	var ctx = w.Context.(*DeleteTenantContext)
	var tenantModel = s.model

	tenant, err := tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}

	cluster, err := s.clusterProvider.GetCluster(c, tenant.ClusterID)
	if err != nil {
		return err
	}
	defer cluster.Close()

	err = cluster.CleanupStorageDeletionTask(c, tenant.ResourceNamespace, tenant.ID)
	if err != nil {
		return err
	}

	return nil
}

func (s *DeleteTenantWorkflowService) newNotificationActivity(notificationEvent notifications.NotificationType) workflow.Activity {
	return activity.WrapDoError(func(c context.Context, w *workflow.Workflow) error {
		var ctx = w.Context.(*DeleteTenantContext)

		tenant, err := s.model.GetTenantByID(c, ctx.TenantID)
		if err != nil {
			return eris.Wrapf(err, "failed to get tenant by id %d, event: %s", ctx.TenantID, notificationEvent)
		}

		notificationBody, err := notifications.GetNotificationTemplate(
			notificationEvent,
			tenant,
		)
		if err != nil {
			logger.FromCtx(c).Named("DeleteTenantWorkflow").Warn(
				"failed to parse notification templates",
				zap.String("orgId", tenant.OrgID.String()),
				zap.Uint64("tenantId", tenant.ID),
				zap.Error(err),
			)
		}

		err = s.account.SendNotificationsToUser(c, notificationBody)
		if err != nil {
			logger.FromCtx(c).Named("DeleteTenantWorkflow").Warn(
				"failed to transmit deletion success notification",
				zap.String("orgId", tenant.OrgID.String()),
				zap.Uint64("tenantId", tenant.ID),
				zap.Error(err),
			)
		}

		return nil
	})
}

func (s *DeleteTenantWorkflowService) deleteAccountService(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*DeleteTenantContext)
	var tenantModel = s.model

	tenant, err := tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}

	accountTenant, err := s.account.GetTenantInAccountService(c, tenant.Region, tenant.ID)
	if err != nil {
		return err
	}
	if accountTenant == nil {
		return nil
	}

	err = s.account.DeleteTenantInAccountService(c, tenant.Region, tenant.ID)
	if err != nil {
		return err
	}

	return nil
}

func (s *DeleteTenantWorkflowService) deleteMsdbRecord(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*DeleteTenantContext)
	var tenantModel = s.model

	_, err := tenantModel.GetTenantByID(c, ctx.TenantID)
	if apierr.IsTenantNotExistError(err) {
		return nil
	}
	if err != nil {
		return err
	}

	err = tenantModel.DeleteTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}

	return nil
}
