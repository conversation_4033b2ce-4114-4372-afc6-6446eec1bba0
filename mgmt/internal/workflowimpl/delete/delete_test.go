package delete

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"

	account_mock "github.com/risingwavelabs/risingwave-cloud/internal/account/mock"
	"github.com/risingwavelabs/risingwave-cloud/internal/agent"
	mockagent "github.com/risingwavelabs/risingwave-cloud/internal/agent/provider/mock"
	"github.com/risingwavelabs/risingwave-cloud/internal/infra/network"
	"github.com/risingwavelabs/risingwave-cloud/internal/managedcluster/settings"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	model_mock "github.com/risingwavelabs/risingwave-cloud/internal/model/mock"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/querier"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenant/resourcespec"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl"
)

func TestDeletePrivateLink(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	var (
		resourceNamespace        = "rwc-g1hjudmn81fq9rhufo6bs0dcv5-test"
		clusterID         uint64 = 1
		tenantID          uint64 = 1
	)

	ctx := &DeleteTenantContext{
		TenantContextImpl: workflowimpl.TenantContextImpl{
			TenantID: tenantID,
		},
	}
	workflow := &workflow.Workflow{
		Context: ctx,
		Type:    DeletePrivateLinksState,
	}

	privateLinkID := uuid.New()
	mockAgentProvider := mockagent.NewMockCloudAgentProviderInterface(ctrl)
	mockAccountAgent := account_mock.NewMockAgent(ctrl)
	cloudAgent := &agent.CloudAgent{}

	q := model_mock.NewMockQuerier(ctrl)
	q.
		EXPECT().
		GetTenantById(gomock.Any(), gomock.Any()).
		Return(&querier.Tenant{
			ResourceNamespace: resourceNamespace,
			ClusterID:         clusterID,
			ID:                tenantID,
			ResourceVersion:   resourcespec.TenantResourceVersionV1,
			Resources:         []byte("{}"),
		}, nil)

	q.
		EXPECT().
		GetClusterById(gomock.Any(), clusterID).
		Return(&querier.ManagedCluster{
			Name: "testCluster",
		}, nil)

	q.
		EXPECT().
		GetPrivateLinksByTenantID(gomock.Any(), gomock.Any()).
		Return([]*querier.TenantPrivateLink{
			{
				ID:       privateLinkID,
				TenantID: tenantID,
				Status:   string(model.PrivateLinkStatusCreated),
			},
		}, nil)
	q.
		EXPECT().
		GetClusterSettings(gomock.Any(), gomock.Any()).
		Return([]*querier.ManagedClusterSetting{
			{
				Value: settings.CloudProviderLocal,
				Key:   settings.SettingsKeyCloudProvider,
			},
		}, nil)

	q.
		EXPECT().
		UpdatePrivateLinkOfStatus(gomock.Any(), gomock.Eq(querier.UpdatePrivateLinkOfStatusParams{
			ID:         privateLinkID,
			Status:     string(model.PrivateLinkStatusDeleting),
			Prevstatus: string(model.PrivateLinkStatusCreated),
		})).
		Return(pgconn.NewCommandTag("UPDATE 1"), nil)

	q.
		EXPECT().
		DeletePrivateLink(gomock.Any(), gomock.Eq(privateLinkID)).
		Return(nil)

	mockAgentProvider.
		EXPECT().
		GetAgent(gomock.Any(), gomock.Any()).
		Return(cloudAgent, nil)

	mockAccountAgent.
		EXPECT().
		DeletePrivateLinkInAccountService(context.Background(), gomock.Eq(privateLinkID)).
		Return(nil)

	workflowService := &DeleteTenantWorkflowService{
		cloudAgentProvider:     mockAgentProvider,
		account:                mockAccountAgent,
		networkProviderFactory: network.NewFactory(model.NewModel(q)),
		model:                  model.NewModel(q),
	}
	err := workflowService.deletePrivateLinks(context.Background(), workflow)
	require.NoError(t, err)
}
