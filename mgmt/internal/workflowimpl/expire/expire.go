package expire

import (
	"context"
	"fmt"
	"math"
	"strconv"
	"time"

	"github.com/risingwavelabs/risingwave-cloud/internal/infra/storage"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/modeltx"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow/builder"

	"github.com/google/uuid"
	"github.com/risingwavelabs/eris"
	"go.uber.org/zap"

	"github.com/risingwavelabs/risingwave-cloud/internal/account"
	agentprovider "github.com/risingwavelabs/risingwave-cloud/internal/agent/provider"
	"github.com/risingwavelabs/risingwave-cloud/internal/apiservice/errors"
	"github.com/risingwavelabs/risingwave-cloud/internal/config"
	rwmetrics "github.com/risingwavelabs/risingwave-cloud/internal/infra/metrics"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/extensions"
	"github.com/risingwavelabs/risingwave-cloud/internal/metrics"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow/activity"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow/fsm"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/delete"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/notifications"
	"github.com/risingwavelabs/risingwave-cloud/shared/logger"
	"github.com/risingwavelabs/risingwave-cloud/shared/ptr"
)

const ExpireTenantWorkflowType = "ExpireTenant"

const (
	CheckExpireState             = "CheckExpire"
	SendEmailExpireInOnedayState = "SendEmailExpireInOneday"
	PrepareExpireState           = "PrepareExpire"
	StopRisingwaveState          = "StopRisingwave"
	PostStopRisingWaveState      = "PostStopRisingWave"
	ValidateStopState            = "ValidateStop"

	CheckDeleteState      = "CheckDelete"
	PrepareDeleteState    = "PrepareDelete"
	DeleteRisingwaveState = "DeleteRisingwave"
	ValidateDeleteState   = "ValidateDelete"

	UsageCheckState = "UsageCheck"
)

const (
	SkipExpireEvent   fsm.Event = "SkipExpireEvent"
	SkipDeleteEvent   fsm.Event = "SkipDeleteEvent"
	TraceByUsageEvent fsm.Event = "TraceByUsageEvent"
)

type ExpireInfo struct { //nolint:revive
	ExpireAt *time.Time
	DeleteAt *time.Time
}

type ExpireTenantContext struct { //nolint:revive
	workflowimpl.TenantContextImpl
	DeleteWorkflowID uuid.UUID  `json:"DeleteWorkflowId"`
	ExpireAt         *time.Time `json:"ExpireAt"`
	DeleteAt         *time.Time `json:"DeleteAt"`
}

func (ctx *ExpireTenantContext) Labels() map[string]string {
	return map[string]string{
		"tenantId": strconv.FormatUint(ctx.TenantID, 10),
	}
}

type ExpireTenantWorkflowService struct { //nolint:revive
	builder                *builder.WorkflowBuilder
	repository             *workflow.Repository
	clusterProvider        k8s.IKubernetesClusterProvider
	deleteSvc              *delete.DeleteTenantWorkflowService
	accountAgent           account.Agent
	agentProvider          agentprovider.CloudAgentProviderInterface
	metricsProviderFactory rwmetrics.Factory
	compaction             extensions.ServerlessCompaction
	tenantModel            *model.Model
	txProvider             modeltx.TxProvider
	storageFactory         storage.FactoryInterface
}

func NewExpireTenantWorkflowService(
	repository *workflow.Repository,
	clusterProvider k8s.IKubernetesClusterProvider,
	deleteSvc *delete.DeleteTenantWorkflowService,
	accountAgent account.Agent,
	metricsProviderFactory rwmetrics.Factory,
	compaction extensions.ServerlessCompaction,
	tenantModel *model.Model,
	txProvider modeltx.TxProvider,
	storageFactory storage.FactoryInterface,
) *ExpireTenantWorkflowService {
	s := &ExpireTenantWorkflowService{
		repository:             repository,
		clusterProvider:        clusterProvider,
		deleteSvc:              deleteSvc,
		accountAgent:           accountAgent,
		agentProvider:          agentprovider.NewCloudAgentProvider(tenantModel),
		metricsProviderFactory: metricsProviderFactory,
		compaction:             compaction,
		tenantModel:            tenantModel,
		txProvider:             txProvider,
		storageFactory:         storageFactory,
	}

	var (
		// for Expire branch
		CheckExpireActivity             = activity.WrapDoResult(s.checkExpire)
		SendEmailExpireInOnedayActivity = activity.WrapDoResult(s.sendEmailExpireInOneday)
		PrepareExpireActivity           = activity.WrapDoResult(s.prepareExpire)
		StopRisingwaveActivity          = activity.WrapDoError(s.stopRisingwave)
		PostStopRisingWaveActivity      = activity.WrapDoError(s.postStopRisingWave)
		ValidateStopActivity            = activity.WrapDoError(s.validateStop)

		// for Delete branch
		CheckDeleteActivity      = activity.WrapDoResult(s.checkDelete)
		PrepareDeleteActivity    = activity.WrapDoResult(s.prepareDelete)
		DeleteRisingwaveActivity = activity.WrapDoError(s.deleteRisingwave)
		ValidateDeleteActivity   = activity.WrapDoResult(s.validateDelete)

		// for UsageCheck branch
		CheckUsageActivity = activity.WrapDoResult(s.checkUsage)
	)

	var (
		retry        = activity.RetryWrapper(repository, 3)
		fail         = activity.FailWorkflowWrapper
		contToDelete = activity.ContinueWithResultWrapper(workflow.ActivityResult{
			Type:     workflow.ActivityCompletedResultType,
			FsmEvent: SkipExpireEvent,
		})
	)

	s.builder = builder.NewBuilder(ExpireTenantWorkflowType).
		// expire
		Next(CheckExpireState).On(SkipExpireEvent, CheckDeleteState).On(TraceByUsageEvent, UsageCheckState).
		/* */ Activity(CheckExpireActivity).Wrap(retry).Wrap(fail).
		Next(SendEmailExpireInOnedayState).On(SkipExpireEvent, CheckDeleteState).
		/* */ Activity(SendEmailExpireInOnedayActivity).Wrap(retry).Wrap(contToDelete).
		Next(PrepareExpireState).On(SkipExpireEvent, CheckDeleteState).
		/* */ Activity(PrepareExpireActivity).Wrap(retry).Wrap(contToDelete).
		Next(StopRisingwaveState).
		/* */ Activity(StopRisingwaveActivity).Wrap(retry).Wrap(contToDelete).
		/* */ Defer(PostStopRisingWaveState, activity.WrapDefer(retry(PostStopRisingWaveActivity), repository)).
		/* */ On(SkipExpireEvent, CheckDeleteState).
		Next(ValidateStopState).On(SkipExpireEvent, CheckDeleteState).
		/* */ Activity(ValidateStopActivity).Wrap(retry).Wrap(contToDelete).
		// delete
		Next(CheckDeleteState).On(SkipDeleteEvent, workflow.SucceededState).
		/* */ Activity(CheckDeleteActivity).Wrap(retry).Wrap(fail).
		Next(PrepareDeleteState).
		/* */ Activity(PrepareDeleteActivity).Wrap(retry).Wrap(fail).
		Next(DeleteRisingwaveState).
		/* */ Activity(DeleteRisingwaveActivity).Wrap(retry).Wrap(fail).
		Next(ValidateDeleteState).
		/* */ Activity(ValidateDeleteActivity).Wrap(retry).Wrap(fail).
		Succeed().
		// usage check is optional step between CheckExpireState and PrepareExpireState
		From(UsageCheckState).
		/* */ Activity(CheckUsageActivity).Wrap(retry).Wrap(fail).
		/* */ Next(PrepareExpireState)

	contextProvider := func() any { return &ExpireTenantContext{} }
	repository.Registry.Register(ExpireTenantWorkflowType, s.builder.WorkflowConstructor(contextProvider))

	return s
}

func (s *ExpireTenantWorkflowService) NewExpireTenantWorkflow(tenantID uint64, expireAt *time.Time, deleteAt *time.Time) *workflow.Workflow {
	ctx := &ExpireTenantContext{
		TenantContextImpl: workflowimpl.NewTenantContext(tenantID),
		ExpireAt:          expireAt,
		DeleteAt:          deleteAt,
	}
	var w = s.builder.Build()
	w.Context = ctx

	return w
}

func (s *ExpireTenantWorkflowService) GetExpireInfo(ctx context.Context, tenantID uint64) (*ExpireInfo, error) {
	w, err := s.repository.GetRunningWorkflowOfTenant(ctx, ExpireTenantWorkflowType, tenantID)
	if err != nil {
		return nil, eris.Wrap(err, "failed to get expire workflow")
	}
	if w == nil {
		return nil, nil
	}
	return &ExpireInfo{
		ExpireAt: w.Context.(*ExpireTenantContext).ExpireAt,
		DeleteAt: w.Context.(*ExpireTenantContext).DeleteAt,
	}, nil
}

func (s *ExpireTenantWorkflowService) RunExpireTenantWorkflow(ctx context.Context, tenantID uint64) (uuid.UUID, error) {
	w := s.NewExpireTenantWorkflow(tenantID, nil, nil)
	metrics.ReportWorkflowCreated(ExpireTenantWorkflowType)
	return s.repository.Create(ctx, w)
}

func (s *ExpireTenantWorkflowService) RunExpireTenantWorkflowWithTime(ctx context.Context, tenantID uint64, expireAt *time.Time, deleteAt *time.Time) (uuid.UUID, error) {
	w := s.NewExpireTenantWorkflow(tenantID, expireAt, deleteAt)
	metrics.ReportWorkflowCreated(ExpireTenantWorkflowType)
	return s.repository.Create(ctx, w)
}

func (s *ExpireTenantWorkflowService) CancelExpireWorkflow(ctx context.Context, tenantID uint64) error {
	w, err := s.repository.GetRunningWorkflowOfTenant(ctx, ExpireTenantWorkflowType, tenantID)
	if err != nil {
		return eris.Wrap(err, "failed to get expire workflow")
	}
	if w == nil {
		return nil
	}
	err = s.repository.ForceCancel(ctx, w.ID)
	if err != nil {
		return eris.Wrap(err, "failed to cancel expire workflow")
	}
	return nil
}

func (s *ExpireTenantWorkflowService) checkExpire(c context.Context, w *workflow.Workflow) workflow.ActivityResult {
	var ctx = w.Context.(*ExpireTenantContext)
	var tenantModel = s.tenantModel

	tenant, err := tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return workflow.FailActivityWithError(err)
	}

	// if the expiration date is not specified, decide it according to expiration mechanism
	if ctx.ExpireAt == nil && ctx.DeleteAt == nil {
		validityDays := tenant.ValidityDays
		retentionDays := tenant.RetentionDays
		now := time.Now()

		tier := config.GetResourceDef().GetTierByID(tenant.TierID)
		if tier.ExpirationMechanism == config.ExpirationMechanismNone {
			return workflow.CompleteActivityWithEvent(SkipExpireEvent)
		}
		if tier.ExpirationMechanism == config.ExpirationMechanismUsage {
			return workflow.ActivityResult{
				Type:     workflow.ActivityCompletedResultType,
				FsmEvent: TraceByUsageEvent,
				DelayAt:  ptr.Ptr(now.Add(usageCheckDuration)),
			}
		}

		if validityDays != 0 {
			expireAt := now.Add(time.Duration(validityDays) * 24 * time.Hour)
			ctx.ExpireAt = &expireAt
		}

		if retentionDays != 0 {
			deleteAt := now.Add(time.Duration(validityDays+retentionDays) * 24 * time.Hour)
			ctx.DeleteAt = &deleteAt
		}
	}

	if ctx.ExpireAt == nil {
		return workflow.CompleteActivityWithEvent(SkipExpireEvent)
	}

	// send email one day before tenant is expired
	delayAt := ctx.ExpireAt.Add(-1 * 24 * time.Hour)
	return workflow.CompleteActivityWithDelay(delayAt)
}

func (s *ExpireTenantWorkflowService) sendEmailExpireInOneday(c context.Context, w *workflow.Workflow) workflow.ActivityResult {
	var ctx = w.Context.(*ExpireTenantContext)

	// check tenant exist
	tenant, err := s.tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil && errors.IsTenantNotExistError(err) {
		return workflow.CompleteWorkflow()
	}
	if err != nil {
		return workflow.FailActivityWithError(err)
	}

	// send email
	type EmailData struct {
		TenantName string
		Timestamp  string
	}
	err = s.accountAgent.SendEmailToUser(
		c,
		tenant.OrgID,
		"[RisingWave Cloud] 1 day until expiration",
		"ExpirationInOneday",
		EmailData{
			TenantName: tenant.TenantName,
			Timestamp:  ctx.ExpireAt.Format(time.RFC3339),
		},
	)
	if err != nil {
		return workflow.FailActivityWithError(err)
	}

	return workflow.CompleteActivityWithDelay(*ctx.ExpireAt)
}

func (s *ExpireTenantWorkflowService) prepareExpire(c context.Context, w *workflow.Workflow) workflow.ActivityResult {
	var ctx = w.Context.(*ExpireTenantContext)

	// check tenant exist
	tenant, err := s.tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil && errors.IsTenantNotExistError(err) {
		return workflow.CompleteWorkflow()
	}
	if err != nil {
		return workflow.FailActivityWithError(err)
	}

	// send email
	err = s.sendExpireEmailOrNotification(c, tenant, ctx)
	if err != nil {
		return workflow.FailActivityWithError(err)
	}

	// update status
	err = s.tenantModel.UpdateTenantStatusByID(c, ctx.TenantID, model.TenantStatusExpired)
	if err != nil {
		return workflow.FailActivityWithError(eris.Wrap(err, "failed to update tenant status"))
	}

	return workflow.CompleteActivity()
}

func (s *ExpireTenantWorkflowService) sendExpireEmailOrNotification(ctx context.Context, tenant model.Tenant, expireTenantContext *ExpireTenantContext) error {
	tier := config.GetResourceDef().GetTierByID(tenant.TierID)
	if tier.ExpirationMechanism == config.ExpirationMechanismUsage {
		tmpl, err := notifications.GetNotificationTemplate(notifications.TenantPaused, tenant)
		if err != nil {
			return err
		}
		return s.accountAgent.SendNotificationsToUser(ctx, tmpl)
	}
	type EmailData struct {
		TenantName string
		Timestamp  string
	}
	return s.accountAgent.SendEmailToUser(ctx, tenant.OrgID, "[RisingWave Cloud] Cluster expired", "Expiration", EmailData{
		TenantName: tenant.TenantName,
		Timestamp:  expireTenantContext.DeleteAt.Format(time.RFC3339),
	})
}

func (s *ExpireTenantWorkflowService) stopRisingwave(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ExpireTenantContext)
	var tenantModel = s.tenantModel

	tenant, err := tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}
	cluster, err := s.clusterProvider.GetCluster(c, tenant.ClusterID)
	if err != nil {
		return err
	}
	defer cluster.Close()

	err = cluster.StopRisingWaveResource(c, tenant)
	if err != nil {
		return eris.Wrap(err, "failed to stop tenant")
	}

	ws, err := tenantext.NewServerlessCompactionWorkflowService(
		c,
		s.tenantModel,
		s.txProvider,
		s.agentProvider,
		s.storageFactory,
		ctx.TenantID,
		true,
	)
	if err != nil {
		return eris.Wrapf(err, "failed to create serverless compaction workflow service for tenant %d", ctx.TenantID)
	}

	return ws.DisableIfExists(c)
}

func (s *ExpireTenantWorkflowService) postStopRisingWave(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ExpireTenantContext)
	var tenantModel = s.tenantModel

	tenant, err := tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}
	cluster, err := s.clusterProvider.GetCluster(c, tenant.ClusterID)
	if err != nil {
		return err
	}
	defer cluster.Close()

	err = cluster.PostStopRisingWaveResource(c, tenant)
	if err != nil {
		return eris.Wrap(err, "failed to clean resources after stoppping RisingWave")
	}
	return nil
}

func (s *ExpireTenantWorkflowService) validateStop(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ExpireTenantContext)
	var tenantModel = s.tenantModel

	tenant, err := tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}
	cluster, err := s.clusterProvider.GetCluster(c, tenant.ClusterID)
	if err != nil {
		return err
	}
	defer cluster.Close()

	err = cluster.WaitForPodsTerminated(c, tenant)
	if err != nil {
		return eris.Wrap(err, "failed to wait for the pods end")
	}

	return nil
}

func (s *ExpireTenantWorkflowService) checkDelete(c context.Context, w *workflow.Workflow) workflow.ActivityResult {
	var ctx = w.Context.(*ExpireTenantContext)
	var tenantModel = s.tenantModel

	// check tenant exist
	_, err := tenantModel.GetTenantByID(c, ctx.TenantID)
	if errors.IsTenantNotExistError(err) {
		logger.FromCtx(c).Named("ExpireWorkflow").Info("Tenant doesn't exist, skip expire")
		return workflow.CompleteWorkflow()
	}
	if err != nil {
		return workflow.FailActivityWithError(err)
	}

	deletedAt := ctx.DeleteAt
	if deletedAt == nil {
		return workflow.CompleteActivityWithEvent(SkipDeleteEvent)
	}

	return workflow.CompleteActivityWithDelay(*deletedAt)
}

func (s *ExpireTenantWorkflowService) prepareDelete(c context.Context, w *workflow.Workflow) workflow.ActivityResult {
	var ctx = w.Context.(*ExpireTenantContext)
	var tenantModel = s.tenantModel

	_, err := tenantModel.GetTenantByID(c, ctx.TenantID)
	if errors.IsTenantNotExistError(err) {
		logger.FromCtx(c).Named("ExpireWorkflow").Info("Tenant doesn't exist, skip expire")
		return workflow.CompleteWorkflow()
	}
	if err != nil {
		return workflow.FailActivityWithError(err)
	}

	return workflow.CompleteActivity()
}

func (s *ExpireTenantWorkflowService) deleteRisingwave(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ExpireTenantContext)

	emptyID := uuid.UUID{}
	if ctx.DeleteWorkflowID != emptyID {
		return nil
	}

	id, err := s.deleteSvc.DeleteTenantWithWorkflow(c, ctx.TenantID)
	if err != nil {
		return eris.Wrap(err, "failed to trigger delete workflow")
	}

	logger.FromCtx(c).Named("ExpireWorkflow").Info("trigger delete workflow", zap.String("delete_workflow_id", id.String()))
	ctx.DeleteWorkflowID = id

	return nil
}

const WaitDeleteWorkflowTimeoutMinutes = 15

func (s *ExpireTenantWorkflowService) validateDelete(c context.Context, w *workflow.Workflow) workflow.ActivityResult {
	var ctx = w.Context.(*ExpireTenantContext)

	// TODO new mechanism to wait other workflow finished
	deleteWorkflow, err := s.repository.Get(c, ctx.DeleteWorkflowID)
	if err != nil {
		return workflow.FailActivityWithError(err)
	}
	if deleteWorkflow.State != workflow.RunningWorkflowState {
		return workflow.CompleteActivity()
	}

	if getReenterCount(w) > WaitDeleteWorkflowTimeoutMinutes {
		return workflow.FailActivityWithError(eris.New("Timeout to wait DeleteTenantWorkflow"))
	}
	return workflow.ReenterActivityWithDelay(time.Now().Add(time.Minute))
}

func getReenterCount(w *workflow.Workflow) int {
	var count = 0
	for i := len(w.Events) - 1; i >= 0; i-- {
		event := w.Events[i]
		if event.Type == workflow.TransitionEventType {
			attr := event.Attributes.(workflow.TransitionAttributes)
			if attr.StateBefore == w.Fsm.GetState() && attr.FsmEvent == workflow.NotDoneEvent {
				count++
			} else {
				return count
			}
		}
	}
	return math.MaxInt
}

// TODO: validityDays unit is day, we hard code 30 minutes here. refactor this part later.
const usageCheckDuration = 30 * time.Minute

const noUsagePeriod = 120 * time.Minute

func (s *ExpireTenantWorkflowService) checkUsage(c context.Context, w *workflow.Workflow) workflow.ActivityResult {
	var ctx = w.Context.(*ExpireTenantContext)

	tenant, err := s.tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil && errors.IsTenantNotExistError(err) {
		return workflow.CompleteWorkflow()
	}
	if err != nil {
		return workflow.FailActivityWithError(err)
	}

	if tenant.Status != model.TenantStatusRunning {
		return workflow.ReenterActivityWithDelay(time.Now().Add(usageCheckDuration))
	}
	hasUsage, err := s.checkThroughputUsage(c, tenant)
	if err != nil {
		logger.FromCtx(c).Named("ExpireWorkflow").Info("failed to check throughput usage", zap.Error(err))
		return workflow.ReenterActivityWithDelay(time.Now().Add(usageCheckDuration))
	}
	if hasUsage {
		return workflow.ReenterActivityWithDelay(time.Now().Add(usageCheckDuration))
	}

	now := time.Now()
	ctx.ExpireAt = &now
	if tenant.RetentionDays != 0 {
		deleteAt := now.Add(time.Duration(tenant.RetentionDays) * 24 * time.Hour)
		ctx.DeleteAt = &deleteAt
	}
	return workflow.CompleteActivity()
}

func (s *ExpireTenantWorkflowService) checkThroughputUsage(ctx context.Context, tenant model.Tenant) (bool, error) {
	cloudAgent, err := s.agentProvider.GetAgent(ctx, tenant.ClusterID)
	if err != nil {
		return false, eris.Wrap(err, "cannot create cloud agent")
	}
	defer func() {
		_ = cloudAgent.Close()
	}()
	metricsProvider, err := s.metricsProviderFactory.NewProvider(ctx, tenant.ClusterID, cloudAgent)
	if err != nil {
		return false, eris.Wrap(err, "cannot create cluster metrics provider")
	}
	period := noUsagePeriod.String()
	now := time.Now()
	// TODO(CLOUD-2469): we only support QueryRange now, only query 1 data point here.
	res, err := metricsProvider.QueryRange(ctx, rwmetrics.RangeInput{
		Query: fmt.Sprintf(
			`sum(increase(stream_source_output_rows_counts{namespace="%s", risingwave_name="%s"}[%s]))`,
			tenant.ResourceNamespace, k8s.DefaultRisingWaveName, period,
		),
		StartTime: now.Add(-1 * time.Minute),
		EndTime:   now,
		Step:      "45s",
	})
	if err != nil {
		return false, err
	}
	for _, item := range res.Items {
		for _, point := range item.Points {
			if point.Value != 0 {
				return true, nil
			}
		}
	}
	// No data also means no throughput
	return false, nil
}
