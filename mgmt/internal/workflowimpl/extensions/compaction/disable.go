package compaction

import (
	"context"

	"github.com/samber/lo"

	"github.com/google/uuid"
	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/risingwave-cloud/internal/infra/storage"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenant/resourcespec"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow/builder"

	"github.com/risingwavelabs/risingwave-cloud/internal/account"
	agentprovider "github.com/risingwavelabs/risingwave-cloud/internal/agent/provider"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/extensions"
	"github.com/risingwavelabs/risingwave-cloud/internal/metrics"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/modeltx"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow/activity"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl"
)

const DisableWorkflowType = "DisableExtensionsCompaction"

const (
	preprocessingDisableRequestState = "PreprocessingDisableRequest"
	disableState                     = "DisableExtensionsCompaction"
	postDisableState                 = "PostDisableExtensionsCompaction"
	validationDisableState           = "ValidatingDisableExtensionsCompaction"
)

type DisableContext struct {
	workflowimpl.TenantContextImpl

	CompactorReplicas uint32 `json:"compactor_replicas,omitempty"`
}

type DisableWorkflowService struct {
	builder    *builder.WorkflowBuilder
	repository *workflow.Repository
	account    account.Agent

	tenantModel        *model.Model
	txProvider         modeltx.TxProvider
	compaction         extensions.ServerlessCompaction
	cloudAgentProvider agentprovider.CloudAgentProviderInterface
	storageFactory     storage.FactoryInterface
}

func NewDisableWorkflowService(
	repository *workflow.Repository,
	account account.Agent,
	tenantModel *model.Model,
	txProvider modeltx.TxProvider,
	compaction extensions.ServerlessCompaction,
	cloudAgentProvider agentprovider.CloudAgentProviderInterface,
	storageFactory storage.FactoryInterface,
) *DisableWorkflowService {
	s := &DisableWorkflowService{
		repository:         repository,
		account:            account,
		tenantModel:        tenantModel,
		txProvider:         txProvider,
		compaction:         compaction,
		cloudAgentProvider: cloudAgentProvider,
		storageFactory:     storageFactory,
	}
	var (
		PreprocessRequestActivity               = activity.WrapDoError(s.preprocessRequest)
		DisableExtensionsCompactionActivity     = activity.WrapDoError(s.disableExtensionsCompaction)
		PostDisableExtensionsCompactionActivity = activity.WrapDoError(s.postDisableExtensionsCompaction)
		ValidateRisingwaveExtensionsActivity    = activity.WrapDoError(s.validateRisingwaveExtensionsStopped)
	)

	retry := activity.RetryWrapper(repository, 3)
	s.builder = builder.NewBuilder(DisableWorkflowType).
		Next(preprocessingDisableRequestState).Activity(PreprocessRequestActivity).Wrap(retry).
		Next(disableState).Activity(DisableExtensionsCompactionActivity).Wrap(retry).
		Defer(postDisableState, activity.WrapDefer(retry(PostDisableExtensionsCompactionActivity), repository)).
		Next(validationDisableState).Activity(ValidateRisingwaveExtensionsActivity).Wrap(retry).
		Succeed()

	contextProvider := func() any { return &DisableContext{} }
	repository.Registry.Register(DisableWorkflowType, s.builder.WorkflowConstructor(contextProvider))

	return s
}

func (s *DisableWorkflowService) NewDisableWorkflow(tenantID uint64, compactorReplicas uint32) *workflow.Workflow {
	ctx := &DisableContext{
		TenantContextImpl: workflowimpl.NewTenantContext(tenantID),
		CompactorReplicas: compactorReplicas,
	}
	var w = s.builder.Build()
	w.Context = ctx
	return w
}

func (s *DisableWorkflowService) StartDisableWorkflow(ctx context.Context, tenantID uint64, compactorReplicas uint32) (uuid.UUID, error) {
	ctx, cancel := context.WithTimeout(ctx, 5*model.TransactionTimeout())
	defer cancel()
	workflowID, err := modeltx.Query(ctx, s.txProvider, func(ctx context.Context, txModel *model.Model) (uuid.UUID, error) {
		err := txModel.CompareAndUpdateTenantStatusByIDAndStatuses(ctx, tenantID, model.TenantStatusExtensionCompactionDisabling, []string{model.TenantStatusRunning})
		if err != nil {
			return uuid.UUID{}, eris.Wrap(err, "failed to update tenant status")
		}

		err = txModel.CompareAndUpdateTenantExtensionStatusByTenantIDAndResourceTypeAndStatuses(ctx, tenantID, model.ExtensionsResourceTypeCompaction, model.ExtensionStatusDisabling, []string{model.ExtensionStatusRunning})
		if err != nil {
			return uuid.UUID{}, eris.Wrap(err, "failed to update tenant extension status")
		}

		tenant, err := txModel.GetTenantByID(ctx, tenantID)
		if err != nil {
			return uuid.UUID{}, eris.Wrap(err, "failed to get tenant by ID")
		}
		if tenant.Resources.IsStandalone() {
			return uuid.UUID{}, eris.Errorf("tenant %d is a standalone tenant, cannot disable extensions compaction", tenantID).WithCode(eris.CodeFailedPrecondition)
		}

		// Patch the tenant context with compactor replicas.
		resources := lo.Must(tenant.Resources.AsTenantResourceSpecV1Regular())
		resources.CompactorReplica = int32(compactorReplicas)
		err = txModel.UpdateTenantResources(ctx, tenantID, resourcespec.FromValidTenantResourceSpec(&resources))
		if err != nil {
			return uuid.UUID{}, eris.Wrap(err, "failed to update tenant resources")
		}

		id, err := s.repository.CreateInTx(ctx, txModel, s.NewDisableWorkflow(tenantID, compactorReplicas))
		if err != nil {
			return uuid.UUID{}, eris.Wrap(err, "failed to create workflow")
		}
		return id, nil
	})
	if err != nil {
		return uuid.UUID{}, err
	}
	metrics.ReportWorkflowCreated(DisableWorkflowType)
	return workflowID, nil
}

func (s *DisableWorkflowService) preprocessRequest(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*DisableContext)

	tenant, err := s.tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}

	if tenant.Status != model.TenantStatusExtensionCompactionDisabling {
		return eris.Errorf("illegal state, tenant %d not in %s status", tenant.ID, model.TenantStatusExtensionCompactionDisabling)
	}

	// Scale the compactor up to replicas in the request context.
	err = s.compaction.ScaleCompactor(c, tenant, ctx.CompactorReplicas)
	if err != nil {
		return eris.Wrapf(err, "failed to scale back compactor for tenant %d", ctx.TenantID)
	}

	return nil
}

func (s *DisableWorkflowService) disableExtensionsCompaction(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*DisableContext)

	ws, err := tenantext.NewServerlessCompactionWorkflowService(
		c,
		s.tenantModel,
		s.txProvider,
		s.cloudAgentProvider,
		s.storageFactory,
		ctx.TenantID,
		false,
	)
	if err != nil {
		return eris.Wrapf(err, "failed to create serverless compaction workflow service for tenant %d", ctx.TenantID)
	}

	return ws.DisableIfExists(c)
}

func (s *DisableWorkflowService) postDisableExtensionsCompaction(_ context.Context, _ *workflow.Workflow) error {
	return nil
}

func (s *DisableWorkflowService) validateRisingwaveExtensionsStopped(c context.Context, w *workflow.Workflow) error {
	ctx := w.Context.(*DisableContext)
	tenantID := ctx.TenantID
	err := modeltx.Exec(c, s.txProvider, func(ctx context.Context, txModel *model.Model) error {
		err := txModel.CompareAndUpdateTenantStatusByIDAndStatuses(ctx, tenantID, model.TenantStatusRunning, []string{model.TenantStatusExtensionCompactionDisabling})
		if err != nil {
			return eris.Wrap(err, "failed to update tenant status")
		}
		return nil
	})
	if err != nil {
		return err
	}

	return nil
}
