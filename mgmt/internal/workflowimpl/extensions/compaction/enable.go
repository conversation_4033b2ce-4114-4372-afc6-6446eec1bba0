package compaction

import (
	"context"

	"github.com/samber/lo"

	"github.com/google/uuid"

	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/risingwave-cloud/internal/account"
	agentprovider "github.com/risingwavelabs/risingwave-cloud/internal/agent/provider"
	"github.com/risingwavelabs/risingwave-cloud/internal/infra/storage"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/extensions"
	"github.com/risingwavelabs/risingwave-cloud/internal/metrics"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/modeltx"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenant/resourcespec"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow/activity"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow/builder"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl"
	"github.com/risingwavelabs/risingwave-cloud/shared/logger"
	"github.com/risingwavelabs/risingwave-cloud/shared/ptr"
)

const EnableWorkflowType = "EnableExtensionsCompaction"

const (
	preprocessingEnableRequestState = "PreprocessingEnableRequest"
	enableState                     = "EnableExtensionsCompaction"
	postEnableState                 = "PostEnableExtensionsCompaction"
	validationState                 = "ValidatingEnableExtensionsCompaction"
	cleanUpState                    = "CleanUpExtensionsCompaction"
	postDeleteState                 = "PostDeleteExtensionsCompaction"
)

type EnableContext struct {
	workflowimpl.TenantContextImpl
	CompactorReplicasToRollBack uint32 `json:"compactor_replicas_to_roll_back,omitempty"`
}

type EnableWorkflowService struct {
	builder    *builder.WorkflowBuilder
	repository *workflow.Repository
	account    account.Agent

	tenantModel        *model.Model
	txProvider         modeltx.TxProvider
	storageFactory     storage.FactoryInterface
	compaction         extensions.ServerlessCompaction
	cloudAgentProvider agentprovider.CloudAgentProviderInterface
}

func NewEnableWorkflowService(
	repository *workflow.Repository,
	account account.Agent,
	tenantModel *model.Model,
	txProvider modeltx.TxProvider,
	compaction extensions.ServerlessCompaction,
	cloudAgentProvider agentprovider.CloudAgentProviderInterface,
	storageFactory storage.FactoryInterface,
) *EnableWorkflowService {
	s := &EnableWorkflowService{
		repository:         repository,
		account:            account,
		tenantModel:        tenantModel,
		txProvider:         txProvider,
		compaction:         compaction,
		cloudAgentProvider: cloudAgentProvider,
		storageFactory:     storageFactory,
	}
	var (
		PreprocessRequestActivity              = activity.WrapDoError(s.preprocessRequest)
		EnableExtensionsCompactionActivity     = activity.WrapDoError(s.enableExtensionsCompaction)
		PostEnableExtensionsCompactionActivity = activity.WrapDoError(s.postEnableExtensionsCompaction)
		ValidateExtensionsCompactionActivity   = activity.WrapDoError(s.validateExtensionsCompactionReady)
		CleanUpExtensionsCompactionActivity    = activity.WrapDoResult(s.cleanUpExtensionsCompaction)
		PostDeleteExtensionsCompactionActivity = activity.WrapDoError(s.postDeleteExtensionsCompaction)
	)

	retry := activity.RetryWrapper(repository, 3)
	s.builder = builder.NewBuilder(EnableWorkflowType).
		Next(preprocessingEnableRequestState).Activity(PreprocessRequestActivity).Wrap(retry).On(workflow.ErrorEvent, cleanUpState).
		Next(enableState).Activity(EnableExtensionsCompactionActivity).Wrap(retry).
		Defer(postEnableState, activity.WrapDefer(retry(PostEnableExtensionsCompactionActivity), repository)).On(workflow.ErrorEvent, cleanUpState).
		Next(validationState).Activity(ValidateExtensionsCompactionActivity).Wrap(retry).On(workflow.ErrorEvent, cleanUpState).
		Succeed().
		From(cleanUpState).Activity(CleanUpExtensionsCompactionActivity).Wrap(retry).
		Defer(postDeleteState, activity.WrapDefer(retry(PostDeleteExtensionsCompactionActivity), repository)).On(workflow.ErrorEvent, workflow.FailedState).
		Fail()

	contextProvider := func() any { return &EnableContext{} }
	repository.Registry.Register(EnableWorkflowType, s.builder.WorkflowConstructor(contextProvider))

	return s
}

func (s *EnableWorkflowService) StartEnableWorkflow(ctx context.Context, tenantID uint64) (uuid.UUID, error) {
	ctx, cancel := context.WithTimeout(ctx, 5*model.TransactionTimeout())
	defer cancel()
	workflowID, err := modeltx.Query(ctx, s.txProvider, func(ctx context.Context, txModel *model.Model) (uuid.UUID, error) {
		err := txModel.CompareAndUpdateTenantStatusByIDAndStatuses(ctx, tenantID, model.TenantStatusExtensionCompactionEnabling, []string{model.TenantStatusRunning})
		if err != nil {
			return uuid.UUID{}, eris.Wrap(err, "failed to update tenant status")
		}

		tenant, err := txModel.GetTenantByID(ctx, tenantID)
		if err != nil {
			return uuid.UUID{}, eris.Wrap(err, "failed to get tenant")
		}

		// Get the latest resource spec
		resource := tenant.Resources
		if resource.IsStandalone() {
			return uuid.UUID{}, eris.Errorf("tenant %d is standalone, cannot enable compaction extension", tenantID)
		}

		t := lo.Must(resource.AsTenantResourceSpecV1Regular())
		compactorReplicas := t.CompactorReplica

		// Update the RW resource spec, set compactor replica to 0
		t.CompactorReplica = 0
		resource = resourcespec.FromValidTenantResourceSpec(&t)
		err = txModel.UpdateTenantResources(ctx, tenantID, resource)
		if err != nil {
			return uuid.UUID{}, eris.Wrap(err, "failed to update tenant resources")
		}

		id, err := s.repository.CreateInTx(ctx, txModel, s.NewEnableWorkflow(tenantID, uint32(compactorReplicas)))
		if err != nil {
			return uuid.UUID{}, eris.Wrap(err, "failed to create workflow")
		}
		return id, nil
	})
	if err != nil {
		return uuid.UUID{}, err
	}
	metrics.ReportWorkflowCreated(EnableWorkflowType)
	return workflowID, nil
}

func (s *EnableWorkflowService) NewEnableWorkflow(tenantID uint64, compactorReplicas uint32) *workflow.Workflow {
	ctx := &EnableContext{
		TenantContextImpl:           workflowimpl.NewTenantContext(tenantID),
		CompactorReplicasToRollBack: compactorReplicas,
	}
	var w = s.builder.Build()
	w.Context = ctx
	return w
}

func (s *EnableWorkflowService) preprocessRequest(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*EnableContext)
	var tenantModel = s.tenantModel

	tenant, err := tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}

	if tenant.Status != model.TenantStatusExtensionCompactionEnabling {
		return eris.Errorf("illegal state, tenant %d not in %s status", tenant.ID, model.TenantStatusExtensionCompactionEnabling)
	}

	// Shutdown all the compactor pods before enabling the compaction extension.
	return s.compaction.ScaleCompactor(c, tenant, 0)
}

func (s *EnableWorkflowService) enableExtensionsCompaction(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*EnableContext)

	ws, err := tenantext.NewServerlessCompactionWorkflowService(
		c,
		s.tenantModel,
		s.txProvider,
		s.cloudAgentProvider,
		s.storageFactory,
		ctx.TenantID,
		true,
	)
	if err != nil {
		return eris.Wrapf(err, "failed to create serverless compaction workflow service for tenant %d", ctx.TenantID)
	}

	return ws.EnableOnAbsenceOrUpdateIfExists(c, tenantext.ServerlessCompactionParams{
		MaxConcurrency: ptr.Ptr(16), // TODO: make this configurable
	})
}

func (s *EnableWorkflowService) postEnableExtensionsCompaction(_ context.Context, _ *workflow.Workflow) error {
	return nil
}

func (s *EnableWorkflowService) validateExtensionsCompactionReady(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*EnableContext)
	tenant, err := s.tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}

	err = modeltx.Exec(c, s.txProvider, func(ctx context.Context, txModel *model.Model) error {
		err = txModel.CompareAndUpdateTenantStatusByIDAndStatuses(ctx, tenant.ID, model.TenantStatusRunning, []string{model.TenantStatusExtensionCompactionEnabling})
		if err != nil {
			return eris.Wrap(err, "failed to update tenant status")
		}
		return nil
	})
	if err != nil {
		return err
	}

	return nil
}

func (s *EnableWorkflowService) cleanUpExtensionsCompaction(c context.Context, w *workflow.Workflow) workflow.ActivityResult {
	var ctx = w.Context.(*EnableContext)

	logger.FromCtx(c).Named("EnableExtensionsCompactionWorkflow").Warn("Enable compaction extension workflow failed")

	tenant, err := s.tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return workflow.FailActivityWithError(eris.Wrapf(err, "failed to get tenant %d", ctx.TenantID))
	}

	// If the compaction extension is not enabled, we should scale back the compactor to the original replicas.
	err = s.compaction.ScaleCompactor(c, tenant, uint32(ctx.CompactorReplicasToRollBack))
	if err != nil {
		return workflow.FailActivityWithError(eris.Wrapf(err, "failed to scale back compactor for tenant %d", ctx.TenantID))
	}

	ws, err := tenantext.NewServerlessCompactionWorkflowService(
		c,
		s.tenantModel,
		s.txProvider,
		s.cloudAgentProvider,
		s.storageFactory,
		ctx.TenantID,
		true,
	)
	if err != nil {
		return workflow.FailActivityWithError(eris.Wrapf(err, "failed to create serverless compaction workflow service for tenant %d", ctx.TenantID))
	}

	err = ws.DisableIfExists(c)
	if err != nil {
		return workflow.FailActivityWithError(eris.Wrapf(err, "failed to disable serverless compaction for tenant %d", ctx.TenantID))
	}

	// resume database configuration
	_, err = modeltx.Query(c, s.txProvider, func(ctx context.Context, txModel *model.Model) (uuid.UUID, error) {
		err = txModel.CompareAndUpdateTenantStatusByIDAndStatuses(ctx, tenant.ID, model.TenantStatusRunning, []string{model.TenantStatusRunning, model.TenantStatusExtensionCompactionEnabling})
		if err != nil {
			return uuid.UUID{}, err
		}
		return uuid.UUID{}, nil
	})
	if err != nil {
		return workflow.FailActivityWithError(err)
	}

	return workflow.CompleteActivity()
}

func (s *EnableWorkflowService) postDeleteExtensionsCompaction(_ context.Context, _ *workflow.Workflow) error {
	return nil
}
