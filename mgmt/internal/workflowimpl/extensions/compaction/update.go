package compaction

import (
	"context"

	"github.com/google/uuid"
	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/risingwave-cloud/internal/infra/storage"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/extensions"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow/builder"

	"github.com/risingwavelabs/risingwave-cloud/internal/account"
	agentprovider "github.com/risingwavelabs/risingwave-cloud/internal/agent/provider"
	"github.com/risingwavelabs/risingwave-cloud/internal/metrics"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/modeltx"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow/activity"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl"
)

const UpdateWorkflowType = "UpdateExtensionsCompaction"

const (
	preprocessingUpdateRequestState = "PreprocessingUpdateRequest"
	updateState                     = "UpdateExtensionsCompaction"
	postUpdateState                 = "PostUpdateExtensionsCompaction"
	validationUpdateState           = "ValidatingUpdateExtensionsCompaction"
)

type UpdateContext struct {
	workflowimpl.TenantContextImpl

	Params tenantext.ServerlessCompactionParams `json:"params,omitempty"`
}

type UpdateWorkflowService struct {
	builder    *builder.WorkflowBuilder
	repository *workflow.Repository
	account    account.Agent

	tenantModel        *model.Model
	txProvider         modeltx.TxProvider
	compaction         extensions.ServerlessCompaction
	cloudAgentProvider agentprovider.CloudAgentProviderInterface
	storageFactory     storage.FactoryInterface
}

func NewUpdateWorkflowService(
	repository *workflow.Repository,
	account account.Agent,
	tenantModel *model.Model,
	txProvider modeltx.TxProvider,
	compaction extensions.ServerlessCompaction,
	cloudAgentProvider agentprovider.CloudAgentProviderInterface,
	storageFactory storage.FactoryInterface,
) *UpdateWorkflowService {
	s := &UpdateWorkflowService{
		repository:         repository,
		account:            account,
		tenantModel:        tenantModel,
		txProvider:         txProvider,
		compaction:         compaction,
		cloudAgentProvider: cloudAgentProvider,
		storageFactory:     storageFactory,
	}
	var (
		PreprocessRequestActivity              = activity.WrapDoError(s.preprocessRequest)
		UpdateExtensionsCompactionActivity     = activity.WrapDoError(s.updateExtensionsCompaction)
		PostUpdateExtensionsCompactionActivity = activity.WrapDoError(s.postUpdateExtensionsCompaction)
		ValidateExtensionsCompactionActivity   = activity.WrapDoError(s.validateExtensionsCompactionReady)
	)

	retry := activity.RetryWrapper(repository, 2)
	s.builder = builder.NewBuilder(UpdateWorkflowType).
		Next(preprocessingUpdateRequestState).Activity(PreprocessRequestActivity).Wrap(retry).
		Next(updateState).Activity(UpdateExtensionsCompactionActivity).Wrap(retry).
		Defer(postUpdateState, activity.WrapDefer(retry(PostUpdateExtensionsCompactionActivity), repository)).
		Next(validationUpdateState).Activity(ValidateExtensionsCompactionActivity).Wrap(retry).
		Succeed()

	contextProvider := func() any { return &UpdateContext{} }
	repository.Registry.Register(UpdateWorkflowType, s.builder.WorkflowConstructor(contextProvider))

	return s
}

func (s *UpdateWorkflowService) StartUpdateWorkflow(ctx context.Context, tenantID uint64, params tenantext.ServerlessCompactionParams) (uuid.UUID, error) {
	ctx, cancel := context.WithTimeout(ctx, 5*model.TransactionTimeout())
	defer cancel()
	workflowID, err := modeltx.Query(ctx, s.txProvider, func(ctx context.Context, txModel *model.Model) (uuid.UUID, error) {
		// Update the tenant status to updating.
		err := txModel.CompareAndUpdateTenantStatusByIDAndStatuses(ctx, tenantID, model.TenantStatusUpdating, []string{model.TenantStatusRunning})
		if err != nil {
			return uuid.UUID{}, eris.Wrap(err, "failed to update tenant status")
		}

		// Update the tenant extension status to updating.
		err = txModel.CompareAndUpdateTenantExtensionStatusByTenantIDAndResourceTypeAndStatuses(ctx, tenantID, model.ExtensionsResourceTypeCompaction, model.ExtensionStatusUpdating, []string{model.ExtensionStatusRunning})
		if err != nil {
			return uuid.UUID{}, eris.Wrap(err, "failed to update tenant extension status")
		}

		id, err := s.repository.CreateInTx(ctx, txModel, s.NewUpdateWorkflow(tenantID, params))
		if err != nil {
			return uuid.UUID{}, eris.Wrap(err, "failed to create update extension compaction workflow")
		}
		return id, nil
	})
	if err != nil {
		return uuid.UUID{}, err
	}
	metrics.ReportWorkflowCreated(UpdateWorkflowType)
	return workflowID, nil
}

func (s *UpdateWorkflowService) NewUpdateWorkflow(tenantID uint64, params tenantext.ServerlessCompactionParams) *workflow.Workflow {
	ctx := &UpdateContext{
		TenantContextImpl: workflowimpl.NewTenantContext(tenantID),
		Params:            params,
	}
	var w = s.builder.Build()
	w.Context = ctx
	return w
}

func (s *UpdateWorkflowService) preprocessRequest(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*UpdateContext)
	var tenantModel = s.tenantModel

	tenant, err := tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}

	if tenant.Status != model.TenantStatusUpdating {
		return eris.Errorf("illegal state, tenant %d not in %s status", tenant.ID, model.TenantStatusUpdating)
	}
	return nil
}

func (s *UpdateWorkflowService) updateExtensionsCompaction(c context.Context, w *workflow.Workflow) error {
	ctx := w.Context.(*UpdateContext)
	ws, err := tenantext.NewServerlessCompactionWorkflowService(
		c,
		s.tenantModel,
		s.txProvider,
		s.cloudAgentProvider,
		s.storageFactory,
		ctx.TenantID,
		true,
	)
	if err != nil {
		return eris.Wrapf(err, "failed to create serverless compaction workflow service for tenant %d", ctx.TenantID)
	}

	return ws.Update(c, ctx.Params)
}

func (s *UpdateWorkflowService) postUpdateExtensionsCompaction(_ context.Context, _ *workflow.Workflow) error {
	return nil
}

func (s *UpdateWorkflowService) validateExtensionsCompactionReady(c context.Context, w *workflow.Workflow) error {
	ctx := w.Context.(*UpdateContext)
	tenantID := ctx.TenantID

	err := modeltx.Exec(c, s.txProvider, func(ctx context.Context, txModel *model.Model) error {
		err := txModel.CompareAndUpdateTenantStatusByIDAndStatuses(ctx, tenantID, model.TenantStatusRunning, []string{model.TenantStatusUpdating, model.TenantStatusRunning})
		if err != nil {
			return eris.Wrapf(err, "failed to update tenant %d status to %s", tenantID, model.TenantStatusRunning)
		}
		return nil
	})
	if err != nil {
		return eris.Wrapf(err, "failed to update tenant %d status to %s", tenantID, model.TenantStatusRunning)
	}

	return nil
}
