package compaction

import (
	"encoding/json"

	"github.com/risingwavelabs/eris"
	"github.com/samber/lo"
	"k8s.io/apimachinery/pkg/api/resource"

	"github.com/risingwavelabs/risingwave-cloud/internal/config"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/extensions"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/querier"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenant/resourcespec"
)

// ToExtensionConfigWithRWConfig converts a TenantExtension to a RisingWaveExtensionsCompactionWithRWConfig.
// If extensionsConfig is nil or its Config is nil, returns a config with MaxReplicas set to 0.
// If ignoreDisableStatus is true and the extension status is disabled, also returns a config with MaxReplicas set to 0.
// Otherwise, unmarshals the config from the provided TenantExtension.
func ToExtensionConfigWithRWConfig(extensionsConfig *querier.TenantExtension, ignoreDisableStatus bool) (*config.RisingWaveExtensionsCompactionWithRWConfig, error) {
	extensionConfigWithRWConfig := config.NewRisingWaveExtensionsCompactionWithRWConfig()
	if extensionsConfig == nil || extensionsConfig.Config == nil {
		// set the max replicas to 0 which means the serverless compactor is not existing.
		extensionConfigWithRWConfig.Scaler.MaxReplicas = 0
		return extensionConfigWithRWConfig, nil
	}
	if ignoreDisableStatus && extensionsConfig.Status == model.ExtensionStatusDisabled {
		// If ignoreDisableStatus is true and the extension status is disabled,
		// we return a config whose max replica is zero
		extensionConfigWithRWConfig.Scaler.MaxReplicas = 0
		return extensionConfigWithRWConfig, nil
	}
	err := extensionConfigWithRWConfig.UnmarshalJSON([]byte(*extensionsConfig.Config))
	if err != nil {
		return nil, eris.Wrap(err, "failed to unmarshal extensions config")
	}
	return extensionConfigWithRWConfig, nil
}

func CreateServerlessCompactionConfigByMaxReplica(
	maximumCPUSize int,
	originalCompactorReplica int,
	tenantResourceSpec *resourcespec.TenantResourceSpecV1Regular) (*config.RisingWaveExtensionsCompactionWithRWConfig, error) {
	extensionConfig := config.NewRisingWaveExtensionsCompactionWithRWConfig()
	if tenantResourceSpec != nil {
		// Set the compactor replicas to half of the compute replicas (rounded up) if it's not set in the context.
		tenantResourceSpec.CompactorReplica = int32(
			extensions.GetServerlessCompactionOriginalCompactorReplica(
				int32(originalCompactorReplica), tenantResourceSpec.ComputeReplica))
		extensionConfig.ResourceSpec = string(lo.Must1(json.Marshal(tenantResourceSpec)))
	}

	// Set the max replicas with the option given. For now, the CPU limit per compactor is fixed to 1RWU.
	if maximumCPUSize > 0 {
		cpuQuantity, err := resource.ParseQuantity(extensionConfig.Compactor.CPULimit)
		if err != nil {
			return nil, eris.Wrapf(err, "failed to parse cpu limit")
		}
		// Round up division.
		extensionConfig.Scaler.MaxReplicas = (int64(maximumCPUSize*1000) + cpuQuantity.MilliValue() - 1) / cpuQuantity.MilliValue()
	} else {
		extensionConfig.Scaler.MaxReplicas = 0
	}
	return extensionConfig, nil
}
