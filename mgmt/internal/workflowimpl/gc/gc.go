package gc

import (
	"context"
	"sync"
	"time"

	"github.com/risingwavelabs/risingwave-cloud/internal/infra/storage"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow/builder"

	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/rwdb"

	delete_workflow "github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/delete"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/metabackup"

	"github.com/risingwavelabs/risingwave-cloud/internal/apiservice/errors"

	"go.uber.org/zap"

	"github.com/risingwavelabs/risingwave-cloud/internal/config"
	"github.com/risingwavelabs/risingwave-cloud/internal/metrics"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/modeltx"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow/fsm"
	"github.com/risingwavelabs/risingwave-cloud/shared/logger"
	"github.com/risingwavelabs/risingwave-cloud/shared/ptr"

	"github.com/google/uuid"

	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/risingwave-cloud/internal/account"
	"github.com/risingwavelabs/risingwave-cloud/internal/agent/provider"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow/activity"
)

const (
	GarbageCollectionWorkflowType = "GarbageCollection"

	GetTenantsState              = "GetTenants"
	TenantGarbageCollectionState = "GarbageCollection"
	GetSnapshotsInDeletionState  = "GetSnapshotsInDeletion"
	SnapshotInDeletionGCState    = "SnapshotInDeletionGC"
	SnapshotInCreationGCState    = "SnapshotInCreationGC"

	TerminateEvent fsm.Event = "terminate"

	defaultConcurrency = 10
	defaultRetryLimit  = 3

	SnapshotGCTimeout = 1 * time.Hour

	SnapshotTimeoutHours = 24
)

type GarbageCollectionContext struct {
	TenantRetryCounts   map[uint64]int
	SnapshotRetryCounts map[uuid.UUID]int
	mutex               sync.RWMutex
}

func (ctx *GarbageCollectionContext) Labels() map[string]string {
	return map[string]string{}
}

func (ctx *GarbageCollectionContext) IncrementTenantRetryCountAndGet(tenantID uint64) int {
	ctx.mutex.Lock()
	defer ctx.mutex.Unlock()

	ctx.TenantRetryCounts[tenantID]++
	return ctx.TenantRetryCounts[tenantID]
}

func (ctx *GarbageCollectionContext) DeleteTenantRetryCount(tenantID uint64) {
	ctx.mutex.Lock()
	defer ctx.mutex.Unlock()

	delete(ctx.TenantRetryCounts, tenantID)
}

func (ctx *GarbageCollectionContext) IncrementSnapshotRetryCountAndGet(snapshotID uuid.UUID) int {
	ctx.mutex.Lock()
	defer ctx.mutex.Unlock()

	ctx.SnapshotRetryCounts[snapshotID]++
	return ctx.SnapshotRetryCounts[snapshotID]
}

func (ctx *GarbageCollectionContext) DeleteSnapshotRetryCount(snapshotID uuid.UUID) {
	ctx.mutex.Lock()
	defer ctx.mutex.Unlock()

	delete(ctx.SnapshotRetryCounts, snapshotID)
}

type GarbageCollectionWorkflowService struct {
	builder         *builder.WorkflowBuilder
	repository      *workflow.Repository
	clusterProvider k8s.IKubernetesClusterProvider
	account         account.Agent
	agentProvider   provider.CloudAgentProviderInterface
	model           *model.Model
	txProvider      modeltx.TxProvider
	rwc             rwdb.RisingWaveClientInterface
	storageFactory  storage.FactoryInterface
}

func NewGarbageCollectionWorkflowService(
	repository *workflow.Repository,
	clusterProvider k8s.IKubernetesClusterProvider,
	account account.Agent,
	agentProvider provider.CloudAgentProviderInterface,
	model *model.Model,
	txProvider modeltx.TxProvider,
	rwc rwdb.RisingWaveClientInterface,
) *GarbageCollectionWorkflowService {
	s := &GarbageCollectionWorkflowService{
		repository:      repository,
		clusterProvider: clusterProvider,
		account:         account,
		agentProvider:   agentProvider,
		model:           model,
		txProvider:      txProvider,
		rwc:             rwc,
		storageFactory:  storage.NewFactory(model),
	}
	var (
		RestartActivity                = activity.WrapDoResult(s.restart)
		GetTenantsActivity             = activity.WrapDoResult(s.getTenants)
		GarbageCollectionActivity      = activity.WrapDoResult(s.tenantGarbageCollecting)
		GetSnapshotsInDeletionActivity = activity.WrapDoResult(s.getSnapshotsInDeletion)
		SnapshotInDeletionGCActivity   = activity.WrapDoResult(s.snapshotInDeletionGC)
		SnapshotInCreationGCActivity   = activity.WrapDoResult(s.snapshotInCreationGC)
	)

	s.builder = builder.NewBuilder(GarbageCollectionWorkflowType).Activity(RestartActivity).
		Next(GetTenantsState).Activity(GetTenantsActivity).
		Next(TenantGarbageCollectionState).Activity(GarbageCollectionActivity).
		On(TerminateEvent, GetSnapshotsInDeletionState).
		Next(GetSnapshotsInDeletionState).Activity(GetSnapshotsInDeletionActivity).
		Next(SnapshotInDeletionGCState).Activity(SnapshotInDeletionGCActivity).
		On(TerminateEvent, SnapshotInCreationGCState).
		Next(SnapshotInCreationGCState).Activity(SnapshotInCreationGCActivity).
		On(TerminateEvent, workflow.StartingState)

	contextProvider := func() any { return &GarbageCollectionContext{} }
	repository.Registry.Register(GarbageCollectionWorkflowType, s.builder.WorkflowConstructor(contextProvider))

	return s
}

func (s *GarbageCollectionWorkflowService) NewGarbageCollectionWorkflow() *workflow.Workflow {
	ctx := &GarbageCollectionContext{}
	var w = s.builder.Build()
	w.Context = ctx
	return w
}

func (s *GarbageCollectionWorkflowService) GCWithWorkflow(ctx context.Context) (uuid.UUID, error) {
	ctx, cancel := context.WithTimeout(ctx, 5*model.TransactionTimeout())
	defer cancel()

	workflowID, err := modeltx.Query(ctx, s.txProvider, func(ctx context.Context, txModel *model.Model) (uuid.UUID, error) {
		err := txModel.AcquireAdvisoryXactLock(ctx, GarbageCollectionWorkflowType)
		if err != nil {
			return uuid.UUID{}, eris.Wrap(err, "failed to get AdvisoryXactLock")
		}
		w, err := txModel.GetWorkflowByType(ctx, GarbageCollectionWorkflowType)
		if err != nil {
			return uuid.UUID{}, eris.Wrap(err, "failed to get gc workflow")
		}
		if w != nil {
			return w.ID, nil
		}
		id, err := s.repository.CreateInTx(ctx, txModel, s.NewGarbageCollectionWorkflow())
		if err != nil {
			return uuid.UUID{}, eris.Wrap(err, "failed to create workflow")
		}
		return id, nil
	})

	if err != nil {
		return uuid.UUID{}, err
	}

	metrics.ReportWorkflowCreated(GarbageCollectionWorkflowType)

	return workflowID, nil
}

func (s *GarbageCollectionWorkflowService) restart(context.Context, *workflow.Workflow) workflow.ActivityResult {
	now := time.Now()
	tenPM := time.Date(now.Year(), now.Month(), now.Day(), 22, 0, 0, 0, time.UTC)
	if now.After(tenPM) {
		tenPM = tenPM.Add(24 * time.Hour)
	}
	return workflow.CompleteActivityWithDelay(tenPM)
}

func (s *GarbageCollectionWorkflowService) getTenants(c context.Context, w *workflow.Workflow) workflow.ActivityResult {
	var gcModel = s.model
	tenants, err := gcModel.GetTenantsForGarbageCollection(c)
	if err != nil {
		logger.FromCtx(c).Named("GarbageCollectionWorkflow").Info("Fail to get tenants for garbage collection", zap.Error(err))
		return workflow.ReenterActivityWithDelay(time.Now().Add(time.Hour))
	}

	var tenantRetryCounts = make(map[uint64]int)
	for _, tenant := range tenants {
		m, err := gcModel.GetRunningWorkflowOfTenant(c, delete_workflow.DeleteTenantWorkflowType, tenant.ID)
		if err != nil {
			logger.FromCtx(c).Named("GarbageCollectionWorkflow").Info("Fail to get running workflow of tenant", zap.Uint64("tenantId", tenant.ID), zap.Error(err))
			continue
		}
		if m == nil {
			tenantRetryCounts[tenant.ID] = 0
		}
	}
	ctx := &GarbageCollectionContext{
		TenantRetryCounts: tenantRetryCounts,
	}
	w.Context = ctx
	return workflow.CompleteActivity()
}

func (s *GarbageCollectionWorkflowService) tenantGarbageCollecting(c context.Context, w *workflow.Workflow) workflow.ActivityResult {
	ctx := w.Context.(*GarbageCollectionContext)

	c, cancel := context.WithTimeout(c, SnapshotGCTimeout)
	defer cancel()

	var wg sync.WaitGroup

	concurrency := ptr.UnwrapOr(config.Conf.WorkflowEngine.GarbageCollectionConcurrency, defaultConcurrency)
	retryLimit := ptr.UnwrapOr(config.Conf.WorkflowEngine.GarbageCollectionRetryLimit, defaultRetryLimit)

	tenantIDs := make([]uint64, 0, len(ctx.TenantRetryCounts))
	for tenantID := range ctx.TenantRetryCounts {
		tenantIDs = append(tenantIDs, tenantID)
	}
	i := 0
	for _, tenantID := range tenantIDs {
		if i >= concurrency {
			break
		}
		wg.Add(1)
		go func(c context.Context, t uint64) {
			defer wg.Done()

			if c.Err() != nil {
				return
			}

			err := s.deleteTenant(c, t)
			if err == nil {
				ctx.DeleteTenantRetryCount(t)
				logger.FromCtx(c).Named("GarbageCollectionWorkflow").Info("Successfully deleted Tenant", zap.Uint64("tenantId", t))
			} else {
				ctx.TenantRetryCounts[t]++
				retryCount := ctx.IncrementTenantRetryCountAndGet(t)
				logger.FromCtx(c).Named("GarbageCollectionWorkflow").Info("Failed to delete Tenant", zap.Uint64("tenantId", t), zap.Int("retryCount", ctx.TenantRetryCounts[t]), zap.Error(err))

				if retryCount >= retryLimit {
					delete(ctx.TenantRetryCounts, t)
					logger.FromCtx(c).Named("GarbageCollectionWorkflow").Info("Retry limit exceeded for Tenant, removed from gc list", zap.Uint64("tenantId", t))
				}
			}
		}(c, tenantID)
		i++
	}

	wg.Wait()

	w.Context = ctx

	if len(ctx.TenantRetryCounts) == 0 {
		logger.FromCtx(c).Named("GarbageCollectionWorkflow").Info("No tenants need to gc, Finish Tenant GarbageCollection workflow")
		return workflow.CompleteActivityWithEvent(TerminateEvent)
	}

	return workflow.ReenterActivity()
}

func (s *GarbageCollectionWorkflowService) deleteTenant(ctx context.Context, tenantID uint64) error {
	var gcModel = s.model

	// PreprocessRequest
	tenant, err := gcModel.GetTenantByID(ctx, tenantID)
	if err != nil {
		if errors.IsTenantNotExistError(err) {
			logger.FromCtx(ctx).Named("GarbageCollectionWorkflow").Info(
				"PreprocessRequest: No tenant to gc",
				zap.Uint64("tenantId", tenantID),
			)
			return nil
		}
		logger.FromCtx(ctx).Named("GarbageCollectionWorkflow").Error(
			"PreprocessRequest: Failed to Get Tenant by ID",
			zap.Error(err),
		)
		return err
	}
	if tenant.Status == model.TenantStatusFailed || tenant.Status == model.TenantStatusCreating {
		logger.FromCtx(ctx).Named("GarbageCollectionWorkflow").Info(
			"PreprocessRequest: Illegal state, tenant is not in deleting status, reset the status",
			zap.Uint64("tenantId", tenantID), zap.String("status", tenant.Status),
		)
		err = gcModel.CompareAndUpdateTenantStatusByIDAndStatuses(ctx, tenantID,
			model.TenantStatusDeleting, []string{tenant.Status},
		)
		if err != nil {
			return err
		}
	} else if tenant.Status != model.TenantStatusDeleting {
		logger.FromCtx(ctx).Named("GarbageCollectionWorkflow").Error(
			"PreprocessRequest: Illegal state, tenant is not in deleting status",
			zap.Uint64("tenantId", tenantID),
		)
		return eris.Errorf("illegal state, tenant %d is not in deleting status", tenantID)
	}

	// DeleteRisingWaveResource
	cluster, err := s.clusterProvider.GetCluster(ctx, tenant.ClusterID)
	if err != nil {
		logger.FromCtx(ctx).Named("GarbageCollectionWorkflow").Error(
			"DeleteRisingWaveResource: Failed to Get Cluster by ID",
			zap.Error(err),
		)
		return err
	}
	defer cluster.Close()

	err = cluster.DeleteRisingWaveResource(ctx, tenant)
	if err != nil {
		logger.FromCtx(ctx).Named("GarbageCollectionWorkflow").Error(
			"DeleteRisingWaveResource: Failed to delete tenant",
			zap.Error(err),
		)
		return eris.Wrap(err, "failed to delete tenant")
	}

	// CleanupStorageDeletionTask
	err = cluster.CleanupStorageDeletionTask(ctx, tenant.ResourceNamespace, tenant.ID)
	if err != nil {
		logger.FromCtx(ctx).Named("GarbageCollectionWorkflow").Error(
			"CleanupStorageDeletionTask: Failed to cleanup StorageDeletionTask",
			zap.Error(err),
		)
		return err
	}

	// ValidateTenant
	err = cluster.WaitForRisingWaveDeleted(ctx, tenant)
	if err != nil {
		logger.FromCtx(ctx).Named("GarbageCollectionWorkflow").Error(
			"ValidateTenant: Failed to wait for risingWave deleted",
			zap.Error(err),
		)
		return err
	}

	// DeleteStorage
	err = cluster.DeleteStorage(ctx, tenant.ResourceNamespace, tenant.ID)
	if err != nil {
		logger.FromCtx(ctx).Named("GarbageCollectionWorkflow").Error(
			"DeleteStorage: Failed to delete storage",
			zap.Error(err),
		)
		return err
	}

	// DeleteSnapshotMsdbRecord
	err = gcModel.DeleteSnapshotsByTenantID(ctx, tenantID)
	if err != nil {
		logger.FromCtx(ctx).Named("GarbageCollectionWorkflow").Error(
			"DeleteSnapshotMsdbRecord: Failed to delete snapshot record",
			zap.Error(err),
		)
		return err
	}

	// CleanupStorageDeletionTask
	err = cluster.CleanupStorageDeletionTask(ctx, tenant.ResourceNamespace, tenant.ID)
	if err != nil {
		logger.FromCtx(ctx).Named("GarbageCollectionWorkflow").Error(
			"CleanupStorageDeletionTask: Failed to cleanup StorageDeletionTask",
			zap.Error(err),
		)
		return err
	}

	// DeleteAccountService
	accountTenant, err := s.account.GetTenantInAccountService(ctx, tenant.Region, tenant.ID)
	if err != nil {
		logger.FromCtx(ctx).Named("GarbageCollectionWorkflow").Error(
			"DeleteAccountService: Failed to Get Tenant in AccountService by ID",
			zap.Error(err),
		)
		return err
	}
	if accountTenant != nil {
		err = s.account.DeleteTenantInAccountService(ctx, tenant.Region, tenant.ID)
		if err != nil {
			logger.FromCtx(ctx).Named("GarbageCollectionWorkflow").Error(
				"DeleteAccountService: Failed to delete Tenant in AccountService by ID",
				zap.Error(err),
			)
			return err
		}
	}

	// DeleteMsdbRecord
	_, err = gcModel.GetTenantByID(ctx, tenantID)
	exist := !errors.IsTenantNotExistError(err)
	if err != nil {
		logger.FromCtx(ctx).Named("GarbageCollectionWorkflow").Error(
			"DeleteMsdbRecord: Failed to determine whether tenant exists",
			zap.Error(err),
		)
		return err
	}
	if exist {
		err = gcModel.DeleteTenantByID(ctx, tenantID)
		if err != nil {
			logger.FromCtx(ctx).Named("GarbageCollectionWorkflow").Error(
				"DeleteMsdbRecord: Failed to delete tenant",
				zap.Error(err),
			)
			return err
		}
	}

	return nil
}

func (s *GarbageCollectionWorkflowService) getSnapshotsInDeletion(c context.Context, w *workflow.Workflow) workflow.ActivityResult {
	c, cancel := context.WithTimeout(c, 10*model.TransactionTimeout())
	defer cancel()

	var gcModel = s.model
	snapshots, err := gcModel.GetSnapshotsByStatus(c, string(model.SnapshotStatusDeleting))
	if err != nil {
		logger.FromCtx(c).Named("GarbageCollectionWorkflow").Info("Fail to get Snapshots for garbage collection", zap.Error(err))
		return workflow.ReenterActivityWithDelay(time.Now().Add(time.Hour))
	}
	var snapshotRetryCounts = make(map[uuid.UUID]int)
	for _, snapshot := range snapshots {
		backup, err := gcModel.GetSnapshotBySnapshotID(c, snapshot.ID)
		if err != nil {
			logger.FromCtx(c).Named("GarbageCollectionWorkflow").Info(
				"Fail to get snapshot by snapshotId",
				zap.String("snapshotId", snapshot.ID.String()),
				zap.Error(err),
			)
			continue
		}
		tenant, err := gcModel.GetTenantByID(c, backup.TenantID)
		if err != nil {
			logger.FromCtx(c).Named("GarbageCollectionWorkflow").Info(
				"Fail to get tenant of snapshot",
				zap.String("snapshotId", snapshot.ID.String()),
				zap.Uint64("TenantId", backup.TenantID), zap.Error(err),
			)
			continue
		}
		if tenant != model.TenantNil && tenant.Status != model.TenantStatusRunning && tenant.Status != model.TenantStatusSnapshotting {
			logger.FromCtx(c).Named("GarbageCollectionWorkflow").Info(
				"Skip tenant because it is not in Running or Snapshotting status",
				zap.String("snapshotId", snapshot.ID.String()),
				zap.Uint64("TenantId", backup.TenantID), zap.Error(err),
			)
			continue
		}
		m, err := gcModel.GetRunningWorkflowOfSnapshot(c, metabackup.DeleteSnapshotWorkflowType, snapshot.ID)
		if err != nil {
			logger.FromCtx(c).Named("GarbageCollectionWorkflow").Info(
				"Fail to get running workflow of snapshot",
				zap.String("snapshotId", snapshot.ID.String()),
				zap.Error(err),
			)
			continue
		}
		if m == nil {
			snapshotRetryCounts[snapshot.ID] = 0
		}
	}
	ctx := &GarbageCollectionContext{
		SnapshotRetryCounts: snapshotRetryCounts,
	}
	w.Context = ctx
	return workflow.CompleteActivity()
}

func (s *GarbageCollectionWorkflowService) snapshotInDeletionGC(c context.Context, w *workflow.Workflow) workflow.ActivityResult {
	ctx := w.Context.(*GarbageCollectionContext)

	c, cancel := context.WithTimeout(c, SnapshotGCTimeout)
	defer cancel()

	var wg sync.WaitGroup

	concurrency := ptr.UnwrapOr(config.Conf.WorkflowEngine.GarbageCollectionConcurrency, defaultConcurrency)
	retryLimit := ptr.UnwrapOr(config.Conf.WorkflowEngine.GarbageCollectionRetryLimit, defaultRetryLimit)

	snapshotIDs := make([]uuid.UUID, 0, len(ctx.SnapshotRetryCounts))
	for snapshotID := range ctx.SnapshotRetryCounts {
		snapshotIDs = append(snapshotIDs, snapshotID)
	}
	i := 0
	for _, snapshotID := range snapshotIDs {
		if i >= concurrency {
			break
		}
		wg.Add(1)
		go func(c context.Context, snapshotID uuid.UUID) {
			defer wg.Done()

			if c.Err() != nil {
				return
			}

			err := s.deleteSnapshot(c, snapshotID)
			if err == nil {
				ctx.DeleteSnapshotRetryCount(snapshotID)
				logger.FromCtx(c).Named("GarbageCollectionWorkflow").Info(
					"Successfully deleted Snapshot",
					zap.String("snapshotId", snapshotID.String()),
				)
			} else {
				retryCount := ctx.IncrementSnapshotRetryCountAndGet(snapshotID)
				logger.FromCtx(c).Named("GarbageCollectionWorkflow").Info(
					"Failed to delete Snapshot",
					zap.String("snapshotId", snapshotID.String()),
					zap.Int("retryCount", ctx.SnapshotRetryCounts[snapshotID]),
					zap.Error(err),
				)

				if retryCount >= retryLimit {
					delete(ctx.SnapshotRetryCounts, snapshotID)
					logger.FromCtx(c).Named("GarbageCollectionWorkflow").Info(
						"Retry limit exceeded for Snapshot, removed from gc list",
						zap.String("snapshotId", snapshotID.String()),
					)
				}
			}
		}(c, snapshotID)
		i++
	}

	wg.Wait()

	w.Context = ctx

	if len(ctx.SnapshotRetryCounts) == 0 {
		logger.FromCtx(c).Named("GarbageCollectionWorkflow").Info("No snapshots need to gc, Finish GarbageCollection workflow")
		return workflow.CompleteActivityWithEvent(TerminateEvent)
	}

	return workflow.ReenterActivity()
}

func (s *GarbageCollectionWorkflowService) deleteSnapshot(ctx context.Context, snapshotID uuid.UUID) error {
	var gcModel = s.model

	snapshot, err := gcModel.GetSnapshotBySnapshotID(ctx, snapshotID)
	if err != nil {
		return eris.Wrap(err, "failed to get snapshot by snapshotId")
	}
	tenant, err := gcModel.GetTenantByID(ctx, snapshot.TenantID)
	if err != nil {
		return eris.Wrap(err, "failed to get tenant")
	}

	// delete snapshot
	snapshot, err = gcModel.GetSnapshot(ctx, tenant.ID, snapshotID)
	if err != nil {
		return eris.Wrap(err, "failed to get snapshot")
	}

	agent, err := s.agentProvider.GetAgent(ctx, tenant.ClusterID)
	if err != nil {
		return eris.Wrap(err, "failed to get cluster")
	}
	defer agent.Close()

	if err := agent.DeleteSnapshot(
		ctx,
		k8s.DefaultRisingWaveName,
		tenant.ResourceNamespace,
		snapshot.RwSnapshotID,
		tenant.ImageTag,
	); err != nil {
		return eris.Wrap(err, "failed to delete snapshot in RisingWave")
	}

	// delete record
	if err := gcModel.DeleteSnapshotRecord(ctx, tenant.ID, snapshotID); err != nil {
		return eris.Wrap(err, "failed to delete snapshot record")
	}

	return nil
}

func (s *GarbageCollectionWorkflowService) snapshotInCreationGC(c context.Context, _ *workflow.Workflow) workflow.ActivityResult {
	ctx, cancel := context.WithTimeout(context.Background(), 10*model.TransactionTimeout())
	defer cancel()

	var gcModel = s.model

	tenants, err := gcModel.GetTenantIDsWithTimedoutSnapshots(ctx, string(model.SnapshotStatusCreating), SnapshotTimeoutHours)
	if err != nil {
		logger.L().Named("GarbageCollectionWorkflow").Info(
			"fail to get tenant ids with timeout snapshots",
		)
		return workflow.ReenterActivityWithDelay(time.Now().Add(time.Hour))
	}

	var wg sync.WaitGroup
	concurrency := ptr.UnwrapOr(config.Conf.WorkflowEngine.GarbageCollectionConcurrency, defaultConcurrency)
	semaphore := make(chan struct{}, concurrency)

	for _, tenantID := range tenants {
		semaphore <- struct{}{}
		wg.Add(1)

		go func(opCtx context.Context, tenantID uint64) {
			defer wg.Done()
			defer func() { <-semaphore }()

			if ctx.Err() != nil {
				return
			}

			allSnapshotsDeletedSuccessfully := true
			defer func() {
				if !allSnapshotsDeletedSuccessfully {
					metrics.IncSnapshotGCFailedTotal()
				}
			}()

			snapshotIDs, err := gcModel.GetSnapshotIDsWithoutRWIDByTenantID(opCtx, tenantID)
			if err != nil {
				logger.L().Named("GarbageCollectionWorkflow").Info(
					"failed to get snapshot ids waiting to be deleted", zap.Uint64("tenantId", tenantID), zap.Error(err),
				)
				allSnapshotsDeletedSuccessfully = false
				return
			}

			// get cloudagent

			tenant, err := s.model.GetTenantByID(ctx, tenantID)
			if err != nil {
				logger.FromCtx(ctx).Named("GarbageCollectionWorkflow").Error(
					"Failed to Get Tenant by ID",
					zap.Uint64("tenantId", tenantID),
					zap.Error(err),
				)
				return
			}

			agent, err := s.agentProvider.GetAgent(ctx, tenant.ClusterID)
			if err != nil {
				logger.FromCtx(ctx).Named("GarbageCollectionWorkflow").Error(
					"Failed to Get Agent by ID",
					zap.Uint64("tenantId", tenantID),
					zap.Error(err),
				)
				return
			}
			defer agent.Close()

			storageProvider, err := s.storageFactory.GetStorageProviderFromCluster(ctx, tenant.ClusterID, agent)
			if err != nil {
				logger.FromCtx(ctx).Named("GarbageCollectionWorkflow").Error(
					"Failed to Get Storage Provider from Cluster",
					zap.Uint64("tenantId", tenantID),
					zap.Error(err),
				)
				return
			}

			snapshotInfos, err := storageProvider.GetAllSnapshots(opCtx, tenant.ResourceNamespace)
			if err != nil {
				logger.L().Named("GarbageCollectionWorkflow").Info(
					"failed to get snapshot info by tenant id", zap.Uint64("tenantId", tenantID), zap.Error(err),
				)
				allSnapshotsDeletedSuccessfully = false
				return
			}
			if tenant != model.TenantNil && tenant.Status != model.TenantStatusRunning && tenant.Status != model.TenantStatusSnapshotting {
				logger.FromCtx(c).Named("GarbageCollectionWorkflow").Info(
					"Skip tenant because it is not in Running or Snapshotting status",
					zap.Uint64("TenantId", tenantID), zap.Error(err),
				)
				return
			}

			for _, snapshotInfo := range snapshotInfos {
				snapshot, err := gcModel.GetSnapshotByTenantIDAndRWID(opCtx, tenantID, snapshotInfo.MetaSnapshotID)
				if err != nil {
					logger.L().Named("GarbageCollectionWorkflow").Info(
						"failed to get snapshot by tenant id and rw id", zap.Uint64("tenantId", tenantID), zap.Error(err),
					)
					allSnapshotsDeletedSuccessfully = false
					continue
				}
				if snapshot == nil && time.Since(snapshotInfo.CreatedAt) >= 24*time.Hour {
					if err := agent.DeleteSnapshot(
						opCtx,
						k8s.DefaultRisingWaveName,
						tenant.ResourceNamespace,
						snapshotInfo.MetaSnapshotID,
						tenant.ImageTag,
					); err != nil {
						logger.L().Named("GarbageCollectionWorkflow").Info(
							"failed to delete snapshot in RisingWave", zap.Uint64("tenantId", tenantID), zap.Uint64("RwSnapshotID", uint64(snapshotInfo.MetaSnapshotID)), zap.Error(err),
						)
						allSnapshotsDeletedSuccessfully = false
						continue
					}
				}
			}

			if allSnapshotsDeletedSuccessfully {
				err := gcModel.DeleteSnapshotRecords(opCtx, tenantID, snapshotIDs)
				if err != nil {
					logger.L().Named("GarbageCollectionWorkflow").Info(
						"failed to delete all snapshot records needed gc", zap.Uint64("tenantId", tenantID), zap.Error(err),
					)
					return
				}
			}
		}(ctx, tenantID)
	}

	wg.Wait()

	return workflow.CompleteActivityWithEvent(TerminateEvent)
}
