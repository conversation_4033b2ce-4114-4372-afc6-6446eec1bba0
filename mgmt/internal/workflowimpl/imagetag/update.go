package imagetag

import (
	"context"
	"encoding/hex"
	"strings"
	"time"

	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext"

	"github.com/google/uuid"
	"github.com/risingwavelabs/eris"
	"github.com/risingwavelabs/risingwave-operator/apis/risingwave/v1alpha1"
	"go.uber.org/zap"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbk8ssvc "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/rwwait"

	agentprovider "github.com/risingwavelabs/risingwave-cloud/internal/agent/provider"
	"github.com/risingwavelabs/risingwave-cloud/internal/apiservice/errors"
	"github.com/risingwavelabs/risingwave-cloud/internal/config"
	"github.com/risingwavelabs/risingwave-cloud/internal/infra/storage"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/extensions"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/rwcrd"
	"github.com/risingwavelabs/risingwave-cloud/internal/metrics"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/modeltx"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/rwdb"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/rwsecret"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/version"
	metastoredef "github.com/risingwavelabs/risingwave-cloud/internal/tenant/metastore"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext/compaction"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow/activity"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow/builder"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/metabackup"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/metamigration"
	"github.com/risingwavelabs/risingwave-cloud/shared/logger"
	"github.com/risingwavelabs/risingwave-cloud/shared/ptr"
	"github.com/risingwavelabs/risingwave-cloud/shared/tenant/tier"
)

const ImageTagUpdateWorkflowType = "ImageUpdate"

const (
	BackupMetaState                            = "BackupMeta"
	SetLicenseKeyState                         = "SetLicenseKey"
	SetOAuthAccess                             = "SetOAuthAccess"
	UpdateImageTagState                        = "UpdateImage"
	PreprocessingRequestState                  = "PreprocessingRequest"
	DisableExtensionsServerlessCompactionState = "DisableExtensionsServerlessCompaction"
	ValidationState                            = "Validating"
	TriggerMetaMigrationState                  = "TriggerMetaMigration"
	EnableExtensionsServerlessCompactionState  = "EnableExtensionsServerlessCompaction"
	PreMetaMigrationState                      = "PreMetaMigration"
	SetSecretStorePrivateKeyState              = "SetSecretStorePrivateKey"
	SetOAuthAccessState                        = "SetOAuthAccess"
)

const (
	IgnoreMetaMigrateSuffix = "--ignore-meta-migrate"
)

type ImageTagUpdateContext struct { //nolint:revive
	workflowimpl.TenantContextImpl
	ImageTag              string    `json:"ImageTag"`
	PreviousImageTag      string    `json:"PreviousImageTag"`
	IgnoreMetaMigrate     *bool     `json:"IgnoreMetaMigrate,omitempty"`
	MetaMigrateWorkflowID uuid.UUID `json:"MetaMigrateWorkflowId"`
	MetaBackupWorkflowID  uuid.UUID `json:"MetaBackupWorkflowId"`
	SkipMetaBackup        bool      `json:"SkipMetaBackup"`
}

type ImageTagUpdateWorkflowService struct { //nolint:revive
	builder            *builder.WorkflowBuilder
	repository         *workflow.Repository
	cloudAgentProvider agentprovider.CloudAgentProviderInterface
	rwWaiter           rwwait.Waiter
	metaMigration      metamigration.MetaMigrationWorkflowService
	rwc                rwdb.RisingWaveClientInterface
	metaBackup         metabackup.BackupWorkflowTriggerInterface

	model          *model.Model
	txProvider     modeltx.TxProvider
	storageFactory storage.FactoryInterface
	compaction     extensions.ServerlessCompaction
}

func NewImageTagUpdateWorkflowService(
	repository *workflow.Repository,
	cloudAgentProvider agentprovider.CloudAgentProviderInterface,
	rwWaiter rwwait.Waiter,
	metaMigration metamigration.MetaMigrationWorkflowService,
	metaBackup metabackup.BackupWorkflowTriggerInterface,
	model *model.Model,
	txProvider modeltx.TxProvider,
	rwc rwdb.RisingWaveClientInterface,
	compaction extensions.ServerlessCompaction,
	storageFactory storage.FactoryInterface,
) *ImageTagUpdateWorkflowService {
	s := &ImageTagUpdateWorkflowService{
		repository:         repository,
		cloudAgentProvider: cloudAgentProvider,
		rwWaiter:           rwWaiter,
		model:              model,
		txProvider:         txProvider,
		metaMigration:      metaMigration,
		rwc:                rwc,
		metaBackup:         metaBackup,
		compaction:         compaction,
		storageFactory:     storageFactory,
	}
	var (
		preprocessRequestActivity                     = activity.WrapDoError(s.preprocessRequest)
		setOAuthAccessActivity                        = activity.WrapDoError(s.setOAuthAccess)
		disableExtensionsServerlessCompactionActivity = activity.WrapDoError(s.disableExtensionsServerlessCompaction)
		setLicenseKeyActivity                         = activity.WrapDoError(s.setLicenseKey)
		setSecretStorePrivateKeyActivity              = activity.WrapDoError(s.setSecretStorePrivateKey)
		updateImageActivity                           = activity.WrapDoError(s.updateImageTag)
		validateImageTagActivity                      = activity.WrapDoError(s.validateImageTag)
		triggerMetaMigrationActivity                  = activity.WrapDoError(s.triggerMetaMigration)
		enableExtensionsServerlessCompactionActivity  = activity.WrapDoError(s.enableExtensionsServerlessCompaction)
		preMetaMigrationActivity                      = activity.WrapDoResult(s.preMetaMigration)
		backupMetaActivity                            = activity.WrapDoResult(s.backupMeta)
	)
	s.builder = builder.NewBuilder(ImageTagUpdateWorkflowType).
		Next(PreprocessingRequestState).Activity(preprocessRequestActivity).
		Next(BackupMetaState).Activity(backupMetaActivity).
		Next(PreMetaMigrationState).Activity(preMetaMigrationActivity).
		Next(DisableExtensionsServerlessCompactionState).Activity(disableExtensionsServerlessCompactionActivity).
		Next(SetLicenseKeyState).Activity(setLicenseKeyActivity).
		Next(SetOAuthAccessState).Activity(setOAuthAccessActivity).
		Next(SetSecretStorePrivateKeyState).Activity(setSecretStorePrivateKeyActivity).
		Next(UpdateImageTagState).Activity(updateImageActivity).
		Next(ValidationState).Activity(validateImageTagActivity).
		Next(TriggerMetaMigrationState).Activity(triggerMetaMigrationActivity).
		Next(EnableExtensionsServerlessCompactionState).Activity(enableExtensionsServerlessCompactionActivity).
		WrapAll(activity.RetryWrapper(repository, 3)).
		WrapAll(activity.FailWorkflowWrapper).
		Succeed()

	contextProvider := func() any { return &ImageTagUpdateContext{} }
	repository.Registry.Register(ImageTagUpdateWorkflowType, s.builder.WorkflowConstructor(contextProvider))

	return s
}

func (s *ImageTagUpdateWorkflowService) NewImageTagUpdateWorkflow(tenantID uint64, imageTag string, previousImageTag string, skipMetaBackup bool) *workflow.Workflow {
	var ignoreMetaMigrate *bool
	if strings.HasSuffix(imageTag, IgnoreMetaMigrateSuffix) {
		imageTag = strings.TrimSuffix(imageTag, IgnoreMetaMigrateSuffix)
		ignoreMetaMigrate = ptr.Ptr(true)
	}

	ctx := &ImageTagUpdateContext{
		TenantContextImpl: workflowimpl.NewTenantContext(tenantID),
		ImageTag:          imageTag,
		PreviousImageTag:  previousImageTag,
		IgnoreMetaMigrate: ignoreMetaMigrate,
		SkipMetaBackup:    skipMetaBackup,
	}
	var w = s.builder.Build()
	w.Context = ctx
	return w
}

func (s *ImageTagUpdateWorkflowService) UpdateImageTagWithWorkflow(ctx context.Context, tenantID uint64, imageTag string, skipMetaBackup bool) (uuid.UUID, error) {
	ctx, cancel := context.WithTimeout(ctx, 5*model.TransactionTimeout())
	defer cancel()

	workflowID, err := modeltx.Query(ctx, s.txProvider, func(ctx context.Context, txModel *model.Model) (uuid.UUID, error) {
		tenant, err := txModel.GetTenantByID(ctx, tenantID)
		if err != nil {
			return uuid.UUID{}, eris.Wrap(err, "failed to get tenant")
		}

		err = txModel.UpdateTenantStatusByID(ctx, tenantID, model.TenantStatusImageTagUpgrading)
		if err != nil {
			return uuid.UUID{}, eris.Wrap(err, "failed to update tenant status")
		}

		id, err := s.repository.CreateInTx(ctx, txModel, s.NewImageTagUpdateWorkflow(tenantID, imageTag, tenant.ImageTag, skipMetaBackup))
		if err != nil {
			return uuid.UUID{}, eris.Wrap(err, "failed to create workflow")
		}
		return id, nil
	})
	if err != nil {
		return uuid.UUID{}, err
	}
	metrics.ReportWorkflowCreated(ImageTagUpdateWorkflowType)
	return workflowID, nil
}

func (s *ImageTagUpdateWorkflowService) preprocessRequest(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ImageTagUpdateContext)
	var tenantModel = s.model

	tenant, err := tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}

	if tenant.Status != model.TenantStatusImageTagUpgrading {
		return eris.Errorf("illegal state, tenant %d not in %s status", tenant.ID, model.TenantStatusImageTagUpgrading)
	}

	return nil
}

func (s *ImageTagUpdateWorkflowService) backupMeta(c context.Context, w *workflow.Workflow) workflow.ActivityResult {
	var ctx = w.Context.(*ImageTagUpdateContext)

	if ctx.SkipMetaBackup {
		return workflow.CompleteActivity()
	}

	// workflow already triggered, check status
	if ctx.MetaBackupWorkflowID != uuid.Nil {
		metaBackupWorkflow, err := s.model.GetWorkflow(c, ctx.MetaBackupWorkflowID)
		if err != nil {
			return workflow.FailActivityWithError(err)
		}
		switch workflow.RunningState(metaBackupWorkflow.RunningState) {
		case workflow.RunningWorkflowState:
			return workflow.ReenterActivityWithDelay(time.Now().Add(15 * time.Second))
		case workflow.CompletedWorkflowState:
			return workflow.CompleteActivity()
		case workflow.CancelledWorkflowState, workflow.FailedWorkflowState:
			return workflow.FailActivityWithError(eris.New("meta backup workflow failed or cancelled"))
		}
	}

	// workflow not triggered, trigger if needed

	// already have another backup workflow
	runningWorkflow, err := s.model.GetRunningWorkflowOfTenant(c, metabackup.MetaBackupWorkflowType, ctx.TenantID)
	if err != nil {
		return workflow.FailActivityWithError(err)
	}
	if runningWorkflow != nil {
		ctx.MetaBackupWorkflowID = runningWorkflow.ID
		return workflow.ReenterActivity()
	}

	// trigger workflow
	const defaultRetention = 3 * 24 * time.Hour
	retention := defaultRetention
	if config.Conf.Mgmt.Backup.OperationalBackupRetentionMins != nil {
		retention = time.Duration(*config.Conf.Mgmt.Backup.OperationalBackupRetentionMins) * time.Minute
	}
	workflowID, _, err := s.metaBackup.StartMetaBackupWorkflow(c, ctx.TenantID, metabackup.BackupSetterControlPlane, ptr.Ptr(retention))

	if err != nil {
		return workflow.FailActivityWithError(err)
	}
	ctx.MetaBackupWorkflowID = *workflowID
	return workflow.ReenterActivityWithDelay(time.Now().Add(15 * time.Second))
}

func (s *ImageTagUpdateWorkflowService) disableExtensionsServerlessCompaction(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ImageTagUpdateContext)
	ws, err := tenantext.NewServerlessCompactionWorkflowService(
		c,
		s.model,
		s.txProvider,
		s.cloudAgentProvider,
		s.storageFactory,
		ctx.TenantID,
		true,
	)
	if err != nil {
		return eris.Wrapf(err, "failed to create serverless compaction workflow service for tenant %d", ctx.TenantID)
	}
	return ws.StopIfExists(c)
}

func (s *ImageTagUpdateWorkflowService) enableExtensionsServerlessCompaction(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ImageTagUpdateContext)
	tenantExt, err := s.model.GetTenantExtensionByTenantIDAndResourceType(c, ctx.TenantID, model.ExtensionsResourceTypeCompaction)
	if err != nil && !eris.Is(err, errors.ErrTenantExtensionNotExist) {
		return eris.Wrapf(err, "failed to get tenant extension of %s for tenant %d", model.ExtensionsResourceTypeCompaction, ctx.TenantID)
	}
	if tenantExt == model.TenantExtensionNil {
		return nil
	}

	if compaction.IsMigratable(ctx.ImageTag, tenantExt) {
		_, err := compaction.TryMigrateAndPersistTenantExtension(
			c,
			ctx.ImageTag,
			tenantExt,
			s.model,
		)
		if err != nil {
			return eris.Wrapf(err, "failed to migrate tenant extension %s for tenant %d", model.ExtensionsResourceTypeCompaction, ctx.TenantID)
		}
	}

	ws, err := tenantext.NewServerlessCompactionWorkflowService(
		c,
		s.model,
		s.txProvider,
		s.cloudAgentProvider,
		s.storageFactory,
		ctx.TenantID,
		true)
	if err != nil {
		return eris.Wrapf(err, "failed to create serverless compaction workflow service for tenant %d", ctx.TenantID)
	}
	return ws.StartIfExists(c)
}

func (s *ImageTagUpdateWorkflowService) preMetaMigration(c context.Context, w *workflow.Workflow) workflow.ActivityResult {
	var ctx = w.Context.(*ImageTagUpdateContext)

	// workflow already triggered, check status
	if ctx.MetaMigrateWorkflowID != uuid.Nil {
		metaMigrateWorkflow, err := s.model.GetWorkflow(c, ctx.MetaMigrateWorkflowID)
		if err != nil {
			return workflow.FailActivityWithError(err)
		}
		switch workflow.RunningState(metaMigrateWorkflow.RunningState) {
		case workflow.RunningWorkflowState:
			return workflow.ReenterActivityWithDelay(time.Now().Add(60 * time.Second))
		case workflow.CompletedWorkflowState:
			return workflow.CompleteActivity()
		case workflow.CancelledWorkflowState, workflow.FailedWorkflowState:
			return workflow.FailActivityWithError(eris.New("meta migration workflow failed or cancelled"))
		}
	}

	// workflow not triggered, trigger if needed
	tenant, err := s.model.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return workflow.FailActivityWithError(err)
	}

	if tenant.Resources.GetMetaStore().Type != metastoredef.Etcd {
		return workflow.CompleteActivity()
	}
	if !version.IsEtcdDeprecated(ctx.ImageTag) {
		return workflow.CompleteActivity()
	}

	// already have another migration workflow
	runningWorkflow, err := s.model.GetRunningWorkflowOfTenant(c, metamigration.MetaMigrationWorkflowType, ctx.TenantID)
	if err != nil {
		return workflow.FailActivityWithError(err)
	}
	if runningWorkflow != nil {
		ctx.MetaMigrateWorkflowID = runningWorkflow.ID
		return workflow.ReenterActivity()
	}

	// trigger workflow
	workflowID, err := s.metaMigration.StartMetaMigration(c, ctx.TenantID, nil)
	if err != nil {
		return workflow.FailActivityWithError(err)
	}
	ctx.MetaMigrateWorkflowID = workflowID
	return workflow.ReenterActivityWithDelay(time.Now().Add(60 * time.Second))
}

func (s *ImageTagUpdateWorkflowService) setLicenseKey(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ImageTagUpdateContext)
	var tenantModel = s.model

	tenant, err := tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}

	agent, err := s.cloudAgentProvider.GetAgent(c, tenant.ClusterID)
	if err != nil {
		return eris.Wrapf(err, "failed to retrieve cloud agent, clusterID: %v", tenant.ClusterID)
	}
	defer agent.Close()

	resp, err := agent.GetRisingWave(c, k8s.DefaultRisingWaveName, tenant.ResourceNamespace)
	if err != nil {
		return eris.Wrap(err, "Failed to get risingwave spec.")
	}

	var licenseKey string
	if !version.IsCustomFeatureSetSupported(ctx.ImageTag) {
		licenseKey = config.Conf.AppTokens.RisingwaveLicenseKey
	} else {
		if tier.IsAdvancedTier(tier.Tier(tenant.TierID)) {
			licenseKey = config.Conf.AppTokens.RisingwaveAdvancedTierLicenseKey
		} else {
			licenseKey = config.Conf.AppTokens.RisingwaveStandardTierLicenseKey
		}
	}

	if resp.GetRisingwaveSpec().GetLicenseKey() == nil || resp.GetRisingwaveSpec().GetLicenseKey().GetSecretName() == "" {
		err = agent.CreateK8sSecret(c, &pbk8ssvc.CreateSecretRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        rwcrd.DefaultLicenseKeySecretName,
				Namespace: tenant.ResourceNamespace,
			},
			SecretSpec: &pbk8s.Secret{
				StringData: map[string]string{
					v1alpha1.RisingWaveLicenseKeySecretKey: licenseKey,
				},
			},
		})
		if err != nil {
			return eris.Wrap(err, "failed to create secret for license key")
		}

		err := agent.UpdateRisingWaveLicenseKey(c, k8s.DefaultRisingWaveName, tenant.ResourceNamespace, rwcrd.DefaultLicenseKeySecretName)
		if err != nil {
			return eris.Wrap(err, "Failed to update risingwave license key.")
		}
	} else if version.IsCustomFeatureSetSupported(ctx.ImageTag) {
		err = agent.DeleteK8sSecret(c, &pbk8ssvc.DeleteSecretRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        rwcrd.DefaultLicenseKeySecretName,
				Namespace: tenant.ResourceNamespace,
			},
		})
		if err != nil {
			return eris.Wrap(err, "failed to clean up legacy license key")
		}

		err = agent.CreateK8sSecret(c, &pbk8ssvc.CreateSecretRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        rwcrd.DefaultLicenseKeySecretName,
				Namespace: tenant.ResourceNamespace,
			},
			SecretSpec: &pbk8s.Secret{
				StringData: map[string]string{
					v1alpha1.RisingWaveLicenseKeySecretKey: licenseKey,
				},
			},
		})
		if err != nil {
			return eris.Wrap(err, "failed to create secret for license key")
		}
	}

	return nil
}

func (s *ImageTagUpdateWorkflowService) setSecretStorePrivateKey(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ImageTagUpdateContext)
	var tenantModel = s.model

	tenant, err := tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}

	// skip the step if the feature flag is not enabled
	if !rwsecret.IsRisingwaveSecretStoreEnabled() {
		return nil
	}

	agent, err := s.cloudAgentProvider.GetAgent(c, tenant.ClusterID)
	if err != nil {
		return eris.Wrapf(err, "failed to retrieve cloud agent, clusterID: %v", tenant.ClusterID)
	}
	defer agent.Close()

	resp, err := agent.GetRisingWave(c, k8s.DefaultRisingWaveName, tenant.ResourceNamespace)
	if err != nil {
		return eris.Wrap(err, "Failed to get risingwave spec.")
	}

	// skip if private key has been set
	if resp.GetRisingwaveSpec().GetSecretStore().GetPrivateKey() != nil {
		return nil
	}

	privateKey, err := rwsecret.DecryptSecretStorePrivateKey(tenant.SecretStoreDataEncryptionKey)
	if err != nil {
		return eris.Wrap(err, "failed to get valid data encryption key for secret store from msdb")
	}
	err = agent.CreateK8sSecret(c, &pbk8ssvc.CreateSecretRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        rwcrd.DefaultSecretStorePrivateKeySecretName,
			Namespace: tenant.ResourceNamespace,
		},
		SecretSpec: &pbk8s.Secret{
			StringData: map[string]string{
				rwcrd.DefaultSecretStorePrivateKeySecretKey: hex.EncodeToString(privateKey),
			},
		},
	})
	if err != nil {
		return eris.Wrap(err, "failed to create secret for secret store")
	}

	err = agent.UpdateRisingWaveSecretStore(c, k8s.DefaultRisingWaveName, tenant.ResourceNamespace, rwcrd.DefaultSecretStorePrivateKeySecretName, rwcrd.DefaultSecretStorePrivateKeySecretKey)
	if err != nil {
		return eris.Wrap(err, "Failed to update risingwave secret store.")
	}

	return nil
}

func (s *ImageTagUpdateWorkflowService) setOAuthAccess(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ImageTagUpdateContext)
	var tenantModel = s.model

	tenant, err := tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}

	if !version.IsOAuthRoleSupported(ctx.ImageTag) {
		return nil
	}

	err = s.rwc.CreateDefaultOAuthUser(c, tenant.ID, tenant.OrgID)
	if err != nil {
		return err
	}

	return nil
}

func (s *ImageTagUpdateWorkflowService) updateImageTag(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ImageTagUpdateContext)
	var tenantModel = s.model

	// --- Update imageTag in MSDB. ---

	err := tenantModel.UpdateTenantImageTagByID(c, ctx.TenantID, ctx.ImageTag)
	if err != nil {
		return err
	}

	tenant, err := tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}

	if tenant.ImageTag != ctx.ImageTag {
		return eris.New("image tag stored in MSDB is not equal to given configuration")
	}

	// --- Update imageTag in cluster. ---
	agent, err := s.cloudAgentProvider.GetAgent(c, tenant.ClusterID)
	if err != nil {
		return eris.Wrapf(err, "failed to retrieve cloud agent, clusterID: %v", tenant.ClusterID)
	}
	defer agent.Close()

	err = agent.UpdateRisingWaveImage(
		c,
		k8s.DefaultRisingWaveName,
		tenant.ResourceNamespace,
		config.Conf.Mgmt.RisingWave.Repository+":"+tenant.ImageTag,
	)
	if err != nil {
		return eris.Wrap(err, "Failed to update config.")
	}

	return nil
}

func (s *ImageTagUpdateWorkflowService) validateImageTag(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ImageTagUpdateContext)
	var tenantModel = s.model

	tenant, err := tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}

	agent, err := s.cloudAgentProvider.GetAgent(c, tenant.ClusterID)
	if err != nil {
		return eris.Wrapf(err, "failed to retrieve cloud agent, clusterID: %v", tenant.ClusterID)
	}
	defer agent.Close()

	err = s.rwWaiter.WaitRwReady(c, agent, tenant)
	if err != nil {
		return eris.Wrap(err, "risingwave is not ready")
	}

	err = tenantModel.UpdateTenantStatusByID(c, tenant.ID, model.TenantStatusRunning)
	if err != nil {
		return eris.Wrap(err, "failed to update tenant status")
	}
	return nil
}

func (s *ImageTagUpdateWorkflowService) triggerMetaMigration(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ImageTagUpdateContext)

	// not enable or ignore migrate
	if ptr.UnwrapOr(ctx.IgnoreMetaMigrate, false) {
		return nil
	}

	// check using etcd
	tenant, err := s.model.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}
	current := tenant.Resources.GetMetaStore()
	if current.Type != metastoredef.Etcd {
		return nil
	}

	preferSQLBackend, err := metamigration.PreferMetaMigration(ctx.ImageTag, tenant.TierID)
	if err != nil {
		return err
	}
	if !preferSQLBackend {
		return nil
	}

	// trigger migrate
	workflowID, err := s.metaMigration.StartMetaMigration(c, ctx.TenantID, nil)
	if err != nil && eris.GetCode(err) == eris.CodeFailedPrecondition {
		logger.L().Warn(
			"failed to trigger meta migration",
			zap.Uint64("tenant_id", ctx.TenantID),
			zap.Error(err),
		)
		return nil
	}
	if err != nil {
		return err
	}
	logger.L().Info(
		"start meta migration",
		zap.Uint64("tenant_id", ctx.TenantID),
		zap.String("workflow_id", workflowID.String()),
	)
	return nil
}
