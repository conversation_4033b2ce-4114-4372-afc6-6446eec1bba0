package privatelink

import (
	"context"
	"strconv"
	"time"

	"github.com/risingwavelabs/risingwave-cloud/internal/workflow/builder"

	"go.uber.org/zap"

	agentprovider "github.com/risingwavelabs/risingwave-cloud/internal/agent/provider"
	"github.com/risingwavelabs/risingwave-cloud/internal/infra/network"
	"github.com/risingwavelabs/risingwave-cloud/shared/logger"

	"github.com/google/uuid"
	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/risingwave-cloud/internal/account"
	"github.com/risingwavelabs/risingwave-cloud/internal/config"
	"github.com/risingwavelabs/risingwave-cloud/internal/metrics"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/modeltx"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow/activity"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl"
)

const (
	PrivateLinkProvisionWorkflowType = "PrivateLinkProvision"

	ProvisionPrivateLinkState = "CreatePrivateLink"
	ProvisionCleanUpState     = "CleanUp"

	PrivateLinkCreationTimeout = 5 * time.Minute
	PrivateLinkCleanUpTimeout  = 5 * time.Minute
)

type ProvisionPrivateLinkContext struct {
	workflowimpl.TenantContextImpl
	PrivateLink model.PrivateLink `json:"PrivateLink"`
}

func (ctx *ProvisionPrivateLinkContext) Labels() map[string]string {
	return map[string]string{
		"tenantId": strconv.FormatUint(ctx.GetTenantID(), 10),
	}
}

type IProvisionPrivateLinkWorkflowService interface {
	StartProvisionPrivateLink(ctx context.Context, privateLink model.PrivateLink) (uuid.UUID, uuid.UUID, error)
}

type MockProvisionPrivateLinkWorkflowService struct{}

func (m *MockProvisionPrivateLinkWorkflowService) StartProvisionPrivateLink(_ context.Context, privateLink model.PrivateLink) (uuid.UUID, uuid.UUID, error) {
	if privateLink.Target == "" {
		return uuid.UUID{}, uuid.UUID{}, eris.New("error creating private link, target must be set").WithCode(eris.CodeInvalidArgument)
	}
	if privateLink.ConnectionName == "" {
		return uuid.UUID{}, uuid.UUID{}, eris.New("error creating private link, connection name must be set").WithCode(eris.CodeInvalidArgument)
	}
	if privateLink.TenantID == 0 {
		return uuid.UUID{}, uuid.UUID{}, eris.New("error creating private link, tenant id must be set").WithCode(eris.CodeInvalidArgument)
	}
	if privateLink.Org == uuid.Nil {
		return uuid.UUID{}, uuid.UUID{}, eris.New("error creating private link, org id must be set").WithCode(eris.CodeInvalidArgument)
	}

	return uuid.UUID{}, uuid.UUID{}, nil
}

func NewMockProvisionPrivateLinkWorkflowService() IProvisionPrivateLinkWorkflowService {
	return &MockProvisionPrivateLinkWorkflowService{}
}

type ProvisionPrivateLinkWorkflowService struct {
	builder    *builder.WorkflowBuilder
	repository *workflow.Repository

	cloudAgentProvider     agentprovider.CloudAgentProviderInterface
	networkProviderFactory *network.Factory

	model        *model.Model
	accountAgent account.Agent
	txProvider   modeltx.TxProvider
}

func NewProvisionPrivateLinkWorkflowService(
	repository *workflow.Repository,
	model *model.Model,
	cloudAgentProvider agentprovider.CloudAgentProviderInterface,
	networkProviderFactory *network.Factory,
	accountAgent account.Agent,
	txProvider modeltx.TxProvider,
) IProvisionPrivateLinkWorkflowService {
	s := &ProvisionPrivateLinkWorkflowService{
		repository:             repository,
		model:                  model,
		cloudAgentProvider:     cloudAgentProvider,
		networkProviderFactory: networkProviderFactory,
		accountAgent:           accountAgent,
		txProvider:             txProvider,
	}
	var (
		ProvisionPrivateLinkActivity = activity.WrapDoError(s.provisionPrivateLink)
		ProvisionCleanUpActivity     = activity.WrapDoResult(s.cleanUp)
	)

	s.builder = builder.NewBuilder(PrivateLinkProvisionWorkflowType).
		Next(ProvisionPrivateLinkState).Activity(ProvisionPrivateLinkActivity).On(workflow.ErrorEvent, ProvisionCleanUpState).
		Succeed().
		From(ProvisionCleanUpState).Activity(ProvisionCleanUpActivity).On(workflow.ErrorEvent, workflow.FailedState).
		Fail().
		WrapAll(activity.RetryWrapperLinDelay(repository, 3, time.Second*20))

	contextProvider := func() any { return &ProvisionPrivateLinkContext{} }
	repository.Registry.Register(PrivateLinkProvisionWorkflowType, s.builder.WorkflowConstructor(contextProvider))

	return s
}

func (s *ProvisionPrivateLinkWorkflowService) NewPrivateLinkWorkflow(privateLinkModel model.PrivateLink) *workflow.Workflow {
	ctx := &ProvisionPrivateLinkContext{
		PrivateLink:       privateLinkModel,
		TenantContextImpl: workflowimpl.NewTenantContext(privateLinkModel.TenantID),
	}
	var w = s.builder.Build()
	w.Context = ctx
	return w
}

func (s *ProvisionPrivateLinkWorkflowService) StartProvisionPrivateLink(ctx context.Context, privateLink model.PrivateLink) (uuid.UUID, uuid.UUID, error) {
	ctx, cancel := context.WithTimeout(ctx, 5*model.TransactionTimeout())
	defer cancel()

	workflowID, err := modeltx.Query(ctx, s.txProvider, func(ctx context.Context, txModel *model.Model) (uuid.UUID, error) {
		privateLinkID, err := txModel.InsertPrivateLink(ctx, privateLink)
		if err != nil {
			if eris.GetCode(err) == eris.CodeAlreadyExists {
				return uuid.UUID{}, eris.WithCode(eris.Wrapf(err, "could not insert private link"), eris.CodeAlreadyExists)
			}
			return uuid.UUID{}, eris.Wrapf(err, "could not insert private link")
		}
		// TODO this api is not idempotent
		err = s.accountAgent.CreatePrivateLinkInAccountService(ctx, privateLink.TenantID, privateLink.Org, *privateLinkID, privateLink.ConnectionName, config.Conf.Region, privateLink.Target)
		if err != nil {
			return uuid.UUID{}, eris.Wrapf(err, "failed to sync privatelink with account service")
		}
		privateLink.ID = *privateLinkID

		workflowID, err := s.repository.CreateInTx(ctx, txModel, s.NewPrivateLinkWorkflow(privateLink))
		if err != nil {
			return uuid.UUID{}, eris.Wrap(err, "failed to create workflow")
		}
		return workflowID, nil
	})
	if err != nil {
		return uuid.UUID{}, uuid.UUID{}, err
	}
	metrics.ReportWorkflowCreated(PrivateLinkProvisionWorkflowType)

	return workflowID, privateLink.ID, nil
}

func (s *ProvisionPrivateLinkWorkflowService) provisionPrivateLink(c context.Context, w *workflow.Workflow) error {
	workflowCtx := w.Context.(*ProvisionPrivateLinkContext)
	c, cancel := context.WithTimeout(c, PrivateLinkCreationTimeout)
	defer cancel()

	var tenantModel = s.model
	tenant, err := tenantModel.GetTenantByID(c, workflowCtx.GetTenantID())
	if err != nil {
		return eris.Wrapf(err, "failed to retrieve tenant, tenantID: %v", workflowCtx.GetTenantID())
	}

	agent, err := s.cloudAgentProvider.GetAgent(c, tenant.ClusterID)
	if err != nil {
		return eris.Wrapf(err, "failed to retrieve cloud agent, clusterID: %v", tenant.ClusterID)
	}
	defer agent.Close()

	networkProvider, err := s.networkProviderFactory.NewProvider(c, tenant.ClusterID, agent)
	if err != nil {
		return eris.Wrapf(err, "failed to retrieve network provider, clusterID: %v", tenant.ClusterID)
	}

	logger.FromCtx(c).Named(PrivateLinkProvisionWorkflowType).Info(
		"Creating privatelink",
		zap.String("namespace", tenant.ResourceNamespace),
	)

	option := network.CreatePrivateLinkOption{
		Target:        workflowCtx.PrivateLink.Target,
		TenantID:      workflowCtx.PrivateLink.TenantID,
		Namespace:     tenant.ResourceNamespace,
		PrivateLinkID: workflowCtx.PrivateLink.ID.String(),
		Tier:          tenant.TierID,
	}
	res, err := networkProvider.CreatePrivateLinkAndWait(c, option)
	if err != nil {
		return eris.Wrapf(err, "failed to create privatelink, privatelinkID %v", workflowCtx.PrivateLink.ID)
	}

	err = s.accountAgent.MarkPrivateLinkAsBillableInAccountService(c, workflowCtx.PrivateLink.ID)
	if err != nil {
		return eris.Wrapf(err, "failed to mark private link (%s) as billable in Account", workflowCtx.PrivateLink.ID)
	}

	workflowCtx.PrivateLink.Status = string(model.PrivateLinkStatusCreated)
	if res.Endpoint == "" {
		return eris.Wrapf(err, "empty endpoint of privatelink, privatelinkID %v", workflowCtx.PrivateLink.ID)
	}
	workflowCtx.PrivateLink.Endpoint = res.Endpoint
	err = s.model.UpdatePrivateLink(c, workflowCtx.PrivateLink, model.PrivateLinkStatusCreating)
	if err != nil {
		return eris.Wrapf(err, "could not update private link")
	}

	return nil
}

func (s *ProvisionPrivateLinkWorkflowService) cleanUp(c context.Context, w *workflow.Workflow) workflow.ActivityResult {
	workflowCtx := w.Context.(*ProvisionPrivateLinkContext)
	c, cancel := context.WithTimeout(c, PrivateLinkCleanUpTimeout)
	defer cancel()
	workflowCtx.PrivateLink.Status = string(model.PrivateLinkStatusError)
	err := s.model.UpdatePrivateLink(c, workflowCtx.PrivateLink, model.PrivateLinkStatusCreating)
	if err != nil {
		return workflow.FailActivityWithError(eris.Wrap(err, "failed to update privatelink status to ERROR"))
	}

	var tenantModel = s.model
	tenant, err := tenantModel.GetTenantByID(c, workflowCtx.GetTenantID())
	if err != nil {
		return workflow.FailActivityWithError(eris.Wrapf(err, "failed to retrieve tenant, tenantID: %v", workflowCtx.GetTenantID()))
	}

	agent, err := s.cloudAgentProvider.GetAgent(c, tenant.ClusterID)
	if err != nil {
		return workflow.FailActivityWithError(eris.Wrapf(err, "failed to retrieve cloud agent, clusterID: %v", tenant.ClusterID))
	}
	defer agent.Close()

	networkProvider, err := s.networkProviderFactory.NewProvider(c, tenant.ClusterID, agent)
	if err != nil {
		return workflow.FailActivityWithError(eris.Wrapf(err, "failed to retrieve network provider, clusterID: %v", tenant.ClusterID))
	}
	logger.FromCtx(c).Named("PrivateLinkDeletionWorkflow").Info(
		"deleting privatelink",
		zap.String("privatelinkId", workflowCtx.PrivateLink.ID.String()),
	)
	err = networkProvider.DeletePrivateLinkAndWait(c, tenant.ResourceNamespace, workflowCtx.PrivateLink.ID.String())
	if err != nil {
		return workflow.FailActivityWithError(eris.Wrapf(err, "failed to clean up errored private link, privatelinkID %v", workflowCtx.PrivateLink.ID))
	}
	return workflow.CompleteActivity()
}
