package provision

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext/compaction"

	agentprovider "github.com/risingwavelabs/risingwave-cloud/internal/agent/provider"
	"github.com/risingwavelabs/risingwave-cloud/internal/infra/iam"
	"github.com/risingwavelabs/risingwave-cloud/internal/infra/metastore"
	"github.com/risingwavelabs/risingwave-cloud/internal/infra/storage"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/extensions"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/rwworkload"
	"github.com/risingwavelabs/risingwave-cloud/internal/rwproxy"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow/builder"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl"

	"github.com/google/uuid"
	"github.com/risingwavelabs/eris"
	"go.uber.org/zap"

	"github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbsvcsql "github.com/risingwavelabs/cloudagent/pbgen/services/psql"
	"github.com/risingwavelabs/cloudagent/pbgen/services/rwc"

	"github.com/risingwavelabs/risingwave-cloud/internal/account"
	"github.com/risingwavelabs/risingwave-cloud/internal/apiservice/errors"
	"github.com/risingwavelabs/risingwave-cloud/internal/config"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s"
	"github.com/risingwavelabs/risingwave-cloud/internal/metrics"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/modeltx"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/client"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/rwdb"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/version"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow/activity"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/expire"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/monitor"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/notifications"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/stopstart"
	"github.com/risingwavelabs/risingwave-cloud/shared/logger"
	"github.com/risingwavelabs/risingwave-cloud/shared/namespace"
)

const (
	ProvisionTenantWorkflowType       = "ProvisionTenant"
	SystemParamStateStore             = "state_store"
	SystemParamDataDirectory          = "data_directory"
	SystemParamBackupStorageURL       = "backup_storage_url"
	SystemParamBackupStorageDirectory = "backup_storage_directory"
)

const (
	PreprocessRequestState           = "PreprocessRequest"
	PushAccountServiceState          = "PushAccountService"
	PrepareStorageState              = "PrepareStorage"
	CreateRisingWaveInfraState       = "CreateRisingWaveInfra"
	CreateRisingWaveCRDState         = "CreateRisingWaveCRD"
	PostCreateTenantInfraState       = "PostCreateRisingWaveInfra"
	ScalePodsDownState               = "ScalePodsDown"
	CloneObjectStoreState            = "CloneObjectStore"
	CloneBackupObjectStoreState      = "CloneBackupObjectStore"
	PrepareMetaRestoreState          = "PrepareMetaRestore"
	MetaRestoreState                 = "MetaRestore"
	CleanupMetaRestoreTaskState      = "CleanupMetaRestoreTask"
	UpdateSystemParametersState      = "UpdateSystemParameters"
	ScalePodsUpState                 = "ScalePodsUp"
	ValidateState                    = "Validate"
	TransmitSuccessNotificationState = "TransmitSuccessNotification"
	TransmitFailNotificationState    = "TransmitFailNotification"
	ResetPasswordState               = "ResetPassword"
	SetOAuthAccessState              = "SetOAuthAccess"
	MarkAsProvisionedState           = "MarkAsProvisioned"
	MarkAsBillableState              = "MarkAsBillable"
	QuiesceOriginalTenantState       = "QuiesceOriginalTenant"
	WaitOriginalTenantQuiescedState  = "WaitOriginalTenantQuiesced"
	StartMonitoringState             = "StartMonitoring"
	CleanUpState                     = "CleanUp"
	StopRisingWaveInstanceState      = "StopRisingWaveInstance"
	PostStopRisingWaveInstanceState  = "PostStopRisingWaveInstance"
)

// States for extensions.
const (
	EnableServerlessCompactionExtensionState  = "EnableServerlessCompactionExtension"
	DisableServerlessCompactionExtensionState = "DisableServerlessCompactionExtension"
)

// ProvisionContextOption is a function that modifies the provision context.
type ProvisionContextOption func(*ProvisionContext) //nolint:revive

type IProvisionTenantWorkflowService interface {
	StartProvisionTenant(ctx context.Context, tenant model.Tenant, opts ...ProvisionContextOption) (uint64, uuid.UUID, error)
	NewProvisionTenantWorkflow(tenant model.Tenant, opts ...ProvisionContextOption) *workflow.Workflow
}

type MockProvisionTenantWorkflowService struct{}

func (m *MockProvisionTenantWorkflowService) StartProvisionTenant(_ context.Context, _ model.Tenant, _ ...ProvisionContextOption) (uint64, uuid.UUID, error) {
	return 1, uuid.UUID{}, nil
}

func (m *MockProvisionTenantWorkflowService) NewProvisionTenantWorkflow(tenant model.Tenant, _ ...ProvisionContextOption) *workflow.Workflow {
	ctx := &ProvisionContext{Tenant: tenant}
	w := workflow.Workflow{}
	w.Context = ctx
	return &w
}

func NewMockProvisionTenantWorkflowService() IProvisionTenantWorkflowService {
	return &MockProvisionTenantWorkflowService{}
}

// WithAsTrial sets the provision context to be a trial.
func WithAsTrial(asTrial bool) ProvisionContextOption {
	return func(ctx *ProvisionContext) {
		ctx.AsTrial = asTrial
	}
}

// WithServerlessCompaction sets the provision context to have serverless compaction options.
func WithServerlessCompaction(options *ServerlessCompactionOptions) ProvisionContextOption {
	return func(ctx *ProvisionContext) {
		ctx.ServerlessCompaction = options
	}
}

// With restore sets the provision context to have restore cluster options.
func AsRestoration(options *RestoreOptions) ProvisionContextOption {
	return func(ctx *ProvisionContext) {
		ctx.RestoreOption = options
	}
}

type ServerlessCompactionOptions struct {
	MaximumCPUSize           int
	OriginalCompactorReplica int
}

type RestoreOptions struct {
	OldTenant                       model.Tenant `json:"OldTenant"`
	Cursor                          string       `json:"Cursor"`
	MSDBSnapshotID                  uuid.UUID    `json:"MSDBSnapshotID"`
	QuiesceOriginalTenantWorkflowID uuid.UUID    `json:"QuiesceOriginalTenantWorkflowID"`
}

type ProvisionContext struct { //nolint:revive
	workflowimpl.TenantContextImpl
	Tenant               model.Tenant                 `json:"Tenant"`
	AsTrial              bool                         `json:"AsTrial"`
	MonitorWorkflowID    uuid.UUID                    `json:"MonitorWorkflowId"`
	ExpireWorkflowID     uuid.UUID                    `json:"ExpireWorkflowId"`
	ServerlessCompaction *ServerlessCompactionOptions `json:"ServerlessCompaction"`
	RestoreOption        *RestoreOptions              `json:"RestoreOptions"`
}

func (ctx *ProvisionContext) Labels() map[string]string {
	return map[string]string{
		"tenantId":   strconv.FormatUint(ctx.Tenant.ID, 10),
		"tenantName": ctx.Tenant.TenantName,
	}
}

func (ctx *ProvisionContext) IsServerlessCompactionRequested() bool {
	return ctx.ServerlessCompaction != nil
}

type ProvisionTenantWorkflowService struct { //nolint:revive
	builder          *builder.WorkflowBuilder
	repository       *workflow.Repository
	monitor          *monitor.MonitorWorkflowService
	expire           *expire.ExpireTenantWorkflowService
	stopstart        *stopstart.StopStartTenantWorkflowService
	clusterProvider  k8s.IKubernetesClusterProvider
	account          account.Agent
	agentProvider    agentprovider.CloudAgentProviderInterface
	storageFactory   storage.FactoryInterface
	metaStoreFactory metastore.Factory
	rwc              rwdb.RisingWaveClientInterface
	rwWorkload       rwworkload.RisingWaveWorkloadManager
	compaction       extensions.ServerlessCompaction

	model      *model.Model
	txProvider modeltx.TxProvider
}

func NewProvisionTenantWorkflowService(
	repository *workflow.Repository,
	monitor *monitor.MonitorWorkflowService,
	expire *expire.ExpireTenantWorkflowService,
	stopstart *stopstart.StopStartTenantWorkflowService,
	clusterProvider k8s.IKubernetesClusterProvider,
	account account.Agent,
	rwc rwdb.RisingWaveClientInterface,
	rwWorkload rwworkload.RisingWaveWorkloadManager,
	compaction extensions.ServerlessCompaction,

	model *model.Model,
	txProvider modeltx.TxProvider,
	cloudAgentProvider agentprovider.CloudAgentProviderInterface,
	storageFactory storage.FactoryInterface,
	metaStoreFactory metastore.Factory,
) IProvisionTenantWorkflowService {
	s := &ProvisionTenantWorkflowService{
		repository:       repository,
		monitor:          monitor,
		expire:           expire,
		stopstart:        stopstart,
		clusterProvider:  clusterProvider,
		account:          account,
		model:            model,
		txProvider:       txProvider,
		rwc:              rwc,
		rwWorkload:       rwWorkload,
		agentProvider:    cloudAgentProvider,
		storageFactory:   storageFactory,
		metaStoreFactory: metaStoreFactory,
		compaction:       compaction,
	}

	var (
		PreprocessRequestActivity                    = activity.WrapDoError(s.preprocessRequest)
		PushAccountServiceActivity                   = activity.WrapDoError(s.pushAccountService)
		PrepareStorageActivity                       = activity.WrapDoError(s.prepareStorage)
		CreateTenantInfraActivity                    = activity.WrapDoError(s.createTenantInfra)
		PostCreateTenantInfraActivity                = activity.WrapDoError(s.postCreateTenantInfra)
		CreateTenantCRDActivity                      = activity.WrapDoError(s.createTenantCRD)
		ScalePodsDownActivity                        = activity.WrapDoError(s.scalePodsDown)
		CloneObjectStoreActivity                     = activity.WrapDoResult(s.cloneObjectStore)
		CloneBackupObjectStoreActivity               = activity.WrapDoResult(s.cloneBackupObjectStore)
		PrepareMetaRestoreActivity                   = activity.WrapDoError(s.prepareMetaRestore)
		MetaRestoreActivity                          = activity.WrapDoError(s.metaRestore)
		CleanupMetaRestoreTaskActivity               = activity.WrapDoError(s.cleanupMetaRestoreTask)
		UpdateSystemParametersActivity               = activity.WrapDoError(s.updateSystemParameters)
		ScalePodsUpActivity                          = activity.WrapDoError(s.scalePodsUp)
		ValidateActivity                             = activity.WrapDoError(s.validateTenant)
		TransmitFailNotificationActivity             = s.newNotificationActivity(notifications.CreateFailure, time.Now().Add(24*time.Hour))
		TransmitSuccessNotificationActivity          = s.newNotificationActivity(notifications.CreateSuccess, time.Now())
		ResetPasswordActivity                        = activity.WrapDoError(s.resetPassword)
		SetOAuthAccessActivity                       = activity.WrapDoError(s.setOAuthAccess)
		QuiesceOriginalTenantActivity                = activity.WrapDoError(s.quiesceOriginalTenant)
		WaitOriginalTenantQuiescedActivity           = activity.WrapDoError(s.waitOriginalTenantQuiesced)
		MarkAsProvisionedActivity                    = activity.WrapDoError(s.markAsProvisioned)
		MarkAsBillableActivity                       = activity.WrapDoError(s.markAsBillable)
		StartMonitoringActivity                      = activity.WrapDoError(s.startMonitoring)
		CleanUpActivity                              = activity.WrapDoResult(s.cleanUp)
		StopTenantInstanceActivity                   = activity.WrapDoError(s.stopTenantInstance)
		PostStopTenantInstanceActivity               = activity.WrapDoError(s.postStopTenantInstance)
		EnableServerlessCompactionExtensionActivity  = activity.WrapDoError(s.enableServerlessCompactionExtension)
		DisableServerlessCompactionExtensionActivity = activity.WrapDoError(s.disableServerlessCompactionExtension)
	)

	retryExp := activity.RetryWrapperExpDelay(repository, 4, 10*time.Second)        // total wait 10+20+40=70s
	retryQuiesceExp := activity.RetryWrapperExpDelay(repository, 6, 30*time.Second) // total wait 30+60+120+240+480=15m30s
	retryLin := activity.RetryWrapperLinDelay(repository, 5, time.Second*20)
	s.builder = builder.NewBuilder(ProvisionTenantWorkflowType).
		Next(PreprocessRequestState).Activity(PreprocessRequestActivity).Wrap(retryExp).On(workflow.ErrorEvent, CleanUpState).
		Next(PushAccountServiceState).Activity(PushAccountServiceActivity).Wrap(retryExp).On(workflow.ErrorEvent, CleanUpState).
		Next(PrepareStorageState).Activity(PrepareStorageActivity).Wrap(retryExp).On(workflow.ErrorEvent, CleanUpState).
		Next(CreateRisingWaveInfraState).Activity(CreateTenantInfraActivity).Wrap(retryExp).
		Defer(PostCreateTenantInfraState, activity.WrapDefer(retryExp(PostCreateTenantInfraActivity), repository)).On(workflow.ErrorEvent, CleanUpState).
		Next(CreateRisingWaveCRDState).Activity(CreateTenantCRDActivity).Wrap(retryExp).
		// start cluster restore
		Next(ScalePodsDownState).Activity(ScalePodsDownActivity).Wrap(retryExp).On(workflow.ErrorEvent, CleanUpState).
		Next(CloneObjectStoreState).Activity(CloneObjectStoreActivity).Wrap(retryExp).On(workflow.ErrorEvent, CleanUpState).
		Next(CloneBackupObjectStoreState).Activity(CloneBackupObjectStoreActivity).Wrap(retryExp).On(workflow.ErrorEvent, CleanUpState).
		Next(PrepareMetaRestoreState).Activity(PrepareMetaRestoreActivity).Wrap(retryExp).On(workflow.ErrorEvent, CleanUpState).
		Next(MetaRestoreState).Activity(MetaRestoreActivity).Wrap(retryExp).
		Defer(CleanupMetaRestoreTaskState, activity.WrapDefer(retryLin(CleanupMetaRestoreTaskActivity), repository)).On(workflow.ErrorEvent, CleanUpState).
		Next(UpdateSystemParametersState).Activity(UpdateSystemParametersActivity).Wrap(retryExp).On(workflow.ErrorEvent, CleanUpState).
		Next(ScalePodsUpState).Activity(ScalePodsUpActivity).Wrap(retryExp).On(workflow.ErrorEvent, CleanUpState).
		// end cluster restore
		Next(EnableServerlessCompactionExtensionState).Activity(EnableServerlessCompactionExtensionActivity).Wrap(retryExp).On(workflow.ErrorEvent, CleanUpState).
		Next(ValidateState).Activity(ValidateActivity).Wrap(retryLin).On(workflow.ErrorEvent, CleanUpState).
		Next(StartMonitoringState).Activity(StartMonitoringActivity).Wrap(retryExp).On(workflow.ErrorEvent, CleanUpState).
		Next(ResetPasswordState).Activity(ResetPasswordActivity).Wrap(retryExp).On(workflow.ErrorEvent, CleanUpState).
		Next(SetOAuthAccessState).Activity(SetOAuthAccessActivity).Wrap(retryExp).On(workflow.ErrorEvent, CleanUpState).
		Next(QuiesceOriginalTenantState).Activity(QuiesceOriginalTenantActivity).Wrap(retryExp).On(workflow.ErrorEvent, CleanUpState).
		Next(WaitOriginalTenantQuiescedState).Activity(WaitOriginalTenantQuiescedActivity).Wrap(retryQuiesceExp).On(workflow.ErrorEvent, CleanUpState).
		Next(MarkAsProvisionedState).Activity(MarkAsProvisionedActivity).Wrap(retryExp).On(workflow.ErrorEvent, CleanUpState).
		Next(MarkAsBillableState).Activity(MarkAsBillableActivity).Wrap(retryExp).On(workflow.ErrorEvent, CleanUpState).
		Next(TransmitSuccessNotificationState).Activity(TransmitSuccessNotificationActivity).Wrap(retryExp).On(workflow.ErrorEvent, CleanUpState).
		Succeed().
		From(CleanUpState).Activity(CleanUpActivity).Wrap(retryExp).On(workflow.ErrorEvent, workflow.FailedState).
		Next(TransmitFailNotificationState).Activity(TransmitFailNotificationActivity).Wrap(retryExp).On(workflow.ErrorEvent, CleanUpState).
		Next(DisableServerlessCompactionExtensionState).Activity(DisableServerlessCompactionExtensionActivity).Wrap(retryExp).On(workflow.ErrorEvent, workflow.FailedState).
		Next(StopRisingWaveInstanceState).Activity(StopTenantInstanceActivity).Wrap(retryExp).
		Defer(PostStopRisingWaveInstanceState, activity.WrapDefer(retryLin(PostStopTenantInstanceActivity), repository)).On(workflow.ErrorEvent, workflow.FailedState).
		Fail()

	contextProvider := func() any { return &ProvisionContext{} }
	repository.Registry.Register(ProvisionTenantWorkflowType, s.builder.WorkflowConstructor(contextProvider))

	return s
}

func (s *ProvisionTenantWorkflowService) newNotificationActivity(notificationEvent notifications.NotificationType, delayAt time.Time) workflow.Activity {
	return activity.WrapDoResult(func(c context.Context, w *workflow.Workflow) workflow.ActivityResult {
		var ctx = w.Context.(*ProvisionContext)

		notificationBody, err := notifications.GetNotificationTemplate(
			notificationEvent,
			ctx.Tenant,
		)
		if err != nil {
			logger.FromCtx(c).Named("ProvisionWorkflow").Warn(
				"failed to parse notification templates",
				zap.String("orgId", ctx.Tenant.OrgID.String()),
				zap.Uint64("tenantId", ctx.Tenant.ID),
				zap.Error(err),
			)
		}

		err = s.account.SendNotificationsToUser(c, notificationBody)
		if err != nil {
			logger.L().Named("provision").Warn(
				"failed to transmit creation success notification",
				zap.String("orgId", ctx.Tenant.OrgID.String()),
				zap.Uint64("tenantId", ctx.Tenant.ID),
				zap.Error(err),
			)
		}

		return workflow.CompleteActivityWithDelay(delayAt)
	})
}

func (s *ProvisionTenantWorkflowService) NewProvisionTenantWorkflow(tenant model.Tenant, opts ...ProvisionContextOption) *workflow.Workflow {
	ctx := &ProvisionContext{
		TenantContextImpl: workflowimpl.NewTenantContext(tenant.ID),
		Tenant:            tenant,
	}
	for _, opt := range opts {
		opt(ctx)
	}

	var w = s.builder.Build()
	w.Context = ctx
	return w
}

func (s *ProvisionTenantWorkflowService) StartProvisionTenant(ctx context.Context, tenant model.Tenant, opts ...ProvisionContextOption) (uint64, uuid.UUID, error) {
	// get endpoint
	ctx, txCancel := context.WithTimeout(ctx, 5*model.TransactionTimeout())
	defer txCancel()

	type txResult struct {
		tenantID   uint64
		workflowID uuid.UUID
	}
	res, err := modeltx.Query(ctx, s.txProvider, func(ctx context.Context, txModel *model.Model) (txResult, error) {
		createdTenant, err := txModel.CreateTenantInTx(ctx, tenant)
		if err != nil {
			return txResult{}, eris.Wrap(err, "failed to create cluster")
		}

		wf := s.NewProvisionTenantWorkflow(*createdTenant, opts...)
		workflowID, err := s.repository.CreateInTx(ctx, txModel, wf)
		if err != nil {
			return txResult{}, eris.Wrap(err, "failed to create workflow")
		}

		// Create the serverless compaction extension if requested.
		if wf.Context.(*ProvisionContext).IsServerlessCompactionRequested() {
			tenantExt := compaction.NewTenantExtension(createdTenant.ID, createdTenant.ImageTag, model.ExtensionStatusEnabling)
			err := txModel.CreateTenantExtensionRaw(ctx, tenantExt)
			if err != nil {
				return txResult{}, eris.Wrap(err, "failed to create serverless compaction extension")
			}
		}

		return txResult{createdTenant.ID, workflowID}, nil
	})
	if err != nil {
		return 0, uuid.UUID{}, err
	}
	metrics.ReportWorkflowCreated(ProvisionTenantWorkflowType)

	return res.tenantID, res.workflowID, nil
}

func (s *ProvisionTenantWorkflowService) preprocessRequest(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ProvisionContext)
	var tenantModel = s.model

	tenant, err := tenantModel.GetTenantByID(c, ctx.Tenant.ID)
	if err != nil {
		return err
	}
	if tenant.Status != model.TenantStatusCreating {
		return eris.Errorf("illegal state, tenant %d already exists", ctx.Tenant.ID)
	}

	return nil
}

func (s *ProvisionTenantWorkflowService) pushAccountService(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ProvisionContext)

	tenant, err := s.account.GetTenantInAccountService(c, ctx.Tenant.Region, ctx.Tenant.ID)
	if err != nil {
		return err
	}
	if tenant != nil {
		return nil
	}

	nsID, err := namespace.Parse(ctx.Tenant.ResourceNamespace)
	if err != nil {
		return eris.Wrapf(err, "tenant has invalid namespace: '%s'", ctx.Tenant.ResourceNamespace)
	}

	err = s.account.CreateTenantInAccountService(
		c,
		ctx.Tenant.ID,
		ctx.Tenant.TierID,
		ctx.Tenant.TenantName,
		ctx.Tenant.Region,
		nsID,
		ctx.Tenant.OrgID,
		ctx.AsTrial,
	)
	if err != nil {
		return err
	}
	return nil
}

// TODO.
func (s *ProvisionTenantWorkflowService) prepareStorage(context.Context, *workflow.Workflow) error {
	return nil
}

func (s *ProvisionTenantWorkflowService) createTenantInfra(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ProvisionContext)

	cluster, err := s.clusterProvider.GetCluster(c, ctx.Tenant.ClusterID)
	if err != nil {
		return err
	}
	defer cluster.Close()

	err = cluster.CreateRisingWaveResourceInfra(c, ctx.Tenant)

	if err != nil {
		return eris.Wrap(err, "failed to create tenant infra")
	}

	return nil
}

func (s *ProvisionTenantWorkflowService) createTenantCRD(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ProvisionContext)

	cluster, err := s.clusterProvider.GetCluster(c, ctx.Tenant.ClusterID)
	if err != nil {
		return err
	}
	defer cluster.Close()

	err = cluster.CreateRisingWaveResourceCRD(c, ctx.Tenant)

	if err != nil {
		return eris.Wrap(err, "failed to create tenant CRD")
	}

	return nil
}

func (s *ProvisionTenantWorkflowService) postCreateTenantInfra(_ context.Context, _ *workflow.Workflow) error {
	return nil
}

func (s *ProvisionTenantWorkflowService) scalePodsDown(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ProvisionContext)
	if ctx.RestoreOption == nil {
		return nil
	}

	cluster, err := s.clusterProvider.GetCluster(c, ctx.Tenant.ClusterID)
	if err != nil {
		return err
	}
	defer cluster.Close()

	err = cluster.WaitForRisingWaveReady(c, ctx.Tenant)
	if err != nil {
		return eris.Wrapf(err, "failed to wait for risingwave crd to be ready")
	}

	agent, err := s.agentProvider.GetAgent(c, ctx.Tenant.ClusterID)
	if err != nil {
		return eris.Wrapf(err, "failed to get cloud agent, clusterId: %d", ctx.Tenant.ClusterID)
	}
	defer agent.Close()

	err = s.rwWorkload.StopWorkload(c, agent, ctx.Tenant)
	if err != nil {
		return eris.Wrap(err, "failed to scale risingwave pods down")
	}
	return nil
}

func (s *ProvisionTenantWorkflowService) cloneObjectStore(c context.Context, w *workflow.Workflow) workflow.ActivityResult {
	var ctx = w.Context.(*ProvisionContext)
	if ctx.RestoreOption == nil {
		return workflow.CompleteActivity()
	}

	agent, err := s.agentProvider.GetAgent(c, ctx.Tenant.ClusterID)
	if err != nil {
		return workflow.FailActivityWithError(eris.Wrapf(err, "failed to get cloud agent, clusterId: %d", ctx.Tenant.ClusterID))
	}
	defer agent.Close()

	storageProvider, err := s.storageFactory.GetStorageProviderFromCluster(c, ctx.Tenant.ClusterID, agent)
	if err != nil {
		return workflow.FailActivityWithError(eris.Wrapf(err, "failed to get storage provider, clusterId: %d", ctx.Tenant.ClusterID))
	}

	oldTenant := ctx.RestoreOption.OldTenant
	nextCursor, err := storageProvider.CloneStorageAwait(c, oldTenant.ResourceNamespace, ctx.Tenant.ResourceNamespace, "", ctx.Tenant.ID)
	if err != nil {
		return workflow.FailActivityWithError(eris.Wrap(err, "could not clone object store"))
	}
	ctx.RestoreOption.Cursor = nextCursor

	// clean up
	err = storageProvider.CleanupStorageCloneTaskAwait(c, ctx.Tenant.ResourceNamespace, ctx.Tenant.ID)
	if err != nil {
		return workflow.FailActivityWithError(err)
	}

	if ctx.RestoreOption.Cursor != "" {
		return workflow.ReenterActivity()
	}
	return workflow.CompleteActivity()
}

func (s *ProvisionTenantWorkflowService) cloneBackupObjectStore(c context.Context, w *workflow.Workflow) workflow.ActivityResult {
	var ctx = w.Context.(*ProvisionContext)
	if ctx.RestoreOption == nil {
		return workflow.CompleteActivity()
	}

	agent, err := s.agentProvider.GetAgent(c, ctx.Tenant.ClusterID)
	if err != nil {
		return workflow.FailActivityWithError(eris.Wrapf(err, "failed to get cloud agent, clusterId: %d", ctx.Tenant.ClusterID))
	}
	defer agent.Close()

	storageProvider, err := s.storageFactory.GetStorageProviderFromCluster(c, ctx.Tenant.ClusterID, agent)
	if err != nil {
		return workflow.FailActivityWithError(eris.Wrapf(err, "failed to get storage provider, clusterId: %d", ctx.Tenant.ClusterID))
	}

	oldTenant := ctx.RestoreOption.OldTenant
	nextCursor, err := storageProvider.CloneBackupStorageAwait(c, oldTenant.ResourceNamespace, ctx.Tenant.ResourceNamespace, "", ctx.Tenant.ID)
	if err != nil {
		return workflow.FailActivityWithError(eris.Wrap(err, "could not clone backup object store"))
	}
	ctx.RestoreOption.Cursor = nextCursor

	// clean up
	err = storageProvider.CleanupBackupStorageCloneTaskAwait(c, ctx.Tenant.ResourceNamespace, ctx.Tenant.ID)
	if err != nil {
		return workflow.FailActivityWithError(err)
	}

	if ctx.RestoreOption.Cursor != "" {
		return workflow.ReenterActivity()
	}
	return workflow.CompleteActivity()
}

func (s *ProvisionTenantWorkflowService) prepareMetaRestore(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ProvisionContext)
	if ctx.RestoreOption == nil {
		return nil
	}

	agent, err := s.agentProvider.GetAgent(c, ctx.Tenant.ClusterID)
	if err != nil {
		return eris.Wrapf(err, "failed to get cloud agent, clusterId: %d", ctx.Tenant.ClusterID)
	}
	defer agent.Close()

	metaStoreProvider, err := s.metaStoreFactory.NewProvider(c, agent, ctx.Tenant)
	if err != nil {
		return eris.Wrap(err, "failed to create meta store provider")
	}
	newMetastore, err := metaStoreProvider.GetMetaStore(c, ctx.Tenant)
	if err != nil {
		return eris.Wrapf(err, "failed to get metastore, clusterId: %d", ctx.Tenant.ClusterID)
	}

	err = agent.TruncateTables(c, &pbsvcsql.TruncateTablesRequest{
		Connection: newMetastore.ToConn(),
	})
	if err != nil {
		return eris.Wrapf(err, "failed to truncate tables, clusterId: %d", ctx.Tenant.ClusterID)
	}
	return nil
}

func (s *ProvisionTenantWorkflowService) metaRestore(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ProvisionContext)
	if ctx.RestoreOption == nil {
		return nil
	}

	agent, err := s.agentProvider.GetAgent(c, ctx.Tenant.ClusterID)
	if err != nil {
		return eris.Wrapf(err, "failed to get cloud agent, clusterId: %d", ctx.Tenant.ClusterID)
	}
	defer agent.Close()

	storageProvider, err := s.storageFactory.GetStorageProviderFromCluster(c, ctx.Tenant.ClusterID, agent)
	if err != nil {
		return eris.Wrapf(err, "failed to get storage provider, clusterId: %d", ctx.Tenant.ClusterID)
	}

	taskID := fmt.Sprintf("%v-restore", ctx.Tenant.NsID)
	logger.FromCtx(c).Named("Provision").Info(
		"prepare meta restore cloudagent task",
		zap.String("taskId", taskID),
	)

	metaStoreProvider, err := s.metaStoreFactory.NewProvider(c, agent, ctx.Tenant)
	if err != nil {
		return eris.Wrap(err, "failed to create meta store provider")
	}
	newMetastore, err := metaStoreProvider.GetMetaStore(c, ctx.Tenant)
	if err != nil {
		return eris.Wrap(err, "failed to get metastore")
	}

	snapshot, err := s.model.GetSnapshotBySnapshotID(c, ctx.RestoreOption.MSDBSnapshotID)
	if err != nil {
		return eris.Wrapf(err, "failed to get rw snapshot id, msdb snaphot id: %d", ctx.RestoreOption.MSDBSnapshotID)
	}

	storageURL, err := storageProvider.GetStorageURL(c, ctx.Tenant.TenantName, ctx.Tenant.ResourceNamespace)
	if err != nil {
		return err
	}

	req := &rwc.RestoreMetaRequest{
		ResourceMeta: &resource.Meta{
			Id:        taskID,
			Namespace: ctx.Tenant.ResourceNamespace,
		},
		MetaStoreType:     "sql",
		MetaSnapshotId:    snapshot.RwSnapshotID,
		HummockStorageUrl: storageURL,
		HummockStorageDir: storageProvider.GetDataDirectoryName(ctx.Tenant.ResourceNamespace),
		BackupStorageUrl:  storageProvider.GetBackupStorageURL(ctx.Tenant.ResourceNamespace),
		BackupStorageDir:  storageProvider.GetBackupDirectoryName(ctx.Tenant.ResourceNamespace),
		MetastoreConfig: &rwc.RestoreMetaRequest_SqlConfig{
			SqlConfig: &rwc.RestoreMetaRequestSql{
				SqlEndpoint: newMetastore.PostgreSQL.SQLEndpoint,
			},
		},
		RwImageTag:     snapshot.RwVersion,
		ServiceAccount: iam.GetServiceAccountName(ctx.Tenant),
	}
	if s, ok := storageProvider.(*storage.AZRStorageProvider); ok {
		req.Envs = map[string]string{
			"AZBLOB_ENDPOINT": s.GetEndpoint(),
		}
	}

	err = agent.MetaRestore(c, req, ctx.Tenant.ID)
	if err != nil {
		return err
	}

	logger.FromCtx(c).Named("Provision").Info(
		"meta restore successful",
		zap.String("snapshotId", fmt.Sprintf("%d", ctx.RestoreOption.MSDBSnapshotID)),
		zap.String("taskId", taskID),
	)

	return nil
}

func (s *ProvisionTenantWorkflowService) cleanupMetaRestoreTask(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ProvisionContext)
	if ctx.RestoreOption == nil {
		return nil
	}

	agent, err := s.agentProvider.GetAgent(c, ctx.Tenant.ClusterID)
	if err != nil {
		return eris.Wrapf(err, "failed to get cloud agent, clusterId: %d", ctx.Tenant.ClusterID)
	}
	defer agent.Close()

	taskID := fmt.Sprintf("%v-restore", ctx.Tenant.NsID)
	err = agent.CleanupMetaRestoreTask(c, taskID)
	if err != nil {
		return eris.Wrapf(err, "failed to cleanup metarestore task, clusterId: %d", ctx.Tenant.ClusterID)
	}
	return nil
}

func (s *ProvisionTenantWorkflowService) updateSystemParameters(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ProvisionContext)
	if ctx.RestoreOption == nil {
		return nil
	}

	agent, err := s.agentProvider.GetAgent(c, ctx.Tenant.ClusterID)
	if err != nil {
		return eris.Wrapf(err, "failed to get cloud agent, clusterId: %d", ctx.Tenant.ClusterID)
	}
	defer agent.Close()

	metaStoreProvider, err := s.metaStoreFactory.NewProvider(c, agent, ctx.Tenant)
	if err != nil {
		return eris.Wrap(err, "failed to create meta store provider")
	}
	metaStore, err := metaStoreProvider.GetMetaStore(c, ctx.Tenant)
	if err != nil {
		return eris.Wrap(err, "failed to get metastore")
	}

	storageProvider, err := s.storageFactory.GetStorageProviderFromCluster(c, ctx.Tenant.ClusterID, agent)
	if err != nil {
		return eris.Wrapf(err, "failed to get storage provider, clusterId: %d", ctx.Tenant.ClusterID)
	}

	storageURL, err := storageProvider.GetStorageURL(c, ctx.Tenant.TenantName, ctx.Tenant.ResourceNamespace)
	if err != nil {
		return err
	}

	err = agent.UpdateSystemParameters(c, &pbsvcsql.UpdateSystemParametersRequest{
		Connection: metaStore.ToConn(),
		SystemParameters: map[string]string{
			SystemParamStateStore:             fmt.Sprintf("hummock+%s", storageURL),
			SystemParamDataDirectory:          storageProvider.GetDataDirectoryName(ctx.Tenant.ResourceNamespace),
			SystemParamBackupStorageURL:       storageProvider.GetBackupStorageURL(ctx.Tenant.ResourceNamespace),
			SystemParamBackupStorageDirectory: storageProvider.GetBackupDirectoryName(ctx.Tenant.ResourceNamespace),
		},
	})
	if err != nil {
		return eris.Wrapf(err, "failed to get metastore, clusterId: %d", ctx.Tenant.ClusterID)
	}

	return nil
}

func (s *ProvisionTenantWorkflowService) scalePodsUp(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ProvisionContext)
	if ctx.RestoreOption == nil {
		return nil
	}

	agent, err := s.agentProvider.GetAgent(c, ctx.Tenant.ClusterID)
	if err != nil {
		return eris.Wrapf(err, "failed to get cloud agent, clusterId: %d", ctx.Tenant.ClusterID)
	}
	err = s.rwWorkload.StartWorkload(c, agent, ctx.Tenant)

	if err != nil {
		return eris.Wrap(err, "failed to create tenant CRD")
	}

	return nil
}

func (s *ProvisionTenantWorkflowService) validateTenant(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ProvisionContext)

	cluster, err := s.clusterProvider.GetCluster(c, ctx.Tenant.ClusterID)
	if err != nil {
		return err
	}
	defer cluster.Close()

	err = cluster.WaitForRisingWaveReady(c, ctx.Tenant)
	if err != nil {
		return err
	}

	rwClient, err := client.ObtainRisingWaveClient(c, s.model, ctx.Tenant.ID, client.DefaultDb, client.RootCredential)
	if err != nil {
		return err
	}

	err = rwClient.Ping(c, ctx.Tenant.ImageTag)
	if err != nil {
		return err
	}

	return nil
}

func (s *ProvisionTenantWorkflowService) resetPassword(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ProvisionContext)
	tenantModel := s.model
	endpoint, err := rwproxy.GetTenantEndpointByTenantID(c, tenantModel, ctx.Tenant.ID)
	if err != nil {
		return err
	}

	// kernel dependency: the new created instance should always have two super users: (root, ) and (postgres, postgres)

	// alter root password
	rootEmptyPassword := client.RisingWaveCredential{
		Username: "root",
		Password: "",
	}
	rootDefaultPassword := client.RootCredential

	conn, err := client.NewRisingWaveClient(endpoint, rootEmptyPassword).Open(c)
	if err != nil {
		// check that password already be altered
		checkConn, checkErr := client.NewRisingWaveClient(endpoint, rootDefaultPassword).Open(c)
		if checkErr != nil {
			// cannot connect by both passwords
			return eris.Wrap(err, "failed to open connection using empty password")
		}
		// password already be altered
		checkConn.Close(c)
	} else {
		defer conn.Close(c)
		// the password is an empty string, alter password
		query := fmt.Sprintf("ALTER USER \"%s\" WITH PASSWORD $1;", "root")
		_, err = conn.Exec(c, query, config.Conf.Mgmt.RisingWave.Password)
		if err != nil {
			return eris.Wrap(err, "failed to reset root password")
		}
		conn.Close(c)
	}

	// alter postgres password
	rootConn, err := client.NewRisingWaveClient(endpoint, rootDefaultPassword).Open(c)
	if err != nil {
		return eris.Wrap(err, "failed to open connection using default password when changing postgres password")
	}
	defer rootConn.Close(c)
	query := fmt.Sprintf("ALTER USER \"%s\" WITH PASSWORD $1;", "postgres")
	_, err = rootConn.Exec(c, query, config.Conf.Mgmt.RisingWave.Password)
	if err != nil {
		return eris.Wrap(err, "failed to reset postgres password")
	}
	rootConn.Close(c)

	return nil
}

func (s *ProvisionTenantWorkflowService) setOAuthAccess(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ProvisionContext)

	kernelVersion, err := s.rwc.GetKernelVersion(c, ctx.Tenant.ID)
	if err != nil {
		return err
	}

	if !version.IsOAuthRoleSupported(kernelVersion) {
		return nil
	}

	orgID := ctx.Tenant.OrgID
	err = s.rwc.CreateDefaultOAuthUser(c, ctx.Tenant.ID, orgID)
	if err != nil {
		return err
	}

	return nil
}

func (s *ProvisionTenantWorkflowService) markAsBillable(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ProvisionContext)

	tenantID, err := namespace.Parse(ctx.Tenant.ResourceNamespace)
	if err != nil {
		return err
	}

	err = s.account.MarkTenantAsBillable(c, tenantID)
	if err != nil {
		return err
	}

	return nil
}

func (s *ProvisionTenantWorkflowService) quiesceOriginalTenant(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ProvisionContext)
	if ctx.RestoreOption == nil {
		return nil
	}

	oldTenant, err := s.model.GetTenantByID(c, ctx.RestoreOption.OldTenant.ID)
	if err != nil {
		return err
	}

	if oldTenant.Status == model.TenantStatusStopped || oldTenant.Status == model.TenantStatusStopping {
		return nil
	}

	emptyID := uuid.UUID{}
	if ctx.RestoreOption.QuiesceOriginalTenantWorkflowID == emptyID {
		id, err := s.stopstart.StopTenantWithWorkflow(c, ctx.RestoreOption.OldTenant.ID)
		if err != nil {
			return eris.Wrap(err, "failed to run stop workflow")
		}
		ctx.RestoreOption.QuiesceOriginalTenantWorkflowID = id
	}

	return nil
}

func (s *ProvisionTenantWorkflowService) waitOriginalTenantQuiesced(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ProvisionContext)
	if ctx.RestoreOption == nil {
		return nil
	}

	oldTenant, err := s.model.GetTenantByID(c, ctx.RestoreOption.OldTenant.ID)
	if err != nil {
		return err
	}

	if oldTenant.Status != model.TenantStatusStopping && oldTenant.Status != model.TenantStatusStopped {
		return eris.Errorf("expected original cluster to be stopping or stopped after restore but state is %s", oldTenant.Status)
	}

	err = s.model.CompareAndUpdateTenantStatusByIDAndStatuses(c, oldTenant.ID, model.TenantStatusQuiesced, []string{model.TenantStatusStopped})
	if err != nil {
		return err
	}

	return nil
}

func (s *ProvisionTenantWorkflowService) markAsProvisioned(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ProvisionContext)
	tenantModel := s.model

	// Mark tenant as provisioned in MSDB.
	err := tenantModel.UpdateTenantStatusByID(c, ctx.Tenant.ID, model.TenantStatusRunning)
	if err != nil {
		return eris.Wrap(err, "failed to mark tenant as provisioned")
	}
	return nil
}

func (s *ProvisionTenantWorkflowService) startMonitoring(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ProvisionContext)

	emptyID := uuid.UUID{}

	// run matview workflow. This will be changed to rwdb monitoring in the future
	if ctx.MonitorWorkflowID == emptyID {
		id, err := s.monitor.RunMonitorWorkflow(c, ctx.Tenant.ID)
		if err != nil {
			return eris.Wrap(err, "failed to run monitor workflow")
		}
		ctx.MonitorWorkflowID = id
	}

	// run expire workflow. This will expire the free tier tenant and cleanup test tenant
	if ctx.ExpireWorkflowID == emptyID {
		id, err := s.expire.RunExpireTenantWorkflow(c, ctx.Tenant.ID)
		if err != nil {
			return eris.Wrap(err, "failed to run expire workflow")
		}
		ctx.ExpireWorkflowID = id
	}

	return nil
}

func (s *ProvisionTenantWorkflowService) cleanUp(c context.Context, w *workflow.Workflow) workflow.ActivityResult {
	var ctx = w.Context.(*ProvisionContext)

	logger.FromCtx(c).Named("ProvisionWorkflow").Warn("Provision failed")

	var m = s.model
	err := m.UpdateTenantStatusByID(c, ctx.Tenant.ID, model.TenantStatusFailed)
	if err != nil {
		return workflow.FailActivityWithError(err)
	}

	return workflow.CompleteActivity()
}

func (s *ProvisionTenantWorkflowService) stopTenantInstance(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ProvisionContext)

	_, err := s.model.GetTenantByID(c, ctx.Tenant.ID)
	if errors.IsTenantNotExistError(err) {
		return nil
	}
	if err != nil {
		return err
	}

	cluster, err := s.clusterProvider.GetCluster(c, ctx.Tenant.ClusterID)
	if err != nil {
		return err
	}
	defer cluster.Close()

	// delete risingwave resource
	err = cluster.DeleteRisingWaveResource(c, ctx.Tenant)
	if err != nil {
		logger.FromCtx(c).Named("ProvisionWorkflow").Warn(
			"failed to clean up risingwave resource",
			zap.Error(err),
		)
	}
	err = cluster.WaitForRisingWaveDeleted(c, ctx.Tenant)
	if err != nil {
		logger.FromCtx(c).Named("ProvisionWorkflow").Warn(
			"failed to clean up risingwave resource",
			zap.Error(err),
		)
	}

	return nil
}

func (s *ProvisionTenantWorkflowService) postStopTenantInstance(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ProvisionContext)

	tenant, err := s.model.GetTenantByID(c, ctx.Tenant.ID)
	if errors.IsTenantNotExistError(err) {
		return nil
	}
	if err != nil {
		return err
	}

	cluster, err := s.clusterProvider.GetCluster(c, ctx.Tenant.ClusterID)
	if err != nil {
		return err
	}
	defer cluster.Close()

	err = cluster.CleanupStorageDeletionTask(c, tenant.ResourceNamespace, tenant.ID)
	if err != nil {
		return err
	}

	return nil
}
