package provision

import (
	"context"

	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow"
)

func (p *ProvisionTenantWorkflowService) enableServerlessCompactionExtension(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ProvisionContext)

	// Skip if the tenant is standalone.
	if ctx.Tenant.Resources.IsStandalone() {
		return nil
	}

	// Check if the extension is requested.
	if !ctx.IsServerlessCompactionRequested() {
		return nil
	}

	ws, err := tenantext.NewServerlessCompactionWorkflowService(c,
		p.model,
		p.txProvider,
		p.agentProvider,
		p.storageFactory,
		ctx.TenantID,
		false,
	)
	if err != nil {
		return eris.Wrapf(err, "failed to create serverless compaction workflow service for tenant %d", ctx.TenantID)
	}

	return ws.Enable(c, tenantext.ServerlessCompactionParams{
		MaxConcurrency: &ctx.ServerlessCompaction.MaximumCPUSize,
	})
}

func (p *ProvisionTenantWorkflowService) disableServerlessCompactionExtension(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*ProvisionContext)

	// Skip if the tenant is standalone.
	if ctx.Tenant.Resources.IsStandalone() {
		return nil
	}

	// Check if the extension is requested.
	if !ctx.IsServerlessCompactionRequested() {
		return nil
	}

	ws, err := tenantext.NewServerlessCompactionWorkflowService(c,
		p.model,
		p.txProvider,
		p.agentProvider,
		p.storageFactory,
		ctx.TenantID,
		true,
	)
	if err != nil {
		return eris.Wrapf(err, "failed to create serverless compaction workflow service for tenant %d", ctx.TenantID)
	}

	return ws.DisableIfExists(c)
}
