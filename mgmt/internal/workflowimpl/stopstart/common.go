package stopstart

import (
	"context"
	"time"

	"github.com/risingwavelabs/risingwave-cloud/internal/infra/storage"

	agentprovider "github.com/risingwavelabs/risingwave-cloud/internal/agent/provider"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/extensions"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/rwworkload"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/rwdb"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow/builder"

	"github.com/risingwavelabs/eris"
	"go.uber.org/zap"

	"github.com/risingwavelabs/risingwave-cloud/internal/account"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/modeltx"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow/activity"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/expire"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/notifications"
	"github.com/risingwavelabs/risingwave-cloud/shared/logger"
)

const (
	PreprocessingRequestState        = "PreprocessingRequest"
	StopRisingWaveResourceState      = "StopRisingWaveResource"
	PostStopRisingWaveResourceState  = "PostStopRisingWaveResource"
	StopRisingWaveWorkloadState      = "StopRisingWaveWorkload"
	StartRisingWaveResourceState     = "StartRisingWaveResource"
	EnableExtensionsIfExistState     = "EnableExtensionsIfExist"
	PostStartRisingWaveResourceState = "PostStartRisingWaveResource"
	StartRisingWaveWorkloadState     = "StartRisingWaveWorkload"
	TransmitSuccessNotificationState = "TransmitSuccessNotification"
	ValidationState                  = "Validating"
	TriggerExpirationState           = "TriggerExpiration"
	AwakeMonitorState                = "AwakeMonitor"
	CleanUpState                     = "CleanUp"
	CompleteState                    = "Complete"
)

type StopStartTenantWorkflowService struct { //nolint:revive
	startBuilder    *builder.WorkflowBuilder
	stopBuilder     *builder.WorkflowBuilder
	restartBuilder  *builder.WorkflowBuilder
	repository      *workflow.Repository
	storageFactory  storage.FactoryInterface
	clusterProvider k8s.IKubernetesClusterProvider
	agentProvider   agentprovider.CloudAgentProviderInterface
	rwWorkload      rwworkload.RisingWaveWorkloadManager
	account         account.Agent
	expire          *expire.ExpireTenantWorkflowService
	userCli         rwdb.RisingWaveClientUserInterface
	compaction      extensions.ServerlessCompaction

	tenantModel *model.Model
	txProvider  modeltx.TxProvider
}

func NewStopStartTenantWorkflowService(
	repository *workflow.Repository,
	clusterProvider k8s.IKubernetesClusterProvider,
	agentProvider agentprovider.CloudAgentProviderInterface,
	rwWorkload rwworkload.RisingWaveWorkloadManager,
	account account.Agent,
	expire *expire.ExpireTenantWorkflowService,
	tenantModel *model.Model,
	txProvider modeltx.TxProvider,
	rwCli rwdb.RisingWaveClientInterface,
	compaction extensions.ServerlessCompaction,
	storageFactory storage.FactoryInterface,
) *StopStartTenantWorkflowService {
	s := &StopStartTenantWorkflowService{
		repository:      repository,
		clusterProvider: clusterProvider,
		agentProvider:   agentProvider,
		rwWorkload:      rwWorkload,
		account:         account,
		expire:          expire,
		tenantModel:     tenantModel,
		txProvider:      txProvider,
		userCli:         rwCli,
		compaction:      compaction,
		storageFactory:  storageFactory,
	}

	var (
		// Activities to start RW.
		PreprocessStartRequestActivity           = activity.WrapDoError(s.preprocessStartRequest)
		StartRisingWaveResourceActivity          = activity.WrapDoError(s.startRisingWaveResource)
		EnableExtensionsIfExistActivity          = activity.WrapDoError(s.enableExtensionsIfExist)
		PostStartRisingWaveResourceActivity      = activity.WrapDoError(s.postStartRisingWaveResource)
		ValidateTenantReadyActivity              = activity.WrapDoError(s.validateTenantReady)
		TransmitStartSuccessNotificationActivity = s.newNotificationActivity(notifications.StartSuccess)
		TransmitStartFailNotificationActivity    = s.newNotificationActivity(notifications.StartFailure)

		// Activities to stop RW.
		PreprocessStopRequestActivity           = activity.WrapDoError(s.preprocessStopRequest)
		StopRisingWaveResourceActivity          = activity.WrapDoError(s.stopRisingWaveResource)
		PostStopRisingWaveResourceActivity      = activity.WrapDoError(s.postStopRisingWaveResource)
		ValidateTenantStoppedActivity           = activity.WrapDoError(s.validateTenantStopped)
		TriggerExpirationActivity               = activity.WrapDoError(s.triggerExpiration)
		AwakeMonitorActivity                    = activity.WrapDoError(s.awakeMonitor)
		TransmitStopSuccessNotificationActivity = s.newNotificationActivity(notifications.StopSuccess)
		TransmitStopFailNotificationActivity    = s.newNotificationActivity(notifications.StopFailure)

		// Activities to restart RW.
		RestartPreprocessRequestActivity       = activity.WrapDoError(s.preprocessRestartRequest)
		RestartStopRisingWaveWorkloadActivity  = activity.WrapDoError(s.stopRisingWaveWorkload)
		RestartStartRisingWaveWorkloadActivity = activity.WrapDoError(s.startRisingWaveWorkload)
		RestartCompleteActivity                = activity.WrapDoError(s.completeRestart)
	)

	retry := activity.RetryWrapperLinDelay(repository, 3, time.Minute)
	fail := activity.FailWorkflowWrapper

	// Extensions need to connect to risingwave.
	// Using 750 sec timeout determined in https://github.com/risingwavelabs/risingwave-cloud/pull/10787
	retryStart := activity.RetryWrapperLinDelay(repository, 150, 5*time.Second)

	s.startBuilder = builder.NewBuilder(StartTenantWorkflowType).
		Next(PreprocessingRequestState).Activity(PreprocessStartRequestActivity).Wrap(retry).On(workflow.ErrorEvent, CleanUpState).
		Next(StartRisingWaveResourceState).Activity(StartRisingWaveResourceActivity).Wrap(retry).
		Next(EnableExtensionsIfExistState).Activity(EnableExtensionsIfExistActivity).Wrap(retryStart).
		Defer(PostStartRisingWaveResourceState, activity.WrapDefer(retry(PostStartRisingWaveResourceActivity), repository)).On(workflow.ErrorEvent, CleanUpState).
		Next(ValidationState).Activity(ValidateTenantReadyActivity).Wrap(retry).On(workflow.ErrorEvent, CleanUpState).
		Next(TriggerExpirationState).Activity(TriggerExpirationActivity).Wrap(retry).On(workflow.ErrorEvent, AwakeMonitorState).
		Next(AwakeMonitorState).Activity(AwakeMonitorActivity).Wrap(retry).On(workflow.ErrorEvent, TransmitSuccessNotificationState).
		Next(TransmitSuccessNotificationState).Activity(TransmitStartSuccessNotificationActivity).
		Succeed().
		From(CleanUpState).Activity(TransmitStartFailNotificationActivity).
		Fail()

	s.stopBuilder = builder.NewBuilder(StopTenantWorkflowType).
		Next(PreprocessingRequestState).Activity(PreprocessStopRequestActivity).Wrap(retry).On(workflow.ErrorEvent, CleanUpState).
		Next(StopRisingWaveResourceState).Activity(StopRisingWaveResourceActivity).Wrap(retry).
		Defer(PostStopRisingWaveResourceState, activity.WrapDefer(retry(PostStopRisingWaveResourceActivity), repository)).On(workflow.ErrorEvent, CleanUpState).
		Next(ValidationState).Activity(ValidateTenantStoppedActivity).Wrap(retry).On(workflow.ErrorEvent, CleanUpState).
		Next(TransmitSuccessNotificationState).Activity(TransmitStopSuccessNotificationActivity).
		Succeed().
		From(CleanUpState).Activity(TransmitStopFailNotificationActivity).
		Fail()

	s.restartBuilder = builder.NewBuilder(RestartTenantWorkflowType).
		Next(PreprocessingRequestState).Activity(RestartPreprocessRequestActivity).
		Next(StopRisingWaveWorkloadState).Activity(RestartStopRisingWaveWorkloadActivity).
		Next(StartRisingWaveWorkloadState).Activity(RestartStartRisingWaveWorkloadActivity).
		Next(CompleteState).Activity(RestartCompleteActivity).
		WrapAll(retry).
		WrapAll(fail).
		Succeed()

	startContextProvider := func() any { return &StartTenantContext{} }
	stopContextProvider := func() any { return &StopTenantContext{} }
	restartContextProvider := func() any { return &RestartTenantContext{} }
	repository.Registry.Register(StartTenantWorkflowType, s.startBuilder.WorkflowConstructor(startContextProvider))
	repository.Registry.Register(StopTenantWorkflowType, s.stopBuilder.WorkflowConstructor(stopContextProvider))
	repository.Registry.Register(RestartTenantWorkflowType, s.restartBuilder.WorkflowConstructor(restartContextProvider))
	return s
}

func (s *StopStartTenantWorkflowService) newNotificationActivity(notificationEvent notifications.NotificationType) workflow.Activity {
	return activity.WrapDoError(func(c context.Context, w *workflow.Workflow) error {
		var ctx = w.Context.(workflowimpl.TenantContext)

		tenant, err := s.tenantModel.GetTenantByID(c, ctx.GetTenantID())
		if err != nil {
			return eris.Wrapf(err, "failed to get tenant by id %d, event: %s", ctx.GetTenantID(), notificationEvent)
		}

		notificationBody, err := notifications.GetNotificationTemplate(
			notificationEvent,
			tenant,
		)
		if err != nil {
			logger.FromCtx(c).Named("stop start").Warn(
				"failed to parse notification templates",
				zap.String("orgId", tenant.OrgID.String()),
				zap.Uint64("tenantId", tenant.ID),
				zap.Error(err),
			)
		}

		err = s.account.SendNotificationsToUser(c, notificationBody)
		if err != nil {
			logger.FromCtx(c).Named("stop start").Warn(
				"failed to transmit stop start notification",
				zap.String("orgId", tenant.OrgID.String()),
				zap.Uint64("tenantId", tenant.ID),
				zap.Error(err),
			)
		}

		return nil
	})
}
