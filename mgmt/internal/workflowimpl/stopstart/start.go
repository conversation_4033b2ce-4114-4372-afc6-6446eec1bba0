package stopstart

import (
	"context"
	"strconv"

	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext"

	"github.com/google/uuid"
	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/extensions"
	"github.com/risingwavelabs/risingwave-cloud/internal/metrics"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/modeltx"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/extensions/backfill"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/monitor"
	"github.com/risingwavelabs/risingwave-cloud/shared/logger"
)

const StartTenantWorkflowType = "StartTenant"

type StartTenantContext struct {
	workflowimpl.TenantContextImpl
	RequiredStatus    string `json:"RequiredStatus"`
	TriggerExpiration bool   `json:"TriggerExpiration"`
}

func (ctx *StartTenantContext) Labels() map[string]string {
	return map[string]string{
		"tenantId": strconv.FormatUint(ctx.TenantID, 10),
	}
}

func (s *StopStartTenantWorkflowService) NewStartTenantWorkflow(tenantID uint64, triggerExpiration bool) *workflow.Workflow {
	ctx := &StartTenantContext{
		TenantContextImpl: workflowimpl.NewTenantContext(tenantID),
		RequiredStatus:    model.TenantStatusStarting,
		TriggerExpiration: triggerExpiration,
	}
	var w = s.startBuilder.Build()
	w.Context = ctx
	return w
}

func (s *StopStartTenantWorkflowService) StartTenantWithWorkflow(ctx context.Context, tenantID uint64) (uuid.UUID, error) {
	ctx, cancel := context.WithTimeout(ctx, 5*model.TransactionTimeout())
	defer cancel()

	workflowID, err := modeltx.Query(ctx, s.txProvider, func(ctx context.Context, txModel *model.Model) (uuid.UUID, error) {
		tenant, err := txModel.GetTenantByID(ctx, tenantID)
		if err != nil {
			return uuid.UUID{}, eris.Wrap(err, "failed to get tenant")
		}
		triggerExpire := tenant.Status == model.TenantStatusExpired

		err = txModel.UpdateTenantStatusByID(ctx, tenantID, model.TenantStatusStarting)
		if err != nil {
			return uuid.UUID{}, eris.Wrap(err, "failed to update tenant status")
		}

		// Update the tenant extension status to enabling if it exists.
		err = txModel.UpdateTenantExtensionStatusByTenantIDAndResourceType(ctx, tenantID,
			model.ExtensionsResourceTypeCompaction, model.ExtensionStatusEnabling)
		if err != nil && eris.GetCode(err) != eris.CodeNotFound {
			return uuid.UUID{}, eris.Wrap(err, "failed to update tenant extension status")
		}

		id, err := s.repository.CreateInTx(ctx, txModel, s.NewStartTenantWorkflow(tenantID, triggerExpire))
		if err != nil {
			return uuid.UUID{}, eris.Wrap(err, "failed to create workflow")
		}
		return id, nil
	})
	if err != nil {
		return uuid.UUID{}, err
	}
	metrics.ReportWorkflowCreated(StartTenantWorkflowType)

	return workflowID, nil
}

func (s *StopStartTenantWorkflowService) preprocessStartRequest(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*StartTenantContext)
	var tenantModel = s.tenantModel

	tenant, err := tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}

	if tenant.Status != ctx.RequiredStatus {
		return eris.Errorf("illegal state, tenant %d not in %s status", tenant.ID, ctx.RequiredStatus)
	}

	return nil
}

func (s *StopStartTenantWorkflowService) startRisingWaveResource(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*StartTenantContext)
	var tenantModel = s.tenantModel

	tenant, err := tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}

	cluster, err := s.clusterProvider.GetCluster(c, tenant.ClusterID)
	if err != nil {
		return err
	}
	defer cluster.Close()

	err = cluster.StartRisingWaveResource(c, tenant)
	if err != nil {
		return eris.Wrap(err, "failed to start tenant")
	}
	return nil
}

func (s *StopStartTenantWorkflowService) enableExtensionsIfExist(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*StartTenantContext)
	var tenantModel = s.tenantModel

	// setup
	tenant, err := tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}
	cluster, err := s.clusterProvider.GetCluster(c, tenant.ClusterID)
	if err != nil {
		return err
	}
	defer cluster.Close()

	// enable extensions if exist
	backExt := extensions.NewServerlessBackfill(
		logger.L().Named("ServerlessBackfill"),
		s.agentProvider,
		tenant.ClusterID,
	)
	if err = backfill.EnableExtensionsBackfillIfRunning(c, tenant, backExt, s.tenantModel, s.userCli); err != nil {
		return err
	}

	ws, err := tenantext.NewServerlessCompactionWorkflowService(
		c,
		s.tenantModel,
		s.txProvider,
		s.agentProvider,
		s.storageFactory,
		tenant.ID,
		false,
	)
	if err != nil {
		return eris.Wrapf(err, "failed to create serverless compaction workflow service for tenant %d", tenant.ID)
	}
	return ws.StartIfExists(c)
}

func (s *StopStartTenantWorkflowService) postStartRisingWaveResource(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*StartTenantContext)
	var tenantModel = s.tenantModel

	tenant, err := tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}
	cluster, err := s.clusterProvider.GetCluster(c, tenant.ClusterID)
	if err != nil {
		return err
	}
	defer cluster.Close()

	err = cluster.PostStartRisingWaveResource(c, tenant)
	if err != nil {
		return eris.Wrap(err, "failed to clean resources after starting tenant")
	}
	return nil
}

func (s *StopStartTenantWorkflowService) validateTenantReady(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*StartTenantContext)
	var tenantModel = s.tenantModel

	tenant, err := tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}
	cluster, err := s.clusterProvider.GetCluster(c, tenant.ClusterID)
	if err != nil {
		return err
	}
	defer cluster.Close()

	err = cluster.WaitForRisingWaveReady(c, tenant)
	if err != nil {
		return eris.Wrap(err, "failed to wait for the pods ready")
	}

	err = tenantModel.UpdateTenantStatusByID(c, tenant.ID, model.TenantStatusRunning)
	if err != nil {
		return eris.Wrap(err, "failed to update tenant status")
	}
	return nil
}

func (s *StopStartTenantWorkflowService) triggerExpiration(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*StartTenantContext)
	if !ctx.TriggerExpiration {
		return nil
	}

	err := s.expire.CancelExpireWorkflow(c, ctx.TenantID)
	if err != nil {
		return eris.Wrap(err, "failed to cancel expire workflow")
	}
	_, err = s.expire.RunExpireTenantWorkflow(c, ctx.TenantID)
	if err != nil {
		return eris.Wrap(err, "failed to run expire workflow")
	}
	return nil
}

func (s *StopStartTenantWorkflowService) awakeMonitor(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*StartTenantContext)

	monitorWorkflow, err := s.tenantModel.GetRunningWorkflowOfTenant(c, monitor.MonitorWorkflowType, ctx.TenantID)
	if err != nil {
		return err
	}
	if monitorWorkflow == nil {
		return nil
	}

	// try to awake the monitoring workflow. If it's in running, will fail and retry later
	err = s.tenantModel.AwakeWorkflow(c, monitorWorkflow.ID)
	if err != nil {
		return eris.Wrap(err, "failed to awake monitoring workflow")
	}
	return nil
}
