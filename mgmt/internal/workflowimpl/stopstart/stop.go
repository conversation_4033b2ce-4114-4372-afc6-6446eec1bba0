package stopstart

import (
	"context"
	"strconv"

	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext"

	"github.com/google/uuid"
	"github.com/risingwavelabs/eris"

	agentprovider "github.com/risingwavelabs/risingwave-cloud/internal/agent/provider"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/extensions"
	"github.com/risingwavelabs/risingwave-cloud/internal/metrics"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/modeltx"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/extensions/backfill"
	"github.com/risingwavelabs/risingwave-cloud/shared/logger"
)

const StopTenantWorkflowType = "StopTenant"

type StopTenantContext struct {
	workflowimpl.TenantContextImpl
	RequiredStatus string `json:"RequiredStatus"`
}

func (ctx *StopTenantContext) Labels() map[string]string {
	return map[string]string{
		"tenantId": strconv.FormatUint(ctx.TenantID, 10),
	}
}

func (s *StopStartTenantWorkflowService) NewStopTenantWorkflow(tenantID uint64) *workflow.Workflow {
	ctx := &StopTenantContext{
		TenantContextImpl: workflowimpl.NewTenantContext(tenantID),
		RequiredStatus:    model.TenantStatusStopping,
	}
	var w = s.stopBuilder.Build()
	w.Context = ctx
	return w
}

func (s *StopStartTenantWorkflowService) StopTenantWithWorkflow(ctx context.Context, tenantID uint64) (uuid.UUID, error) {
	ctx, cancel := context.WithTimeout(ctx, 5*model.TransactionTimeout())
	defer cancel()

	workflowID, err := modeltx.Query(ctx, s.txProvider, func(ctx context.Context, txModel *model.Model) (uuid.UUID, error) {
		err := txModel.UpdateTenantStatusByID(ctx, tenantID, model.TenantStatusStopping)
		if err != nil {
			return uuid.UUID{}, eris.Wrap(err, "failed to update tenant status")
		}

		// Update the tenant extension status to disabling if it exists.
		err = txModel.UpdateTenantExtensionStatusByTenantIDAndResourceType(ctx, tenantID,
			model.ExtensionsResourceTypeCompaction, model.ExtensionStatusDisabling)
		if err != nil && eris.GetCode(err) != eris.CodeNotFound {
			return uuid.UUID{}, eris.Wrap(err, "failed to update tenant extension status")
		}

		id, err := s.repository.CreateInTx(ctx, txModel, s.NewStopTenantWorkflow(tenantID))
		if err != nil {
			return uuid.UUID{}, eris.Wrap(err, "failed to create workflow")
		}

		return id, nil
	})
	if err != nil {
		return uuid.UUID{}, err
	}
	metrics.ReportWorkflowCreated(StopTenantWorkflowType)

	return workflowID, nil
}

func (s *StopStartTenantWorkflowService) preprocessStopRequest(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*StopTenantContext)
	var tenantModel = s.tenantModel

	tenant, err := tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}

	if tenant.Status != ctx.RequiredStatus {
		return eris.Errorf("illegal state, tenant %d not in %s status", tenant.ID, ctx.RequiredStatus)
	}

	return nil
}

func (s *StopStartTenantWorkflowService) stopRisingWaveResource(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*StopTenantContext)
	var tenantModel = s.tenantModel

	tenant, err := tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}
	cluster, err := s.clusterProvider.GetCluster(c, tenant.ClusterID)
	if err != nil {
		return err
	}
	defer cluster.Close()

	err = cluster.StopRisingWaveResource(c, tenant)
	if err != nil {
		return eris.Wrap(err, "failed to stop tenant")
	}

	agentProvider := agentprovider.NewCloudAgentProvider(tenantModel)

	backExt := extensions.NewServerlessBackfill(
		logger.L().Named("ServerlessBackfill"),
		agentProvider,
		tenant.ClusterID,
	)
	if err = backfill.DeleteExtensionsBackfillIfRunning(c, ctx.TenantID, tenant, backExt, tenantModel); err != nil {
		return err
	}

	ws, err := tenantext.NewServerlessCompactionWorkflowService(
		c,
		s.tenantModel,
		s.txProvider,
		s.agentProvider,
		s.storageFactory,
		tenant.ID,
		false,
	)
	if err != nil {
		return eris.Wrapf(err, "failed to create serverless compaction workflow service for tenant %d", tenant.ID)
	}

	return ws.StopIfExists(c)
}

func (s *StopStartTenantWorkflowService) postStopRisingWaveResource(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*StopTenantContext)
	var tenantModel = s.tenantModel

	tenant, err := tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}
	cluster, err := s.clusterProvider.GetCluster(c, tenant.ClusterID)
	if err != nil {
		return err
	}
	defer cluster.Close()

	err = cluster.PostStopRisingWaveResource(c, tenant)
	if err != nil {
		return eris.Wrap(err, "failed to clean resources after stoppping tenant")
	}
	return nil
}

func (s *StopStartTenantWorkflowService) validateTenantStopped(c context.Context, w *workflow.Workflow) error {
	var ctx = w.Context.(*StopTenantContext)
	var tenantModel = s.tenantModel

	tenant, err := tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}
	cluster, err := s.clusterProvider.GetCluster(c, tenant.ClusterID)
	if err != nil {
		return err
	}
	defer cluster.Close()

	err = cluster.WaitForPodsTerminated(c, tenant)
	if err != nil {
		return eris.Wrap(err, "failed to wait for the pods end")
	}

	err = tenantModel.UpdateTenantStatusByID(c, tenant.ID, model.TenantStatusStopped)
	if err != nil {
		return eris.Wrap(err, "failed to update tenant status")
	}
	return nil
}
