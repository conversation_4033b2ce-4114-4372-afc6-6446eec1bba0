package update

import (
	"context"
	"strconv"
	"time"

	"github.com/risingwavelabs/risingwave-cloud/internal/infra/storage"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/extensions"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/podstrategy"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenantext"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl/provision"

	"github.com/google/uuid"
	"github.com/risingwavelabs/eris"
	"github.com/samber/lo"

	pbrw "github.com/risingwavelabs/cloudagent/pbgen/common/rw"
	pbk8ssvc "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/configset"

	"github.com/risingwavelabs/risingwave-cloud/internal/account"
	agentprovider "github.com/risingwavelabs/risingwave-cloud/internal/agent/provider"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/rwconfig"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/rwcrd"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/rwwait"
	"github.com/risingwavelabs/risingwave-cloud/internal/k8s/rwworkload"
	"github.com/risingwavelabs/risingwave-cloud/internal/metrics"
	"github.com/risingwavelabs/risingwave-cloud/internal/model"
	"github.com/risingwavelabs/risingwave-cloud/internal/model/modeltx"
	"github.com/risingwavelabs/risingwave-cloud/internal/risingwave/version"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenant/component"
	"github.com/risingwavelabs/risingwave-cloud/internal/tenant/resourcespec"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow/activity"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflow/builder"
	"github.com/risingwavelabs/risingwave-cloud/internal/workflowimpl"
	"github.com/risingwavelabs/risingwave-cloud/shared/namespace"
	"github.com/risingwavelabs/risingwave-cloud/shared/ptr"
)

const WorkflowType = "UpdateResources"

const (
	PreprocessingRequestState         = "PreprocessingRequest"
	NotifyAccountService              = "NotifyAccountService"
	CordonDeletingWorkers             = "CordonDeletingWorkers"
	DrainDeletingWorkers              = "DrainDeletingWorkers"
	UpdatingResourcesState            = "UpdatingResources"
	UpdatingServerlessCompactionState = "UpdatingServerlessCompaction"
	WaitBeforeUpdatedState            = "WaitBeforeUpdated"
	ScaleOnNewWorkers                 = "ScaleToNewWorkers"
	DeleteWorkers                     = "DeleteWorkers"
	UpdateTenantStatus                = "UpdateTenantStatus"
)

type ComponentResourcesRequest struct {
	Replicas  int32                  `json:"replicas,omitempty"`
	Resources component.ResourceSpec `json:"resources,omitempty"`
}

type TenantResourcesRequest struct {
	Standalone           *ComponentResourcesRequest             `json:"standalone,omitempty"`
	Meta                 *ComponentResourcesRequest             `json:"meta,omitempty"`
	Frontend             *ComponentResourcesRequest             `json:"frontend,omitempty"`
	Compute              *ComponentResourcesRequest             `json:"compute,omitempty"`
	Compactor            *ComponentResourcesRequest             `json:"compactor,omitempty"`
	ServerlessCompaction *provision.ServerlessCompactionOptions `json:"serverlessCompaction,omitempty"`
}

func (request TenantResourcesRequest) ApplyTo(target resourcespec.TenantResourceSpec) resourcespec.TenantResourceSpec {
	if target.IsStandalone() {
		t := lo.Must(target.AsTenantResourceSpecV1Standalone())
		if request.Standalone == nil && request.Compute != nil && request.Frontend != nil && request.Meta != nil && request.Compactor != nil {
			// convert to distributed
			return resourcespec.FromValidTenantResourceSpec(&resourcespec.TenantResourceSpecV1Regular{
				TenantResourceSpecV1Common: t.TenantResourceSpecV1Common,
				MetaReplica:                request.Meta.Replicas,
				MetaResourceSpec:           request.Meta.Resources,
				ComputeReplica:             request.Compute.Replicas,
				ComputeResourceSpec:        request.Compute.Resources,
				FrontendReplica:            request.Frontend.Replicas,
				FrontendResourceSpec:       request.Frontend.Resources,
				CompactorReplica:           request.Compactor.Replicas,
				CompactorResourceSpec:      request.Compactor.Resources,
			})
		}
		if request.Standalone != nil {
			t.StandaloneReplica = request.Standalone.Replicas
			t.StandaloneResourceSpec = request.Standalone.Resources
		}
		return resourcespec.FromValidTenantResourceSpec(&t)
	}
	t := lo.Must(target.AsTenantResourceSpecV1Regular())
	if request.Standalone != nil && request.Compute == nil && request.Frontend == nil && request.Meta == nil && request.Compactor == nil {
		// convert to standalone
		return resourcespec.FromValidTenantResourceSpec(&resourcespec.TenantResourceSpecV1Standalone{
			TenantResourceSpecV1Common: t.TenantResourceSpecV1Common,
			StandaloneReplica:          request.Standalone.Replicas,
			StandaloneResourceSpec:     request.Standalone.Resources,
		})
	}
	if request.Meta != nil {
		t.MetaReplica = request.Meta.Replicas
		t.MetaResourceSpec = request.Meta.Resources
	}
	if request.Compute != nil {
		t.ComputeReplica = request.Compute.Replicas
		t.ComputeResourceSpec = request.Compute.Resources
	}
	if request.Frontend != nil {
		t.FrontendReplica = request.Frontend.Replicas
		t.FrontendResourceSpec = request.Frontend.Resources
	}
	if request.Compactor != nil {
		t.CompactorReplica = request.Compactor.Replicas
		t.CompactorResourceSpec = request.Compactor.Resources
	}
	return resourcespec.FromValidTenantResourceSpec(&t)
}

type NodeReplicasDiff struct {
	Group   string `json:"group,omitempty"`
	Current int32  `json:"current,omitempty"`
	New     int32  `json:"new,omitempty"`
}

type UpdateTenantResourcesContext struct { //nolint:revive
	workflowimpl.TenantContextImpl
	ClusterID            uint64                          `json:"cluster_id,omitempty"`          // deprecated
	EnableAutoScaling    bool                            `json:"enable_auto_scaling,omitempty"` // deprecated
	ResourcesRequest     TenantResourcesRequest          `json:"resources_request,omitempty"`
	ComputeWorkerChanges []NodeReplicasDiff              `json:"worker_changes,omitempty"` // deprecated
	PreviousResources    resourcespec.TenantResourceSpec `json:"previous_resources,omitempty"`
	TenantNsID           uuid.UUID                       `json:"tenant_nsid,omitempty"`
	AllowsTrial          bool                            `json:"allows_trial,omitempty"`
}

func (ctx *UpdateTenantResourcesContext) Labels() map[string]string {
	return map[string]string{
		"tenantId": strconv.FormatUint(ctx.TenantID, 10),
	}
}

type UpdateTenantResourcesService struct { //nolint:revive
	repository          *workflow.Repository
	workflowBuilder     *builder.WorkflowBuilder
	account             account.Agent
	rwWorkload          rwworkload.RisingWaveWorkloadManager
	rwConfigFactory     rwconfig.Factory
	agentProvider       agentprovider.CloudAgentProviderInterface
	rwWaiter            rwwait.Waiter
	configSetRepository configset.Repository
	storageFactory      storage.FactoryInterface
	compaction          extensions.ServerlessCompaction
	tenantModel         *model.Model
	txProvider          modeltx.TxProvider
}

func NewTenantResourcesService(
	repository *workflow.Repository,
	account account.Agent,
	rwWorkload rwworkload.RisingWaveWorkloadManager,
	rwConfigFactory rwconfig.Factory,
	agentProvider agentprovider.CloudAgentProviderInterface,
	rwWaiter rwwait.Waiter,
	configSetRepository configset.Repository,
	compaction extensions.ServerlessCompaction,
	tenantModel *model.Model,
	txProvider modeltx.TxProvider,
	storageFactory storage.FactoryInterface,
) *UpdateTenantResourcesService {
	s := &UpdateTenantResourcesService{
		repository:          repository,
		account:             account,
		rwWorkload:          rwWorkload,
		rwConfigFactory:     rwConfigFactory,
		agentProvider:       agentProvider,
		rwWaiter:            rwWaiter,
		configSetRepository: configSetRepository,
		compaction:          compaction,
		tenantModel:         tenantModel,
		txProvider:          txProvider,
		storageFactory:      storageFactory,
	}

	var (
		preprocessRequestActivity          = activity.WrapDoError(s.preprocessRequest)
		notifyAccountServiceActivity       = activity.WrapDoError(s.notifyAccountService)
		updateResourcesActivity            = activity.WrapDoError(s.updateResources)
		updateServerlessCompactionActivity = activity.WrapDoError(s.updateServerlessCompaction)
		waitBeforeUpdatedActivity          = activity.WrapDoError(s.waitBeforeUpdated)
		updateTenantStatusActivity         = activity.WrapDoError(s.updateTenantStatus)
	)

	// Increase retries, since scaling can trigger node addition
	// https://linear.app/risingwave-labs/issue/CLOUD-2290/bug-scaling-e2e-test-is-flaky-occasionally
	retry := activity.RetryWrapperExpDelay(repository, 5, time.Minute)
	fail := activity.FailWorkflowWrapper

	s.workflowBuilder = builder.NewBuilder(WorkflowType).
		Next(PreprocessingRequestState).Activity(preprocessRequestActivity).Wrap(retry).Wrap(fail).
		Next(NotifyAccountService).Activity(notifyAccountServiceActivity).Wrap(retry).Wrap(fail).
		Next(UpdatingResourcesState).Activity(updateResourcesActivity).Wrap(retry).Wrap(fail).
		Next(UpdatingServerlessCompactionState).Activity(updateServerlessCompactionActivity).Wrap(retry).Wrap(fail).
		Next(WaitBeforeUpdatedState).Activity(waitBeforeUpdatedActivity).Wrap(retry).Wrap(fail).
		Next(UpdateTenantStatus).Activity(updateTenantStatusActivity).Wrap(retry).Wrap(fail).
		Succeed()

	contextProvider := func() any { return &UpdateTenantResourcesContext{} }
	repository.Registry.Register(WorkflowType, s.workflowBuilder.WorkflowConstructor(contextProvider))
	return s
}

func (s *UpdateTenantResourcesService) Submit(ctx context.Context, tenantID uint64, resourcesRequest TenantResourcesRequest, allowsTrial bool) (uuid.UUID, error) {
	ctx, cancel := context.WithTimeout(ctx, 5*model.TransactionTimeout())
	defer cancel()

	workflowID, err := modeltx.Query(ctx, s.txProvider, func(ctx context.Context, txModel *model.Model) (uuid.UUID, error) {
		tenant, err := txModel.GetTenantByID(ctx, tenantID)
		if err != nil {
			return uuid.UUID{}, err
		}

		if tenant.Status != model.TenantStatusRunning && tenant.Status != model.TenantStatusUpdating {
			return uuid.UUID{}, eris.New("tenant status is not running or updating")
		}

		err = txModel.CompareAndUpdateTenantStatusByIDAndStatuses(ctx, tenantID, model.TenantStatusUpdating,
			[]string{model.TenantStatusRunning, model.TenantStatusUpdating},
		)
		if err != nil {
			return uuid.UUID{}, eris.Wrap(err, "failed to update tenant status")
		}

		nsID, err := namespace.Parse(tenant.ResourceNamespace)
		if err != nil {
			return uuid.UUID{}, err
		}

		isAutoScalingEnabled := version.IsAutoScalingEnabled(tenant.ImageTag)
		if !isAutoScalingEnabled {
			return uuid.UUID{}, eris.New("Stop to support the version").WithCode(eris.CodeFailedPrecondition)
		}
		if resourcesRequest.ServerlessCompaction != nil && resourcesRequest.ServerlessCompaction.MaximumCPUSize > 0 {
			// Make sure the compactor update request is valid.
			if spec, ok := tenant.Resources.AsTenantResourceSpecV1Regular(); ok {
				resourcesRequest.Compactor = &ComponentResourcesRequest{
					Replicas:  0,
					Resources: spec.CompactorResourceSpec,
				}
			}
		}
		workflowToRun := s.workflowBuilder.Build()
		workflowToRun.Context = &UpdateTenantResourcesContext{
			TenantContextImpl: workflowimpl.NewTenantContext(tenantID),
			ResourcesRequest:  resourcesRequest,
			PreviousResources: tenant.Resources,
			TenantNsID:        nsID,
			AllowsTrial:       allowsTrial,
		}

		workflowID, err := s.repository.CreateInTx(ctx, txModel, workflowToRun)
		if err != nil {
			return uuid.UUID{}, eris.Wrap(err, "failed to create workflow")
		}
		return workflowID, nil
	})
	if err != nil {
		return uuid.UUID{}, err
	}
	metrics.ReportWorkflowCreated(WorkflowType)

	return workflowID, nil
}

func (s *UpdateTenantResourcesService) preprocessRequest(c context.Context, w *workflow.Workflow) error {
	ctx := w.Context.(*UpdateTenantResourcesContext)

	tenant, err := s.tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}

	if tenant.Status != model.TenantStatusUpdating {
		return eris.New("tenant status is not updating")
	}

	return nil
}

func (s *UpdateTenantResourcesService) notifyAccountService(c context.Context, w *workflow.Workflow) error {
	workflowCtx := w.Context.(*UpdateTenantResourcesContext)

	err := s.account.ScaleTenant(c, workflowCtx.TenantNsID, workflowCtx.AllowsTrial)
	if err != nil {
		return err
	}

	return nil
}

func (s *UpdateTenantResourcesService) updateResources(c context.Context, w *workflow.Workflow) error {
	ctx := w.Context.(*UpdateTenantResourcesContext)

	tenant, err := s.tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}

	newResource := ctx.ResourcesRequest.ApplyTo(tenant.Resources)
	if ctx.PreviousResources.IsStandalone() != newResource.IsStandalone() {
		err = s.convertDeploymentMode(c, tenant, newResource)
		if err != nil {
			return eris.Wrap(err, "failed to convert deployment mode")
		}
	} else {
		tenant.Resources = newResource
		err = s.scaleRisingWave(c, tenant)
		if err != nil {
			return err
		}
	}

	// Update tenant record.
	err = s.tenantModel.UpdateTenantResources(c, ctx.TenantID, newResource)
	if err != nil {
		return err
	}

	return nil
}

func (s *UpdateTenantResourcesService) updateServerlessCompaction(c context.Context, w *workflow.Workflow) error {
	ctx := w.Context.(*UpdateTenantResourcesContext)
	serverlessCompaction := ctx.ResourcesRequest.ServerlessCompaction
	if serverlessCompaction == nil {
		// If serverless compaction is not requested, we can skip this step.
		return nil
	}
	// need to update the serverless compaction extension
	tenant, err := s.tenantModel.GetTenantByID(c, ctx.TenantID)
	if err != nil {
		return err
	}
	if tenant.Resources.IsStandalone() {
		return nil
	}

	ws, err := tenantext.NewServerlessCompactionWorkflowService(
		c,
		s.tenantModel,
		s.txProvider,
		s.agentProvider,
		s.storageFactory,
		tenant.ID,
		true, // FIXME: enforce execution for now, should be replaced with a status update in the workflow submission.
	)
	if err != nil {
		return eris.Wrapf(err, "failed to create serverless compaction workflow service for tenant %d", tenant.ID)
	}

	if serverlessCompaction.MaximumCPUSize <= 0 {
		return ws.DisableIfExists(c)
	}

	return ws.EnableOnAbsenceOrUpdateIfExists(c, tenantext.ServerlessCompactionParams{
		MaxConcurrency: &serverlessCompaction.MaximumCPUSize,
	})
}

func (s *UpdateTenantResourcesService) scaleRisingWave(ctx context.Context, tenant model.Tenant) error {
	var request *pbk8ssvc.ScaleRisingWaveRequestOneOf
	tenantOption := podstrategy.ToTenantOption(tenant)
	rwComponentsPodManagementSpec, err := podstrategy.GetRisingWaveComponentsPodManagementSpec(tenantOption, nil)
	if err != nil {
		return err
	}
	if tenant.Resources.IsStandalone() {
		replicas, resources, err := tenant.Resources.GetReplicasAndResources(component.Standalone)
		if err != nil {
			return err
		}
		request = &pbk8ssvc.ScaleRisingWaveRequestOneOf{
			ResourceMeta: k8s.GetRisingWaveResourceMeta(tenant),
			Mode: &pbk8ssvc.ScaleRisingWaveRequestOneOf_StandaloneSpec{
				StandaloneSpec: &pbrw.ScaleSpec{
					Replicas:  uint32(replicas),
					Resources: resources.ToResourceRequirementProto(),
					Affinity:  rwComponentsPodManagementSpec.Standalone.Affinities,
				},
			},
		}
	} else {
		metaReplicas, metaResources, err := tenant.Resources.GetReplicasAndResources(component.Meta)
		if err != nil {
			return eris.Wrap(err, "meta component not found")
		}
		frontendReplicas, frontendResources, err := tenant.Resources.GetReplicasAndResources(component.Frontend)
		if err != nil {
			return eris.Wrap(err, "frontend component not found")
		}
		computeReplicas, computeResources, err := tenant.Resources.GetReplicasAndResources(component.Compute)
		if err != nil {
			return eris.Wrap(err, "compute component not found")
		}
		compactorReplicas, compactorResources, err := tenant.Resources.GetReplicasAndResources(component.Compactor)
		if err != nil {
			return eris.Wrap(err, "compactor component not found")
		}

		request = &pbk8ssvc.ScaleRisingWaveRequestOneOf{
			ResourceMeta: k8s.GetRisingWaveResourceMeta(tenant),
			Mode: &pbk8ssvc.ScaleRisingWaveRequestOneOf_ClusterSpec{
				ClusterSpec: &pbk8ssvc.ClusterScaleSpec{
					FrontendScaleSpec: &pbrw.ScaleSpec{
						Replicas:  uint32(frontendReplicas),
						Resources: frontendResources.ToResourceRequirementProto(),
						Affinity:  rwComponentsPodManagementSpec.Frontend.Affinities,
					},
					ComputeScaleSpec: &pbrw.ScaleSpec{
						Replicas:  uint32(computeReplicas),
						Resources: computeResources.ToResourceRequirementProto(),
						Affinity:  rwComponentsPodManagementSpec.Compute.Affinities,
					},
					CompactorScaleSpec: &pbrw.ScaleSpec{
						Replicas:  uint32(compactorReplicas),
						Resources: compactorResources.ToResourceRequirementProto(),
						Affinity:  rwComponentsPodManagementSpec.Compactor.Affinities,
					},
					MetaScaleSpec: &pbrw.ScaleSpec{
						Replicas:  uint32(metaReplicas),
						Resources: metaResources.ToResourceRequirementProto(),
						Affinity:  rwComponentsPodManagementSpec.Meta.Affinities,
					},
				},
			},
		}
	}

	cloudAgent, err := s.agentProvider.GetAgent(ctx, tenant.ClusterID)
	if err != nil {
		return err
	}
	defer cloudAgent.Close()

	err = cloudAgent.ScaleRisingWaveOneOf(ctx, request)
	if err != nil {
		return eris.Wrap(err, "failed to scale risingwave pods")
	}

	return nil
}

func (s *UpdateTenantResourcesService) convertDeploymentMode(c context.Context, tenant model.Tenant, newResources resourcespec.TenantResourceSpec) error {
	targetIsStandalone := newResources.IsStandalone()
	cloudAgent, err := s.agentProvider.GetAgent(c, tenant.ClusterID)
	if err != nil {
		return err
	}
	defer cloudAgent.Close()
	rw, err := cloudAgent.GetRisingWave(c, k8s.DefaultRisingWaveName, tenant.ResourceNamespace)
	if err != nil {
		return err
	}

	// already converted
	currentIsStandalone := rw.GetRisingwaveSpec().GetEnableStandaloneMode()
	if currentIsStandalone == targetIsStandalone {
		return nil
	}

	// stop and wait
	err = s.rwWorkload.StopWorkload(c, cloudAgent, tenant)
	if err != nil {
		return err
	}

	// create/delete compute configmap
	rwConfigManager, err := s.rwConfigFactory.GetRwConfigManager(c, tenant.ClusterID, cloudAgent)
	if err != nil {
		return err
	}

	newTenant := tenant
	newTenant.Resources = newResources
	err = rwConfigManager.DeleteRisingWaveConfigMap(c, tenant)
	if err != nil {
		return err
	}
	err = rwConfigManager.CreateRisingWaveConfigMap(c, newTenant)
	if err != nil {
		return err
	}

	// convert deployment mode
	rwBuilder := rwcrd.NewRwBuilder(s.tenantModel)
	configSet, err := s.configSetRepository.GetConfigSet(c, tenant.NsID)
	if err != nil {
		return err
	}
	componentsSpec, err := rwBuilder.GenComponentsSpecWithResourcesOverride(c, cloudAgent, tenant, newResources, configSet.IsLegacy)
	if err != nil {
		return err
	}
	err = cloudAgent.UpdateRisingWaveComponents(c, &pbk8ssvc.UpdateRisingWaveComponentsRequest{
		ResourceMeta:         k8s.GetRisingWaveResourceMeta(tenant),
		EnableStandaloneMode: ptr.Ptr(targetIsStandalone),
		ComponentsSpec:       componentsSpec,
	})
	if err != nil {
		return eris.Wrap(err, "failed to update risingwave components")
	}
	return nil
}

func (s *UpdateTenantResourcesService) waitBeforeUpdated(c context.Context, w *workflow.Workflow) error {
	workflowCtx := w.Context.(*UpdateTenantResourcesContext)

	tenant, err := s.tenantModel.GetTenantByID(c, workflowCtx.TenantID)
	if err != nil {
		return eris.Wrap(err, "failed to get tenant")
	}

	cloudAgent, err := s.agentProvider.GetAgent(c, tenant.ClusterID)
	if err != nil {
		return err
	}
	defer cloudAgent.Close()

	err = s.rwWaiter.WaitRwReady(c, cloudAgent, tenant)
	if err != nil {
		return err
	}

	return nil
}

func (s *UpdateTenantResourcesService) updateTenantStatus(c context.Context, w *workflow.Workflow) error {
	workflowCtx := w.Context.(*UpdateTenantResourcesContext)

	err := s.tenantModel.UpdateTenantStatusByID(c, workflowCtx.TenantID, model.TenantStatusRunning)
	if err != nil {
		return eris.Wrap(err, "failed to update tenant status")
	}

	return nil
}
