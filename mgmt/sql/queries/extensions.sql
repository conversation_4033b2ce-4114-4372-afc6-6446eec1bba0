-- name: GetTenantExtensionByTenantIdAndResourceType :one
SELECT
    *
FROM
    tenant_extensions
WHERE
    tenant_id = $1
    AND resource_type = $2
LIMIT
    1;


-- name: CreateTenantExtension :execresult
INSERT INTO
    tenant_extensions (
        tenant_id,
        resource_type,
        config,
        version,
        status
    )
VALUES
    (
        $1,
        $2,
        $3,
        $4,
        $5
    );

-- name: UpdateTenantExtensionStatusByTenantIdAndResourceType :execresult
UPDATE
    tenant_extensions
SET
    status = $3,
    updated_at = CURRENT_TIMESTAMP
WHERE
    tenant_id = $1 AND resource_type=$2;

-- name: CompareAndUpdateTenantExtensionStatusByTenantIdAndResourceTypeAndStatuses :execresult
UPDATE
    tenant_extensions
SET
    status = $3,
    updated_at = CURRENT_TIMESTAMP
WHERE
    tenant_id = $1
    AND resource_type = $2
    AND status = ANY(sqlc.arg(expected_statuses) :: text []);

-- name: UpdateTenantExtensionConfigByTenantIdAndResourceType :execresult
UPDATE
    tenant_extensions
SET
    config = $3,
    updated_at = CURRENT_TIMESTAMP
WHERE
    tenant_id = $1 AND resource_type = $2;

-- name: GetTenantExtensionsByTenantId :many
SELECT
    *
FROM
    tenant_extensions
WHERE
    tenant_id = $1;

-- name: UpdateTenantExtensionByTenantIdAndResourceTypeIfStatusEquals :execresult
UPDATE
    tenant_extensions
SET
    config = $4,
    version = $5,
    updated_at = CURRENT_TIMESTAMP
WHERE
    tenant_id = $1
    AND resource_type = $2
    AND status = $3;

-- name: DeleteTenantExtensionByTenantIdAndResourceType :execresult
DELETE FROM
    tenant_extensions
WHERE
    tenant_id = $1
    AND resource_type = $2;
