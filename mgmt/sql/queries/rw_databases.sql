-- name: UpsertRwDatabase :exec
INSERT INTO rw_databases (name, tenant_id, resource_group)
VALUES ($1, $2, $3)
ON CONFLICT(tenant_id, name) DO UPDATE SET resource_group = excluded.resource_group,
                                           updated_at     = CURRENT_TIMESTAMP;


-- name: GetRwDatabases :many
SELECT * FROM rw_databases WHERE tenant_id = $1 ORDER BY name;

-- name: GetRwDatabasesCountByTenantId :one
SELECT COUNT(*) FROM rw_databases WHERE tenant_id = $1;

-- name: GetRwDatabasesCountGroupByResourceGroup :many
SELECT resource_group, COUNT(*) AS cnt FROM rw_databases
WHERE tenant_id = $1
GROUP BY resource_group;

-- name: GetRwDatabasesPaginated :many
SELECT * FROM rw_databases WHERE tenant_id = $1 ORDER BY name OFFSET $2 LIMIT $3;

-- name: IsRwDatabaseExist :one
SELECT 1 FROM rw_databases WHERE tenant_id = $1 AND name = $2;

-- name: DeleteRwDatabasesByName :exec
DELETE FROM rw_databases WHERE tenant_id = $1 AND name = $2;
