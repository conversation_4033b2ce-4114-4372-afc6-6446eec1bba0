-- name: GetTenantById :one
SELECT * FROM tenants WHERE id = $1 AND deactivated_at IS NULL;

-- name: GetTenantByNsId :one
SELECT * FROM tenants WHERE ns_id = sqlc.arg(ns_id)::uuid AND deactivated_at IS NULL;

-- name: GetOrgTenantById :one
SELECT * FROM tenants WHERE id = $1 AND org_id = $2 AND deactivated_at IS NULL;

-- name: GetOrgTenantByNsId :one
SELECT * FROM tenants WHERE ns_id = sqlc.arg(ns_id)::uuid AND org_id = $1 AND deactivated_at IS NULL;

-- name: GetOrgTenantByName :one
SELECT * FROM tenants WHERE tenant_name = $1 AND org_id = $2 AND deactivated_at IS NULL;

-- name: GetTenantsByOrgId :many
SELECT * FROM tenants WHERE org_id = $1 AND deactivated_at IS NULL OFFSET $2 LIMIT $3;

-- name: GetTenants :many
SELECT * FROM tenants WHERE deactivated_at IS NULL ORDER BY id OFFSET $1 LIMIT $2;

-- name: GetTenantsCountByOrgId :one
SELECT COUNT(*) FROM tenants WHERE org_id = $1 AND deactivated_at IS NULL;

-- name: GetTenantsCount :one
SELECT COUNT(*) FROM tenants WHERE deactivated_at IS NULL;

-- name: GetOrgTenantsCountByStatus :many
SELECT tenants.status, COUNT(*) FROM tenants WHERE org_id = $1 AND deactivated_at IS NULL GROUP BY status;

-- name: GetOrgTenantsCountBySku :many
SELECT tenants.sku, COUNT(*) FROM tenants WHERE org_id = $1 AND deactivated_at IS NULL GROUP BY sku;

-- name: GetTenantsForGarbageCollection :many
SELECT * FROM tenants
WHERE (status = 'Deleting' AND deactivated_at IS NULL)
   OR ((status = 'Creating' OR status = 'Failed')
    AND updated_at < CURRENT_TIMESTAMP - '3 DAYS'::INTERVAL
    AND deactivated_at IS NULL);

-- name: CreateTenant :one
INSERT INTO tenants (tenant_name, resource_namespace, ns_id, status, sku, image_tag, region, cluster_id, replica, rw_config, etcd_config, tier_id, retention_days, validity_days, resource_version, resources, org_id, secret_store_data_encryption_key, usage_type)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19)
RETURNING *;

-- name: DeleteTenantById :exec
UPDATE tenants SET status='Deleted', deactivated_at = CURRENT_TIMESTAMP WHERE id = $1 AND deactivated_at IS NULL;

-- name: UpdateTenantEtcdConfigById :exec
UPDATE tenants SET etcd_config = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2 AND deactivated_at IS NULL;

-- name: UpdateTenantStatusById :execresult
UPDATE tenants SET status = $1, health_status = 'Unknown', updated_at = CURRENT_TIMESTAMP WHERE id = $2 AND deactivated_at IS NULL;

-- name: CompareAndUpdateTenantStatusByIdAndStatuses :execresult 
UPDATE tenants SET status = sqlc.arg(new_status), health_status = 'Unknown', updated_at = CURRENT_TIMESTAMP WHERE id = $1 AND status = ANY(sqlc.arg(old_statuses) :: text []) AND deactivated_at IS NULL;

-- name: CompareAndUpdateTenantHealthStatusById :execresult
UPDATE tenants SET health_status = sqlc.arg(new_status), updated_at = CURRENT_TIMESTAMP WHERE id = $1 AND health_status = sqlc.arg(old_status) AND deactivated_at IS NULL;

-- name: UpdateTenantTierById :execresult
UPDATE tenants SET tier_id = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2 AND deactivated_at IS NULL;

-- name: UpdateTenantRwConfigById :execresult
UPDATE tenants SET rw_config = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2 AND deactivated_at IS NULL;

-- name: UpdateTenantRwConfigByNsId :execresult
UPDATE tenants SET rw_config = $1, updated_at = CURRENT_TIMESTAMP WHERE ns_id = $2 AND deactivated_at IS NULL;

-- name: UpdateTenantImageTagById :execresult
UPDATE tenants SET image_tag= $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2 AND deactivated_at IS NULL;

-- name: UpdateTenantResourcesById :execresult
UPDATE tenants SET resources = $2, updated_at = CURRENT_TIMESTAMP WHERE id = $1 AND deactivated_at IS NULL;

-- name: SetValueForEmptyTenantSecretStoreDekById :execresult
UPDATE tenants SET secret_store_data_encryption_key= $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2 AND deactivated_at IS NULL AND secret_store_data_encryption_key='';
