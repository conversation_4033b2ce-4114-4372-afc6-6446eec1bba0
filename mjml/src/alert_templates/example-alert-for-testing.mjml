<mjml>
  <mj-head>
    <mj-title>RisingWave Cloud</mj-title>

    <mj-font
      name="Montserrat"
      href="https://fonts.googleapis.com/css?family=Montserrat:400,700"
    />

    <mj-attributes>
      <mj-all font-family="Montserrat, Helvetica, Arial, sans-serif"></mj-all>
      <mj-text
        font-weight="400"
        font-size="14px"
        color="#000000"
        line-height="140%"
        font-family="Montserrat, Helvetica, Arial, sans-serif"
      ></mj-text>
    </mj-attributes>

    <mj-style inline="inline">
      .text-link { color: #cca250; text-decoration: none; }
    </mj-style>
  </mj-head>

  <mj-body width="580px" background-color="#F6F7F8">
    <mj-wrapper>
      <mj-include path="../components/header.mjml" />
      <mj-section background-color="white" padding="36px 20px 0 20px">
        <mj-column>
          <mj-text
            font-weight="700"
            font-size="22px"
            line-height="28px"
            align="left"
          >
            This is an alert example for testing
          </mj-text>
          <mj-text font-size="14px" line-height="22px">
            Hi there,
          </mj-text>
          <mj-text font-size="14px" line-height="22px">
            Here is an alert example for testing
          </mj-text>
          <mj-text font-size="14px" line-height="22px">
            <div><strong>• Description:</strong> {{ .description }}</div>
            <div><strong>• Summary:</strong> {{ .summary }}</div>
          </mj-text>
        </mj-column>
      </mj-section>
      <mj-section background-color="white" padding="0 20px 36px 20px">
        <mj-column>
          <mj-text font-size="14px" line-height="22px">
            Questions or need help? Our support team is here for you at <a href="mailto:<EMAIL>"> <EMAIL> </a>.
          </mj-text>
          <mj-text font-size="14px" line-height="22px">
             Best regards,
          </mj-text>
          <mj-text font-size="14px" line-height="22px" font-weight=700>
             <strong>The RisingWave team</strong>
          </mj-text>
        </mj-column>
      </mj-section>
      <mj-include path="../components/footer.mjml" />
    </mj-wrapper>
  </mj-body>
</mjml>
