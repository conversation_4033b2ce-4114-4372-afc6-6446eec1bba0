apiVersion: v1
kind: Namespace
metadata:
  name: cloudagent
  labels:
    name: cloudagent
---
# ##################################################
#
# In cluster Authentication to the API server
#
# ##################################################
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: cloudagent
  namespace: cloudagent
rules:
- apiGroups:
  - 'risingwave.risingwavelabs.com'
  - 'monitoring.coreos.com'
  - 'acid.zalan.do' # postgresql
  - 'batch'
  - 'rbac.authorization.k8s.io'
  - 'networking.k8s.io'
  - 'policy' # need poddisruptionbudgets.policy in helm releases
  - 'apps' # need statefulsets.apps in helm releases
  - '' # core API
  resources: ["*"]
  verbs: ["*"]
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: cloudagent
  namespace: cloudagent
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: cloudagent-role-binding
subjects:
- namespace: cloudagent
  kind: ServiceAccount
  name: cloudagent
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cloudagent
---
# ##################################################
#
# Application Service
#
# ##################################################
apiVersion: v1
kind: Secret
metadata:
  name: cloudagent-tls
  namespace: cloudagent
type: kubernetes.io/tls
data:
  tls.crt: {{.TLS.Cert}}
  tls.key: {{.TLS.Key}}
  ca.crt: {{.TLS.CA}}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: cloudagent-cluster-config-path
  namespace: cloudagent
data:
  config.textproto: |
    k8s_config {
      cluster_id: "local_cluster"
      in_cluster_auth {}
      allow_helm_charts: ["https://charts.bitnami.com/bitnami/etcd-*", "https://risingwavelabs.github.io/risingwave-extensions/charts/risingwave-extensions-*", "https://risingwavelabs.github.io/risingwave-extensions/charts/risingwave-serverless-compaction-*", "https://risingwavelabs.github.io/serverless-backfill-controller/charts/serverless-backfill-controller-*", "https://risingwavelabs.github.io/risingwave-extensions/risingwave-extensions-*"]
      task_config {
        image: "{{.TaskRunnerImage}}"
        service_account: "cloudagent"
        namespace: "cloudagent"
        pull_policy: PULL_IF_NOT_PRESENT
      }
    }
    local_config {}
    tls_config {
      cert_path: "/app/certs/tls.crt"
      key_path: "/app/certs/tls.key"
      client_ca_path: "/app/certs/ca.crt"
    }
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cloudagent
  namespace: cloudagent
  labels:
    app: cloudagent
spec:
  replicas: 1
  selector:
    matchLabels:
      app: cloudagent
  template:
    metadata:
      labels:
        app: cloudagent
    spec:
      serviceAccountName: cloudagent
      containers:
      - name: cloudagent
        command: ["/app/cloudagent"]
        args:
        - "-config_path"
        - "/app/config/config.textproto"
        - "-port"
        - "40001"
        - "-zpageport"
        - "40090"
        image: {{.ServiceImage}}
        imagePullPolicy: IfNotPresent
        ports:
        - name: user-port
          containerPort: 40001
        - name: zpage-port
          containerPort: 40090
        volumeMounts:
        - name: cloudagent-cluster-config-volume
          mountPath: /app/config
          readOnly: true
        - name: cert-vol
          readOnly: true
          mountPath: /app/certs
      volumes:
      - name: cloudagent-cluster-config-volume
        configMap:
          name: cloudagent-cluster-config-path
      - name: cert-vol
        secret:
          secretName: cloudagent-tls
---
apiVersion: v1
kind: Service
metadata:
  name: cloudagent
  namespace: cloudagent
  labels:
    app: cloudagent
spec:
  selector:
    app: cloudagent
  ports:
  - name: user-port
    port: 40001
    targetPort: 40001
    nodePort: 30110
    protocol: TCP
  - name: zpage-port
    port: 40090
    targetPort: 40090
    nodePort: 30109
    protocol: TCP
  type: NodePort
