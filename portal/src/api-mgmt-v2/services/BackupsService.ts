/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { BackupSnapshotsPagination } from '../models/BackupSnapshotsPagination';
import type { PostSnapshotResponseBody } from '../models/PostSnapshotResponseBody';
import type { PostTenantRestoreRequestBody } from '../models/PostTenantRestoreRequestBody';

import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class BackupsService {

    /**
     * Get all available backup snapshots
     * @param nsId
     * @param offset
     * @param limit
     * @returns BackupSnapshotsPagination Get all available backup snapshots.
     * @throws ApiError
     */
    public static getTenantsBackups(
        nsId: string,
        offset?: number,
        limit: number = 10,
    ): CancelablePromise<BackupSnapshotsPagination> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/tenants/{nsId}/backups',
            path: {
                'nsId': nsId,
            },
            query: {
                'offset': offset,
                'limit': limit,
            },
        });
    }

    /**
     * Start a new backup procedure
     * @param nsId
     * @returns PostSnapshotResponseBody The backup procedure is started.
     * @throws ApiError
     */
    public static postTenantsBackups(
        nsId: string,
    ): CancelablePromise<PostSnapshotResponseBody> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/tenants/{nsId}/backups',
            path: {
                'nsId': nsId,
            },
            errors: {
                404: `404 Not Found`,
            },
        });
    }

    /**
     * Delete a backup snapshot
     * @param nsId
     * @param snapshotId
     * @returns any The snapshot deletion procedure is started.
     * @throws ApiError
     */
    public static deleteTenantsBackups(
        nsId: string,
        snapshotId: string,
    ): CancelablePromise<any> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/tenants/{nsId}/backups/{snapshotId}',
            path: {
                'nsId': nsId,
                'snapshotId': snapshotId,
            },
            errors: {
                404: `404 Not Found`,
            },
        });
    }

    /**
     * Restore a backup snapshot
     * @param nsId
     * @param snapshotId
     * @param requestBody
     * @returns any Starting to restore data from this snapshot.
     * @throws ApiError
     */
    public static postTenantsBackupsRestore(
        nsId: string,
        snapshotId: string,
        requestBody: PostTenantRestoreRequestBody,
    ): CancelablePromise<any> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/tenants/{nsId}/backups/{snapshotId}/restore',
            path: {
                'nsId': nsId,
                'snapshotId': snapshotId,
            },
            body: requestBody,
            mediaType: 'application/json',
        });
    }

    /**
     * Restore a backup snapshot
     * @param nsId
     * @param snapshotId
     * @returns any Starting to restore data from this snapshot.
     * @throws ApiError
     */
    public static postTenantsBackupsInPlaceRestore(
        nsId: string,
        snapshotId: string,
    ): CancelablePromise<any> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/tenants/{nsId}/backups/{snapshotId}/in-place-restore',
            path: {
                'nsId': nsId,
                'snapshotId': snapshotId,
            },
        });
    }

    /**
     * Resume a quiesced tenant.
     * @param nsId
     * @returns any Starting to resume the tenant.
     * @throws ApiError
     */
    public static postTenantsUnquiesce(
        nsId: string,
    ): CancelablePromise<any> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/tenants/{nsId}/unquiesce',
            path: {
                'nsId': nsId,
            },
        });
    }

}
