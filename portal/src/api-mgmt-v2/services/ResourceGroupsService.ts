/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CreateResourceGroupsRequestBody } from '../models/CreateResourceGroupsRequestBody';
import type { GetResourceGroupsResponseBody } from '../models/GetResourceGroupsResponseBody';
import type { UpdateResourceGroupsRequestBody } from '../models/UpdateResourceGroupsRequestBody';

import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class ResourceGroupsService {

    /**
     * @param nsId
     * @returns GetResourceGroupsResponseBody OK
     * @throws ApiError
     */
    public static getTenantsResourceGroups(
        nsId: string,
    ): CancelablePromise<GetResourceGroupsResponseBody> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/tenants/{nsId}/resourceGroups',
            path: {
                'nsId': nsId,
            },
            errors: {
                404: `404 Not Found`,
            },
        });
    }

    /**
     * Create resource group
     * @param nsId
     * @param requestBody
     * @returns any Default responses returning msg
     * @throws ApiError
     */
    public static postTenantsResourceGroups(
        nsId: string,
        requestBody?: CreateResourceGroupsRequestBody,
    ): CancelablePromise<{
        msg: string;
    }> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/tenants/{nsId}/resourceGroups',
            path: {
                'nsId': nsId,
            },
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `400 Failed Precondition`,
                404: `404 Not Found`,
            },
        });
    }

    /**
     * Update resource group
     * @param nsId
     * @param resourceGroup
     * @param requestBody
     * @returns any Default responses returning msg
     * @throws ApiError
     */
    public static postTenantsResourceGroups1(
        nsId: string,
        resourceGroup: string,
        requestBody?: UpdateResourceGroupsRequestBody,
    ): CancelablePromise<{
        msg: string;
    }> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/tenants/{nsId}/resourceGroups/{resourceGroup}',
            path: {
                'nsId': nsId,
                'resourceGroup': resourceGroup,
            },
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `400 Failed Precondition`,
                404: `404 Not Found`,
            },
        });
    }

    /**
     * Delete resource group
     * @param nsId
     * @param resourceGroup
     * @returns any Default responses returning msg
     * @throws ApiError
     */
    public static deleteTenantsResourceGroups(
        nsId: string,
        resourceGroup: string,
    ): CancelablePromise<{
        msg: string;
    }> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/tenants/{nsId}/resourceGroups/{resourceGroup}',
            path: {
                'nsId': nsId,
                'resourceGroup': resourceGroup,
            },
            errors: {
                400: `400 Failed Precondition`,
                404: `404 Not Found`,
            },
        });
    }

}
