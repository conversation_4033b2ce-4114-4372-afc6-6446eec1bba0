/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CreateSecretRequestBody } from '../models/CreateSecretRequestBody';
import type { RwSecretReferencesPagination } from '../models/RwSecretReferencesPagination';
import type { SecretsPagination } from '../models/SecretsPagination';

import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class SecretsService {

    /**
     * List all secrets by nsId
     * @param nsId
     * @param databaseName
     * @param offset
     * @param limit
     * @returns SecretsPagination Secrets Response
     * @throws ApiError
     */
    public static getTenantsDatabasesSecrets(
        nsId: string,
        databaseName: string,
        offset?: number,
        limit: number = 10,
    ): CancelablePromise<SecretsPagination> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/tenants/{nsId}/databases/{databaseName}/secrets',
            path: {
                'nsId': nsId,
                'databaseName': databaseName,
            },
            query: {
                'offset': offset,
                'limit': limit,
            },
            errors: {
                404: `404 Not Found`,
            },
        });
    }

    /**
     * Create a database secret
     * @param nsId
     * @param databaseName
     * @param requestBody
     * @returns any Default responses returning msg
     * @throws ApiError
     */
    public static postTenantsDatabasesSecrets(
        nsId: string,
        databaseName: string,
        requestBody: CreateSecretRequestBody,
    ): CancelablePromise<{
        msg: string;
    }> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/tenants/{nsId}/databases/{databaseName}/secrets',
            path: {
                'nsId': nsId,
                'databaseName': databaseName,
            },
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `400 Bad Request`,
                403: `403 Forbidden`,
                404: `404 Not Found`,
                409: `409 Bad Request`,
            },
        });
    }

    /**
     * Delete secret by name
     * @param nsId
     * @param databaseName
     * @param name
     * @returns any Default responses returning msg
     * @throws ApiError
     */
    public static deleteTenantsDatabasesSecrets(
        nsId: string,
        databaseName: string,
        name: string,
    ): CancelablePromise<{
        msg: string;
    }> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/tenants/{nsId}/databases/{databaseName}/secrets/{name}',
            path: {
                'nsId': nsId,
                'databaseName': databaseName,
                'name': name,
            },
            errors: {
                404: `404 Not Found`,
            },
        });
    }

    /**
     * Get references from risingwave by secret name
     * @param nsId
     * @param secretName
     * @param databaseName
     * @param offset
     * @param limit
     * @returns RwSecretReferencesPagination OK
     * @throws ApiError
     */
    public static getTenantsDatabasesSecretsReferences(
        nsId: string,
        secretName: string,
        databaseName: string,
        offset?: number,
        limit: number = 10,
    ): CancelablePromise<RwSecretReferencesPagination> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/tenants/{nsId}/databases/{databaseName}/secrets/{secretName}/references',
            path: {
                'nsId': nsId,
                'secretName': secretName,
                'databaseName': databaseName,
            },
            query: {
                'offset': offset,
                'limit': limit,
            },
            errors: {
                400: `400 Bad Request`,
                404: `404 Not Found`,
            },
        });
    }

}
