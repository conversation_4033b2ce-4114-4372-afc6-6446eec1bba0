/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class BackupsService {

    /**
     * Resume a quiesced tenant.
     * @param tenantId
     * @returns any Starting to resume the tenant.
     * @throws ApiError
     */
    public static postTenantsUnquiesce(
        tenantId: number,
    ): CancelablePromise<any> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/tenants/{tenantId}/unquiesce',
            path: {
                'tenantId': tenantId,
            },
        });
    }

}
