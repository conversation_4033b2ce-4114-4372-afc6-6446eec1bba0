import {
  faCircleExclamation,
  faCircleInfo,
  faEllipsis,
  faGears,
  faPause,
  faPlay,
  faRocket,
  faSpinner,
  faTrashCan,
} from "@fortawesome/free-solid-svg-icons";
import { fomratTenantStatus, getStateColorV2, tenetAction } from "@/components/Cluster/utils";
import UpgradeConfirmPopup from "@/components/Cluster/UpgradeConfirmPopup";
import { ClickAwayListener, IconButton, Tooltip } from "@mui/material";
import WhiteTooltip from "@/components/Project/Creation/WhiteTooltip";
import { Tenant, TenantResourceComponents, TierId } from "@/api-mgmt";
import { TenantsService, OpenAPI as OpenAPIV2 } from "@/api-mgmt-v2";
import { regionDisplayList } from "@/hooks/useRegionForDisplayList";
import { loadingStatusList } from "@/components/Cluster/TenantType";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useTier } from "@/components/Project/Creation/useTier";
import ConnectPopup from "@/components/Cluster/ConnectPopup";
import StandaloneRescale from "./TenantInfo/Rescale/index";
import { useContext, useMemo, useState } from "react";
import { PlatformType } from "@/hooks/useRegionList";
import LoadingIcon from "@/components/LoadingIcon";
import { CopyIcon } from "@/components/Icon/Icon";
import { DetailContext } from "../DetailProvider";
import Popconfirm from "@/components/Popconfirm";
import gcpIcon from "@imgs/tenant/gcp_icon.svg";
import { useOrgInfo } from "@/hooks/useOrgInfo";
import copyToClipboard from "copy-to-clipboard";
import { classNames, If } from "react-extras";
import azureIcon from "@imgs/tenant/azr.svg";
import { useRouter } from "next/navigation";
import { getErrorMsg } from "@/utils/func";
import { Domains } from "@/config/router";
import styles from "./style.module.scss";
import { compare, valid } from "semver";
import aws from "@imgs/tenant/aws.svg";
import Modal from "@/components/Modal";
import { toast } from "react-toastify";
import TenantInfo from "./TenantInfo";
import Decimal from "decimal.js";
import Image from "next/image";
import dayjs from "dayjs";

const platformIcon = {
  [PlatformType.AWS]: <Image alt="aws" width={24} src={aws} />,
  [PlatformType.GCP]: <Image alt="gcp" width={18} src={gcpIcon} />,
  [PlatformType.AZURE]: <Image alt="azure" width={16} src={azureIcon} />,
};

interface ProjectCardProps {}
const ProjectCard: React.FC<ProjectCardProps> = (props) => {
  const { tenant, region, refresh, loading } = useContext(DetailContext);
  const router = useRouter();
  const tier = useTier();
  const [showConnect, setShowConnect] = useState(false);
  const [showPausePopup, setShowPausePopup] = useState(false);
  const [openPopconfirm, setOpenPopconfirm] = useState<boolean>(false);
  const [showMoreTooltip, setShowMoreTooltip] = useState(false);
  const [showStandaloneRescale, setShowStandaloneRescale] = useState(false);
  const { orgInfo } = useOrgInfo();

  const handleOpenMoreTooltip = () => {
    setShowMoreTooltip(true);
  };

  const handleCloseMoreTooltip = () => {
    setShowMoreTooltip(false);
  };

  const unHealthy = tenant?.health_status === Tenant.health_status.UNHEALTHY;

  const tenantStatus = tenant?.status;

  const isTenantProcessing =
    !tenantStatus || (loadingStatusList.includes(tenantStatus) && tenantStatus !== Tenant.status.SNAPSHOTTING);

  const stateColor = useMemo(() => getStateColorV2(tenantStatus, unHealthy), [tenantStatus, unHealthy]);

  const scaleDisabled =
    !tier || tier === TierId.DEVELOPER_FREE || !(tenantStatus === Tenant.status.RUNNING || unHealthy);

  const regionPlatform = useMemo(() => {
    if (!region?.platform) return null;
    const regionList = regionDisplayList[region.platform as PlatformType];
    return regionList.find((item) => item.regionName === region.regionName);
  }, [region?.platform, region?.regionName]);

  const rwuNumber = useMemo(() => {
    if (!!tenant?.tier && (tenant?.tier.includes("developer") || tenant?.tier === TierId.FREE)) {
      return 2;
    }

    if (tenant && tenant?.resources.components.standalone) {
      return +tenant.resources.components.standalone.cpu;
    }

    let total = new Decimal(0);
    ["compute", "frontend", "meta", "compactor", "etcd"].forEach((item) => {
      const configItem = tenant?.resources?.components
        ? tenant?.resources?.components?.[item as unknown as keyof TenantResourceComponents]
        : null;
      const map = {
        label: item === "etcd" ? item : item.charAt(0).toUpperCase() + item.slice(1),
        rwu: configItem?.cpu ? new Decimal(configItem.cpu) : new Decimal(0),
        nodes: configItem?.replica || 0,
      };

      total = total.plus(map.rwu.times(map.nodes));
      return map;
    });

    const metaStore = tenant?.resources?.metaStore?.rwu ? new Decimal(tenant.resources.metaStore.rwu) : new Decimal(0);
    return total.plus(metaStore).toNumber();
  }, [tenant]);

  const autoCompacitionRwu = useMemo(() => {
    if (tenant?.extensions?.serverlessCompaction?.enabled) {
      return tenant?.extensions?.serverlessCompaction?.maximumCompactionConcurrency;
    }
    return 0;
  }, [tenant?.extensions?.serverlessCompaction?.enabled]);

  const handleDelete = () => {
    if (!region?.urlV2 || !tenant?.nsId) return;
    OpenAPIV2.BASE = region.urlV2 || "";
    return TenantsService.deleteTenants(tenant.nsId)
      .then((res) => {
        toast(
          <div className={styles.toast}>
            <div>
              <LoadingIcon />
            </div>
            <span>{`Deleting '${tenant?.tenantName}'...`}</span>
          </div>,
        );
        refresh();
        setTimeout(() => {
          router.replace(Domains.home);
        }, 500);
      })
      .catch((err) => {
        toast.error(getErrorMsg(err, "delete fail"));
      });
  };

  const handleActionClick = (actionName: string) => {
    handleCloseMoreTooltip();
    if (actionName === tenetAction.DELETE) {
      Modal.deleteProject({
        deleteObj: {
          name: "project",
          value: tenant?.tenantName || "",
          warning: `You are about to delete the project "${tenant?.tenantName}". This action is irreversible and will permanently remove all data within the project.`,
          inputLabel: "project name",
          description:
            "Make sure all important data is backed up and no critical tasks depend on this project before you proceed",
        },
        handleDelete,
      });
    } else if (actionName === tenetAction.CONNECT) {
      setShowConnect(true);
    } else if (actionName === tenetAction.START) {
      if (!tenant?.nsId) return;

      OpenAPIV2.BASE = region?.urlV2 || "";
      TenantsService.postTenantsStart(tenant.nsId)
        .then(() => {
          refresh();
        })
        .catch((err) => {
          if (
            (err.status === 403 || err.status === 412) &&
            tenant.tier === TierId.DEVELOPER_FREE &&
            orgInfo?.trialDays === 0
          ) {
            toast.error("Your trial has ended. Please upgrade your plan to resume operations on this cluster.");
          } else {
            toast.error(getErrorMsg(err, "start fail"));
          }
        });
    } else if (actionName === tenetAction.STOP) {
      if (!tenant?.nsId) return;
      OpenAPIV2.BASE = region?.urlV2 || "";
      TenantsService.postTenantsStop(tenant.nsId)
        .then((res) => {
          refresh();
        })
        .catch((err) => {
          toast.error(getErrorMsg(err, "stop fail"));
        });
      setShowPausePopup(false);
    }
  };

  const handleCancelConnectPopup = () => {
    setShowConnect(false);
  };

  const handleUpgrade = () => {
    setOpenPopconfirm(true);
  };

  const handleCopy = (str: string) => {
    if (copyToClipboard(str)) {
      toast.success("Copied to clipboard");
    }
  };

  const upgradeRender = () => {
    if (!tenant) return;
    if (tenantStatus === Tenant.status.UPGRADING) {
      return (
        <>
          <div className="cursor-pointer rounded pl-1 hover:bg-[#F3FEFF]">
            <FontAwesomeIcon className="mr-1.5" icon={faSpinner} spin />
            Upgrading
          </div>
          <hr className="my-1 border-[.5px] border-neutral200" />
        </>
      );
    }
    if (!valid(tenant?.imageTag) || !valid(tenant?.latestImageTag)) {
      return;
    }
    if (tenantStatus !== Tenant.status.RUNNING || compare(tenant?.imageTag, tenant?.latestImageTag) !== -1) {
      return;
    }
    return (
      <>
        <div className="cursor-pointer rounded pl-1 hover:bg-[#F3FEFF]" onClick={handleUpgrade}>
          <FontAwesomeIcon className="mr-1.5" icon={faRocket} />
          Upgrade
        </div>
        <hr className="my-1 border-[.5px] border-neutral200" />
      </>
    );
  };

  const handleRescale = () => {
    if (scaleDisabled) return;
    setShowStandaloneRescale(true);
  };

  return (
    <div className="w-full rounded-lg border border-neutral200 px-6 py-3">
      <div className="flex items-center gap-3 border-b border-neutral200 pb-2 text-xl">
        <div>
          <span className="text-xl">{tenant?.tenantName}</span>
          <WhiteTooltip
            placement="bottom"
            width="320px"
            content={
              <div className="pb-2 text-black">
                <p className="text-[10px] text-neutral500">UUID:</p>
                <p className="whitespace-nowrap border-b border-neutral200 pb-2 text-xs font-normal">
                  {tenant?.nsId}
                  <IconButton
                    sx={{ marginLeft: "12px" }}
                    size="small"
                    onClick={() => tenant?.nsId && handleCopy(tenant?.nsId)}>
                    <CopyIcon />
                  </IconButton>
                </p>
                <p className="text-[10px] text-neutral500">Created time:</p>
                <p className="text-xs font-normal">{dayjs(tenant?.createdAt).format("YYYY-MM-DD HH:mm:ss")}</p>
              </div>
            }>
            <FontAwesomeIcon icon={faCircleInfo} className="ml-5 cursor-pointer text-base text-neutral500" />
          </WhiteTooltip>
        </div>
        <div
          className="h-7 w-fit rounded-2xl px-2.5 text-[12px] leading-7 text-white"
          style={{ backgroundColor: stateColor }}>
          {fomratTenantStatus(tenantStatus, unHealthy)}
        </div>
        {region?.platform && (
          <div className="flex h-7 items-center rounded-2xl border border-neutral200 bg-neutral50 px-5">
            {platformIcon[region.platform as PlatformType]}
          </div>
        )}

        {regionPlatform?.area && (
          <div className="flex h-7 items-center rounded-2xl border border-neutral200 bg-neutral50 px-5 text-xs">
            {regionPlatform?.regionName} ({regionPlatform?.area})
          </div>
        )}

        <Tooltip
          title={<span className="text-xs font-light">1 RWU = 1 CPU core and 4GB RAM</span>}
          placement="right"
          arrow>
          <div className="flex h-7 items-center rounded-2xl border border-neutral200 bg-neutral50 px-5 text-xs">
            {rwuNumber} RWUs {!!autoCompacitionRwu && `- ${rwuNumber + autoCompacitionRwu} RWUs`}
          </div>
        </Tooltip>

        <div className="flex h-7 items-center rounded-2xl border border-neutral200 bg-neutral50 px-5 text-xs">
          {tenant?.tier === TierId.INVITED ? "Advanced" : tenant?.tier}
        </div>

        <ClickAwayListener onClickAway={handleCloseMoreTooltip}>
          <div className="ml-auto flex w-8 cursor-pointer items-center justify-center">
            <WhiteTooltip
              open={showMoreTooltip}
              width="115px"
              padding="8px 10px"
              placement="bottom-start"
              content={
                <div className="font-normal text-black">
                  <If condition={tenantStatus === Tenant.status.STOPPED || tenantStatus === Tenant.status.EXPIRED}>
                    <div
                      className="cursor-pointer rounded pl-1 text-primary hover:bg-[#F3FEFF]"
                      onClick={() => handleActionClick(tenetAction.START)}>
                      <FontAwesomeIcon className="w-4 font-black" icon={faPlay} /> Resume
                    </div>
                    <hr className="my-1 border-[.5px] border-neutral200" />
                  </If>
                  <If condition={tenantStatus === Tenant.status.RUNNING || tenantStatus === Tenant.status.SNAPSHOTTING}>
                    <div
                      className="cursor-pointer rounded pl-1 hover:bg-[#F3FEFF]"
                      onClick={() => {
                        setShowPausePopup(true);
                      }}>
                      <FontAwesomeIcon className="w-4 font-black" icon={faPause} /> Pause
                    </div>
                    <hr className="my-1 border-[.5px] border-neutral200" />
                    {/*  <div onClick={() => handleActionClick(tenetAction.CONNECT)}>Connect</div> */}
                  </If>
                  {upgradeRender()}
                  <If condition={!isTenantProcessing}>
                    <div
                      className="cursor-pointer rounded pl-1 text-[#EF4444] hover:bg-[#F3FEFF]"
                      onClick={() => {
                        handleActionClick(tenetAction.DELETE);
                      }}>
                      <FontAwesomeIcon className="w-4" icon={faTrashCan} /> Delete
                    </div>
                  </If>
                  <If condition={!scaleDisabled}>
                    <hr className="my-1 border-[.5px] border-neutral200" />
                    <Tooltip
                      title={
                        scaleDisabled &&
                        "Rescaling is available with our Standard and Advanced plans. Upgrade now to unlock this feature."
                      }
                      placement="right"
                      arrow>
                      <div
                        className={classNames(
                          "rounded pl-1",
                          scaleDisabled ? "cursor-not-allowed text-neutral300" : "cursor-pointer hover:bg-[#F3FEFF]",
                        )}
                        onClick={handleRescale}>
                        <FontAwesomeIcon className="w-4 font-black" icon={faGears} /> Rescale
                      </div>
                    </Tooltip>
                  </If>

                  <If
                    condition={
                      !(
                        tenantStatus === Tenant.status.STOPPED ||
                        tenantStatus === Tenant.status.EXPIRED ||
                        tenantStatus === Tenant.status.RUNNING ||
                        !isTenantProcessing
                      )
                    }>
                    <div className="text-center">
                      <FontAwesomeIcon icon={faSpinner} spin />
                    </div>
                  </If>
                </div>
              }>
              <IconButton
                sx={{
                  ":hover": {
                    background: "none",
                  },
                  height: "6px",
                  padding: "0",
                }}
                size="small"
                aria-label="more"
                disabled={isTenantProcessing}
                onClick={handleOpenMoreTooltip}>
                <FontAwesomeIcon className={isTenantProcessing ? "text-neutral300" : "text-black"} icon={faEllipsis} />
              </IconButton>
            </WhiteTooltip>
          </div>
        </ClickAwayListener>
      </div>
      <TenantInfo />
      {tenant && region && (
        <UpgradeConfirmPopup
          open={openPopconfirm}
          onCancel={() => {
            setOpenPopconfirm(false);
          }}
          tenant={tenant}
          region={region}
          onUpdate={() => {
            refresh();
          }}
        />
      )}
      {tenant?.nsId && (
        <ConnectPopup
          tenant={tenant || undefined}
          nsId={tenant?.nsId}
          open={showConnect}
          onCancel={handleCancelConnectPopup}
        />
      )}

      <Popconfirm
        title="Pause the project"
        open={showPausePopup}
        onCancel={() => {
          setShowPausePopup(false);
        }}
        confirmText="Confirm"
        cancelText="Cancel"
        confirmType="primary"
        onConfirm={() => handleActionClick(tenetAction.STOP)}>
        <>
          <div className="rounded bg-[#FFFBEB] px-5 py-3 text-[#B45309]">
            <FontAwesomeIcon icon={faCircleExclamation} className="mr-1" />
            You are about to pause the project {tenant?.tenantName}. This action will halt the running streaming
            pipelines and release all the compute resources of the running project.
          </div>
          <div className="mt-4 px-3">
            <p>While the project is paused:</p>
            <p className="ml-2 mt-2">- The project will be accessible.</p>
            <p className="ml-2 mt-2">- No streaming pipelines will run.</p>
            <p className="my-2 ml-2">- You will not incur charges for the compute resources.</p>
            <div className="mt-3">
              Please ensure that all critical tasks are safely paused before proceeding. You can resume your project at
              any time after it has been paused.
            </div>
            <div className="mt-2">Please confirm to proceed with the pause. </div>
          </div>
        </>
      </Popconfirm>

      <StandaloneRescale
        currentRwu={rwuNumber}
        compute={tenant?.resources?.components?.compute}
        open={showStandaloneRescale}
        autoCompacitionRwu={autoCompacitionRwu}
        handleClose={() => setShowStandaloneRescale(false)}
        regionPlatform={`${regionPlatform?.regionName}(${regionPlatform?.area})`}
      />
    </div>
  );
};
export default ProjectCard;

