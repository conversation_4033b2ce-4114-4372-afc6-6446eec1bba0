import { faArrowUp, faArrowDown } from "@fortawesome/free-solid-svg-icons";
import { DistributionMark } from "@/components/Project/Creation/const";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { formatUsdCent } from "@/utils/format";
import { DefaultService } from "@/api-billing";
import { Tenant, TierId } from "@/api-mgmt-v2";
import { Region } from "@/api-user";
import useSWR from "swr";

interface ConfigurationChangesProps {
  currentConfig: DistributionMark & { autoCompaction?: boolean; autoCompactionMaxValue?: number };
  newConfig: DistributionMark & { autoCompaction?: boolean; autoCompactionMaxValue?: number };
  isStandalone?: boolean;
  region?: Region;
  tenant?: Tenant;
}

const ConfigurationChanges = ({
  currentConfig,
  newConfig,
  region,
  tenant,
  isStandalone: isCurrentStandalone,
}: ConfigurationChangesProps) => {
  // Check if configurations have the same total RWUs
  const noChanges = currentConfig.rwu === newConfig.rwu;

  const { data: total, error } = useSWR(
    ["getExpectedCost", region?.regionName, newConfig?.rwu, noChanges],
    () => {
      if (!region?.regionName || !newConfig?.rwu || !tenant?.tier || noChanges) return;
      return DefaultService.getHourlyCost(region.regionName, tenant?.tier, newConfig.rwu).then((res) => {
        return res.total_usd_cent;
      });
    },
    {
      refreshInterval: 0,
    },
  );

  const { data: currentPrice, error: currentPriceError } = useSWR(
    ["getExpectedCost", region?.regionName, currentConfig.rwu, tenant?.tier],
    () => {
      if (!region?.regionName || !tenant?.tier || !currentConfig.rwu) return;
      return DefaultService.getHourlyCost(region.regionName, tenant.tier, currentConfig.rwu).then((res) => {
        return res.total_usd_cent;
      });
    },
    {
      refreshInterval: 0,
    },
  );

  if (noChanges) {
    return <></>;
  }

  const isStandalone = tenant?.tier !== TierId.INVITED;

  const isDowngrade = !isCurrentStandalone && isStandalone;

  // Check if current config and new config are in development or production tier
  const isCurrentDevelopment = currentConfig.rwu <= 8;
  const isNewDevelopment = newConfig.rwu <= 8;

  return (
    <div className="mt-8 w-full overflow-hidden rounded-2xl border border-neutral200 bg-[#F3FEFF]">
      <div className="px-6 pt-4">
        <h3 className="text-center text-lg font-medium text-neutral900">Configuration Changes</h3>
      </div>
      <div className="px-6 pt-4">
        {/* Summary Section - Improved UI Hierarchy and Space Utilization */}
        <div className="mb-4 overflow-hidden rounded-lg border border-neutral200 bg-white">
          <div className="p-4">
            <div className="grid grid-cols-2 gap-4">
              {/* RWU Change - Left Column */}
              <div className="flex flex-col items-center justify-center border-r border-neutral200 pr-4">
                <div className="mb-1 text-sm text-neutral600">Total RWUs</div>
                <div className="flex items-baseline justify-center">
                  <span className="mr-2 text-lg text-neutral600">{currentConfig.rwu || 0}</span>
                  <span className="mx-1 text-lg text-neutral500">→</span>
                  <span className="ml-2 text-2xl font-semibold">{newConfig.rwu || 0}</span>
                </div>
              </div>

              {/* Cost Change - Right Column */}
              <div className="flex flex-col items-center justify-center">
                <div className="mb-1 text-sm text-neutral600">Hourly Cost</div>
                <div className="flex items-baseline justify-center">
                  <span className="mr-2 text-lg text-neutral600">{formatUsdCent(currentPrice || 0)}</span>
                  <span className="mx-1 text-lg text-neutral500">→</span>
                  <span className="ml-2 text-2xl font-semibold">{formatUsdCent(total || 0)}</span>
                </div>
                <div className="mt-1 text-xs text-neutral500">
                  <span className="mr-1">{formatUsdCent((currentPrice || 0) * 720)}</span>
                  <span className="mx-1">→</span>
                  <span className="ml-1">{formatUsdCent((total || 0) * 720)}</span> per month
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Deployment Type Change Messages */}
        {isDowngrade && (
          <div className="mb-4 text-xs text-neutral500">
            * You are downgrading from a{" "}
            <a
              href="https://docs.risingwave.com/reference/architecture"
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary hover:underline">
              distributed cluster
            </a>{" "}
            to a{" "}
            <a
              href="https://docs.risingwave.com/get-started/quickstart#about-risingwave-standalone-mode"
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary hover:underline">
              standalone deployment
            </a>
            .
          </div>
        )}

        {/* Component Changes Section  */}
        {!isCurrentStandalone && !isStandalone && (
          <div className="mb-4 overflow-hidden rounded-lg border border-neutral200 bg-white">
            {(() => {
              // Check if any components have changed
              const hasChanges =
                currentConfig.compute !== newConfig.compute ||
                currentConfig.computeReplica !== (newConfig.computeReplica || 1) ||
                currentConfig.frontend !== newConfig.frontend ||
                currentConfig.frontendReplica !== (newConfig.frontendReplica || 1) ||
                newConfig.autoCompaction ||
                currentConfig.compactor !== newConfig.compactor ||
                currentConfig.compactorReplica !== (newConfig.compactorReplica || 1) ||
                currentConfig.meta !== newConfig.meta ||
                currentConfig.metaReplica !== (newConfig.metaReplica || 1);

              if (!hasChanges) {
                return <div className="px-4 py-3 text-center text-neutral600">No component changes detected</div>;
              }

              // Define component types to render
              const componentTypes = [
                {
                  name: "Compute",
                  current: {
                    value: currentConfig.compute,
                    replica: currentConfig.computeReplica,
                  },
                  new: {
                    value: newConfig.compute,
                    replica: newConfig.computeReplica || 1,
                  },
                  isAutoScaling: false,
                },
                {
                  name: "Frontend",
                  current: {
                    value: currentConfig.frontend,
                    replica: currentConfig.frontendReplica,
                  },
                  new: {
                    value: newConfig.frontend,
                    replica: newConfig.frontendReplica || 1,
                  },
                  isAutoScaling: false,
                },
                {
                  name: "Compactor",
                  current: {
                    value: currentConfig.compactor,
                    replica: currentConfig.compactorReplica,
                  },
                  new: {
                    value: newConfig.compactor,
                    replica: newConfig.compactorReplica || 1,
                  },
                  isAutoScaling: newConfig.autoCompaction,
                  autoScalingMax: newConfig.autoCompactionMaxValue,
                },
                {
                  name: "Meta",
                  current: {
                    value: currentConfig.meta,
                    replica: currentConfig.metaReplica,
                  },
                  new: {
                    value: newConfig.meta,
                    replica: newConfig.metaReplica || 1,
                  },
                  isAutoScaling: false,
                },
              ];

              return (
                <table className="w-full text-sm">
                  <tbody>
                    {componentTypes.map((component, index) => {
                      const isLastRow = index === componentTypes.length - 1;

                      return (
                        <tr key={component.name} className={isLastRow ? "" : "border-b border-neutral200"}>
                          <td className="px-4 py-2.5">
                            <span className="text-neutral600">{component.name}</span>
                          </td>
                          <td className="px-3 py-2.5 text-center">
                            <div className="flex items-center justify-center">
                              {/* Current Value */}
                              {!component.isAutoScaling && (
                                <div className="text-center">
                                  <div className="text-neutral600">{component.current.value} RWUs</div>
                                  <div className="mt-0.5 text-xs text-neutral500">
                                    {component.current.replica} {component.current.replica === 1 ? "node" : "nodes"}
                                  </div>
                                </div>
                              )}

                              {/* Arrow */}
                              {!component.isAutoScaling && <div className="mx-3 text-neutral500">→</div>}

                              {/* New Value */}
                              <div className="text-center">
                                {component.isAutoScaling ? (
                                  <>
                                    <div className="text-blue-600">Auto-scaling</div>
                                    <div className="mt-0.5 text-xs text-neutral500">
                                      0-{component.autoScalingMax} RWUs
                                    </div>
                                  </>
                                ) : (
                                  <>
                                    <div className="text-neutral600">{component.new.value} RWUs</div>
                                    <div className="mt-0.5 text-xs text-neutral500">
                                      {component.new.replica} {component.new.replica === 1 ? "node" : "nodes"}
                                    </div>
                                  </>
                                )}
                              </div>
                            </div>
                          </td>
                          <td className="px-3 py-2.5 text-center">
                            {component.isAutoScaling ? (
                              <span className="text-sm text-blue-600">Dynamic</span>
                            ) : (
                              renderChangeIndicator(
                                component.current.value * (component.current.replica || 1),
                                component.new.value * component.new.replica,
                              )
                            )}
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              );
            })()}
          </div>
        )}
      </div>
    </div>
  );
};

// Helper function to render change indicator
function renderChangeIndicator(oldValue: number, newValue: number) {
  const diff = newValue - oldValue;

  if (diff === 0) {
    return <span className="text-neutral600">No change</span>;
  } else if (diff > 0) {
    return (
      <div className="flex items-center justify-center">
        <FontAwesomeIcon icon={faArrowUp} className="mr-1 text-red-600" />
        <span className="text-sm text-red-600">
          {oldValue} → {newValue}
        </span>
      </div>
    );
  } else {
    return (
      <div className="flex items-center justify-center">
        <FontAwesomeIcon icon={faArrowDown} className="mr-1 text-green-600" />
        <span className="text-sm text-green-600">
          {oldValue} → {newValue}
        </span>
      </div>
    );
  }
}

export default ConfigurationChanges;

