import {
  OpenAPI as MgmtAPI,
  ComponentResource,
  DefaultService,
  TenantService,
  PostTenantResourcesRequestBody,
  Tier,
} from "@/api-mgmt";
import { getCustomComponents, standaloneMarksWithoutPg, trialMarks } from "@/components/Project/Creation/const";
import RescaleConfiguration, { RescaleConfig } from "./RescaleConfiguration";
import { isValidStandaloneTenant } from "@/components/Cluster/utils";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Button, Dialog, DialogTitle } from "@mui/material";
import { faGear } from "@fortawesome/free-solid-svg-icons";
import { DetailContext } from "../../../DetailProvider";
import { MetaStoreType, TierId } from "@/api-mgmt-v2";
import { PlatformType } from "@/hooks/useRegionList";
import React, { useState, useContext } from "react";
import LoadingButton from "@mui/lab/LoadingButton";
import gcpIcon from "@imgs/tenant/gcp_icon.svg";
import CloseIcon from "@/components/Icon/Close";
import azureIcon from "@imgs/tenant/azr.svg";
import { getErrorMsg } from "@/utils/func";
import aws from "@imgs/tenant/aws.svg";
import { toast } from "react-toastify";

import ConfigurationChanges from "./ConfigurationChanges";
import { If } from "react-extras";
import Image from "next/image";

const platformIcon = {
  [PlatformType.AWS]: <Image className="inline-block" alt="aws" width={18} src={aws} />,
  [PlatformType.GCP]: <Image className="inline-block" alt="gcp" width={18} src={gcpIcon} />,
  [PlatformType.AZURE]: <Image className="inline-block" alt="azure" width={16} src={azureIcon} />,
};

interface Props {
  open: boolean;
  compute: ComponentResource | undefined;
  currentRwu: number;
  autoCompacitionRwu?: number;
  regionPlatform?: string;
  handleClose: () => void;
}

const Rescale: React.FC<Props> = ({ open, regionPlatform, handleClose, currentRwu, autoCompacitionRwu }) => {
  const [loading, setLoading] = useState<boolean>(false);
  const { tenant, region, refresh, loading: tenantLoading } = useContext(DetailContext);
  const [rescaleConfig, setRescaleConfig] = useState<RescaleConfig>();

  const isStandAlone = tenant && isValidStandaloneTenant(tenant);

  const isAdvanced = tenant?.resources?.metaStore?.type !== MetaStoreType.SHARING_PG;

  const currentConfig = {
    rwu: currentRwu,
    compute: Number(tenant?.resources.components.compute?.cpu) || 0,
    frontend: Number(tenant?.resources.components.frontend?.cpu) || 0,
    compactor: Number(tenant?.resources.components.compactor?.cpu) || 0,
    meta: Number(tenant?.resources.components.meta?.cpu) || 0,
    computeReplica: Number(tenant?.resources.components.compute?.replica) || 1,
    frontendReplica: Number(tenant?.resources.components.frontend?.replica) || 1,
    compactorReplica: Number(tenant?.resources.components.compactor?.replica) || 1,
    metaReplica: Number(tenant?.resources.components.meta?.replica) || 1,
    autoCompactionMaxValue: autoCompacitionRwu,
    autoCompaction: !!autoCompacitionRwu,
    pg: +(tenant?.resources?.metaStore?.rwu || 0),
  };

  const rescaleCustomized = (creatTier: Tier) => {
    if (!rescaleConfig) return {};
    const components = getCustomComponents(creatTier, {
      compute: rescaleConfig?.compute,
      frontend: rescaleConfig?.frontend,
      compactor: rescaleConfig?.compactor,
      meta: rescaleConfig?.meta,
      computeReplica: rescaleConfig?.computeReplica || 1,
      frontendReplica: rescaleConfig?.frontendReplica || 1,
      compactorReplica: rescaleConfig?.compactorReplica || 1,
      metaReplica: rescaleConfig?.metaReplica || 1,
    });

    if (autoCompacitionRwu && components?.compactor) {
      return {
        compute: components.compute,
        frontend: components.frontend,
        meta: components.meta,
      };
    }

    return components || {};
  };

  const rescaleStandalone = async (creatTier: Tier) => {
    const rwu = rescaleConfig?.rwu;

    const standalone = creatTier.availableStandaloneNodes.find((item) => {
      return +item.cpu === rwu;
    });

    return {
      standalone: standalone
        ? {
            componentTypeId: standalone.id,
            replica: 1,
          }
        : undefined,
    };
  };

  const handleUpdateRescaleConfig = (config: RescaleConfig) => {
    setRescaleConfig(config);
  };

  const onSubmit = async () => {
    if (currentRwu === rescaleConfig?.rwu) {
      handleClose();
      return;
    }
    if (!rescaleConfig?.rwu) return;

    setLoading(true);
    try {
      if (!region) return;
      MgmtAPI.BASE = region.url;
      const tiers = await DefaultService.getTiers().then((res) => {
        return res?.tiers || [];
      });
      const creatTier = tiers.find((item) => item.id === tenant?.tier);
      if (!creatTier) {
        toast.error("No existing tier");
        return;
      }

      let params: PostTenantResourcesRequestBody = {};

      if (tenant?.tier === TierId.STANDARD) {
        params = await rescaleStandalone(creatTier);
      } else {
        params = await rescaleCustomized(creatTier);
      }

      TenantService.postTenantResource(tenant!.id, params)
        .then((res) => {
          refresh();
          toast.success(res?.msg);
          handleClose();
        })
        .catch((err) => {
          toast.error(getErrorMsg(err, "Failed to update project resources"));
        })
        .finally(() => {
          setLoading(false);
        });
    } catch (error) {
      setLoading(false);
    }
  };

  if (!tenant) return null;

  return (
    <>
      <Dialog open={open} hideBackdrop fullScreen sx={{ borderRadius: 0, "&.MuiDialog-root": { top: "56px" } }}>
        <DialogTitle>
          <div className="flex items-center px-4 pt-1">
            <h1 className="text-base">
              <FontAwesomeIcon icon={faGear} />
              <span className="ml-2.5 text-xl">Re-scale the project</span>
            </h1>
            <CloseIcon onClick={handleClose} className="absolute right-10 top-5 cursor-pointer" />
          </div>
        </DialogTitle>
        <div className="mx-auto flex w-[860px] flex-col items-center justify-start text-neutral900">
          <div className="w-full">
            <div className="mb-5 flex items-center justify-start rounded-lg border border-neutral200 bg-neutral50 py-5 text-sm">
              <div className="border-r border-neutral100 px-6">
                <p className="text-neutral400">Project name</p>
                <p>{tenant?.tenantName}</p>
              </div>
              <div className="border-r border-neutral100 px-6">
                <p className="whitespace-nowrap text-neutral400">Cloud provider</p>
                <p>
                  {region?.platform && (
                    <div className="">
                      {platformIcon[region?.platform as PlatformType]}
                      <span className="ml-2">{region.platform}</span>
                    </div>
                  )}
                </p>
              </div>
              <div className="px-5">
                <p className="text-neutral400">Region</p>
                <p className="whitespace-nowrap">{regionPlatform}</p>
              </div>
              <div className="ml-auto mr-4 whitespace-nowrap rounded-2xl border border-[#FCD34D] bg-[#FFFBEB] px-5 py-2 text-sm text-[#B45309]">
                <p>Current size</p>
                <strong>
                  {currentRwu} RWUs
                  {!!autoCompacitionRwu && ` - ${currentRwu + autoCompacitionRwu} RWUs`}
                </strong>
              </div>
            </div>

            <div className="">
              {open && (
                <>
                  <RescaleConfiguration
                    isStandAlone={isStandAlone}
                    tenant={tenant}
                    currentRwu={currentRwu}
                    isAdvanced={isAdvanced}
                    autoCompacitionRwu={autoCompacitionRwu}
                    onUpdateRescaleConfig={handleUpdateRescaleConfig}
                  />
                </>
              )}
              {/* <Summary currentRwu={currentRwu} region={region} tenant={tenant} rescaleConfig={rescaleConfig} /> */}

              <ConfigurationChanges
                tenant={tenant}
                region={region}
                isStandalone={isStandAlone}
                currentConfig={currentConfig}
                newConfig={{
                  rwu: rescaleConfig?.rwu || currentConfig.rwu,
                  compute: rescaleConfig?.compute || currentConfig.compute,
                  frontend: rescaleConfig?.frontend || currentConfig.frontend,
                  compactor: rescaleConfig?.compactor || currentConfig.compactor,
                  meta: rescaleConfig?.meta || currentConfig.meta,
                  computeReplica: rescaleConfig?.computeReplica || currentConfig.computeReplica,
                  frontendReplica: rescaleConfig?.frontendReplica || currentConfig.frontendReplica,
                  compactorReplica: rescaleConfig?.compactorReplica || currentConfig.compactorReplica,
                  metaReplica: rescaleConfig?.metaReplica || currentConfig.metaReplica,
                  // We don't support updating auto compaction for rescale currently
                  autoCompactionMaxValue: currentConfig.autoCompactionMaxValue,
                  autoCompaction: currentConfig.autoCompaction,
                  pg: currentConfig.pg,
                }}
              />
            </div>
          </div>
          <div className="mt-5 flex w-full justify-end border-t-[1px] px-10 py-5">
            <Button sx={{ mr: "auto" }} onClick={() => handleClose()}>
              Cancel
            </Button>
            <If condition={rescaleConfig?.rwu !== currentRwu}>
              <LoadingButton onClick={onSubmit} loading={loading} variant="contained">
                Confirm rescale
              </LoadingButton>
            </If>
          </div>
        </div>
      </Dialog>
    </>
  );
};
export default Rescale;

