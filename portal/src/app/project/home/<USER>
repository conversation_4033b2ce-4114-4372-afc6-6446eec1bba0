"use client";
import { faRotate, faArrowDownWideShort, faArrowUpWideShort, faCircleInfo } from "@fortawesome/free-solid-svg-icons";
import SkeltonTenant from "@/components/Skeltons/SkeltonTenants/SkeltonTenant";
import React, { useState, useEffect, useMemo, Suspense } from "react";
import SkeltonTenants from "@/components/Skeltons/SkeltonTenants";
import { Button, InputAdornment, TextField } from "@mui/material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useTier } from "@/components/Project/Creation/useTier";
import ConnectPopup from "@/components/Cluster/ConnectPopup";
import TenantCardWrapper from "@/components/Project/List";
import { sqlUserAtom } from "@/components/Cluster/store";
import { useRegionList } from "@/hooks/useRegionList";
import { EVENTS, eventEmitter } from "@/utils/events";
import Creation from "@/components/Project/Creation";
import { OpenAPI as MgmtAPIv2 } from "@/api-mgmt-v2";
import SearchIcon from "@mui/icons-material/Search";
import { TenantsService, TierId } from "@/api-user";
import surfing_pic from "@imgs/tenant/surfing.png";
import { useOrgInfo } from "@/hooks/useOrgInfo";
import { OpenAPI as MgmtAPI } from "@/api-mgmt";
import { NEW_TENANT_SESSION_ID } from "./store";
import useUserInfo from "@/hooks/useUserInfo";
import { useSessionStorage } from "react-use";
import { useRouter } from "next/navigation";
import { getErrorMsg } from "@/utils/func";
import { cloneDeep, sortBy } from "lodash";
import { Domains } from "@/config/router";
import { Choose, If } from "react-extras";
import { toast } from "react-toastify";
import { Tenant } from "@/api-mgmt";
import { useSetAtom } from "jotai";
import Image from "next/image";
import Link from "next/link";
import useSWR from "swr";

const DEFAULT_INTERVERL = 8000;

interface TenantsProps {}

const HomePage: React.FC<TenantsProps> = () => {
  const [showConnect, setShowConnect] = useState(false);
  const [activeTenant, setActiveTenant] = useState<Tenant | null>(null);
  const [refreshInterval, setRefreshInterval] = useState(DEFAULT_INTERVERL);
  const [sort, setSort] = useState<"desc" | "asc">();
  const [searchValue, setSearchValue] = useState("");
  // When user finish the creations, the backend api can't get this tenant at once.
  const setDBusers = useSetAtom(sqlUserAtom);
  const regions = useRegionList();
  const [newTenantNsId, setNewTenantNsId] = useSessionStorage(NEW_TENANT_SESSION_ID);
  const { userInfo } = useUserInfo();
  const { orgInfo } = useOrgInfo();

  const {
    data: tenants,
    error,
    mutate,
    isLoading,
  } = useSWR(
    "getTenants",
    () => {
      return TenantsService.getTenants().then(({ tenants }) => {
        return tenants;
      });
    },
    {
      refreshInterval,
    },
  );

  useEffect(() => {
    if (!regions) return;
    const showConnect = ({ tenant }: { tenant: Tenant }) => {
      const region = regions?.find((item) => item.regionName === tenant.region);
      MgmtAPI.BASE = region?.url || "";
      MgmtAPIv2.BASE = region?.urlV2 || "";

      setActiveTenant(tenant);
      setShowConnect(true);
    };
    eventEmitter.on(EVENTS.OPEN_CONNECT_POPUP, showConnect);
    return () => {
      eventEmitter.off(EVENTS.OPEN_CONNECT_POPUP, showConnect);
    };
  }, [regions, setDBusers]);

  const showNewTenantLoading = useMemo(() => {
    if (!newTenantNsId) return false;
    const item = tenants?.find((item) => newTenantNsId && item.nsId === newTenantNsId);
    if (item) setNewTenantNsId(null);
    return !item;
  }, [newTenantNsId, tenants, setNewTenantNsId]);

  useEffect(() => {
    if (showNewTenantLoading) {
      setRefreshInterval(1000);
    } else {
      setRefreshInterval(DEFAULT_INTERVERL);
    }
  }, [showNewTenantLoading]);

  const closeCreateTenant = () => {
    setActiveTenant(null);
  };

  const refreshTenant = () => {
    mutate();
  };

  const handleCreateTenant = (nsId: string) => {
    setNewTenantNsId(nsId);
    refreshTenant();
    closeCreateTenant();
  };
  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(event.target.value);
  };

  const handleSort = (sort: "desc" | "asc" | undefined) => {
    setSort(sort === "desc" ? "asc" : "desc");
  };
  const clusters = useMemo(() => {
    if (!tenants) return [];
    let list = !sort && !searchValue ? tenants : cloneDeep(tenants);
    if (sort) {
      list = sort === "desc" ? sortBy(list, "createdAt").reverse() : sortBy(list, "createdAt");
    }
    if (searchValue) {
      list = list.filter((item) => {
        return item?.tenantName?.includes(searchValue);
      });
    }
    return list;
  }, [tenants, sort, searchValue]);

  if (error) {
    toast.error(getErrorMsg(error, "Get projects failed"));
  }
  if (tenants?.length === 0 && !showNewTenantLoading) {
    return (
      <div className="min-w-[700px]">
        <Image className="mx-auto w-2/3 max-w-5xl" src={surfing_pic} alt="" />
        <p className="mb-5 text-center text-[22px] font-bold text-neutral900">
          Welcome to RisingWave, {userInfo?.userName}!
        </p>
        <Creation handleCreateTenant={handleCreateTenant} />
      </div>
    );
  }

  return (
    <>
      <div className="relative min-w-[1024px] px-5">
        <header className="mb-4 flex items-center justify-start border-b border-solid border-[#e6e6e6] py-3">
          <div className="flex items-center">
            {tenants ? (
              <CreateProjectButton tenantsLength={tenants.length} />
            ) : (
              <Button
                variant="contained"
                sx={{
                  padding: "4px 10px",
                }}>
                Create project
              </Button>
            )}
          </div>
          <div className="ml-auto flex items-center">
            <Button
              sx={{
                height: "28px",
                fontSize: "12px",
                fontWeight: "bold",
                minWidth: "32px",
                marginRight: "5px",
                px: "0px",
              }}
              variant="outlined"
              onClick={refreshTenant}>
              <FontAwesomeIcon icon={faRotate} className="text-sm" />
            </Button>
            <Button
              sx={{
                height: "28px",
                fontSize: "12px",
                fontWeight: "bold",
                minWidth: "32px",
                marginRight: "5px",
                px: "0px",
              }}
              variant="outlined"
              onClick={() => handleSort(sort)}>
              <FontAwesomeIcon icon={sort === "desc" ? faArrowDownWideShort : faArrowUpWideShort} className="text-sm" />
            </Button>
            <div className="w-[164px] xl:w-[200px]">
              <TextField
                placeholder="Search by name"
                fullWidth
                value={searchValue}
                autoComplete="off"
                sx={{
                  ".MuiInputBase-root": {
                    height: "28px",
                    fontSize: "12px",
                    paddingRight: "4px",
                  },
                }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon fontSize="small" />
                    </InputAdornment>
                  ),
                }}
                onChange={handleSearch}
                size="small"
              />
            </div>
          </div>
        </header>
        <Choose>
          <Choose.When condition={isLoading && !tenants}>
            <SkeltonTenants />
          </Choose.When>
          <Choose.Otherwise>
            <>
              <div className="relative text-[#363A40]">
                <If condition={!!tenants && tenants.length > 0}>
                  <div className="relative">
                    <TenantCardWrapper tenants={clusters!} />
                  </div>
                </If>
                <If condition={showNewTenantLoading}>
                  <div className="mt-5">
                    <SkeltonTenant />
                  </div>
                </If>
              </div>
            </>
          </Choose.Otherwise>
        </Choose>
      </div>
      {activeTenant?.nsId && (
        <ConnectPopup
          nsId={activeTenant.nsId}
          open={showConnect}
          tenant={activeTenant || undefined}
          onCancel={() => {
            setShowConnect(false);
          }}
        />
      )}
    </>
  );
};

const SuspenseHomePage = () => {
  return (
    <Suspense>
      <HomePage />
    </Suspense>
  );
};

function CreateProjectButton({ tenantsLength }: { tenantsLength: number }) {
  const router = useRouter();
  const { orgInfo } = useOrgInfo();
  const tier = useTier();

  const onClickCreateProject = () => {
    if (!orgInfo) return;
    if (tenantsLength === 1 && tier === TierId.DEVELOPER_FREE && orgInfo?.trialDays > 0) {
      toast.error(
        "You can create only one project during the free trial. To create more projects, please upgrade your plan.",
      );
    } else {
      router.push(Domains.creation);
    }
  };

  return (
    <Button
      onClick={onClickCreateProject}
      variant="contained"
      sx={{
        padding: "4px 10px",
      }}>
      Create project
    </Button>
  );
}

export default SuspenseHomePage;

