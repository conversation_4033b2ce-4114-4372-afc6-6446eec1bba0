"use client";

import {
  <PERSON>box,
  MenuItem,
  SelectChangeEvent,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  TablePagination,
  CircularProgress,
} from "@mui/material";
import { GetUserNotificationsResponseBody, Notification, NotificationService } from "@/api-user";
import { faTrashCan, faGear, faBell, faAngleRight } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import SettingModal, { SettingsType } from "../SettingModal";
import { Choose, classNames } from "react-extras";
import CloseIcon from "@/components/Icon/Close";
import { useState, useEffect } from "react";
import Select from "@mui/material/Select";
import Dialog from "@mui/material/Dialog";
import styles from "./style.module.scss";
import EvelopEntry from "./EvelopEntry";
import { If } from "react-extras";
import { marked } from "marked";
import Empty from "./Empty";
import dayjs from "dayjs";
import useSWR from "swr";

const POLL_INTERVAL = 30 * 1000;

interface Props {}

type Order = "createdAtAsc" | "createdAtDesc";

enum messageType {
  ALERT = "Alert",
  MESSAGE = "Message",
  NOTIFICATION = "Notification",
}
const MessagesModal: React.FC<Props> = () => {
  const [open, setOpen] = useState(false);
  const [sort, setSort] = useState<Order>("createdAtDesc");
  const [expandedArr, setExpandedArr] = useState<Array<number>>([]);
  const [selectedArr, setSelectedArr] = useState<Array<number>>([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [showSetting, setShowSetting] = useState(false);

  const {
    data: notifications,
    isLoading,
    mutate,
  } = useSWR<GetUserNotificationsResponseBody | undefined>(
    ["getNotifications", page, rowsPerPage, sort],
    () => {
      const offset = page * rowsPerPage;
      const limit = rowsPerPage;
      return NotificationService.getNotifications(offset, limit, sort).then((res) => {
        if (offset >= res.size && page !== 1) {
          setPage(0);
        }
        return res;
      });
    },
    {
      refreshInterval: POLL_INTERVAL,
    },
  );

  const messagesList = notifications?.notifications || [];
  const size = notifications?.size || 0;

  useEffect(() => {
    setExpandedArr([]);
    setSelectedArr([]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page, sort]);

  const handleClose = () => {
    setOpen(false);
  };

  const handleChange = (event: SelectChangeEvent) => {
    setSort(event.target.value as Order);
  };

  const onSelect = (event: React.MouseEvent<HTMLButtonElement>, id: number) => {
    event && event.stopPropagation();
    const index = selectedArr.indexOf(id);
    if (index === -1) {
      setSelectedArr([...selectedArr, id]);
    } else {
      const updatedArr = [...selectedArr];
      updatedArr.splice(index, 1);
      setSelectedArr(updatedArr);
    }
  };

  const onSelectAll = () => {
    if (!messagesList?.length) return;
    const isAll = isSelectedAll();
    if (isAll) {
      setSelectedArr([]);
    } else {
      const AllId = messagesList.map((item) => item.id);
      setSelectedArr(AllId);
    }
  };

  const isSelectedAll = () => {
    return !isLoading && selectedArr.length === messagesList.length;
  };

  const isSelected = () => {
    return selectedArr.length !== 0;
  };

  const onExpand = (id: number, status: Notification.status) => {
    const index = expandedArr.indexOf(id);
    if (index === -1) {
      setExpandedArr([...expandedArr, id]);
      if (status === Notification.status.UNREAD) {
        setMessageStatus([id], Notification.status.READ);
      }
    } else {
      const updatedArr = [...expandedArr];
      updatedArr.splice(index, 1);
      setExpandedArr(updatedArr);
    }
  };

  const onExpandAll = () => {
    if (!messagesList?.length) return;
    const isAll = isExpandAll();
    if (isAll) {
      setExpandedArr([]);
    } else {
      const unreadList: number[] = [];
      const AllId = messagesList.map((item) => {
        if (item.status === Notification.status.UNREAD) {
          unreadList.push(item.id);
        }
        return item.id;
      });
      setExpandedArr(AllId);
      unreadList.length && setMessageStatus(unreadList, Notification.status.READ);
    }
  };

  const isExpandAll = () => {
    return expandedArr.length === messagesList.length;
  };

  const onDelete = () => {
    if (selectedArr.length === 0) return;
    setMessageStatus(selectedArr, Notification.status.DELETED).then(() => {
      setSelectedArr([]);
    });
  };

  const setMessageStatus = (ids: number[], statuses: Notification.status) => {
    const playload = {
      id: [...ids],
      status: statuses,
    };
    return NotificationService.putNotificationsStatuses(playload)
      .then((res) => {
        mutate();
      })
      .catch((err) => console.error(err));
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const getTitleColor = (status: Notification.status, type: string) => {
    if (status === Notification.status.READ) {
      return "#9DA1A6";
    }
    if (type === messageType.ALERT) {
      return "#B45309";
    } else {
      return "#16171A";
    }
  };

  const controlOpen = () => {
    setOpen((pre) => !pre);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const currentPerPage = parseInt(event.target.value, 10);
    if (currentPerPage < rowsPerPage) {
      setSelectedArr([]);
    }
    setRowsPerPage(currentPerPage);
    setPage(0);
  };

  return (
    <>
      <EvelopEntry onClick={controlOpen} unreadNum={notifications?.unreadNum || 0} />

      <Dialog fullScreen open={open} hideBackdrop={true} sx={{ top: "56px" }}>
        <div className="relative px-10 py-5">
          <div className="flex w-full flex-row-reverse items-center">
            <CloseIcon onClick={handleClose} className="cursor-pointer" />
            {/* <div
              onClick={() => {
                setShowSetting(true);
                setTimeout(() => {
                  setOpen(false);
                }, 300);
              }}
              className="mr-5 cursor-pointer rounded border border-neutral200 bg-neutral50 px-2 py-0.5 transition-colors duration-150 hover:bg-neutral100">
              <FontAwesomeIcon className="text-sm" icon={faGear} />
            </div> */}
          </div>
          <div className="flex items-center pb-4 text-[#081F29]">
            <FontAwesomeIcon icon={faBell} />
            <h1 className="ml-2.5 text-[20px]">Messages</h1>
          </div>
          <div className="flex items-center justify-between py-2 pl-4 pr-[15px] text-sm">
            <div className="flex h-full items-center">
              <div className="flex cursor-pointer items-center" onClick={onSelectAll}>
                <Checkbox size="small" checked={isSelectedAll()} indeterminate={!isSelectedAll() && isSelected()} />
                Select all
              </div>

              <Divider
                sx={{
                  margin: "0px 20px",
                  height: "18px",
                  alignSelf: "center",
                }}
                orientation="vertical"
                variant="middle"
                flexItem
              />
              <FontAwesomeIcon
                onClick={() => onDelete()}
                icon={faTrashCan}
                className="cursor-pointer text-sm text-error700"
              />
            </div>
            <div className="flex h-full items-center">
              <div className="cursor-pointer text-primary" onClick={onExpandAll}>
                {isExpandAll() ? "Collapse all" : "Expand all"}
              </div>
              <Divider
                sx={{
                  margin: "0px 15px 0 20px",
                  height: "24px",
                  alignSelf: "center",
                }}
                orientation="vertical"
                variant="middle"
                flexItem
              />
              <span className="mr-1">Sort by</span>
              <Select className="h-[24px] w-[122px]" sx={{ fontSize: "14px" }} value={sort} onChange={handleChange}>
                <MenuItem sx={{ fontSize: "14px" }} value={"createdAtDesc"}>
                  Latest
                </MenuItem>
                <MenuItem sx={{ fontSize: "14px" }} value={"createdAtAsc"}>
                  Oldest
                </MenuItem>
              </Select>
            </div>
          </div>
          <Divider />
          <Choose>
            <Choose.When condition={isLoading}>
              <div className="mt-20 flex w-full items-center justify-center">
                <CircularProgress />
              </div>
            </Choose.When>
            <Choose.Otherwise>
              <If condition={messagesList?.length === 0 && !isLoading}>
                <div className="mt-36 flex items-center">
                  <Empty />
                </div>
              </If>
              <If condition={messagesList?.length > 0}>
                <div>
                  {messagesList.map((item, index) => (
                    <Accordion
                      key={index}
                      expanded={expandedArr.includes(item?.id)}
                      onChange={() => onExpand(item.id, item.status)}
                      sx={{
                        boxShadow: "none",
                        border: "none",
                        borderBottom: "1px solid #E6E6E6",
                        "&.MuiAccordion-root": {
                          margin: 0,
                        },
                        ":before": {
                          height: 0,
                        },
                        ":hover": {
                          bgcolor: "#F3FEFF",
                        },
                      }}>
                      <AccordionSummary
                        expandIcon={<FontAwesomeIcon icon={faAngleRight} />}
                        aria-controls="panel1a-content"
                        sx={{
                          paddingRight: "20px",
                          ".MuiAccordionSummary-expandIconWrapper.Mui-expanded": {
                            transform: "rotate(90deg)",
                          },
                        }}
                        id="panel1a-header">
                        <div className="flex">
                          <div className="text-[14px]">
                            <div className="flex items-center">
                              <Checkbox
                                size="small"
                                checked={selectedArr.includes(item.id)}
                                onClick={(event) => onSelect(event, item.id)}
                              />
                              <div
                                className="ml-1 flex h-[22px] items-center font-bold"
                                style={{ color: getTitleColor(item.status, item.type) }}>
                                {item.title}
                              </div>
                            </div>
                            <div className="ml-[42px] flex">
                              From: {item.sender.name} ({item.sender.email})
                              <Divider
                                sx={{
                                  margin: "0px 12px",
                                  borderRightWidth: "1.5px",
                                  height: "16px",
                                  alignSelf: "center",
                                }}
                                orientation="vertical"
                                variant="middle"
                                flexItem
                              />
                              {dayjs(item.time).format("YYYY-MM-DD HH:mm:ss")}
                            </div>
                          </div>
                        </div>
                      </AccordionSummary>
                      <AccordionDetails>
                        <div
                          className={classNames(styles["reset-markdown"], "px-[42px] pb-6 text-[14px]")}
                          dangerouslySetInnerHTML={{ __html: marked(item.content) }}
                        />
                        <div className="flex w-full flex-row-reverse">
                          <FontAwesomeIcon
                            onClick={() => setMessageStatus([item.id], Notification.status.DELETED)}
                            icon={faTrashCan}
                            className="cursor-pointer text-xs font-bold text-[#091720]"
                          />
                        </div>
                      </AccordionDetails>
                    </Accordion>
                  ))}
                </div>
                <TablePagination
                  sx={{
                    mt: 1,
                    color: "#474B65",
                    ".MuiTablePagination-selectLabel": {
                      color: "#727489",
                    },
                  }}
                  component="div"
                  rowsPerPageOptions={[5, 10, 25]}
                  rowsPerPage={rowsPerPage}
                  page={page}
                  count={size}
                  onPageChange={handleChangePage}
                  onRowsPerPageChange={handleChangeRowsPerPage}
                  labelRowsPerPage="Message per page"
                />
              </If>
            </Choose.Otherwise>
          </Choose>
        </div>
      </Dialog>

      <SettingModal
        open={showSetting}
        onCancel={() => {
          setShowSetting(false);
        }}
        type={SettingsType.AlertAndMessages}
      />
    </>
  );
};
export default MessagesModal;

