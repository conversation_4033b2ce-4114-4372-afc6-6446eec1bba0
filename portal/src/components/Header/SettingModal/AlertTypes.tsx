"use client";

import {
  faCircleExclamation,
  faCoins,
  faCubes,
  faGears,
  faStethoscope,
  faTriangleExclamation,
} from "@fortawesome/free-solid-svg-icons";
import { AlertSeverity, AlertType, AlertTypeArray, NotificationsService } from "@/api-user-v2";
import { CircularProgress, MenuItem, Select, SelectChangeEvent } from "@mui/material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useEffect, useState } from "react";
import { getErrorMsg } from "@/utils/func";
import linesPic from "@imgs/lines.svg";
import { toast } from "react-toastify";
import Image from "next/image";

enum showType {
  SEVERITY = "severity levels",
  CATEGORIES = "function categories",
}

enum tagType {
  HEALTH = "Service Health",
  STREAMING = "Streaming",
  COMPACTION = "Compaction",
  OPERATION = "Cluster Operations",
  BILLING = "Billing System",
}

const MsgCard = ({ data }: { data: AlertType }) => {
  const { name, severity, category, description } = data;
  if (severity === AlertSeverity.CRITICAL)
    return (
      <div className="rounded-md px-5 py-3 text-sm shadow-[1px_1px_5.1px_0px_rgba(0,0,0,0.10)]">
        <p className="ml-auto w-fit rounded-full border border-error700 bg-error50 px-3 text-xs font-medium leading-5 text-error700">
          <FontAwesomeIcon className="mr-1" icon={faTriangleExclamation} />
          Critical
        </p>
        <p className="font-semibold">{name}</p>
        <p className="mb-3 mt-1 italic text-neutral500">{category}</p>
        <p>{description}</p>
      </div>
    );
  return (
    <div className="rounded-md px-5 py-3 text-sm shadow-[1px_1px_5.1px_0px_rgba(0,0,0,0.10)]">
      <p className="ml-auto w-fit rounded-full border border-warning700 bg-warning50 px-3 text-xs font-medium leading-5 text-warning700">
        <FontAwesomeIcon className="mr-1" icon={faCircleExclamation} />
        Warning
      </p>
      <p className="font-semibold">{name}</p>
      <p className="mb-3 mt-1 italic text-neutral500">{category}</p>
      <p>{description}</p>
    </div>
  );
};

export const AlertTypes = () => {
  const [categrate, setCategrate] = useState<showType>(showType.SEVERITY);
  const [loading, setLoading] = useState(false);
  const [alertTypes, setAlertTypes] = useState<AlertTypeArray>();

  useEffect(() => {
    setLoading(true);
    NotificationsService.getAlertTypes()
      .then((res) => {
        setAlertTypes(res);
        setLoading(false);
      })
      .catch((err) => {
        toast.error(getErrorMsg(err, "Failed to fetch alert types"));
        setLoading(false);
      });
  }, []);

  const handleChange = (event: SelectChangeEvent) => {
    setCategrate(event.target.value as showType);
  };

  return (
    <div className="">
      <div className="flex items-center text-sm">
        Overview of all alert types by {categrate}:
        <Select
          className="ml-auto h-[40px] w-[218px]"
          sx={{ fontSize: "14px" }}
          value={categrate}
          onChange={handleChange}>
          <MenuItem sx={{ fontSize: "14px" }} value={showType.SEVERITY}>
            Show by severity levels
          </MenuItem>
          <MenuItem sx={{ fontSize: "14px" }} value={showType.CATEGORIES}>
            Show by categories
          </MenuItem>
        </Select>
      </div>
      {loading ? (
        <div className="flex h-[300px] w-[800px] items-center justify-center">
          <CircularProgress />
        </div>
      ) : categrate === showType.SEVERITY ? (
        <>
          <div className="my-4 rounded border border-error300 bg-error50 py-2 text-center text-sm text-error700">
            <FontAwesomeIcon className="mr-2 text-error700" icon={faTriangleExclamation} />
            Critical
          </div>
          <div className="grid grid-cols-3 gap-3">
            {alertTypes?.map((data, index) => {
              if (data.severity === AlertSeverity.CRITICAL) return <MsgCard key={index} data={data} />;
            })}
          </div>
          <div className="my-4 rounded border border-warning300 bg-warning50 py-2 text-center text-sm text-warning700">
            <FontAwesomeIcon className="mr-2 text-warning700" icon={faCircleExclamation} />
            Warning
          </div>
          <div className="grid grid-cols-3 gap-3">
            {alertTypes?.map((data, index) => {
              if (data.severity === AlertSeverity.WARNING) return <MsgCard key={index} data={data} />;
            })}
          </div>
        </>
      ) : (
        <div className="text-black">
          <div className="my-4 rounded border border-neutral200 bg-neutral100 py-2 text-center text-sm font-medium">
            <FontAwesomeIcon className="mr-2" icon={faStethoscope} />
            {tagType.HEALTH}
          </div>
          <div className="grid grid-cols-3 gap-3">
            {alertTypes?.map((data, index) => {
              if (data.category === tagType.HEALTH) return <MsgCard key={index} data={data} />;
            })}
          </div>
          <div className="my-4 flex items-center justify-center rounded border border-neutral200 bg-neutral100 py-2 text-sm font-medium">
            <Image className="mr-2 w-[14px]" src={linesPic} alt="line" />
            {tagType.STREAMING}
          </div>
          <div className="grid grid-cols-3 gap-3">
            {alertTypes?.map((data, index) => {
              if (data.category === tagType.STREAMING) return <MsgCard key={index} data={data} />;
            })}
          </div>
          <div className="my-4 rounded border border-neutral200 bg-neutral100 py-2 text-center text-sm font-medium">
            <FontAwesomeIcon className="mr-2" icon={faCubes} />
            {tagType.COMPACTION}
          </div>
          <div className="grid grid-cols-3 gap-3">
            {alertTypes?.map((data, index) => {
              if (data.category === tagType.COMPACTION) return <MsgCard key={index} data={data} />;
            })}
          </div>
          <div className="my-4 rounded border border-neutral200 bg-neutral100 py-2 text-center text-sm font-medium">
            <FontAwesomeIcon className="mr-2" icon={faGears} />
            {tagType.OPERATION}
          </div>
          <div className="grid grid-cols-3 gap-3">
            {alertTypes?.map((data, index) => {
              if (data.category === tagType.OPERATION) return <MsgCard key={index} data={data} />;
            })}
          </div>
          <div className="my-4 rounded border border-neutral200 bg-neutral100 py-2 text-center text-sm font-medium">
            <FontAwesomeIcon className="mr-2" icon={faCoins} />
            {tagType.BILLING}
          </div>
          <div className="grid grid-cols-3 gap-3">
            {alertTypes?.map((data, index) => {
              if (data.category === tagType.BILLING) return <MsgCard key={index} data={data} />;
            })}
          </div>
        </div>
      )}
    </div>
  );
};

