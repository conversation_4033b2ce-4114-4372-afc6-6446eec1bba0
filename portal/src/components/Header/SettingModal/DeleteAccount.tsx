"use client";
import { faCircleExclamation } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { OrgService, UserService } from "@/api-user";
import { Checkbox, Button } from "@mui/material";
import useUserInfo from "@/hooks/useUserInfo";
import { getErrorMsg } from "@/utils/func";
import Modal from "@/components/Modal";
import { toast } from "react-toastify";
import Auth from "@/utils/auth/Auth";
import { If } from "react-extras";
import { useState } from "react";
import useSWR from "swr";

interface DeleteAccountProps {
  onCancel: () => void;
}
const DeleteAccount: React.FC<DeleteAccountProps> = ({ onCancel }) => {
  const { userInfo } = useUserInfo();
  const [confirmDelete, setConfirmDelete] = useState(false);
  const { data: orgInfo } = useSWR("getOrgInfo", () => {
    return OrgService.getOrg().then((orgInfo) => {
      return orgInfo;
    });
  });

  const handleDeleteAccount = () => {
    UserService.deleteUser()
      .then((res) => {
        toast.success("Your account is deleted.");
        Auth.logout();
      })
      .catch((err) => {
        if (err.status === 400) {
          Modal.delete({
            deleteObj: {
              name: "Organization",
              value: orgInfo?.name || "org",
              warning:
                "You are the last admin of this organization. Deleting your account will also delete the organization. Do you want to continue?",
              inputLabel: "Organization name",
            },
            handleDelete: handleDeleteOrg,
          });
        } else {
          toast.error(getErrorMsg(err, "Failed to delete"));
        }
      });
  };

  const handleDeleteOrg = () => {
    return OrgService.deleteOrg()
      .then((res) => {
        toast.success("Your account and organization are deleted.");
        Auth.logout();
      })
      .catch((err) => {
        toast.error(getErrorMsg(err, "Failed to delete"));
      });
  };

  return (
    <div className="text-sm text-[#16171A]">
      <p className="text-[#B91C1C] bg-error50 px-5 py-3 rounded">
        <FontAwesomeIcon icon={faCircleExclamation} />
        <span className="ml-2">
          All associated data will be deleted upon account deletion. Are you sure you want to delete the account?{" "}
        </span>
      </p>

      <div className="mb-3 mt-5">
        <Checkbox
          size="small"
          value={confirmDelete}
          sx={{ p: 0 }}
          onChange={(event) => {
            setConfirmDelete(event.target.checked);
          }}
        />
        <span> Yes, I want to delete the account.</span>
      </div>
      <If condition={confirmDelete}>
        <p className="text-base font-medium pl-6">{userInfo?.userEmail}</p>

        <div className="mt-4 flex justify-end">
          <Button variant="outlined" onClick={onCancel}>Cancel</Button>
          <Button sx={{ ml: "26px" }} color="error" variant="contained" onClick={()=>{
            Modal.delete({
            deleteObj: {
              name: "Organization",
              value: orgInfo?.name || "org",
              warning:
                "You are the last admin of this organization. Deleting your account will also delete the organization. Do you want to continue?",
              inputLabel: "Organization name",
            },
            handleDelete: handleDeleteOrg,
          });
            // handleDeleteAccount
            }}>
            Delete
          </Button>
        </div>
      </If>
    </div>
  );
};
export default DeleteAccount;

