import {
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Button,
  Checkbox,
  TextField,
  Dialog,
  CircularProgress,
} from "@mui/material";
import {
  faBook,
  faCircleCheck,
  faCircleExclamation,
  faEnvelope,
  faPlus,
  faTrashCan,
  faTriangleExclamation,
} from "@fortawesome/free-solid-svg-icons";
import { AlertSeverity, NotificationsService, SubscriptionsService } from "@/api-user-v2";
import EmptyStatusIcon from "@/components/EmptyStatus/EmptyStatusIcon";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { yupResolver } from "@hookform/resolvers/yup";
import { useEffect, useRef, useState } from "react";
import CloseIcon from "@/components/Icon/Close";
import { getErrorMsg } from "@/utils/func";
import { useForm } from "react-hook-form";
import { classNames } from "react-extras";
import { AlertTypes } from "./AlertTypes";
import styles from "./style.module.scss";
import { LoadingButton } from "@mui/lab";
import Modal from "@/components/Modal";
import { toast } from "react-toastify";
import * as yup from "yup";
import useSWR from "swr";

type Inputs = {
  email: string;
  critical: boolean;
  warning: boolean;
};

interface CheckboxType {
  [key: string]: { critical: boolean; warning: boolean };
}

const schema = yup.object().shape({
  email: yup.string().required("Email is required").email("Invalid email"),
  critical: yup.boolean().required(),
  warning: yup.boolean().required(),
});

const diffCheckboxs = (preCheckboxs: CheckboxType, newCheckboxs: CheckboxType) => {
  const res = [];
  for (let recipientID in preCheckboxs) {
    if (
      preCheckboxs[recipientID].critical !== newCheckboxs[recipientID].critical ||
      preCheckboxs[recipientID].warning !== newCheckboxs[recipientID].warning
    ) {
      const severities: Array<AlertSeverity> = [];
      if (newCheckboxs[recipientID].critical) severities.push(AlertSeverity.CRITICAL);
      if (newCheckboxs[recipientID].warning) severities.push(AlertSeverity.WARNING);
      res.push({
        id: recipientID,
        severities,
      });
    }
  }

  return res;
};

export const EmailRecipients = () => {
  const [showAddEmail, setShowAddEmail] = useState(false);
  const [focusInput, setFocusInput] = useState(false);
  const [showTestNotification, setShowTestNotification] = useState(false);
  const [notificationSent, setNotificationSent] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [addEmailLoading, setAddEmailLoading] = useState(false);
  const [updateRecipientsLoading, setUpdateRecipientsLoading] = useState(false);
  const [selectedEmails, setSelectedEmails] = useState<string[]>([]);
  const [showAlertTypes, setShowAlertTypes] = useState(false);
  const [isCheckboxsChange, setIsCheckboxsChange] = useState(false);
  const [checkboxStates, setCheckboxStates] = useState<CheckboxType>({});
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<Inputs>({
    mode: "all",
    resolver: yupResolver(schema),
  });
  const preCheckboxsRef = useRef({});

  const {
    data: recipients,
    isLoading: loadingRecipients,
    mutate: refreshRecipients,
  } = useSWR(["getRecipients"], () => {
    return NotificationsService.getRecipients().then((recipients) => {
      return NotificationsService.getSubscriptions().then((subscriptions) => {
        return recipients
          .filter((r) => r.config.type === "email")
          .map((r) => {
            const severities = subscriptions.map((s) => {
              if (s.recipientId === r.id) return s.severity;
            });
            return {
              id: r.id,
              email: (r.config.type === "email" && r.config.email) || "",
              critical: severities.includes(AlertSeverity.CRITICAL) || false,
              warning: severities.includes(AlertSeverity.WARNING) || false,
            };
          });
      });
    });
  });

  useEffect(() => {
    const isChange = JSON.stringify(preCheckboxsRef.current) !== JSON.stringify(checkboxStates);
    if (isChange !== isCheckboxsChange) {
      setIsCheckboxsChange(isChange);
    }
  }, [checkboxStates]);

  useEffect(() => {
    if (!!recipients?.length) {
      const newCheckboxStates = recipients.reduce(
        (acc, recipient) => {
          acc[recipient.id] = {
            critical: recipient.critical,
            warning: recipient.warning,
          };
          return acc;
        },
        {} as { [key: string]: { critical: boolean; warning: boolean } },
      );
      setCheckboxStates(newCheckboxStates);
      preCheckboxsRef.current = newCheckboxStates;
    }
  }, [recipients]);

  const handleCheckboxChange = (id: string, type: "critical" | "warning", checked: boolean) => {
    setCheckboxStates((prev) => ({
      ...prev,
      [id]: {
        ...prev[id],
        [type]: checked,
      },
    }));
  };

  const onClickCancel = () => {
    setShowAddEmail(false);
    reset({ email: "", critical: false, warning: false });
    if (isCheckboxsChange) {
      setCheckboxStates(preCheckboxsRef.current);
      setIsCheckboxsChange(false);
    }
  };

  const onClose = () => {
    setIsLoading(false);
    setShowTestNotification(false);
    setNotificationSent(false);
  };

  const handleSelectEmail = (id: string) => {
    if (selectedEmails.includes(id)) {
      setSelectedEmails(selectedEmails.filter((item) => item !== id));
    } else {
      setSelectedEmails([...selectedEmails, id]);
    }
  };

  const onSelectAll = () => {
    if (selectedEmails.length > 0 && selectedEmails.length === recipients?.length) {
      setSelectedEmails([]);
    } else {
      setSelectedEmails(recipients?.map((item) => item.id) || []);
    }
  };

  const onSubmit = (data: Inputs) => {
    if (showAddEmail) {
      // add new email
      setAddEmailLoading(true);
      const severities: Array<AlertSeverity> = [];
      if (data.critical) severities.push(AlertSeverity.CRITICAL);
      if (data.warning) severities.push(AlertSeverity.WARNING);

      SubscriptionsService.postRecipients({
        config: {
          type: "email",
          email: data.email,
        },
      })
        .then(() => {
          //wait for the recipients to be updated
          setTimeout(() => {
            NotificationsService.getRecipients().then((recipients) => {
              const matchedRecipient = recipients.find(
                (r) => r.config.type === "email" && r.config.email === data.email,
              );
              if (matchedRecipient) {
                NotificationsService.putRecipientsSubscriptions(matchedRecipient.id, { severities })
                  .then(() => {
                    toast.success("Email recipient added successfully");
                    reset({ email: "", critical: false, warning: false });
                    refreshRecipients();
                  })
                  .catch((error) => {
                    toast.error(getErrorMsg(error, "Failed to add email recipient"));
                  })
                  .finally(() => {
                    setAddEmailLoading(false);
                    setShowAddEmail(false);
                  });
              } else {
                setShowAddEmail(false);
              }
            });
          }, 1000);
        })
        .catch(() => {
          setAddEmailLoading(false);
        });
    }
  };

  const updateRecipientsList = () => {
    setUpdateRecipientsLoading(true);
    const updateList = diffCheckboxs(preCheckboxsRef.current, checkboxStates);
    Promise.all([
      updateList.map((recipient) =>
        NotificationsService.putRecipientsSubscriptions(recipient.id, { severities: recipient.severities }),
      ),
    ])
      .then((res) => {
        toast.success("Email recipients updated successfully");
      })
      .catch((err) => {
        toast.success(getErrorMsg(err, "Failed to update email recipients"));
      })
      .finally(() => {
        refreshRecipients();
        setUpdateRecipientsLoading(false);
      });
  };

  const onDeleteRecipient = (email: string, recipientId: string) => {
    Modal.delete({
      deleteObj: {
        name: "email recipient",
        value: email,
        warning: "",
        deleteLabel: "Email recipient",
        inputLabel: "Email address",
      },
      handleDelete: () => handleDeleteRecipient(recipientId),
    });
  };

  const handleDeleteRecipient = (recipientId: string) => {
    return NotificationsService.deleteRecipients(recipientId).then(() => {
      toast.success("Email recipient deleted successfully");
      refreshRecipients();
    });
  };

  const handleConfirmTestNotification = () => {
    setIsLoading(true);
    Promise.all(
      selectedEmails.map((recipientId) => {
        return NotificationsService.postRecipientsTest(recipientId);
      }),
    )
      .then(() => {
        setNotificationSent(true);
        setTimeout(() => {
          setNotificationSent(false);
        }, 5000);
        setSelectedEmails([]);
      })
      .catch((error) => {
        toast.error(getErrorMsg(error, "Failed to send test notifications"));
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  return (
    <>
      <div className="mb-4">
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="rounded-b border-x border-b px-5 pt-5">
            <div className="mb-4 flex w-full items-center text-sm">
              Configure email destinations by critical levels:
              <Button
                onClick={() => {
                  setShowAddEmail(true);
                }}
                sx={{ marginLeft: "20px" }}
                variant="contained"
                size="small">
                <FontAwesomeIcon className="mr-2" icon={faPlus} />
                Add new email
              </Button>
              <Button
                disabled={recipients?.length === 0}
                onClick={() => setShowTestNotification(true)}
                sx={{
                  marginLeft: "auto",
                  marginRight: "20px",
                  borderColor: "#E6E6E6",
                  "&:hover": { borderColor: "#E6E6E6" },
                  backgroundColor: "#FAFAFA",
                }}
                variant="outlined"
                size="small">
                <FontAwesomeIcon className="mr-2" icon={faEnvelope} />
                Test notification
              </Button>
              <Button onClick={() => setShowAlertTypes(true)} variant="text" size="small">
                <FontAwesomeIcon className="mr-1" icon={faBook} />
                <span className="underline">Alert types</span>
              </Button>
            </div>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow
                    className="rounded-t bg-neutral50 text-sm"
                    sx={{
                      "& .MuiTableCell-root": {
                        paddingX: "15px",
                        paddingY: "8px",
                        border: "none",
                        color: "#9DA1A6",
                        fontWeight: "400",
                      },
                    }}>
                    <TableCell>Email address</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-[50px]">
                        <div>
                          <FontAwesomeIcon className="mr-2 text-error700" icon={faTriangleExclamation} />
                          Critical
                        </div>
                        <div>
                          <FontAwesomeIcon className="mr-2 text-warning500" icon={faCircleExclamation} />
                          Warning
                        </div>
                      </div>
                    </TableCell>
                    <TableCell></TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {showAddEmail && (
                    <TableRow
                      sx={{
                        "& .MuiTableCell-root:not(:first-child)": {
                          paddingX: "30px",
                        },
                        "& .MuiTableCell-root": {
                          paddingY: "10px",
                          borderTop: "1px solid #FAFAFA",
                          borderBottom: "none",
                        },
                      }}
                      className={classNames(
                        styles["email-item"],
                        "text-sm hover:bg-aquablue50",
                        focusInput && "bg-aquablue50",
                      )}>
                      <TableCell>
                        <TextField
                          {...register("email")}
                          placeholder="Type in a email"
                          autoFocus
                          onFocus={() => setFocusInput(true)}
                          onBlur={() => setFocusInput(false)}
                          autoComplete="off"
                          sx={{
                            ".MuiInputBase-root": {
                              height: "30px",
                              fontSize: "14px",
                              position: "relative",
                            },
                            "& .MuiFormHelperText-root": {
                              position: "absolute",
                              bottom: "-16px",
                            },
                          }}
                          fullWidth
                          size="small"
                          error={errors.email?.message ? true : false}
                          helperText={errors.email?.message}
                        />
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center ">
                          <div className="w-24 pl-2">
                            <Checkbox {...register("critical")} size="small" />
                          </div>
                          <div className="w-24 pl-8">
                            <Checkbox {...register("warning")} size="small" />
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="w-3">
                          <FontAwesomeIcon
                            icon={faTrashCan}
                            onClick={() => {
                              setShowAddEmail(false);
                              reset({ email: "" });
                            }}
                            className={classNames(styles["trash-icon"], "cursor-pointer text-sm text-error700")}
                          />
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                  {!loadingRecipients &&
                    recipients?.map((item, index) => {
                      return (
                        <TableRow
                          key={item.id}
                          sx={{
                            "& .MuiTableCell-root": {
                              paddingX: "30px",
                              paddingY: "8px",
                              borderTop: "1px solid #FAFAFA",
                              borderBottom: "none",
                            },
                          }}
                          className={classNames(styles["email-item"], "text-sm hover:bg-aquablue50")}>
                          <TableCell>{item.email}</TableCell>
                          <TableCell>
                            <div className="flex items-center ">
                              <div className="w-24 pl-2">
                                <Checkbox
                                  checked={checkboxStates[item.id]?.critical || false}
                                  size="small"
                                  onChange={(e) => handleCheckboxChange(item.id, "critical", e.target.checked)}
                                />
                              </div>
                              <div className="w-24 pl-8">
                                <Checkbox
                                  checked={checkboxStates[item.id]?.warning || false}
                                  size="small"
                                  onChange={(e) => handleCheckboxChange(item.id, "warning", e.target.checked)}
                                />
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="w-3">
                              <FontAwesomeIcon
                                onClick={() => onDeleteRecipient(item.email, item.id)}
                                icon={faTrashCan}
                                className={classNames(styles["trash-icon"], "cursor-pointer text-sm text-error700")}
                              />
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                </TableBody>
              </Table>
            </TableContainer>
            {!loadingRecipients && recipients?.length === 0 && (
              <div className="flex h-[200px] w-full items-center pl-10 text-center text-sm font-medium">
                <EmptyStatusIcon />
                <p className="my-1">No emails found</p>
              </div>
            )}
            {loadingRecipients && (
              <div className="flex w-full items-center justify-center py-20">
                <CircularProgress />
              </div>
            )}
          </div>
          {(showAddEmail || isCheckboxsChange) && (
            <div className="flex items-center justify-end gap-2.5 pr-1 pt-5">
              <Button onClick={onClickCancel} size="small" variant="outlined">
                Cancel
              </Button>
              <LoadingButton
                onClick={() => {
                  if (isCheckboxsChange) {
                    updateRecipientsList();
                  }
                }}
                loading={addEmailLoading || updateRecipientsLoading}
                type="submit"
                size="small"
                variant="contained">
                Confirm
              </LoadingButton>
            </div>
          )}
        </form>
      </div>
      <Dialog open={showTestNotification}>
        <header className="flex items-center justify-between border-b border-neutral200 px-5 py-4 text-lg font-medium">
          Test notification
          <CloseIcon className="cursor-pointer" onClick={onClose} />
        </header>
        <div className="px-10 py-4">
          <p>Choose emails to receive a test notification and verify the communication channel is working.</p>
          <div className="mt-4 flex items-center border-t border-neutral200 px-2.5 py-2 font-medium">
            <Checkbox
              checked={selectedEmails.length > 0 && selectedEmails.length === recipients?.length}
              indeterminate={selectedEmails.length > 0 && selectedEmails.length !== recipients?.length}
              onClick={onSelectAll}
              size="small"
            />
            <p className="ml-8">Email</p>
          </div>
          {recipients?.map((item) => {
            return (
              <div key={item.email} className="flex items-center border-t border-neutral200 px-2.5 py-2">
                <Checkbox
                  checked={selectedEmails.includes(item.id)}
                  onChange={(e) => handleSelectEmail(item.id)}
                  size="small"
                />
                <p className="ml-8">{item.email}</p>
              </div>
            );
          })}
        </div>
        <footer className="flex justify-between border-t border-neutral200 px-[30px] py-5">
          <Button disabled={notificationSent} onClick={onClose} variant="outlined" size="small">
            Cancel
          </Button>
          {notificationSent ? (
            <Button onClick={onClose} variant="outlined" size="small" sx={{ color: "#2dc2a1", borderColor: "#2dc2a1" }}>
              <FontAwesomeIcon className="mr-2 text-turquoise500" icon={faCircleCheck} /> Notification has been sent
            </Button>
          ) : (
            <LoadingButton
              onClick={handleConfirmTestNotification}
              disabled={selectedEmails.length === 0}
              loading={isLoading}
              variant="contained"
              size="small">
              Send test notification
            </LoadingButton>
          )}
        </footer>
      </Dialog>
      <Dialog maxWidth="lg" open={showAlertTypes}>
        <div className="flex h-full flex-col overflow-hidden">
          <header className="flex w-full  items-center justify-between border-b bg-white px-5 py-2">
            <div className="text-lg font-medium">
              <FontAwesomeIcon className="mr-1" icon={faBook} />
              <span>Alert types</span>
            </div>
            <CloseIcon onClick={() => setShowAlertTypes(false)} />
          </header>
          <div className="flex-1 overflow-auto px-10 py-5">
            <AlertTypes />
          </div>
          <footer className="flex justify-end border-t px-5 py-2">
            <Button onClick={() => setShowAlertTypes(false)} size="small" variant="outlined">
              Close
            </Button>
          </footer>
        </div>
      </Dialog>
    </>
  );
};

