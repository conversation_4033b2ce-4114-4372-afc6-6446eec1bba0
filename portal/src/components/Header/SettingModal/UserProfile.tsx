"use client";

import { faEnvelope, faLinkSlash, faUser, faRightToBracket } from "@fortawesome/free-solid-svg-icons";
import { TextField, Button, InputAdornment, ClickAwayListener } from "@mui/material";
import { AuthType, PutUserRequestBody, UserService } from "@/api-user";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useForm, SubmitHandler } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import useUserInfo from "@/hooks/useUserInfo";
import { getErrorMsg } from "@/utils/func";
import { useMemo, useState } from "react";
import Modal from "@/components/Modal";
import { toast } from "react-toastify";
import auth from "@/utils/auth/Auth";
import { If } from "react-extras";
import * as yup from "yup";

type Inputs = {
  username: string;
};

const schema = yup.object().shape({
  username: yup
    .string()
    .max(50, "cannot exceed 50 characters")
    .required("Name is required")
    .matches(/^[^<>]*$/, "Username cannot contain < or > characters"),
});

interface UserProfileProps {}
const UserProfile: React.FC<UserProfileProps> = (props) => {
  const { userInfo, updateInfo } = useUserInfo();
  const authType = userInfo?.userAuthType;
  const [showSettings, setShowSettings] = useState(false);

  const onCancel = () => {
    reset({ username: "" });
    setShowSettings(false);
  };

  const {
    register,
    handleSubmit,
    getValues,
    formState: { errors },
    reset,
  } = useForm<Inputs>({
    mode: "all",
    resolver: yupResolver(schema),
  });

  const onSubmit: SubmitHandler<Inputs> = (data) => {
    const putUserBody: PutUserRequestBody = {
      username: getValues().username,
    };

    updateInfo(putUserBody)
      .then(() => {
        toast.success("Username updated.");
        onCancel();
      })
      .catch((err) => {
        toast.error(getErrorMsg(err, "Changing username failed. Please try again."));
      });
  };

  const userAuthType = useMemo(() => {
    if (authType) {
      return {
        [AuthType.GITHUB]: "Github",
        [AuthType.GOOGLE_OAUTH2]: "Google",
        [AuthType.LOCAL]: "Email",
        [AuthType.WINDOWSLIVE]: "Microsoft",
        [AuthType.SSO]: "SSO",
      }[authType];
    }
    return "";
  }, [authType]);

  const onConfirmUnlink = () => {
    UserService.putUserAuthType({
      newAuthType: AuthType.LOCAL,
    })
      .then(() => {
        toast.success("Unlink successfully!");
        setTimeout(() => {
          auth.logout();
        }, 200);
      })
      .catch((err) => {
        toast.error(getErrorMsg(err, "Failed to unlink"));
      });
  };

  const showUnlinkDialog = () => {
    Modal.info({
      title: `Unlink with ${userAuthType} login`,
      icon: <FontAwesomeIcon className="mr-4" icon={faLinkSlash} />,
      content: (
        <div className="text-sm text-[#081F29]">
          <p>{`Once unlink with the ${userAuthType} login, you will not be able to login RisingWave Cloud via the 3rd party method with your ${userAuthType} account:`}</p>
          <strong>{userInfo?.userEmail}</strong>
          <p className="mt-3">
            A confirmation message has been sent to the above email. You can login RisingWave Cloud by following prompts
            for other methods.
          </p>
        </div>
      ),
      onConfirm: onConfirmUnlink,
    });
  };

  return (
    <div className="relative pt-2.5 text-sm text-[#16171A]">
      <If condition={!showSettings}>
        <p>
          <FontAwesomeIcon icon={faUser} /> <span className="ml-1">{userInfo?.userName}</span>{" "}
          <span
            onClick={() => {
              setShowSettings(true);
            }}
            className="ml-1 cursor-pointer text-primary underline">
            Change name
          </span>
        </p>
      </If>
      <If condition={showSettings}>
        <ClickAwayListener onClickAway={onCancel}>
          <form onSubmit={handleSubmit(onSubmit)}>
            <p className="mb-1 pl-1 text-sm">New name</p>
            <TextField
              fullWidth
              required
              {...register("username", {
                required: { value: true, message: "Required name" },
              })}
              error={errors.username?.message ? true : false}
              helperText={errors.username?.message}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <Button size="small" variant="outlined" type="submit">
                      Save
                    </Button>
                  </InputAdornment>
                ),
              }}
            />
          </form>
        </ClickAwayListener>
      </If>

      <p className="mt-3">
        <FontAwesomeIcon icon={faEnvelope} /> <span className="ml-1">{userInfo?.userEmail}</span>
      </p>
      <p className="mt-3">
        <FontAwesomeIcon icon={faRightToBracket} /> <span className="ml-1">Login method: {userAuthType}</span>
        <If
          condition={
            authType === AuthType.GITHUB || authType === AuthType.GOOGLE_OAUTH2 || authType === AuthType.WINDOWSLIVE
          }>
          <span>
            <FontAwesomeIcon className="mx-2" icon={faLinkSlash} />
            <span className="cursor-pointer underline" onClick={showUnlinkDialog}>
              Unlink with {userAuthType} login
            </span>
          </span>
        </If>
      </p>
    </div>
  );
};
export default UserProfile;
