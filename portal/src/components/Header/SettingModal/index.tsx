"use client";

import { faB<PERSON>, faGear, faUser, faWrench } from "@fortawesome/free-solid-svg-icons";
import { passwordRules, passwordYup } from "@/components/auth/formYup";
import { <PERSON><PERSON>, <PERSON>alog, DialogTitle, Stack } from "@mui/material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import PasswordInput from "@/components/Input/PasswordInput";
import { SubmitHandler, useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { EmailRecipients } from "./EmailRecipients";
import { AuthType, UserService } from "@/api-user";
import CloseIcon from "@/components/Icon/Close";
import useUserInfo from "@/hooks/useUserInfo";
import RuleList from "@/components/RuleList";
import DeleteAccount from "./DeleteAccount";
import { getErrorMsg } from "@/utils/func";
import { classNames } from "react-extras";
import UserProfile from "./UserProfile";
import { toast } from "react-toastify";
import Auth from "@/utils/auth/Auth";
import { If } from "react-extras";
import { useState } from "react";
import * as yup from "yup";

const schema = yup.object().shape({
  currentPassword: yup.string().required("Current password is required"),
  password: passwordYup,
  confirmPassword: yup
    .string()
    .required()
    .oneOf([yup.ref("password")], "Passwords do not match"),
});

type Inputs = {
  currentPassword: string;
  confirmPassword: string;
  password: string;
};

export enum SettingsType {
  AccountSetting = "Account settings",
  AlertAndMessages = "Alert",
  // SystemPreference = "System preference",
}

const tabs = [
  {
    name: SettingsType.AccountSetting,
    icon: <FontAwesomeIcon icon={faUser} />,
  },
  {
    name: SettingsType.AlertAndMessages,
    icon: <FontAwesomeIcon icon={faBell} />,
  },
  // {
  //   name: SettingsType.SystemPreference,
  //   icon: <FontAwesomeIcon icon={faWrench} />,
  // },
];

interface SettingModalProps {
  open: boolean;
  onCancel: () => void;
  type: SettingsType;
}

const SettingModal: React.FC<SettingModalProps> = ({ open, onCancel, type }) => {
  const [hideRules, setHideRules] = useState(true);
  const [activeTab, setActiveTab] = useState(type);
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<Inputs>({
    mode: "all",
    resolver: yupResolver(schema),
  });

  const watchPassword = watch("password");
  const watchCurPw = watch("currentPassword");
  const watchConfirmPw = watch("confirmPassword");

  const { userInfo } = useUserInfo();

  const handleCancel = (event: object, reason: string) => {
    if (reason === "backdropClick") return;
    onCancel?.();
  };

  const handleResetPassword: SubmitHandler<Inputs> = (data) => {
    UserService.putUserPassword({
      currentPassword: data.currentPassword,
      newPassword: data.password,
      newPasswordConfirm: data.confirmPassword,
    })
      .then((res) => {
        toast.success("Password changed successfully!");
        Auth.logout();
      })
      .catch((err) => {
        toast.error(getErrorMsg(err, "Failed to delete"));
      });
  };

  return (
    <Dialog
      open={open}
      onClose={handleCancel}
      hideBackdrop
      fullScreen
      sx={{ borderRadius: 0, "&.MuiDialog-root": { top: "56px" } }}>
      <DialogTitle>
        <div className="relative px-6 pt-5">
          <div>
            <FontAwesomeIcon className="text-base font-black" icon={faGear} />
            <span className="ml-2 text-xl font-normal">Settings</span>
          </div>
          <CloseIcon onClick={onCancel} className="absolute right-4 top-1 cursor-pointer text-base" />
        </div>
      </DialogTitle>
      <div className="px-10">
        <div className="rounded pt-3">
          <div className="rounded-t border-x border-t px-5 pt-3">
            <div className="flex w-full border-b py-4">
              {tabs.map((item, index) => {
                return (
                  <div
                    onClick={() => {
                      setActiveTab(item.name);
                    }}
                    className={`cursor-pointer px-8 text-sm ${item.name === activeTab ? "" : "text-primary"} ${index < tabs.length - 1 ? "border-r border-neutral200" : ""}`}
                    key={item.name}>
                    {item.icon}
                    <span className="ml-2">{item.name}</span>
                  </div>
                );
              })}
            </div>
          </div>
          <div className="">
            <If condition={activeTab === SettingsType.AccountSetting}>
              <div className="grid grid-cols-3 gap-5 border-x px-5 pt-5">
                <div className="rounded border bg-[rgba(250,250,250,0.5)] px-8 py-5">
                  <div className="mb-6 text-xl">Profile details</div>
                  <UserProfile />
                </div>
                <If condition={userInfo?.userAuthType === AuthType.LOCAL}>
                  <div className="rounded border bg-[rgba(250,250,250,0.5)] px-8 py-5">
                    <div className="mb-6 text-xl">Change password</div>
                    <form onSubmit={handleSubmit(handleResetPassword)}>
                      <Stack direction="column" justifyContent="center" alignItems="flex-end" spacing={"10px"}>
                        <p className="w-full pl-1 text-sm">Current password</p>
                        <PasswordInput
                          fullWidth
                          {...register("currentPassword", {
                            required: { value: true, message: "Required current password" },
                          })}
                          error={errors.currentPassword?.message ? true : false}
                          helperText={errors.currentPassword?.message}
                        />
                        <div className="relative w-full">
                          <p className="w-full pl-1 text-sm">New password</p>
                          <PasswordInput
                            fullWidth
                            {...register("password", {
                              required: { value: true, message: "Required new password" },
                            })}
                            error={errors.password?.message ? true : false}
                            helperText={errors.password?.message}
                            onBlur={() => {
                              setHideRules(true);
                            }}
                            onFocus={() => {
                              setHideRules(false);
                            }}
                          />
                        </div>
                        <p className="w-full pl-1 text-sm">Confirm password</p>
                        <PasswordInput
                          fullWidth
                          {...register("confirmPassword", {
                            required: { value: true, message: "Required confirm password" },
                          })}
                          error={errors.confirmPassword?.message ? true : false}
                          helperText={errors.confirmPassword?.message}
                        />
                      </Stack>
                      {!!watchPassword && !!watchCurPw && !!watchConfirmPw && (
                        <div className="foot flex items-center justify-end pt-4">
                          <Button variant="outlined" onClick={onCancel}>
                            Cancel
                          </Button>
                          <Button sx={{ ml: "20px" }} variant="contained" type="submit">
                            Confirm
                          </Button>
                        </div>
                      )}
                    </form>
                  </div>
                </If>

                <div className="rounded border bg-[rgba(250,250,250,0.5)] px-8 py-5">
                  <div className="mb-6 text-xl">Delete account</div>
                  <DeleteAccount onCancel={onCancel} />
                </div>
              </div>
              <div className="h-5 rounded-b border-x border-b px-5"></div>
            </If>
            <If condition={activeTab === SettingsType.AlertAndMessages}>
              <EmailRecipients />
            </If>
          </div>
        </div>
      </div>
    </Dialog>
  );
};
export default SettingModal;

