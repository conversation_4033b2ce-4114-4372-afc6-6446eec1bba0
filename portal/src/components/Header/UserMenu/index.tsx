"use client";
import { faCircleUser, faCoins, faRightFromBracket, faSuitcase } from "@fortawesome/free-solid-svg-icons";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import SettingModal, { SettingsType } from "../SettingModal";
import { Menu, MenuItem } from "@mui/material";
import useUserInfo from "@/hooks/useUserInfo";
import EditUserModal from "./EditUserModal";
import { useEffect, useState } from "react";
import { Domains } from "@/config/router";
import Auth from "@/utils/auth/Auth";
import Link from "next/link";

interface UserMenuProps {}
const UserMenu: React.FC<UserMenuProps> = ({}) => {
  const { userInfo } = useUserInfo();
  const [userEmail, setUserEmail] = useState("");
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showSettingModal, setShowSettingModal] = useState(false);
  const handleClick = (event: React.MouseEvent<HTMLDivElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleCancel = () => {
    setShowEditModal(false);
  };

  useEffect(() => {
    setUserEmail(userInfo?.userEmail || "");
  }, [userInfo?.userEmail]);

  return (
    <>
      <div>
        <div className="flex cursor-pointer items-center text-sm" onClick={handleClick}>
          <FontAwesomeIcon className="ml-2 mr-2 text-base" icon={faCircleUser} />
          <span>{userEmail}</span>
          <ArrowDropDownIcon sx={{ transform: `rotateZ(${open ? "180deg" : "0deg"})` }} fontSize="small" />
        </div>

        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          MenuListProps={{ disablePadding: true }}
          anchorOrigin={{
            vertical: "bottom",
            horizontal: "right",
          }}
          transformOrigin={{
            vertical: "top",
            horizontal: "right",
          }}>
          <hr className="border-[#f2f3f4]" />
          <MenuItem
            sx={{ pt: "12px", pb: "8px", pl: "24px", pr: "28px" }}
            onClick={() => {
              setShowSettingModal(true);
              handleClose();
            }}>
            <p className="flex w-full items-center justify-start gap-2 text-sm text-[#16171A]">
              <FontAwesomeIcon icon={faCircleUser} />
              <span>Account settings</span>
            </p>
          </MenuItem>
          <hr className="border-[#f2f3f4]" />
          <MenuItem
            sx={{ py: "8px", pl: "24px", pr: "28px" }}
            onClick={() => {
              handleClose();
            }}>
            <Link href={Domains.organization}>
              <p className="flex w-full items-center justify-start gap-2 text-sm text-[#16171A]">
                <FontAwesomeIcon icon={faSuitcase} />
                <span>Organization</span>
              </p>
            </Link>
          </MenuItem>
          <hr className="border-[#f2f3f4]" />
          <MenuItem
            sx={{ py: "8px", pl: "24px", pr: "28px" }}
            onClick={() => {
              handleClose();
            }}>
            <Link href={Domains.billing}>
              <p className="flex w-full items-center justify-start gap-2 text-sm text-[#16171A]">
                <FontAwesomeIcon icon={faCoins} />
                <span> Plans & Billings </span>
              </p>
            </Link>
          </MenuItem>
          <hr className="border-[#f2f3f4]" />
          <MenuItem sx={{ py: "8px", pl: "24px", pr: "28px" }} onClick={() => Auth.logout()}>
            <p className="flex w-full items-center justify-start gap-2 text-sm text-[#EF4444]">
              Sign out
              <FontAwesomeIcon icon={faRightFromBracket} />
            </p>
          </MenuItem>
        </Menu>
      </div>
      <EditUserModal open={showEditModal} onCancel={handleCancel} />
      <SettingModal
        open={showSettingModal}
        onCancel={() => {
          setShowSettingModal(false);
        }}
        type={SettingsType.AccountSetting}
      />
    </>
  );
};

export default UserMenu;

