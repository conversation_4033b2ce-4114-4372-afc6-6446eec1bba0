import { faTriangleExclamation } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import NiceModal, { useModal } from "@ebay/nice-modal-react";
import { useForm, SubmitHandler } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import React, { useEffect, useState } from "react";
import { TextField } from "@mui/material";
import BasePopup from "./BasePopup";
import * as yup from "yup";

type Inputs = {
  value: string;
};

interface Delete {
  name: string;
  value: string;
  warning?: string;
  inputLabel: string;
  deleteLabel?: string;
}

export interface DeleteProps {
  deleteObj: Delete;
  onCancel?: () => void;
  handleDelete: () => Promise<void> | undefined;
}
export default NiceModal.create(({ deleteObj, handleDelete, onCancel }: DeleteProps) => {
  const modal = useModal();
  const [loading, setLoading] = useState(false);
  const schema = yup.object().shape({
    value: yup
      .string()
      .required(deleteObj.inputLabel + " is required")
      .oneOf([deleteObj.value || ""], deleteObj.inputLabel + " do not match"),
  });

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<Inputs>({
    mode: "all",
    resolver: yupResolver(schema),
  });

  const handleConfirmDelete: SubmitHandler<Inputs> = () => {
    setLoading(true);
    handleDelete()?.finally(() => {
      setLoading(false);
      modal.hide();
    });
  };
  const handleCancel = () => {
    onCancel?.();
    modal.hide();
  };

  useEffect(() => {
    reset({
      value: "",
    });
  }, [modal.visible, reset]);

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === "Enter") {
      handleSubmit(handleConfirmDelete)();
    }
  };

  return (
    <BasePopup
      title={"Delete " + deleteObj.name}
      confirmText="Delete"
      confirmType="error"
      loading={loading}
      open={modal.visible}
      onCancel={handleCancel}
      onConfirm={handleSubmit(handleConfirmDelete)}>
      <div className="text-base">
        {deleteObj?.warning && (
          <p className="rounded bg-[#FEF2F2] px-5 py-3 text-[#B91C1C]">
            <FontAwesomeIcon icon={faTriangleExclamation} />
            <span className="ml-2">{deleteObj.warning}</span>
          </p>
        )}
        <p className="mt-4 text-[#363A40]">
          {deleteObj?.deleteLabel ? (
            <span>{deleteObj?.deleteLabel} </span>
          ) : (
            <span className="capitalize">{deleteObj.name} </span>
          )}
          to be deleted:
        </p>
        <p className="mb-7 mt-1 pl-5 text-lg font-bold text-[#16171A]">{deleteObj.value}</p>
        <p className="my-0.5 text-[#363A40]">
          Enter the <span className="lowercase">{deleteObj.inputLabel}</span> to confirm the deletion
        </p>
        <div className="mb-5 mt-4 min-w-[355px]">
          <TextField
            fullWidth
            label={deleteObj.inputLabel}
            required
            {...register("value")}
            error={errors.value?.message ? true : false}
            helperText={errors.value?.message}
            onKeyDown={handleKeyDown}
          />
        </div>
      </div>
    </BasePopup>
  );
});

