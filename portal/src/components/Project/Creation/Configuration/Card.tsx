"use client";
import { faArrowUpRightFromSquare, faChart<PERSON>ie, faLock } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { ClickAwayListener, Tooltip } from "@mui/material";
import { SizeTooltipTitle } from "./SizeTooltipTitle";
import { useRouter } from "next/navigation";
import WhiteTooltip from "../WhiteTooltip";
import { Domains } from "@/config/router";
import { classNames } from "react-extras";
import { useState } from "react";

interface ConfigurationCardsProps {
  locked?: boolean;
  rwuNumber: number;
  active?: boolean;
  customTooltip?: React.ReactNode;
  options?: {
    computeRwu: number;
    metaRwu: number;
    compactorRwu: number;
    frontendRwu: number;
    metaStoreRwu?: number;
  };
}
const ConfigurationCards: React.FC<ConfigurationCardsProps> = ({
  rwuNumber,
  locked,
  active,
  options,
  customTooltip,
}) => {
  const [showUnlockTooltip, setShowUnlockTooltip] = useState(false);
  const router = useRouter();

  const handleSelect = () => {
    if (locked) {
      setShowUnlockTooltip(true);
      return;
    }
  };

  const handleTooltipClose = () => {
    setShowUnlockTooltip(false);
  };

  const handleTooltipOpen = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowUnlockTooltip(true);
  };

  const goToBilling = () => {
    router.replace(Domains.plans);
  };

  return (
    <ClickAwayListener onClickAway={handleTooltipClose}>
      <div
        className={classNames(
          "relative h-[84px] w-full rounded-2xl border-[2px] pb-3 pt-4 text-center transition-all duration-300 ease-in-out",
          active ? "border-[#018D99] bg-white shadow-lg" : "border-neutral200 bg-white shadow-sm",
          !active && !locked ? "hover:border-neutral300 hover:shadow-md" : "",
          locked ? "cursor-not-allowed text-neutral300" : "cursor-pointer text-neutral900",
        )}
        onClick={handleSelect}>
        {active && options && (
          <WhiteTooltip
            open={showUnlockTooltip}
            handleClose={handleTooltipClose}
            content={
              <div className="flex flex-col gap-1 text-start text-sm font-normal text-neutral900">
                <p className="border-b border-neutral200 pb-1">Configuration breakdown:</p>
                {!!options.computeRwu && (
                  <div className="flex justify-between">
                    Compute Node: <span>{options.computeRwu} RWUs</span>
                  </div>
                )}
                {!!options.metaRwu && (
                  <div className="flex justify-between">
                    Meta Node: <span>{options.metaRwu} RWUs</span>
                  </div>
                )}
                {!!options.compactorRwu && (
                  <div className="flex justify-between">
                    Compactor: <span>{options.compactorRwu} RWUs</span>
                  </div>
                )}
                {!!options.frontendRwu && (
                  <div className="flex justify-between">
                    Frontend Node: <span>{options.frontendRwu} RWUs</span>
                  </div>
                )}
                {!!options.metaStoreRwu && (
                  <div className="flex justify-between">
                    MetaStore: <span>{options.metaStoreRwu} RWUs</span>
                  </div>
                )}
              </div>
            }
            padding="8px 12px"
            width="208px"
            placement="right-start">
            <div
              className="absolute right-2 top-2 rounded-2xl border-[#48DCBC] bg-[#BAFFF0] px-2.5 py-[1px] text-xs text-[#138F74]"
              onClick={handleTooltipOpen}>
              <FontAwesomeIcon icon={faChartPie} />
            </div>
          </WhiteTooltip>
        )}
        <p className="text-[32px] font-semibold leading-none">{rwuNumber}</p>
        <p>RWUs</p>
        {locked && (
          <WhiteTooltip
            open={showUnlockTooltip}
            handleClose={handleTooltipClose}
            content={
              customTooltip || (
                <div className="text-start font-normal text-neutral900">
                  <span className="mr-2">
                    Go to{" "}
                    <span className="cursor-pointer underline" onClick={goToBilling}>
                      Billing
                    </span>{" "}
                    <FontAwesomeIcon className="cursor-pointer" onClick={goToBilling} icon={faArrowUpRightFromSquare} />{" "}
                    to add a payment method and unlock more compute size options.
                  </span>
                </div>
              )
            }>
            <FontAwesomeIcon className="absolute right-2 top-2 text-neutral900" icon={faLock} />
          </WhiteTooltip>
        )}
      </div>
    </ClickAwayListener>
  );
};
export default ConfigurationCards;

