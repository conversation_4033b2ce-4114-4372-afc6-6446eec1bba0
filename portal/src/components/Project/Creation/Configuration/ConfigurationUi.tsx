import {
  DistributionMark,
  distributionMarks,
  standaloneMarks,
  standaloneMarksWithoutPg,
  standardProductionMarks,
  trialMarks,
} from "../const";
import { faMicrochip, faArrowUpRightFromSquare, faSortDown, faPenToSquare } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import CustomConfiguration from "./CustomConfiguration";
import { ClickAwayListener } from "@mui/material";
import { classNames, If } from "react-extras";
import { CustomizedOption } from "../Store";
import WhiteTooltip from "../WhiteTooltip";
import ConfigurationCards from "./Card";
import { useState } from "react";

interface ConfigurationUiProps {
  regionName: string;
  isLockedAll?: boolean;
  isCustom?: boolean;
  rwuNumber?: number;
  isAdvanced?: boolean;
  isFree?: boolean;
  distribution?: DistributionMark;
  metaStore?: number;
  autoCompacitionRwu?: number;
  initialConfig?: CustomizedOption;
  disabledAutoCompacition?: boolean;
  customLockedTooltip?: React.ReactNode;
  handleChooseRwuNumber: (rwuNumber: number) => void;
  handleChooseDistribution: (distribution: DistributionMark) => void;
  onCustomize: (config: DistributionMark, autoCompaction?: boolean) => void;
}
const ConfigurationUi: React.FC<ConfigurationUiProps> = ({
  regionName,
  isLockedAll,
  isCustom,
  rwuNumber,
  distribution,
  metaStore = 0,
  isAdvanced = false,
  isFree = false,
  autoCompacitionRwu,
  initialConfig,
  disabledAutoCompacition,
  customLockedTooltip,
  onCustomize,
  handleChooseRwuNumber,
  handleChooseDistribution,
}) => {
  const [showRwuTooltip, setShowRwuTooltip] = useState(false);
  const [openConfigDialog, setOpenConfigDialog] = useState(false);
  const handleTooltipClose = () => {
    if (!showRwuTooltip) return;
    setShowRwuTooltip(false);
  };
  const handleTooltipOpen = () => {
    setShowRwuTooltip(true);
  };

  const handleOpenConfigDialog = () => {
    setOpenConfigDialog(true);
  };

  const handleCloseConfigDialog = () => {
    setOpenConfigDialog(false);
  };

  const handleCloseConfirmCustomize = (config: DistributionMark, autoCompaction?: boolean) => {
    setOpenConfigDialog(false);
    onCustomize(config, autoCompaction || false);
  };

  return (
    <>
      <div>
        <div className="relative">
          <div className="mr-2.5 inline-block h-6 w-6 rounded-full bg-aquablue text-center text-sm leading-6">
            <FontAwesomeIcon className="text-white" icon={faMicrochip} />
          </div>
          <ClickAwayListener onClickAway={handleTooltipClose}>
            <span>
              Select compute size in{" "}
              <WhiteTooltip
                open={showRwuTooltip}
                width="260px"
                handleClose={handleTooltipClose}
                content={
                  <div className="text-start font-normal text-neutral900">
                    <span>1 RWU = 1 vCPU, 4 GB RAM</span>
                    <p className="mt-3">
                      <a
                        href="https://docs.risingwave.com/cloud/pricing/#risingwave-unit-rwu"
                        target="_blank"
                        rel="noreferrer"
                        className="text-neutral900 underline">
                        View documentation of RWU
                        <FontAwesomeIcon className="ml-2" icon={faArrowUpRightFromSquare} />
                      </a>
                    </p>
                  </div>
                }>
                <span className="cursor-pointer text-primary underline underline-offset-1" onClick={handleTooltipOpen}>
                  RWU
                </span>
              </WhiteTooltip>{" "}
            </span>
          </ClickAwayListener>
          <span className="absolute right-0">
            <a
              href="https://risingwave.com/pricing/"
              target="_blank"
              rel="noreferrer"
              className="font-normal text-neutral500 underline">
              View pricing plans
              <FontAwesomeIcon className="ml-2" icon={faArrowUpRightFromSquare} />
            </a>
          </span>
        </div>
        <div className="mt-5">
          <div className="">
            {!isAdvanced && (
              <div className="mx-auto mb-4 rounded-2xl border border-neutral200 bg-neutral50 pb-4 pt-3 text-sm">
                <h3 className="mb-4 text-base font-medium">Development</h3>
                <div className="flex items-center justify-center gap-3">
                  <If condition={!isAdvanced}>
                    {trialMarks.map((item, index) => {
                      return (
                        <div
                          key={index}
                          className="w-[194px]"
                          onClick={() => {
                            if (isLockedAll) return;
                            handleChooseRwuNumber(item);
                          }}>
                          <ConfigurationCards
                            locked={isLockedAll}
                            active={!isCustom && rwuNumber === item}
                            rwuNumber={item}
                            customTooltip={customLockedTooltip}
                          />
                        </div>
                      );
                    })}

                    {standaloneMarksWithoutPg.map((item, index) => {
                      return (
                        <div
                          key={index}
                          className="w-[194px]"
                          onClick={() => {
                            if (isLockedAll) return;
                            handleChooseRwuNumber(item);
                          }}>
                          <ConfigurationCards
                            active={!isCustom && rwuNumber === item}
                            locked={isLockedAll || isFree}
                            rwuNumber={item}
                            customTooltip={customLockedTooltip}
                          />
                        </div>
                      );
                    })}
                  </If>

                  <If condition={isAdvanced}>
                    {standaloneMarks.map((item, index) => {
                      return (
                        <div
                          key={index}
                          className="w-[194px]"
                          onClick={() => {
                            if (isLockedAll) return;
                            handleChooseRwuNumber(item + metaStore);
                          }}>
                          <ConfigurationCards
                            active={!isCustom && rwuNumber === item + metaStore}
                            locked={isLockedAll || isFree}
                            rwuNumber={item + metaStore}
                            customTooltip={customLockedTooltip}
                          />
                        </div>
                      );
                    })}
                  </If>
                </div>
              </div>
            )}
            <div className="mx-auto mb-4 rounded-2xl border border-neutral200 bg-neutral50 pb-4 pt-3 text-sm">
              <h3 className="mb-4 text-base font-medium">
                Production{" "}
                <span className="ml-1 inline-flex items-center text-primary">
                  (<span className="font-semibold">Recommended</span>)
                </span>
              </h3>
              <div className="grid grid-cols-4 place-items-center gap-3 px-6">
                {!isAdvanced &&
                  standardProductionMarks.map((item, index) => {
                    return (
                      <div
                        key={index}
                        className="w-[194px]"
                        onClick={() => {
                          if (isLockedAll) return;
                          handleChooseRwuNumber(item);
                        }}>
                        <ConfigurationCards
                          locked={isLockedAll || isFree}
                          active={rwuNumber === item}
                          rwuNumber={item}
                          customTooltip={customLockedTooltip}
                        />
                      </div>
                    );
                  })}
                {isAdvanced &&
                  distributionMarks.map((item, index) => {
                    const _rwuNumber = !isAdvanced ? item.rwu : item.rwu + metaStore;

                    return (
                      <div
                        key={index}
                        className="w-[194px]"
                        onClick={() => {
                          if (isLockedAll) return;
                          handleChooseDistribution(item);
                        }}>
                        <ConfigurationCards
                          active={!isCustom && rwuNumber === _rwuNumber}
                          locked={isLockedAll || isFree}
                          customTooltip={customLockedTooltip}
                          rwuNumber={_rwuNumber}
                          options={{
                            computeRwu: item.compute,
                            metaRwu: item.meta,
                            compactorRwu: item.compactor,
                            frontendRwu: item.frontend,
                            metaStoreRwu: metaStore,
                          }}
                        />
                      </div>
                    );
                  })}
              </div>

              {isAdvanced && (
                <div className="relative mx-6 mt-5 border-t border-neutral200 pt-5">
                  <p className="text-left text-sm">
                    Default options are recommended, advanced configurations are available for specific needs.
                  </p>

                  <div
                    className={classNames(
                      "relative mt-1 w-[194px] cursor-pointer rounded-2xl border-[2px] px-5 pb-3 pt-4 text-center",
                      isCustom
                        ? "border-[#018D99] bg-white shadow-lg"
                        : "border-neutral200 bg-white shadow-sm hover:border-neutral300 hover:shadow-md",
                    )}
                    onClick={handleOpenConfigDialog}>
                    {isCustom ? (
                      <p className="text-[32px] font-bold">{distribution?.rwu}</p>
                    ) : (
                      <p className="text-lg font-semibold">Custom</p>
                    )}
                    <p className="mt-1 text-sm">RWUs</p>
                    <FontAwesomeIcon className="absolute left-3 top-2 text-[#018D99]" icon={faPenToSquare} />
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <CustomConfiguration
        regionName={regionName}
        openConfigDialog={openConfigDialog}
        handleCloseConfigDialog={handleCloseConfigDialog}
        handleConfirmCustomize={handleCloseConfirmCustomize}
        isInvited={isAdvanced}
        metaStore={metaStore}
        disableRecommendation={isCustom}
        autoCompacitionRwu={autoCompacitionRwu}
        initialConfig={initialConfig}
        disabledAutoCompacition={disabledAutoCompacition}
      />
    </>
  );
};

export default ConfigurationUi;

