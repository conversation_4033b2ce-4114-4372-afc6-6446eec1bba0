"use client";
import { distributionAtom, regionChoosedAtom, rwuNumberAtom, isAutoCompactionOnAtom } from "../Store";
import { faCircleArrowDown } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { DistributionMark, distributionMarks } from "../const";
import { useAtom, useAtomValue, useSetAtom } from "jotai";
import { Button, Tooltip } from "@mui/material";

import { SizeTooltipTitle } from "./SizeTooltipTitle";
import { useEffect, useMemo, useState } from "react";
import Envprepare from "../Invitation/Envprepare";
import { useOrgInfo } from "@/hooks/useOrgInfo";
import ConfigurationUi from "./ConfigurationUi";
import Summary from "../Summary/index";
import { useTier } from "../useTier";
import { TierId } from "@/api-user";
import { If } from "react-extras";
import Estimate from "./Estimate";

interface ConfigurationProps {}
const Configuration: React.FC<ConfigurationProps> = (props) => {
  const [active, setActive] = useState(false);
  const [isCustom, setIsCustom] = useState(false);
  const [rwuNumber, setRwuNumber] = useAtom(rwuNumberAtom);
  const [distribution, setDistribution] = useAtom(distributionAtom);
  const setIsAutoCompactionOn = useSetAtom(isAutoCompactionOnAtom);

  const region = useAtomValue(regionChoosedAtom);
  const tier = useTier();
  const { orgInfo } = useOrgInfo();

  const isStandard = tier === TierId.STANDARD;
  const isInvited = tier === TierId.INVITED;

  useEffect(() => {
    if (isInvited) {
      handleChooseDistribution(distributionMarks[0]);
    } else if (isStandard) {
      setRwuNumber(2);
    } else if (orgInfo?.trialDays && orgInfo.trialDays > 0) {
      setRwuNumber(2);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isInvited, isStandard, orgInfo?.trialDays, setRwuNumber]);

  const isLockedAll = !orgInfo || (!(orgInfo.trialDays > 0) && !isStandard && !isInvited);

  const handleChooseRwuNumber = (rwuNumber: number) => {
    if (isLockedAll || tier === TierId.DEVELOPER_FREE) return;
    setRwuNumber(rwuNumber);
    setIsCustom(false);
    setIsAutoCompactionOn(false);
  };

  const handleChooseDistribution = (config: DistributionMark) => {
    if (tier === TierId.DEVELOPER_FREE || isLockedAll) return;
    setRwuNumber(isInvited ? config.rwu + config.pg : config.rwu);
    setIsCustom(false);
    setDistribution(config);
    setIsAutoCompactionOn(false);
  };

  const handleCloseConfirmCustomize = (config: DistributionMark, autoCompaction?: boolean) => {
    setIsCustom(true);
    setDistribution(config);
    setRwuNumber(config.rwu);
    setIsAutoCompactionOn(autoCompaction || false);
  };

  const description = useMemo(() => {
    if (isCustom || !isInvited) return "";
    switch (rwuNumber) {
      case 2:
      case 4:
      case 8:
        return null;

      default:
        return (
          <span className="cursor-pointer">
            You&apos;ve selected{" "}
            <Tooltip
              title={
                <SizeTooltipTitle
                  computeRwu={distribution?.compute}
                  metaRwu={(distribution?.meta || 0) + (distribution?.pg || 0)}
                  compactorRwu={distribution?.compactor}
                  frontendRwu={distribution?.frontend}
                />
              }
              arrow>
              <span className="underline">{rwuNumber} RWUs</span>
            </Tooltip>{" "}
            for configuration.
          </span>
        );
    }
  }, [
    distribution?.compactor,
    distribution?.compute,
    distribution?.frontend,
    distribution?.meta,
    distribution?.pg,
    isCustom,
    isInvited,
    rwuNumber,
  ]);

  if (!orgInfo || !region) return null;

  return (
    <div className="relative w-full text-center">
      <If condition={!active}>
        <Button variant="contained" sx={{ px: "20px" }} onClick={() => setActive(true)}>
          Configuration
          <FontAwesomeIcon className="ml-2" icon={faCircleArrowDown} />
        </Button>
      </If>
      <If condition={active}>
        <Envprepare />
        <hr className="my-4 w-full border-neutral200" />
        <ConfigurationUi
          regionName={region?.regionName}
          isLockedAll={isLockedAll}
          isCustom={isCustom}
          rwuNumber={rwuNumber}
          isAdvanced={isInvited}
          distribution={distribution}
          handleChooseRwuNumber={handleChooseRwuNumber}
          handleChooseDistribution={handleChooseDistribution}
          isFree={tier === TierId.DEVELOPER_FREE}
          onCustomize={handleCloseConfirmCustomize}
          metaStore={isInvited ? 2 : 0}
        />

        {!isLockedAll && (
          <>
            <div className="mt-5 border-t border-neutral200 pt-5">
              <Estimate rwuNumber={rwuNumber} />
            </div>
            <p className="mt-2 text-left">{description}</p>

            <div className="mt-5 border-t border-neutral200 pt-5">
              <Summary showSummary={!isLockedAll} />
            </div>
          </>
        )}
      </If>
    </div>
  );
};
export default Configuration;

