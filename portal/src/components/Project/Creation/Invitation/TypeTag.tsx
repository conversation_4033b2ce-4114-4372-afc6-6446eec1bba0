"use client";
import { faArrowUpRightFromSquare, faGear, faLock } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { InvitedType, InvitedTypeAtom } from "../Store";
import { ClickAwayListener } from "@mui/material";
import { useOrgInfo } from "@/hooks/useOrgInfo";
import { useRouter } from "next/navigation";
import WhiteTooltip from "../WhiteTooltip";
import { Domains } from "@/config/router";
import { classNames } from "react-extras";
import { useState } from "react";
import { useAtom } from "jotai";

interface TypeTagProps {}
const TypeTag: React.FC<TypeTagProps> = (props) => {
  const { orgInfo } = useOrgInfo();
  const [inviteType, setInviteType] = useAtom(InvitedTypeAtom);
  const [showUnlockTooltip, setShowUnlockTooltip] = useState(false);
  const router = useRouter();

  const isAdvancedUser = !!orgInfo?.hasInvitationCode;

  const handleChooseType = (type: InvitedType) => {
    if (!isAdvancedUser) {
      return;
    }
    setInviteType(type);
  };

  const goToBilling = () => {
    router.replace(Domains.plans);
  };

  const handleTooltipClose = () => {
    setShowUnlockTooltip(false);
  };

  return (
    <>
      <hr className="mx-auto my-4 border-neutral200" />
      <div className="mb-6 text-center text-neutral900">
        <div className="mr-2.5 inline-block h-6 w-6 rounded-full bg-aquablue text-center text-xs leading-6">
          <FontAwesomeIcon className="text-white" icon={faGear} />
        </div>
        <span>Select a deployment mode</span>
      </div>
      <div className="mb-5 flex items-stretch justify-center gap-3 text-center text-neutral900">
        <div
          className={classNames(
            "w-[297px] cursor-pointer rounded-2xl border-[2px] bg-neutral50 px-5 pb-6 pt-5",
            inviteType === InvitedType.HOSTED ? "border-aquablue shadow-xl" : "border-neutral200",
          )}
          onClick={() => handleChooseType(InvitedType.HOSTED)}>
          <strong className="text-2xl leading-normal">Hosted</strong>
          <p className="mt-2 text-left text-sm">Focus on the development with no underlying infrastructure issues.</p>
        </div>

        <div className="relative">
          <div
            className={classNames(
              "h-full w-[297px] rounded-2xl border-[2px] bg-neutral50 px-8 py-5",
              inviteType === InvitedType.BYOC ? "border-aquablue shadow-xl" : "border-neutral200",
              isAdvancedUser ? "cursor-pointer" : "cursor-not-allowed opacity-50",
            )}
            onClick={() => handleChooseType(InvitedType.BYOC)}>
            <strong className="text-2xl leading-normal">BYOC</strong>
            <p className="mt-2 text-left text-sm">Full control over the resources and networking configurations.</p>
          </div>
          {!isAdvancedUser && (
            <ClickAwayListener onClickAway={handleTooltipClose}>
              <div className="absolute right-5 top-3 cursor-pointer text-sm">
                <WhiteTooltip
                  open={showUnlockTooltip}
                  content={
                    <div className="text-start font-normal text-neutral900">
                      <span className="mr-2">
                        Go to{" "}
                        <span className="cursor-pointer underline" onClick={goToBilling}>
                          Billing
                        </span>{" "}
                        <FontAwesomeIcon
                          className="cursor-pointer"
                          onClick={goToBilling}
                          icon={faArrowUpRightFromSquare}
                        />{" "}
                        , upgrade to the Advanced plan and unlock this feature.
                      </span>
                    </div>
                  }>
                  <FontAwesomeIcon onClick={() => setShowUnlockTooltip(true)} icon={faLock} />
                </WhiteTooltip>
              </div>
            </ClickAwayListener>
          )}
        </div>
      </div>
    </>
  );
};
export default TypeTag;

