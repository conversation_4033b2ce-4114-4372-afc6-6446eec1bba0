import { TextField, Button, ClickAwayListener } from "@mui/material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faFileCode } from "@fortawesome/free-solid-svg-icons";
import { SubmitHand<PERSON>, useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { projectNameAtom } from "./Store";
import { Choose } from "react-extras";
import { useState } from "react";
import { useAtom } from "jotai";
import * as yup from "yup";

export type Inputs = {
  tenantName: string;
};

const BasicSchema = {
  tenantName: yup
    .string()
    .required("Project name is a required field")
    .matches(
      /^[a-z0-9]([-a-z0-9]*[a-z0-9])?$/,
      "The project name must consist of lowercase alphanumeric characters or '-', and must start and end with an alphanumeric character.",
    )
    .max(25, "Project name cannot exceed more than 25 characters"),
};

interface ProjectNameProps {}
const ProjectName: React.FC<ProjectNameProps> = (props) => {
  const [edit, setEdit] = useState(true);
  const [name, setName] = useAtom(projectNameAtom);

  const {
    register,
    handleSubmit,
    reset,
    getValues,
    formState: { errors },
  } = useForm<Inputs>({
    mode: "all",
    resolver: yupResolver(
      yup.lazy(() => {
        return yup.object().shape(BasicSchema);
      }),
    ),
  });

  const handleRename = () => {
    setEdit(true);
  };

  const exit = () => {
    const currentValue = getValues("tenantName");
    const schema = yup.object().shape(BasicSchema);

    let valid = schema.isValidSync({ tenantName: currentValue });
    if (valid) {
      setName(currentValue);
      setEdit(false);
    }
  };

  const onSubmit: SubmitHandler<Inputs> = (data) => {
    setName(data.tenantName);
    setEdit(false);
  };

  return (
    <Choose>
      <Choose.When condition={!edit}>
        <div className="flex items-center justify-center gap-2">
          <div className="h-6 w-6 rounded-full bg-aquablue text-center text-sm leading-6">
            <FontAwesomeIcon className="text-white" icon={faFileCode} />
          </div>
          {name}
          <span className="cursor-pointer text-aquablue underline underline-offset-2" onClick={handleRename}>
            Rename
          </span>
        </div>
      </Choose.When>
      <Choose.When condition={edit}>
        <ClickAwayListener mouseEvent="onMouseDown" onClickAway={exit}>
          <div className="relative mx-auto w-full max-w-[510px]">
            <form onSubmit={handleSubmit(onSubmit)}>
              <TextField
                autoFocus
                fullWidth
                label="Project name"
                defaultValue={name}
                sx={{
                  borderColor: "red",
                }}
                {...register("tenantName")}
                error={errors.tenantName?.message ? true : false}
                helperText={errors.tenantName?.message}
              />
              <div className="absolute right-2 top-2.5">
                <Button variant="outlined" type="submit" color={errors.tenantName?.message ? "error" : "primary"}>
                  Save
                </Button>
              </div>
            </form>
          </div>
        </ClickAwayListener>
      </Choose.When>
    </Choose>
  );
};
export default ProjectName;

