"use client";
import { Dialog, Button, Select, MenuItem, TextField } from "@mui/material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import NiceModal, { useModal } from "@ebay/nice-modal-react";
import { faXmark } from "@fortawesome/free-solid-svg-icons";
import { sendNotificationToSlack } from "@/lib/slack";
import { PlatformType } from "@/hooks/useRegionList";
import useUserInfo from "@/hooks/useUserInfo";
import { usePostHog } from "posthog-js/react";
import { platformIcon } from "./RegionCard";
import { If } from "react-extras";
import { useState } from "react";

const platformMap = {
  [PlatformType.AWS]: "AWS",
  [PlatformType.AZURE]: "Azure",
  [PlatformType.GCP]: "GCP",
};

const regionList = {
  [PlatformType.AWS]: [
    "us-west-1 (N. California)",
    "eu-central-1 (Frankfurt)",
    "ap-northeast-1 (Tokyo)",
    "ap-south-1 (Mumbai)",
    "ap-southeast-2 (Sydney)",
    "ca-central-1 (Canada)",
    "others",
  ],
  [PlatformType.GCP]: [
    "us-central1 (Iowa)",
    "us-east4 (N. Virginia)",
    "us-west1 (Oregon)",
    "europe-west1 (Belgium)",
    "asia-southeast1 (Singapore)",
    "asia-east1 (Taiwan)",
    "australia-southeast1 (Sydney)",
    "southamerica-east1 (São Paulo)",
    "others",
  ],
  [PlatformType.AZURE]: [
    "eastus (Virginia)",
    "westeurope (Netherlands)",
    "southeastasia (Singapore)",
    "brazilsouth (São Paulo)",
    "northeurope (Ireland)",
    "eastasia (Hong Kong)",
    "westus2 (Washington)",
    "japaneast (Tokyo)",
    "others",
  ],
};

export interface RegionRequestModalProps {
  platform: PlatformType;
  onConfirm?: () => void;
  onCancel?: () => void;
}
export default NiceModal.create(
  ({
    platform,
    // title,
    // icon,
    // content,
    // confirmText = "Confirm",
    // cancelText = "Cancel",
    onConfirm,
    onCancel,
  }: RegionRequestModalProps) => {
    const modal = useModal();
    const [selectRegion, setSelectRegion] = useState<string>("");
    const [otherRegion, setOtherRegion] = useState("");
    const posthog = usePostHog();
    const { userInfo } = useUserInfo();

    const handleCancel = () => {
      setSelectRegion("");
      onCancel?.();
      modal.hide();
    };

    const handleConfirm = () => {
      if (!selectRegion || (selectRegion === "others" && !otherRegion)) return;
      const requestMsg = selectRegion === "others" ? `${otherRegion} - (others)` : selectRegion;

      posthog.capture("survey sent", {
        $survey_id: "0191c2b1-2e5b-0000-7f02-0abc3c0dc8cb",
        $survey_response: `${userInfo?.userEmail}: ${platform} -> ${requestMsg}`,
      });

      sendNotificationToSlack(`Region request: ${userInfo?.userEmail}: ${platform} -> ${requestMsg}`);

      onConfirm?.();
      modal.hide();
      setSelectRegion("");
      setOtherRegion("");
    };

    return (
      <Dialog open={modal.visible} sx={{ overflow: "hidden" }} onClose={handleCancel}>
        <div className="bg-neutral50">
          <header className="flex w-full justify-between border-b px-5 py-4 pb-4 text-base">
            <h1 className="text-lg font-medium">Request a new region</h1>
            <FontAwesomeIcon icon={faXmark} className="cursor-pointer" onClick={handleCancel} />
          </header>
          <div className="mx-auto my-6 flex h-[100px] w-[100px] items-center justify-center rounded-2xl border border-neutral200 bg-white">
            {platformIcon[platform]}
          </div>
          <div>
            <div className="mt-8 px-8 text-base">
              Please select the {platformMap[platform]} region you need from the list. We’ll review your request and
              provision access based on demand:
            </div>
            <div className="mb-4 mt-2 px-[30px]">
              <Select
                sx={{ textAlign: "start" }}
                fullWidth
                size="small"
                displayEmpty
                renderValue={(value: any) => {
                  if (!selectRegion) {
                    return <p className="text-sm leading-7">Select a region</p>;
                  }
                  return selectRegion;
                }}>
                {regionList?.[platform]?.map?.((region, index) => {
                  return (
                    <MenuItem
                      key={index}
                      value={region}
                      onClick={() => {
                        setSelectRegion(region);
                      }}>
                      {region}
                    </MenuItem>
                  );
                })}
              </Select>
              {selectRegion === "others" && (
                <TextField
                  value={otherRegion}
                  onChange={(e) => setOtherRegion(e.target.value)}
                  placeholder="Please input your region"
                  sx={{ marginTop: "16px" }}
                  size="small"
                  fullWidth
                />
              )}
            </div>
            <footer className="flex justify-end gap-2.5 border-t py-4 pr-5">
              <Button variant="outlined" sx={{ borderRadius: "4px", width: "120px" }} onClick={handleCancel}>
                Cancel
              </Button>
              <If condition={!!onConfirm}>
                <Button variant="contained" sx={{ height: "36px", width: "155px" }} onClick={handleConfirm}>
                  Send request
                </Button>
              </If>
            </footer>
          </div>
        </div>
      </Dialog>
    );
  },
);

