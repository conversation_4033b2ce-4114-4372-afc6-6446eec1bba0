import { faCircleCheck, faCircleInfo } from "@fortawesome/free-solid-svg-icons";
import { Button, MenuItem, Select, SelectChangeEvent } from "@mui/material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import RegionRequestModal from "./RegionRequestModal";
import { PlatformType } from "@/hooks/useRegionList";
import NiceModal from "@ebay/nice-modal-react";
import { DisplayRegion } from "../Store";
import { Region } from "@/api-user";
import { useState } from "react";

interface RegionSelectProps {
  regionList: DisplayRegion[];
  activeRegion: Region | null;
  activePlatform: PlatformType;
  onChange?: (region: DisplayRegion) => void;
}

export const SortRegions = (regionList: DisplayRegion[]) => {
  const usRegions: DisplayRegion[] = [],
    euRegions: DisplayRegion[] = [],
    apRegions: DisplayRegion[] = [],
    otherRegions: DisplayRegion[] = [];

  regionList.forEach((region) => {
    if (region.regionName.startsWith("us")) {
      usRegions.push(region);
    } else if (region.regionName.startsWith("eu")) {
      euRegions.push(region);
    } else if (region.regionName.startsWith("ap")) {
      apRegions.push(region);
    } else {
      otherRegions.push(region);
    }
  });

  usRegions.sort((regionA, regionB) => regionA.regionName.localeCompare(regionB.regionName));
  euRegions.sort((regionA, regionB) => regionA.regionName.localeCompare(regionB.regionName));
  apRegions.sort((regionA, regionB) => regionA.regionName.localeCompare(regionB.regionName));
  otherRegions.sort((regionA, regionB) => regionA.regionName.localeCompare(regionB.regionName));

  return [...usRegions, ...euRegions, ...apRegions, ...otherRegions];
};

const RegionSelect: React.FC<RegionSelectProps> = ({ regionList, activeRegion, activePlatform, onChange }) => {
  const [requestRegions, setRequestRegions] = useState<Array<PlatformType>>([]);
  const handleChange = (event: SelectChangeEvent) => {
    const id = +event.target?.value;
    const region = regionList.find((item) => item.id == id);
    region && onChange?.(region);
  };

  const onConfirm = () => {
    if (activePlatform !== PlatformType.AZURE && !requestRegions.includes(activeRegion?.platform as PlatformType)) {
      setRequestRegions([...requestRegions, activeRegion?.platform as PlatformType]);
    } else if (activePlatform === PlatformType.AZURE) {
      setRequestRegions([...requestRegions, PlatformType.AZURE]);
    }
  };

  const showRegionRequestModal = () => {
    if (activePlatform === PlatformType.AZURE) {
      NiceModal.show(RegionRequestModal, {
        platform: PlatformType.AZURE,
        onConfirm,
      });
    } else {
      NiceModal.show(RegionRequestModal, {
        platform: activeRegion?.platform as PlatformType,
        onConfirm,
      });
    }
  };

  if (
    activePlatform === PlatformType.AZURE &&
    (process?.env?.AMPLIFY_PORTAL_ENV || process.env.NEXT_PUBLIC_ENV) === "production" &&
    (!regionList || regionList?.length === 0)
  ) {
    return (
      <div className="mx-auto flex w-[564px] items-center justify-between rounded border border-[#AAECF2] bg-[#F3FEFF] px-5 py-3 text-start">
        {requestRegions.includes(PlatformType.AZURE) ? (
          <p>
            <FontAwesomeIcon className="mr-2.5 text-base text-primary" icon={faCircleCheck} />
            Azure region request sent.
          </p>
        ) : (
          <p>
            <FontAwesomeIcon icon={faCircleInfo} className="mr-2 text-base" />
            Azure regions are provisioned on demand.
          </p>
        )}
        <Button sx={{ backgroundColor: "white" }} variant="outlined" onClick={showRegionRequestModal}>
          Request Azure region
        </Button>
      </div>
    );
  }

  if (!activeRegion) return null;

  return (
    <div className="mx-auto w-[564px]">
      <div className="flex items-center justify-between px-2.5">
        <span>Select a region</span>
        <span className="cursor-pointer text-primary" onClick={showRegionRequestModal}>
          Need a different region?
        </span>
      </div>
      <div className="my-2">
        <Select
          sx={{ textAlign: "start" }}
          fullWidth
          size="small"
          value={activeRegion ? activeRegion.id.toString() : ""}
          onChange={handleChange}>
          {SortRegions(regionList).map((item) => {
            return (
              <MenuItem key={item.id} value={item.id.toString()}>
                {item.regionName}
                {item.area && ` (${item.area})`}
              </MenuItem>
            );
          })}
        </Select>
      </div>
      {requestRegions.includes(activeRegion.platform as PlatformType) && (
        <div className="rounded border border-[#AAECF2] bg-[#F3FEFF] px-5 py-3 text-start">
          <FontAwesomeIcon className="mr-2.5 text-base text-primary" icon={faCircleCheck} />
          <span className="uppercase">{activeRegion.platform}</span> region request sent.
        </div>
      )}
    </div>
  );
};
export default RegionSelect;

