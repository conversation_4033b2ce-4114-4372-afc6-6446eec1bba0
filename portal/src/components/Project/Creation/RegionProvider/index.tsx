"use client";
import { DisplayRegion, InvitedType, InvitedTypeAtom, regionChoosedAtom } from "../Store";
import { regionDisplayList } from "@/hooks/useRegionForDisplayList";
import { PlatformType, useRegionList } from "@/hooks/useRegionList";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCloud } from "@fortawesome/free-solid-svg-icons";
import RegionSelect, { SortRegions } from "./RegionSelect";
import { useEffect, useMemo, useState } from "react";
import { useAtom, useAtomValue } from "jotai";
import { isProdEnv } from "@/utils/func";
import RegionCard from "./RegionCard";

const disabledRegions = ["us-central1"];

interface RegionProviderProps {}
const RegionProvider: React.FC<RegionProviderProps> = (props) => {
  const [activePlatform, setActivePlatform] = useState<PlatformType>(PlatformType.AWS);
  const [region, setRegion] = useAtom(regionChoosedAtom);
  const inviteType = useAtomValue(InvitedTypeAtom);
  const regions = useRegionList();

  const platFormList = useMemo(() => {
    const list: { [key: string]: DisplayRegion[] } = {};
    regions?.forEach((item) => {
      if (isProdEnv() && disabledRegions.includes(item.regionName)) {
        return;
      }
      if (Object.values(PlatformType).includes(item.platform as PlatformType)) {
        if (item.isBYOCOnly && inviteType !== InvitedType.BYOC) {
          return;
        }
        const displayList = regionDisplayList[item.platform as PlatformType];
        const regionDisplay = displayList.find((region) => region.regionName === item.regionName);
        list[item.platform] = [
          ...(list[item.platform] || []),
          Object.assign(item, { area: regionDisplay?.area || "" }),
        ];
      }
    });

    return list;
  }, [inviteType, regions]);

  const handleSelectRegion = (region: DisplayRegion) => {
    setRegion(region);
  };

  const handleChangePlatform = (platform: PlatformType) => {
    setActivePlatform(platform);
  };

  useEffect(() => {
    const regionList = SortRegions(platFormList?.[activePlatform] || []);
    if (regionList && regionList.length > 0) {
      setRegion(regionList[0]);
    } else {
      setRegion(null);
    }
  }, [activePlatform, platFormList, setRegion]);

  return (
    <div className="text-center">
      <div className="mr-2.5 inline-block h-6 w-6 rounded-full bg-aquablue text-center text-xs leading-6">
        <FontAwesomeIcon className="text-white" icon={faCloud} />
      </div>
      <span>Select a service provider</span>
      <div className="my-6 flex items-stretch justify-center gap-3">
        {Object.values(PlatformType)?.map((platform, index) => {
          return (
            <RegionCard
              key={index}
              platform={platform}
              activePlatform={activePlatform}
              onChange={handleChangePlatform}
            />
          );
        })}
      </div>
      <div className="pt-2">
        <RegionSelect
          regionList={platFormList[activePlatform] || []}
          activePlatform={activePlatform}
          activeRegion={region}
          onChange={handleSelectRegion}
        />
      </div>
    </div>
  );
};
export default RegionProvider;

