import {
  distribution<PERSON>tom,
  InvitedType,
  InvitedType<PERSON>tom,
  isAutoCompactionOnAtom,
  projectNameAtom,
  regionChoosedAtom,
  rwuNumberAtom,
} from "../Store";
import { TenantsService, OpenAPI as OpenAPIV2, TenantRequestRequestBody } from "@/api-mgmt-v2";
import { DefaultService, TenantResourceRequestComponents, OpenAPI } from "@/api-mgmt";
import { faCircleArrowDown, faCircleCheck } from "@fortawesome/free-solid-svg-icons";
import { getCustomComponents, standaloneMarksWithoutPg, trialMarks } from "../const";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import ByocConfirmDialog from "../Invitation/ByocConfirmDialog";
import { platformIcon } from "../RegionProvider/RegionCard";
import { PlatformType } from "@/hooks/useRegionList";
import LoadingIcon from "@/components/LoadingIcon";
import { useOrgInfo } from "@/hooks/useOrgInfo";
import { useContext, useState } from "react";
import { useRouter } from "next/navigation";
import { getErrorMsg } from "@/utils/func";
import { Domains } from "@/config/router";
import { LoadingButton } from "@mui/lab";
import { toast } from "react-toastify";
import { Button } from "@mui/material";
import { useTier } from "../useTier";
import { useAtomValue } from "jotai";
import { CreationContext } from "..";
import { TierId } from "@/api-user";
import { If } from "react-extras";

interface SummaryProps {
  showSummary: boolean;
}

const Summary: React.FC<SummaryProps> = ({ showSummary }) => {
  const { handleCreateTenant } = useContext(CreationContext);
  const [loading, setLoading] = useState(false);
  const [active, setActive] = useState(false);
  const [showByocCodeGen, setShowByocCodeGen] = useState(false);
  const projectName = useAtomValue(projectNameAtom);
  const region = useAtomValue(regionChoosedAtom);
  const rwuNumber = useAtomValue(rwuNumberAtom);
  const distribution = useAtomValue(distributionAtom);
  const tier = useTier();
  const router = useRouter();
  const invitedType = useAtomValue(InvitedTypeAtom);
  const isAutoCompactionOn = useAtomValue(isAutoCompactionOnAtom);
  const { orgInfo } = useOrgInfo();

  const isAdvanced = tier === TierId.INVITED;

  const handleConfirm = async () => {
    if (invitedType === InvitedType.BYOC) {
      handleByocConfirm();
      return;
    }
    if (!rwuNumber) {
      toast.error("Please select a compute size option to continue.");
      return;
    }
    if (!region?.url || !tier) return;
    setLoading(true);
    const _tier = isAdvanced ? TierId.INVITED : TierId.STANDARD;
    try {
      OpenAPI.BASE = region.url;
      const tiers = await DefaultService.getTiers().then((res) => {
        return res?.tiers || [];
      });

      const creatTier = tiers.find((item) => item.id === _tier);
      if (!creatTier) {
        toast.error("No existing tier");
        return;
      }

      let standAloneComponents;

      // isStandalone
      if (!isAdvanced) {
        const standalone = creatTier.availableStandaloneNodes.find((item) => {
          return +item.cpu === rwuNumber;
        });
        standalone &&
          (standAloneComponents = {
            componentTypeId: standalone.id,
            replica: 1,
          });
      }

      let components = standAloneComponents
        ? {
            standalone: standAloneComponents,
          }
        : (getCustomComponents(creatTier, {
            compute: distribution?.compute || 0,
            frontend: distribution?.frontend || 0,
            compactor: distribution?.compactor || 0,
            meta: distribution?.meta || 0,
            computeReplica: distribution?.computeReplica || 1,
            frontendReplica: distribution?.frontendReplica || 1,
            compactorReplica: distribution?.compactorReplica || 1,
            metaReplica: distribution?.metaReplica || 1,
          }) as TenantResourceRequestComponents);

      const autoCompactionConfig = isAutoCompactionOn
        ? {
            serverlessCompaction: {
              maximumCompactionConcurrency: distribution?.compactor,
            },
          }
        : undefined;

      const { nsId } = await createTenant(
        region.urlV2,
        Object.assign({
          tenantName: projectName,
          tier: _tier,
          resources: {
            components,
            computeFileCacheSizeGiB: creatTier.maximumComputeNodeFileCacheSizeGiB,
          },
          extensions: autoCompactionConfig,
        }),
      );

      toast(
        <div className="flex items-center justify-start bg-white">
          <div className="h-8 w-8">
            <LoadingIcon />
          </div>
          <span className="mx-5 block">{`Creating '${projectName}', just a moment...`}</span>
        </div>,
      );
      handleCreateTenant?.(nsId);
      router.replace(Domains.home);
    } catch (error: any) {
      if ((error.status === 403 || error.status === 412) && tier === TierId.DEVELOPER_FREE) {
        if (orgInfo!.trialDays > 0) {
          toast.error(
            "You can create only one project during the free trial. To create more projects, please upgrade your plan.",
          );
        } else {
          toast.error("Your trial has ended. Please upgrade your plan to resume operations on this cluster.");
        }
      } else if (error.status === 409) {
        toast.error(
          `A project named '${projectName}' already exists. Please choose a different name for your new project.`,
        );
      } else {
        toast.error(getErrorMsg(error, "failed to create project"));
      }
    }
    setLoading(false);
  };

  const handleByocConfirm = () => {
    setShowByocCodeGen(true);
  };

  const handleByocDialogClose = (flag?: boolean) => {
    setShowByocCodeGen(false);
  };

  const createTenant = (url: string, params: TenantRequestRequestBody) => {
    OpenAPIV2.BASE = url;
    return TenantsService.postTenants(params);
  };

  const onClickSummary = () => {
    if (!showSummary) {
      toast.error("Please select a compute size option to continue.");
      return;
    }
    setActive(true);
  };

  return (
    <>
      <div className="w-full text-center">
        <If condition={!active}>
          <Button variant="contained" sx={{ px: "20px" }} onClick={onClickSummary}>
            Summary
            <FontAwesomeIcon className="ml-2" icon={faCircleArrowDown} />
          </Button>
        </If>
        <If condition={active}>
          <p className="mb-6 text-xl">{projectName}:</p>
          <div className="flex justify-center gap-5">
            {tier === TierId.INVITED && (
              <div className="flex h-[124px] w-[124px] flex-col justify-between rounded-2xl border border-neutral200 px-5 py-[26px]">
                <p className="mb-2 text-2xl font-bold leading-none">{invitedType}</p>
                <p className="text-sm">Deployment mode</p>
              </div>
            )}
            <div className="min-h-[124px] w-[124px] rounded-2xl border border-neutral200 px-5 pb-4 pt-5">
              <div className="mx-auto mb-2 flex w-[66px] justify-center">
                {region?.platform && platformIcon[region.platform as PlatformType]}
              </div>
              <p>{region?.regionName}</p>
              {region?.area && <p>({region?.area})</p>}
            </div>
            <div className="flex w-[124px] flex-col justify-between rounded-2xl border border-neutral200 px-5 py-[26px]">
              <p className="text-[44px] font-bold leading-none">{rwuNumber}</p>
              <p className="text-sm">RWUs</p>
            </div>
          </div>

          <LoadingButton
            loading={loading}
            variant="contained"
            sx={{ px: "20px", mt: "20px", mb: "40px" }}
            onClick={handleConfirm}>
            Confirm
            <FontAwesomeIcon className="ml-2" icon={faCircleCheck} />
          </LoadingButton>
        </If>
      </div>

      <ByocConfirmDialog open={showByocCodeGen} onCancel={handleByocDialogClose} />
    </>
  );
};
export default Summary;

