import { AvailableComponentType, ComponentResourceRequest, Tier } from "@/api-mgmt";
import { CustomizedOption } from "./Store";

export const trialMarks = [2];
export const standaloneMarksWithoutPg = [4, 8];
export const standaloneMarks = [2, 6];
export interface DistributionMark {
  rwu: number;
  compute: number;
  frontend: number;
  compactor: number;
  meta: number;
  pg: number;
  computeReplica?: number;
  frontendReplica?: number;
  compactorReplica?: number;
  metaReplica?: number;
}
export const distributionMarks: DistributionMark[] = [
  {
    rwu: 10,
    compute: 4,
    frontend: 2,
    compactor: 2,
    meta: 2,
    pg: 2,
  },
  {
    rwu: 16,
    compute: 8,
    frontend: 2,
    compactor: 4,
    meta: 2,
    pg: 2,
  },
  {
    rwu: 22,
    compute: 12,
    frontend: 2,
    compactor: 6,
    meta: 2,
    pg: 2,
  },
  {
    rwu: 28,
    compute: 16,
    frontend: 2,
    compactor: 8,
    meta: 2,
    pg: 2,
  },
  {
    rwu: 42,
    compute: 24,
    frontend: 2,
    compactor: 12,
    meta: 4,
    pg: 2,
  },
  {
    rwu: 54,
    compute: 32,
    frontend: 2,
    compactor: 16,
    meta: 4,
    pg: 2,
  },
  {
    rwu: 78,
    compute: 48,
    frontend: 2,
    compactor: 24,
    meta: 4,
    pg: 2,
  },
  {
    rwu: 104,
    compute: 64,
    frontend: 2,
    compactor: 32,
    meta: 6,
    pg: 2,
  },
];

export const standardProductionMarks = [16, 24, 32, 44, 56, 64];

export interface Resources {
  etcdVolumeSizeGiB: number;
  // enableComputeFileCache?: boolean;
  computeFileCacheSizeGiB: number;
}

const MatchingSku = (cpu: number, availableComponent: AvailableComponentType[]) => {
  const sku = availableComponent?.find?.((item) => parseFloat(item.cpu) === cpu);
  return sku;
};

export const getCustomComponents = (tier: Tier, customizedOptions: CustomizedOption) => {
  if (!tier || !customizedOptions) return;

  const computeSku = MatchingSku(customizedOptions.compute, tier.availableComputeNodes);
  const frontendSku = MatchingSku(customizedOptions.frontend, tier.availableFrontendNodes);
  const metaSku = MatchingSku(customizedOptions.meta, tier.availableMetaNodes);
  const compactorSku = MatchingSku(customizedOptions.compactor, tier.availableCompactorNodes);

  if (!computeSku && !compactorSku && !frontendSku && !metaSku) return;
  const computeComponent: ComponentResourceRequest = {
    componentTypeId: computeSku?.id as string,
    replica: customizedOptions.computeReplica,
  };
  const frontendComponent: ComponentResourceRequest = {
    componentTypeId: frontendSku?.id as string,
    replica: customizedOptions.frontendReplica,
  };
  const metaComponent: ComponentResourceRequest = {
    componentTypeId: metaSku?.id as string,
    replica: customizedOptions.metaReplica,
  };

  const compactorComponent: ComponentResourceRequest = {
    componentTypeId: compactorSku?.id as string,
    replica: customizedOptions.compactorReplica,
  };

  return {
    compute: computeComponent,
    frontend: frontendComponent,
    meta: metaComponent,
    compactor: compactorComponent,
  };
};

export const customComponentNodes = {
  compute: [4, 8, 12, 16, 24, 32, 48, 64],
  frontend: [1, 1.5, 2, 4, 8, 10, 12, 14, 16, 20, 24, 36, 48, 64],
  compactor: [2, 4, 6, 8, 12, 16, 24, 32, 48, 64],
  meta: [2, 4, 6, 12],
};

export const recommendComponentNodes = {
  compute: [4, 8, 12, 16, 24, 32, 48, 64],
  frontend: [2, 2, 2, 2, 2, 2, 2, 2],
  compactor: [4, 4, 6, 8, 12, 16, 24, 32],
  meta: [2, 2, 2, 2, 4, 4, 4, 6],
};

export const replicaOptions = [1, 2, 3, 4, 5];
export const metaReplicaOptions = [1, 2, 3];

