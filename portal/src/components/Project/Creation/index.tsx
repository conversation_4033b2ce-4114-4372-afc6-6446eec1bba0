"use client";
import CreateContentV2 from "./CreateContentV2";
import { createContext } from "react";

export const CreationContext = createContext<{
  handleCreateTenant?: (nsId: string) => void;
}>({
  handleCreateTenant: () => {},
});

interface Props {
  handleCreateTenant?: (nsId: string) => void;
}
const Creation: React.FC<Props> = ({ handleCreateTenant }) => {
  return (
    <CreationContext.Provider
      value={{
        handleCreateTenant,
      }}>
      <div className="mx-auto w-[860px] px-5 pb-12 pt-4 md:px-0">
        <main className="mx-auto">
          <CreateContentV2 />
        </main>
      </div>
    </CreationContext.Provider>
  );
};
export default Creation;

