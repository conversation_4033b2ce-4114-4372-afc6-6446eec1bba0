import { useOrgInfo } from "@/hooks/useOrgInfo";
import { DefaultService } from "@/api-billing";
import { TierId } from "@/api-user";
import useSWR from "swr";

export const TierList = [TierId.DEVELOPER_FREE];

export const useTier = () => {
  const { orgInfo } = useOrgInfo();
  const { data: paymentMethods, isLoading } = useSWR(
    "getOrgPayments",
    () => DefaultService.getOrgPayments().then((res) => res.payment_methods),
    {
      refreshInterval: 0,
    },
  );
  if (!orgInfo) return null;

  const { hasInvitationCode, trialDays } = orgInfo;

  if (hasInvitationCode) {
    return TierId.INVITED;
  }

  if (orgInfo.pmCount > 0 || (paymentMethods && paymentMethods?.length > 0)) {
    return TierId.STANDARD;
  }

  if (trialDays > 0) {
    return TierId.DEVELOPER_FREE;
  }

  return null;
};
