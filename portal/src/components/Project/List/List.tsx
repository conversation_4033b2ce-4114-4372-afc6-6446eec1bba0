import {
  faTrashCan,
  faRocket,
  faSpinner,
  faCircleExclamation,
  faTriangleExclamation,
  faUpRightFromSquare,
} from "@fortawesome/free-solid-svg-icons";
import { TenantsService, OpenAPI as OpenAPIV2, Tenant as TenantDetail } from "@/api-mgmt-v2";
import { fomratTenantStatus, getStateColor } from "@/components/Cluster/utils";
import { Button, Divider, IconButton, Paper, Tooltip } from "@mui/material";
import UpgradeConfirmPopup from "@/components/Cluster/UpgradeConfirmPopup";
import { loadingStatusList } from "@/components/Cluster/TenantType";
import { operateTenant, TenantOperatorType } from "./operateTenant";
import TenantStateIcon from "@/components/Cluster/TenantStateIcon";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import React, { useState, useMemo, ReactNode } from "react";
import { useRegionList } from "@/hooks/useRegionList";
import { EVENTS, eventEmitter } from "@/utils/events";
import WhiteTooltip from "../Creation/WhiteTooltip";
import DeleteTenantHook from "./DeleteTenantHook";
import Popconfirm from "@/components/Popconfirm";
import { useOrgInfo } from "@/hooks/useOrgInfo";
import { classNames, If } from "react-extras";
import { useRouter } from "next/navigation";
import { Tenant, TierId } from "@/api-user";
import { Domains } from "@/config/router";
import { compare, valid } from "semver";
import dayjs from "dayjs";
import useSWR from "swr";

interface CardProps {
  tenant: Tenant;
}
const List: React.FC<CardProps> = ({ tenant }) => {
  const [open, setOpen] = useState(false);
  const [openPopconfirm, setOpenPopconfirm] = useState<boolean>(false);
  const regions = useRegionList();
  const region = regions?.find((item) => item.regionName === tenant.region);
  const { showDeleteDialog } = DeleteTenantHook();
  const { orgInfo } = useOrgInfo();
  const router = useRouter();
  const {
    data: tenantDetail,
    isLoading,
    error,
    mutate,
  } = useSWR(
    ["getTenantDetail", region?.urlV2, tenant.nsId],
    () => {
      if (!region?.urlV2 || !tenant.nsId) return;
      OpenAPIV2.BASE = region.urlV2 || "";
      return TenantsService.getTenants1(tenant.nsId).then((res) => {
        return res;
      });
    },
    {
      refreshInterval: 5000,
    },
  );

  const unHealthy = tenantDetail?.health_status === TenantDetail.health_status.UNHEALTHY;

  const tenantStatus = tenantDetail?.status;

  const handleDelete = () => {
    if (!region) return;
    return operateTenant?.(region, TenantOperatorType.DELETE, tenantDetail!)?.then(() => {
      mutate({
        ...tenantDetail!,
        status: TenantDetail.status.DELETING,
      });
    });
  };

  const handleConnect = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    event.preventDefault();
    eventEmitter.emit(EVENTS.OPEN_CONNECT_POPUP, { tenant: tenantDetail });
  };

  const handleTenantStop = () => {
    if (!region) return;
    operateTenant?.(region, TenantOperatorType.STOP, tenantDetail!).then(() => {
      mutate({
        ...tenantDetail!,
        status: TenantDetail.status.STOPPING,
      });
    });
    setOpen(false);
  };

  const handleTennatStart = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    event.preventDefault();
    if (!region) return;
    operateTenant?.(region, TenantOperatorType.START, tenantDetail!, orgInfo?.trialDays === 0).then(() => {
      mutate({
        ...tenantDetail!,
        status: TenantDetail.status.STARTING,
      });
    });
  };

  const enterTenant = () => {
    if (isClickDisabled) {
      return;
    }

    router.push(`/cluster/${encodeURIComponent(tenant.nsId)}/${tenant.region}/overview`);
  };

  const handleEnterConsole = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    event.preventDefault();
    if (isClickDisabled) {
      return;
    }
    router.push(`/cluster/${tenant.nsId}/${tenant.region}/console`);
  };

  const isTenantProcessing = useMemo(() => {
    return (
      !tenantStatus || (loadingStatusList.includes(tenantStatus) && tenantStatus !== TenantDetail.status.SNAPSHOTTING)
    );
  }, [tenantStatus]);

  const isClickDisabled = useMemo(
    () => tenantStatus === TenantDetail.status.DELETING || tenantStatus === TenantDetail.status.STARTING,
    [tenantStatus],
  );

  const stateColor = useMemo(() => getStateColor(tenantStatus, unHealthy), [tenantStatus, unHealthy]);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    event.preventDefault();
    setOpen(true);
  };

  const handleUpgrade = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    event.preventDefault();
    setOpenPopconfirm(true);
  };

  const upgradeRender = () => {
    if (tenantStatus === TenantDetail.status.UPGRADING) {
      return <FontAwesomeIcon icon={faSpinner} spin />;
    }
    if (!tenantDetail || !valid(tenantDetail?.imageTag) || !valid(tenantDetail?.latestImageTag)) {
      return;
    }
    if (
      tenantStatus !== TenantDetail.status.RUNNING ||
      compare(tenantDetail?.imageTag, tenantDetail?.latestImageTag) !== -1
    ) {
      return;
    }
    return (
      <Tooltip placement="right" title="New version is available! ">
        <IconButton sx={{ padding: "0", marginLeft: "4px" }} onClick={handleUpgrade}>
          <FontAwesomeIcon className="w-[12px] text-sm text-[#018D99]" icon={faRocket} />
        </IconButton>
      </Tooltip>
    );
  };

  const tier = useMemo(() => {
    if (tenantDetail?.tier === TierId.STANDARD) {
      return "Standard";
    }
    return "Advanced";
  }, [tenantDetail?.tier]);

  const showDiffTierWarning = useMemo(() => {
    if (orgInfo?.hasInvitationCode && tenantDetail?.tier === TierId.STANDARD) {
      return true;
    }
    return false;
  }, [orgInfo?.hasInvitationCode, tenantDetail?.tier]);

  return (
    <>
      <Paper
        className={classNames("relative rounded-[4px] bg-[#fafafa] px-5 py-3", {
          "cursor-pointer": !isClickDisabled,
          "hover:shadow-xxl": !isClickDisabled,
          "opacity-50": isClickDisabled,
        })}
        style={{ color: isClickDisabled ? "#9DA1A6" : "#363A40", backgroundColor: "#fafafa" }}
        onClick={enterTenant}>
        <div className="flex justify-between border-b-[1px] pb-3">
          <div className="flex items-center">
            {showDiffTierWarning && (
              <WhiteTooltip
                content={
                  <div className="text-sm text-neutral900">
                    This project was created using configurations under the Standard Plan, which differs from your
                    current Advanced Plan. To convert this project to use Advanced Plan configurations, please contact
                    our
                    <a
                      href={Domains.salesEmail}
                      target="_blank"
                      className="ml-1 text-neutral400 underline"
                      onClick={(e) => {
                        e.stopPropagation();
                      }}>
                      Support team <FontAwesomeIcon icon={faUpRightFromSquare} />
                    </a>
                  </div>
                }>
                <div className="mr-2.5 h-5 rounded border border-[#F6AD16] bg-[#FDEFD0] px-1 leading-4">
                  <FontAwesomeIcon className="text-xs text-[#94680D]" icon={faTriangleExclamation} />
                </div>
              </WhiteTooltip>
            )}
            <h1 className="mr-2 overflow-hidden text-base text-primary">{tenant?.tenantName}</h1>
            <Tooltip placement="right" title={tenant.nsId} arrow>
              <a href="#" className="text-xs text-[#9da0a5] underline">
                UUID
              </a>
            </Tooltip>
            <div className="ml-4 rounded-[30px] bg-[#2DC2A1] px-1 py-0.5 text-[8px] text-white">{tier}</div>
          </div>
          <div className="flex items-center text-xs" style={{ color: stateColor }}>
            {<TenantStateIcon tenantStatus={tenantStatus} healthStatus={tenantDetail?.health_status} />}
            <span className="ml-1 text-sm font-medium">&nbsp;{fomratTenantStatus(tenantStatus, unHealthy)}</span>
          </div>
        </div>
        <div className="mt-3 flex flex-wrap justify-between">
          <div className="flex gap-[30px]">
            <CardInfo name="Version" desc={tenantDetail?.imageTag || "unkown"} render={upgradeRender} />
            <CardInfo name="Platform" desc={region?.platform || ""} isUppercase={true} />
            <CardInfo name="Region" desc={tenant?.region} />
            <CardInfo
              name="Created at"
              desc={tenant?.createdAt && dayjs(tenant?.createdAt).format("YYYY-MM-DD HH:mm:ss")}
            />
          </div>

          <div className="flex items-center gap-[30px]">
            <If
              condition={
                !(
                  tenantStatus === TenantDetail.status.DELETING ||
                  tenantStatus === TenantDetail.status.CREATING ||
                  isTenantProcessing
                )
              }>
              <IconButton
                className="w-[28px]"
                onClick={(event) => {
                  event.stopPropagation();
                  event.preventDefault();
                  showDeleteDialog(tenant.tenantName, handleDelete);
                }}>
                <FontAwesomeIcon className="text-xs font-bold text-[#091720]" icon={faTrashCan} />
              </IconButton>
            </If>

            <If condition={!isTenantProcessing}>
              <If
                condition={
                  tenantStatus === TenantDetail.status.STOPPED || tenantStatus === TenantDetail.status.EXPIRED
                }>
                <Divider orientation="vertical" flexItem />
                <Button
                  variant="contained"
                  size="small"
                  sx={{ height: "30px", lineHeight: "30px" }}
                  onClick={handleTennatStart}>
                  Resume
                </Button>
              </If>
              <If condition={tenantStatus === TenantDetail.status.RUNNING}>
                <Divider orientation="vertical" flexItem />
                <Button
                  size="small"
                  sx={{
                    minWidth: "0",
                    px: "10px",
                    height: "30px",
                    lineHeight: "30px",
                    marginLeft: "2px",
                    borderRadius: 0,
                  }}
                  onClick={handleClick}>
                  Pause
                </Button>
                <If condition={!unHealthy}>
                  <Divider orientation="vertical" flexItem />
                  <Button onClick={handleConnect} sx={{ height: "30px", lineHeight: "30px" }} size="small">
                    Connect
                  </Button>
                  <Divider orientation="vertical" flexItem />
                  <Button
                    onClick={handleEnterConsole}
                    variant="outlined"
                    sx={{ ml: "11px", height: "30px", lineHeight: "30px" }}
                    size="small">
                    Workspace
                  </Button>
                </If>
              </If>
            </If>
          </div>
        </div>
      </Paper>

      <Popconfirm
        title="Pause the project"
        open={open}
        onCancel={() => {
          setOpen(false);
        }}
        confirmText="Confirm"
        cancelText="Cancel"
        confirmType="primary"
        onConfirm={() => handleTenantStop()}>
        <>
          <div className="rounded bg-[#FFFBEB] px-5 py-3 text-[#B45309]">
            <FontAwesomeIcon icon={faCircleExclamation} className="mr-1" />
            You are about to pause the project {tenant?.tenantName}. This action will halt the running streaming
            pipelines and release all the compute resources of the running project.
          </div>
          <div className="mt-4 px-3">
            <p>While the project is paused:</p>
            <p className="ml-2 mt-2">- The project will be accessible.</p>
            <p className="ml-2 mt-2">- No streaming pipelines will run.</p>
            <p className="my-2 ml-2">- You will not incur charges for the compute resources.</p>
            <div className="mt-3">
              Please ensure that all critical tasks are safely paused before proceeding. You can resume your project at
              any time after it has been paused.
            </div>
            <div className="mt-2">Please confirm to proceed with the pause. </div>
          </div>
        </>
      </Popconfirm>
      {tenantDetail && region && (
        <UpgradeConfirmPopup
          tenant={tenantDetail}
          region={region}
          open={openPopconfirm}
          onCancel={() => {
            setOpenPopconfirm(false);
          }}
          onUpdate={() => {
            mutate();
          }}
        />
      )}
    </>
  );
};
export default List;

interface CardInfoProps {
  name: string;
  desc: string;
  isUppercase?: boolean;
  render?: () => ReactNode;
}
const CardInfo: React.FC<CardInfoProps> = ({ name, desc, render, isUppercase = false }) => (
  <div className="text-sm">
    <p className="mb-[2px] text-[#9DA1A6]">{name}</p>
    <div className={`pt-1 ${isUppercase ? "uppercase" : ""}`}>
      {desc} {render && render()}{" "}
    </div>
  </div>
);

