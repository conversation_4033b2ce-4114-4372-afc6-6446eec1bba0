import {
  AuthService,
  OpenAPI as <PERSON>r<PERSON><PERSON>,
  <PERSON><PERSON>s,
  UserAuth,
  UserService,
  PutUserRequestBody,
  AuthType,
} from "@/api-user";
import { IUser, cookieKey, socialLoginMethods } from "./const";
import { OpenAPI as Billing<PERSON>I } from "@/api-billing";
import { OpenAPI as MgmtAPIv2 } from "@/api-mgmt-v2";
import { OpenAPI as UserAPIV2 } from "@/api-user-v2";
import { OpenAPI as MgmtAPI } from "@/api-mgmt";
import { Domains } from "@/config/router";
import { cookie } from "../storage";

class Auth {
  ping = () => {
    return AuthService.getAuthPing();
  };

  login = (data: { email: string; password: string }) => {
    this.removeAuthCookie();
    return AuthService.postAuthLogin(data).then((data) => {
      this.setCookie(data);
      MgmtAPI.TOKEN = data.tokens?.jwt;
      MgmtAPIv2.TOKEN = data.tokens?.jwt;
      UserAPI.TOKEN = data.tokens?.jwt;
      UserAPIV2.TOKEN = data.tokens?.jwt;
      BillingAPI.TOKEN = data.tokens?.jwt;
      return data;
    });
  };

  setCookie = (data: UserAuth) => {
    cookie.set(cookieKey.TOKEN, data.tokens?.jwt!);
    cookie.set(cookieKey.USERNAME, data.username);
    cookie.set(cookieKey.EMAIL, data.email);
    cookie.set(cookieKey.AUTH_TYPE, data.authType);
    cookie.set(cookieKey.USERID, data.id, 1);
    cookie.set(cookieKey.RESOURCE_ID, data.resourceId, 1);
    data.tokens?.refresh && cookie.set(cookieKey.REFRESH_TOKEN, data.tokens.refresh);
  };

  setTokens = (data: Tokens) => {
    cookie.set(cookieKey.TOKEN, data.jwt || "");
    cookie.set(cookieKey.REFRESH_TOKEN, data.refresh || "");
    MgmtAPI.TOKEN = data.jwt;
    MgmtAPIv2.TOKEN = data.jwt;
    UserAPI.TOKEN = data.jwt;
    UserAPIV2.TOKEN = data.jwt;
    BillingAPI.TOKEN = data.jwt;
  };

  signup = (params: { email: string; username: string; password: string }) => {
    this.removeAuthCookie();
    return AuthService.postAuthRegister(params).then((res) => {
      return res;
    });
  };

  logout = async () => {
    const refreshToken = cookie.get(cookieKey.REFRESH_TOKEN) || "";
    this.removeAuthCookie();
    AuthService.getAuthLogout(refreshToken).finally(() => {
      location.replace(Domains.signin);
    });
  };

  removeAuthCookie = () => {
    const remove = (name: string) => {
      cookie.remove(name);
      cookie.remove(name, "risingwave-cloud.com");
      document?.domain && cookie.remove(name, document?.domain);
    };
    remove(cookieKey.TOKEN);
    remove(cookieKey.USERNAME);
    remove(cookieKey.USERID);
    remove(cookieKey.EMAIL);
    remove(cookieKey.REFRESH_TOKEN);
    remove(cookieKey.AUTH_TYPE);
    remove(cookieKey.RESOURCE_ID);
  };

  checkLogin = () => {
    const userToken = cookie.get(cookieKey.TOKEN);
    const userId = cookie.get(cookieKey.USERID);
    return userToken && userToken.length && userId && userId.length;
  };

  getUserInfo(): IUser {
    const userId = cookie.get(cookieKey.USERID) || 0;
    const userName = cookie.get(cookieKey.USERNAME) || "";
    const userToken = cookie.get(cookieKey.TOKEN) || "";
    const userEmail = cookie.get(cookieKey.EMAIL) || "";
    const userAuthType = cookie.get(cookieKey.AUTH_TYPE) || "";
    const userResourceId = cookie.get(cookieKey.RESOURCE_ID) || "";
    const userRefreshToken = cookie.get(cookieKey.REFRESH_TOKEN) || "";

    return {
      userId: +userId,
      userName,
      userToken,
      userAuthType: userAuthType as AuthType,
      userEmail,
      userResourceId,
      userRefreshToken,
    };
  }

  getUserInfoFromApi() {
    return UserService.getUser();
  }

  async updateUser(props: PutUserRequestBody) {
    const res = await UserService.putUser(props);
    this.setCookie(res);
    return res;
  }

  socialLogin(method: socialLoginMethods, invitationToken?: string) {
    const url = `${
      process.env.AMPLIFY_USER_HOST || process.env.NEXT_PUBLIC_HOST_USER
    }/api/v1/auth0/login?method=${method}${
      invitationToken ? "&invitationToken=" + invitationToken : ""
    }&returnTo=${encodeURIComponent(
      `${typeof location === "undefined" ? "https://www.risingwave.cloud/auth/authhook/`" : location.origin}/auth/authhook/`,
    )}&utmUrl=${encodeURIComponent(window.location.href)}`;
    return url;
  }

  ssoLogin(ssoName: string) {
    const url = `${
      process.env.AMPLIFY_USER_HOST || process.env.NEXT_PUBLIC_HOST_USER
    }/api/v1/auth0/login?method=${ssoName}&returnTo=${decodeURIComponent(
      `${typeof location === "undefined" ? "https://www.risingwave.cloud/auth/authhook/`" : location.origin}/auth/authhook/`,
    )}`;

    return url;
  }

  async refreshJWT() {
    const token = this.getUserInfo().userToken ?? "";
    const refreshToken = this.getUserInfo().userRefreshToken ?? "";
    if (token === "" || refreshToken === "") {
      return false;
    }
    const userResourceId = this.getUserInfo().userResourceId;
    const updatedTokens = await AuthService.postAuthRefresh({ refreshToken, userResourceId });
    if (updatedTokens == null) {
      return false;
    }
    this.setTokens(updatedTokens);
    return true;
  }
}

const auth = new Auth();
export default auth;

