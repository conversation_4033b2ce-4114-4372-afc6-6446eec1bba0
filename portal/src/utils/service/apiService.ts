"use client";

import { OpenAPI as Billing<PERSON><PERSON> } from "@/api-billing";
import { OpenAPI as MgmtAPIv2 } from "@/api-mgmt-v2";
import { OpenAPI as UserAPIV2 } from "@/api-user-v2";
import { OpenAPI as UserAPI } from "@/api-user";
import { OpenAPI as MgmtAPI } from "@/api-mgmt";
import { session } from "../storage";
import Auth from "../auth/Auth";
import axios from "axios";

export const billingBase = `${process.env.AMPLIFY_BILLING_HOST || process.env.NEXT_PUBLIC_BILLING_HOST}/api/v1`;

let isRefreshing = false;
type RetryRequestType = () => void;
let retryRequests: RetryRequestType[] = [];

const initService = () => {
  const baseUrl = session.get("baseUrl");
  UserAPI.BASE = `${baseUrl || process.env.AMPLIFY_USER_HOST || process.env.NEXT_PUBLIC_HOST_USER}/api/v1`;
  UserAPIV2.BASE = `${baseUrl || process.env.AMPLIFY_USER_HOST || process.env.NEXT_PUBLIC_HOST_USER}/api/v2`;
  MgmtAPI.BASE = `${process.env.NEXT_PUBLIC_HOST_MGMT}/api/v1`;
  MgmtAPIv2.BASE = `${process.env.NEXT_PUBLIC_HOST_MGMT}/api/v2`;
  BillingAPI.BASE = billingBase;
  const userInfo = Auth.getUserInfo();
  MgmtAPI.TOKEN = userInfo?.userToken || "";
  MgmtAPIv2.TOKEN = userInfo?.userToken || "";
  UserAPI.TOKEN = userInfo?.userToken || "";
  UserAPIV2.TOKEN = userInfo?.userToken || "";
  BillingAPI.TOKEN = userInfo?.userToken || "";
  // Add a response interceptor
  axios.interceptors.response.use(
    function (response) {
      // Do something with response data
      return response;
    },

    async function (error) {
      const { status, config } = error?.response || {};
      // Sentry.captureException(error);

      if (status === 401) {
        if (!isRefreshing) {
          isRefreshing = true;
          try {
            const refreshed = await Auth.refreshJWT();
            if (!refreshed) return Auth.logout();
            config.headers["Authorization"] = "Bearer " + Auth.getUserInfo().userToken;
            retryRequests.forEach((cb) => cb());
            retryRequests = [];
            isRefreshing = false;
            return axios.request(config);
          } catch (err) {
            retryRequests = [];
            Auth.logout();
            isRefreshing = false;
            throw err;
          }
        } else {
          return new Promise((resolve) => {
            retryRequests.push(() => {
              resolve(axios.request(config));
            });
          });
        }
      } else if (status === 500) {
        return Promise.reject({ message: "An error occurred. Please try again later." });
      } else {
        return Promise.reject(error);
      }
    },
  );
};

export { initService };

