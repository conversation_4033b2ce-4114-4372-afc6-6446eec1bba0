import type { Config } from "tailwindcss";

const config: Config = {
  darkMode: ["class"],
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: "var(--color-primary)",
        primary700: "var(--color-primary-700)",
        neutral: "var(--color-neutral)",
        neutral50: "var(--color-neutral-50)",
        neutral100: "var(--color-neutral-100)",
        neutral200: "var(--color-neutral-200)",
        neutral300: "var(--color-neutral-300)",
        neutral400: "var(--color-neutral-400)",
        neutral500: "var(--color-neutral-500)",
        neutral600: "var(--color-neutral-600)",
        neutral800: "var(--color-neutral-800)",
        neutral900: "var(--color-neutral-900)",
        navyblue50: "#DAEBF2",
        navyblue500: "#538EA6",
        navyblue700: "#144F66",
        warning50: "#FFFBEB",
        warning300: "#FCD34D",
        warning500: "#F59E0B",
        warning700: "var(--color-warning-700)",
        turquoise50: "#BAFFF0",
        turquoise300: "#48DCBC",
        error50: "#FEF2F2",
        error300: "#F2A6A6",
        error500: "#EF4444",
        error700: "#B91C1C",
        turquoise500: "#2DC2A1",
        turquoise700: "#138F74",
        aquablue50: "rgba(243,254,255,0.50)",
        aquablue: "#018D99",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "var(--color-primary)",
        chart: {
          "1": "hsl(var(--chart-1))",
          "2": "hsl(var(--chart-2))",
          "3": "hsl(var(--chart-3))",
          "4": "hsl(var(--chart-4))",
          "5": "hsl(var(--chart-5))",
        },
      },
      fontFamily: {
        Robot: "var(--font-family)",
      },
      boxShadow: {
        xxl: "4px 4px 20px 5px rgba(0, 0, 0, 0.05), 2px 2px 8px 3px rgba(0, 0, 0, 0.1), 1px 1px 3px rgba(0, 0, 0, 0.15)",
        navBar: "2px 1px 3px rgba(0, 0, 0, 0.05), 1px 1px 2px rgba(0, 0, 0, 0.1)",
        gcpOptions: "2px 1px 3px rgba(0, 0, 0, 0.05), 1px 1px 2px rgba(0, 0, 0, 0.1)",
        sourceStepButton:
          "4px 4px 20px 5px rgba(0, 0, 0, 0.05), 2px 2px 8px 3px rgba(0, 0, 0, 0.1), 1px 1px 3px rgba(0, 0, 0, 0.15)",
      },
      screens: {
        "mui-xs": "0px",
        "mui-sm": "600px",
        "mui-md": "900px",
        "mui-lg": "1200px",
        "mui-xl": "1536px",
      },
      backgroundImage: {
        "background-image": `url('/imgs/bg.png')`,
        linearGreen: "linear-gradient(93deg, #E0FFF8 1.35%, #FAFAFA 100%)",
      },
      backgroundSize: {
        full: "100% 100%",
      },
      transitionProperty: {
        height: "height",
        border: "border",
        opacity: "opacity",
      },
      minHeight: {
        calc: "calc(100% - 160px);",
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
};
export default config;

