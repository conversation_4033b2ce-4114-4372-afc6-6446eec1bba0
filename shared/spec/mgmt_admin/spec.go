// Package mgmt_admin provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package mgmt_admin

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"path"
	"strings"
	"time"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/gin-gonic/gin"
	"github.com/oapi-codegen/runtime"
	openapi_types "github.com/oapi-codegen/runtime/types"
)

// Defines values for ClusterReadyStatus.
const (
	After       ClusterReadyStatus = "After"
	Before      ClusterReadyStatus = "Before"
	Interrupted ClusterReadyStatus = "Interrupted"
)

// Defines values for ClusterStatus.
const (
	ClusterStatusDeleted                 ClusterStatus = "Deleted"
	ClusterStatusFailed                  ClusterStatus = "Failed"
	ClusterStatusPendingResourceDeletion ClusterStatus = "PendingResourceDeletion"
	ClusterStatusProvisioned             ClusterStatus = "Provisioned"
	ClusterStatusReady                   ClusterStatus = "Ready"
	ClusterStatusTerminating             ClusterStatus = "Terminating"
	ClusterStatusUninitialized           ClusterStatus = "Uninitialized"
	ClusterStatusUpdating                ClusterStatus = "Updating"
)

// Defines values for MetaStoreType.
const (
	AwsRds      MetaStoreType = "aws_rds"
	AzrPostgres MetaStoreType = "azr_postgres"
	Etcd        MetaStoreType = "etcd"
	GcpCloudsql MetaStoreType = "gcp_cloudsql"
	Postgresql  MetaStoreType = "postgresql"
	SharingPg   MetaStoreType = "sharing_pg"
)

// Defines values for TenantHealthStatus.
const (
	Healthy   TenantHealthStatus = "Healthy"
	Unhealthy TenantHealthStatus = "Unhealthy"
	Unknown   TenantHealthStatus = "Unknown"
)

// Defines values for TenantStatus.
const (
	TenantStatusConfigUpdating                       TenantStatus = "ConfigUpdating"
	TenantStatusCreating                             TenantStatus = "Creating"
	TenantStatusDeleting                             TenantStatus = "Deleting"
	TenantStatusExpired                              TenantStatus = "Expired"
	TenantStatusExtensionCompactionDisabling         TenantStatus = "ExtensionCompactionDisabling"
	TenantStatusExtensionCompactionEnabling          TenantStatus = "ExtensionCompactionEnabling"
	TenantStatusExtensionServerlessBackfillDisabling TenantStatus = "ExtensionServerlessBackfillDisabling"
	TenantStatusExtensionServerlessBackfillEnabling  TenantStatus = "ExtensionServerlessBackfillEnabling"
	TenantStatusExtensionServerlessBackfillUpdate    TenantStatus = "ExtensionServerlessBackfillUpdate"
	TenantStatusFailed                               TenantStatus = "Failed"
	TenantStatusMetaMigrating                        TenantStatus = "MetaMigrating"
	TenantStatusQuiesced                             TenantStatus = "Quiesced"
	TenantStatusResourceGroupsUpdating               TenantStatus = "ResourceGroupsUpdating"
	TenantStatusRestoring                            TenantStatus = "Restoring"
	TenantStatusRunning                              TenantStatus = "Running"
	TenantStatusSnapshotting                         TenantStatus = "Snapshotting"
	TenantStatusStarting                             TenantStatus = "Starting"
	TenantStatusStopped                              TenantStatus = "Stopped"
	TenantStatusStopping                             TenantStatus = "Stopping"
	TenantStatusUpdating                             TenantStatus = "Updating"
	TenantStatusUpgrading                            TenantStatus = "Upgrading"
)

// Defines values for TenantUsageType.
const (
	General  TenantUsageType = "general"
	Pipeline TenantUsageType = "pipeline"
)

// Defines values for TierId.
const (
	BYOC           TierId = "BYOC"
	Benchmark      TierId = "Benchmark"
	DeveloperBasic TierId = "Developer-Basic"
	DeveloperFree  TierId = "Developer-Free"
	DeveloperTest  TierId = "Developer-Test"
	Free           TierId = "Free"
	Invited        TierId = "Invited"
	Standard       TierId = "Standard"
	Test           TierId = "Test"
)

// Defines values for WorkflowRunningState.
const (
	CancelledWorkflow WorkflowRunningState = "CancelledWorkflow"
	CompletedWorkflow WorkflowRunningState = "CompletedWorkflow"
	FailedWorkflow    WorkflowRunningState = "FailedWorkflow"
	RunningWorkflow   WorkflowRunningState = "RunningWorkflow"
)

// ClusterAccessInfo defines model for ClusterAccessInfo.
type ClusterAccessInfo struct {
	CaCertBase64 string `json:"caCertBase64"`
	Endpoint     string `json:"endpoint"`
	Token        string `json:"token"`
}

// ClusterReadyStatus defines model for ClusterReadyStatus.
type ClusterReadyStatus string

// ClusterStatus defines model for ClusterStatus.
type ClusterStatus string

// ComponentResource defines model for ComponentResource.
type ComponentResource struct {
	ComponentTypeId string `json:"componentTypeId"`
	Cpu             string `json:"cpu"`
	Memory          string `json:"memory"`
	Replica         int    `json:"replica"`
}

// ComponentResourceRequest defines model for ComponentResourceRequest.
type ComponentResourceRequest struct {
	ComponentTypeId string `json:"componentTypeId"`
	Replica         int    `json:"replica"`
}

// CreateResourceGroupsRequestBody defines model for CreateResourceGroupsRequestBody.
type CreateResourceGroupsRequestBody struct {
	FileCacheSizeGb *int                     `json:"fileCacheSizeGb,omitempty"`
	Name            string                   `json:"name"`
	Resource        ComponentResourceRequest `json:"resource"`
}

// DatabaseConnectionUrl defines model for DatabaseConnectionUrl.
type DatabaseConnectionUrl struct {
	Url string `json:"url"`
}

// GetTenantExtensionCompactionParametersResponseBody defines model for GetTenantExtensionCompactionParametersResponseBody.
type GetTenantExtensionCompactionParametersResponseBody struct {
	Compactor *PostTenantExtensionCompactionCompactorRequestBody `json:"Compactor,omitempty"`
	Scaler    *PostTenantExtensionCompactionScalerRequestBody    `json:"Scaler,omitempty"`
}

// GetTenantExtensionCompactionStatusResponseBody defines model for GetTenantExtensionCompactionStatusResponseBody.
type GetTenantExtensionCompactionStatusResponseBody struct {
	Status string `json:"status"`
}

// IcebergCompaction defines model for IcebergCompaction.
type IcebergCompaction struct {
	Config    *string            `json:"config,omitempty"`
	Resources *ComponentResource `json:"resources,omitempty"`
	Status    string             `json:"status"`
}

// ManagedCluster defines model for ManagedCluster.
type ManagedCluster struct {
	ClusterServiceAccount string             `json:"cluster_service_account"`
	Id                    uint64             `json:"id"`
	MasterUrl             string             `json:"master_url"`
	Name                  string             `json:"name"`
	Org                   openapi_types.UUID `json:"org"`
	ServingType           string             `json:"serving_type"`
	Settings              map[string]string  `json:"settings"`
	Status                ClusterStatus      `json:"status"`
	Token                 string             `json:"token"`
}

// ManagedClusterArray defines model for ManagedClusterArray.
type ManagedClusterArray = []ManagedCluster

// ManagedClusters defines model for ManagedClusters.
type ManagedClusters struct {
	Clusters ManagedClusterArray `json:"clusters"`
}

// MetaStoreAwsRds defines model for MetaStoreAwsRds.
type MetaStoreAwsRds struct {
	InstanceClass string `json:"instanceClass"`
	SizeGb        int    `json:"sizeGb"`
}

// MetaStoreAzrPostgres defines model for MetaStoreAzrPostgres.
type MetaStoreAzrPostgres struct {
	SizeGb int    `json:"sizeGb"`
	Sku    string `json:"sku"`
}

// MetaStoreEtcd defines model for MetaStoreEtcd.
type MetaStoreEtcd struct {
	Resource ComponentResource `json:"resource"`
	SizeGb   int               `json:"sizeGb"`
}

// MetaStoreGcpCloudSql defines model for MetaStoreGcpCloudSql.
type MetaStoreGcpCloudSql struct {
	SizeGb int    `json:"sizeGb"`
	Tier   string `json:"tier"`
}

// MetaStorePostgreSql defines model for MetaStorePostgreSql.
type MetaStorePostgreSql struct {
	Resource ComponentResource `json:"resource"`
	SizeGb   int               `json:"sizeGb"`
}

// MetaStoreSharingPg defines model for MetaStoreSharingPg.
type MetaStoreSharingPg struct {
	InstanceId string `json:"instanceId"`
}

// MetaStoreType defines model for MetaStoreType.
type MetaStoreType string

// OptionalWorkflowIdResponseBody defines model for OptionalWorkflowIdResponseBody.
type OptionalWorkflowIdResponseBody struct {
	Msg        *string             `json:"msg,omitempty"`
	WorkflowId *openapi_types.UUID `json:"workflowId,omitempty"`
}

// Page defines model for Page.
type Page struct {
	Limit  uint64 `json:"limit"`
	Offset uint64 `json:"offset"`
}

// PostAlertNotificationRequestBody defines model for PostAlertNotificationRequestBody.
type PostAlertNotificationRequestBody struct {
	Name       string            `json:"name"`
	Properties map[string]string `json:"properties"`
}

// PostByocClusterAccessRequestBody defines model for PostByocClusterAccessRequestBody.
type PostByocClusterAccessRequestBody struct {
	// TimeoutMins timeout in minutes
	TimeoutMins int `json:"timeoutMins"`
}

// PostByocClusterUpdateRequestBody defines model for PostByocClusterUpdateRequestBody.
type PostByocClusterUpdateRequestBody struct {
	// CustomSettings base64 encoded custom settings
	CustomSettings *string `json:"customSettings,omitempty"`
	Version        *string `json:"version,omitempty"`
}

// PostClusterRequestBody defines model for PostClusterRequestBody.
type PostClusterRequestBody struct {
	ClusterServiceAccount string            `json:"cluster_service_account"`
	MasterUrl             string            `json:"master_url"`
	Name                  string            `json:"name"`
	ServingType           string            `json:"serving_type"`
	Settings              map[string]string `json:"settings"`
	Token                 string            `json:"token"`
}

// PostSnapshotResponseBody defines model for PostSnapshotResponseBody.
type PostSnapshotResponseBody struct {
	SnapshotId openapi_types.UUID `json:"snapshotId"`
	WorkflowId openapi_types.UUID `json:"workflowId"`
}

// PostTenantComputeCacheRequestBody defines model for PostTenantComputeCacheRequestBody.
type PostTenantComputeCacheRequestBody struct {
	ComputeCacheSpec *TenantResourceComputeCache `json:"computeCacheSpec,omitempty"`
	ResourceGroups   *[]struct {
		ComputeCacheSpec TenantResourceComputeCache `json:"computeCacheSpec"`
		Name             string                     `json:"name"`
	} `json:"resourceGroups,omitempty"`
}

// PostTenantExpireRequestBody defines model for PostTenantExpireRequestBody.
type PostTenantExpireRequestBody struct {
	DeleteAt *time.Time `json:"deleteAt,omitempty"`
	ExpireAt *time.Time `json:"expireAt,omitempty"`
}

// PostTenantExtensionCompactionCompactorRequestBody defines model for PostTenantExtensionCompactionCompactorRequestBody.
type PostTenantExtensionCompactionCompactorRequestBody struct {
	CpuLimit      string `json:"CpuLimit"`
	CpuRequest    string `json:"CpuRequest"`
	MemoryLimit   string `json:"MemoryLimit"`
	MemoryRequest string `json:"MemoryRequest"`
}

// PostTenantExtensionCompactionRequestBody defines model for PostTenantExtensionCompactionRequestBody.
type PostTenantExtensionCompactionRequestBody struct {
	Compactor *PostTenantExtensionCompactionCompactorRequestBody `json:"Compactor,omitempty"`
	Scaler    *PostTenantExtensionCompactionScalerRequestBody    `json:"Scaler,omitempty"`
}

// PostTenantExtensionCompactionScalerRequestBody defines model for PostTenantExtensionCompactionScalerRequestBody.
type PostTenantExtensionCompactionScalerRequestBody struct {
	CollectInterval               int `json:"CollectInterval"`
	CoolDownPeriod                int `json:"CoolDownPeriod"`
	DefaultCapacityReservedBuffer int `json:"DefaultCapacityReservedBuffer"`
	DefaultParallelism            int `json:"DefaultParallelism"`
	DesiredReplicas               int `json:"DesiredReplicas"`
	ExpirationExpireTime          int `json:"ExpirationExpireTime"`
	MaxReplicas                   int `json:"MaxReplicas"`
	MinReplicas                   int `json:"MinReplicas"`
	PollingInterval               int `json:"PollingInterval"`
	ScaleDownToNRequiredTimes     int `json:"ScaleDownToNRequiredTimes"`
	ScaleDownToZeroRequiredTimes  int `json:"ScaleDownToZeroRequiredTimes"`
}

// PostTenantExtensionCompactionStatusRequestBody defines model for PostTenantExtensionCompactionStatusRequestBody.
type PostTenantExtensionCompactionStatusRequestBody struct {
	Previous string `json:"previous"`
	Target   string `json:"target"`
}

// PostTenantResourcesRequestBody defines model for PostTenantResourcesRequestBody.
type PostTenantResourcesRequestBody struct {
	Compactor  *ComponentResourceRequest `json:"compactor,omitempty"`
	Compute    *ComponentResourceRequest `json:"compute,omitempty"`
	Frontend   *ComponentResourceRequest `json:"frontend,omitempty"`
	Meta       *ComponentResourceRequest `json:"meta,omitempty"`
	Standalone *ComponentResourceRequest `json:"standalone,omitempty"`
}

// PostTenantRestoreRequestBody configuration for new tenant to be created in restore command
type PostTenantRestoreRequestBody struct {
	NewTenantName string `json:"newTenantName"`
}

// PostTenantStatusRequestBody defines model for PostTenantStatusRequestBody.
type PostTenantStatusRequestBody struct {
	Previous string `json:"previous"`
	Target   string `json:"target"`
}

// PostTenantTierRequestBody defines model for PostTenantTierRequestBody.
type PostTenantTierRequestBody struct {
	Previous string `json:"previous"`
	Target   string `json:"target"`
}

// PostTenantUpdateVersionRequestBody defines model for PostTenantUpdateVersionRequestBody.
type PostTenantUpdateVersionRequestBody struct {
	SkipBackup *bool  `json:"skipBackup,omitempty"`
	Version    string `json:"version"`
}

// PostWorkflowRerunRequestBody defines model for PostWorkflowRerunRequestBody.
type PostWorkflowRerunRequestBody struct {
	// Context context will be merged into the previous context
	Context   *map[string]interface{} `json:"context,omitempty"`
	DelayAt   *time.Time              `json:"delayAt,omitempty"`
	FromState *string                 `json:"fromState,omitempty"`
}

// PostWorkflowScheduleRequestBody defines model for PostWorkflowScheduleRequestBody.
type PostWorkflowScheduleRequestBody struct {
	DelayAt *time.Time `json:"delayAt"`
}

// RwDiagReport defines model for RwDiagReport.
type RwDiagReport struct {
	Meta            RwDiagReportMeta `json:"meta"`
	PresignedGetUrl string           `json:"presignedGetUrl"`
}

// RwDiagReportMeta defines model for RwDiagReportMeta.
type RwDiagReportMeta struct {
	// Id can be unix timestamp seconds, or other things
	Id          uint64    `json:"id"`
	ProcessedAt time.Time `json:"processedAt"`
	TenantId    uint64    `json:"tenantId"`
}

// RwDiagReportMetaPage defines model for RwDiagReportMetaPage.
type RwDiagReportMetaPage struct {
	Limit  *uint64            `json:"limit,omitempty"`
	Metas  []RwDiagReportMeta `json:"metas"`
	Offset *uint64            `json:"offset,omitempty"`
}

// Tenant defines model for Tenant.
type Tenant struct {
	ClusterName          *string            `json:"clusterName,omitempty"`
	CreatedAt            time.Time          `json:"createdAt"`
	EtcdConfig           string             `json:"etcd_config"`
	HealthStatus         TenantHealthStatus `json:"health_status"`
	Id                   uint64             `json:"id"`
	ImageTag             string             `json:"imageTag"`
	LatestImageTag       string             `json:"latestImageTag"`
	NsId                 openapi_types.UUID `json:"nsId"`
	OrgId                openapi_types.UUID `json:"orgId"`
	Region               string             `json:"region"`
	Resources            TenantResource     `json:"resources"`
	RwConfig             string             `json:"rw_config"`
	Status               TenantStatus       `json:"status"`
	TenantName           string             `json:"tenantName"`
	Tier                 TierId             `json:"tier"`
	UpcomingSnapshotTime *time.Time         `json:"upcomingSnapshotTime,omitempty"`
	UpdatedAt            time.Time          `json:"updatedAt"`
	UsageType            TenantUsageType    `json:"usageType"`
	UserId               uint64             `json:"userId"`
}

// TenantHealthStatus defines model for Tenant.HealthStatus.
type TenantHealthStatus string

// TenantStatus defines model for Tenant.Status.
type TenantStatus string

// TenantUsageType defines model for Tenant.UsageType.
type TenantUsageType string

// TenantArray defines model for TenantArray.
type TenantArray = []Tenant

// TenantExpireInfo defines model for TenantExpireInfo.
type TenantExpireInfo struct {
	DeleteAt *time.Time `json:"deleteAt"`
	ExpireAt *time.Time `json:"expireAt"`
}

// TenantPage defines model for TenantPage.
type TenantPage struct {
	Limit   uint64      `json:"limit"`
	Offset  uint64      `json:"offset"`
	Tenants TenantArray `json:"tenants"`
}

// TenantResource defines model for TenantResource.
type TenantResource struct {
	Components        TenantResourceComponents   `json:"components"`
	ComputeCache      TenantResourceComputeCache `json:"computeCache"`
	EtcdVolumeSizeGiB *int                       `json:"etcdVolumeSizeGiB,omitempty"`
	MetaStore         *TenantResourceMetaStore   `json:"metaStore,omitempty"`
	ResourceGroups    *TenantResourceGroupArray  `json:"resourceGroups,omitempty"`
}

// TenantResourceComponents defines model for TenantResourceComponents.
type TenantResourceComponents struct {
	Compactor  *ComponentResource `json:"compactor,omitempty"`
	Compute    *ComponentResource `json:"compute,omitempty"`
	Etcd       *ComponentResource `json:"etcd,omitempty"`
	Frontend   *ComponentResource `json:"frontend,omitempty"`
	Meta       *ComponentResource `json:"meta,omitempty"`
	Standalone *ComponentResource `json:"standalone,omitempty"`
}

// TenantResourceComputeCache defines model for TenantResourceComputeCache.
type TenantResourceComputeCache struct {
	SizeGb int `json:"sizeGb"`
}

// TenantResourceGroup defines model for TenantResourceGroup.
type TenantResourceGroup struct {
	ComputeCache TenantResourceComputeCache `json:"computeCache"`
	Name         string                     `json:"name"`
	Resource     ComponentResource          `json:"resource"`
}

// TenantResourceGroupArray defines model for TenantResourceGroupArray.
type TenantResourceGroupArray = []TenantResourceGroup

// TenantResourceMetaStore defines model for TenantResourceMetaStore.
type TenantResourceMetaStore struct {
	AwsRds      *MetaStoreAwsRds      `json:"aws_rds,omitempty"`
	AzrPostgres *MetaStoreAzrPostgres `json:"azr_postgres,omitempty"`
	Etcd        *MetaStoreEtcd        `json:"etcd,omitempty"`
	GcpCloudsql *MetaStoreGcpCloudSql `json:"gcp_cloudsql,omitempty"`
	Postgresql  *MetaStorePostgreSql  `json:"postgresql,omitempty"`
	Rwu         string                `json:"rwu"`
	SharingPg   *MetaStoreSharingPg   `json:"sharing_pg,omitempty"`
	Type        MetaStoreType         `json:"type"`
}

// TierId defines model for TierId.
type TierId string

// UpdateResourceGroupsRequestBody defines model for UpdateResourceGroupsRequestBody.
type UpdateResourceGroupsRequestBody struct {
	Resource ComponentResourceRequest `json:"resource"`
}

// Workflow defines model for Workflow.
type Workflow struct {
	Context      interface{}          `json:"context"`
	CreatedAt    time.Time            `json:"createdAt"`
	DelayAt      *time.Time           `json:"delayAt,omitempty"`
	FsmState     string               `json:"fsmState"`
	Id           openapi_types.UUID   `json:"id"`
	LockedAt     *time.Time           `json:"lockedAt,omitempty"`
	RunningState WorkflowRunningState `json:"runningState"`
	UpdatedAt    time.Time            `json:"updatedAt"`
	WorkflowType string               `json:"workflowType"`
}

// WorkflowRunningState defines model for Workflow.RunningState.
type WorkflowRunningState string

// WorkflowArray defines model for WorkflowArray.
type WorkflowArray = []Workflow

// WorkflowEvent defines model for WorkflowEvent.
type WorkflowEvent struct {
	Attributes interface{}        `json:"attributes"`
	Id         uint64             `json:"id"`
	Timestamp  time.Time          `json:"timestamp"`
	Type       string             `json:"type"`
	WorkflowId openapi_types.UUID `json:"workflowId"`
}

// WorkflowEventArray defines model for WorkflowEventArray.
type WorkflowEventArray = []WorkflowEvent

// WorkflowIdResponseBody defines model for WorkflowIdResponseBody.
type WorkflowIdResponseBody struct {
	WorkflowId openapi_types.UUID `json:"workflowId"`
}

// WorkflowPage defines model for WorkflowPage.
type WorkflowPage struct {
	Limit     uint64        `json:"limit"`
	Offset    uint64        `json:"offset"`
	Workflows WorkflowArray `json:"workflows"`
}

// WorkflowWithEvents defines model for WorkflowWithEvents.
type WorkflowWithEvents struct {
	Events   WorkflowEventArray `json:"events"`
	Workflow Workflow           `json:"workflow"`
}

// Workflows defines model for Workflows.
type Workflows struct {
	Workflows WorkflowArray `json:"workflows"`
}

// BadRequestResponse defines model for BadRequestResponse.
type BadRequestResponse struct {
	Msg string `json:"msg"`
}

// ClusterStatusResponse defines model for ClusterStatusResponse.
type ClusterStatusResponse struct {
	ReadyStatus ClusterReadyStatus `json:"ready_status"`
}

// DefaultResponse defines model for DefaultResponse.
type DefaultResponse struct {
	Msg string `json:"msg"`
}

// FailedPreconditionResponse defines model for FailedPreconditionResponse.
type FailedPreconditionResponse struct {
	Msg string `json:"msg"`
}

// NotFoundResponse defines model for NotFoundResponse.
type NotFoundResponse struct {
	Msg string `json:"msg"`
}

// WorkflowIdResponse defines model for WorkflowIdResponse.
type WorkflowIdResponse struct {
	WorkflowId openapi_types.UUID `json:"workflowId"`
}

// PutTenantTenantIdConfigEtcdTextBody defines parameters for PutTenantTenantIdConfigEtcd.
type PutTenantTenantIdConfigEtcdTextBody = string

// PutTenantTenantIdConfigRisingwaveTextBody defines parameters for PutTenantTenantIdConfigRisingwave.
type PutTenantTenantIdConfigRisingwaveTextBody = string

// PutTenantTenantIdConfigRisingwaveParams defines parameters for PutTenantTenantIdConfigRisingwave.
type PutTenantTenantIdConfigRisingwaveParams struct {
	Component *string `form:"component,omitempty" json:"component,omitempty"`
	NodeGroup *string `form:"nodeGroup,omitempty" json:"nodeGroup,omitempty"`
}

// PutTenantTenantIdConfigNoRestartRisingwaveTextBody defines parameters for PutTenantTenantIdConfigNoRestartRisingwave.
type PutTenantTenantIdConfigNoRestartRisingwaveTextBody = string

// PutTenantTenantIdConfigNoRestartRisingwaveParams defines parameters for PutTenantTenantIdConfigNoRestartRisingwave.
type PutTenantTenantIdConfigNoRestartRisingwaveParams struct {
	Component *string `form:"component,omitempty" json:"component,omitempty"`
	NodeGroup *string `form:"nodeGroup,omitempty" json:"nodeGroup,omitempty"`
}

// ListDiagReportTimeRangeParams defines parameters for ListDiagReportTimeRange.
type ListDiagReportTimeRangeParams struct {
	// StartTime RFC3339/RFC3339Nano formatted starting time (inclusive)
	StartTime *time.Time `form:"startTime,omitempty" json:"startTime,omitempty"`

	// EndTime RFC3339/RFC3339Nano formatted ending time (inclusive)
	EndTime *time.Time `form:"endTime,omitempty" json:"endTime,omitempty"`

	// Limit pagination - how many report metas to return
	Limit *uint64 `form:"limit,omitempty" json:"limit,omitempty"`

	// Offset pagination - how many report metas to skip
	Offset *uint64 `form:"offset,omitempty" json:"offset,omitempty"`
}

// PostTenantTenantIdExtensionsServerlessbackfillVersionTextBody defines parameters for PostTenantTenantIdExtensionsServerlessbackfillVersion.
type PostTenantTenantIdExtensionsServerlessbackfillVersionTextBody = string

// PostTenantTenantIdUpgradeMetaStoreParams defines parameters for PostTenantTenantIdUpgradeMetaStore.
type PostTenantTenantIdUpgradeMetaStoreParams struct {
	MetaStore string `form:"metaStore" json:"metaStore"`
}

// GetTenantsParams defines parameters for GetTenants.
type GetTenantsParams struct {
	Offset *uint64 `form:"offset,omitempty" json:"offset,omitempty"`
	Limit  *uint64 `form:"limit,omitempty" json:"limit,omitempty"`
}

// PostTenantsTenantIdExtensionsIcebergCompactionJSONBody defines parameters for PostTenantsTenantIdExtensionsIcebergCompaction.
type PostTenantsTenantIdExtensionsIcebergCompactionJSONBody struct {
	Config    *string                   `json:"config,omitempty"`
	Resources *ComponentResourceRequest `json:"resources,omitempty"`
}

// PutTenantsTenantIdExtensionsIcebergCompactionJSONBody defines parameters for PutTenantsTenantIdExtensionsIcebergCompaction.
type PutTenantsTenantIdExtensionsIcebergCompactionJSONBody struct {
	Config    *string                   `json:"config,omitempty"`
	Resources *ComponentResourceRequest `json:"resources,omitempty"`
}

// GetWorkflowsParams defines parameters for GetWorkflows.
type GetWorkflowsParams struct {
	Offset *uint64 `form:"offset,omitempty" json:"offset,omitempty"`
	Limit  *uint64 `form:"limit,omitempty" json:"limit,omitempty"`
}

// PostAlertNotificationsJSONRequestBody defines body for PostAlertNotifications for application/json ContentType.
type PostAlertNotificationsJSONRequestBody = PostAlertNotificationRequestBody

// PostByocClusterIdAccessJSONRequestBody defines body for PostByocClusterIdAccess for application/json ContentType.
type PostByocClusterIdAccessJSONRequestBody = PostByocClusterAccessRequestBody

// PostByocClusterIdUpdateJSONRequestBody defines body for PostByocClusterIdUpdate for application/json ContentType.
type PostByocClusterIdUpdateJSONRequestBody = PostByocClusterUpdateRequestBody

// PostClusterNameJSONRequestBody defines body for PostClusterName for application/json ContentType.
type PostClusterNameJSONRequestBody = PostClusterRequestBody

// PostClustersJSONRequestBody defines body for PostClusters for application/json ContentType.
type PostClustersJSONRequestBody = PostClusterRequestBody

// PostTenantTenantIdBackupSnapshotIdRestoreJSONRequestBody defines body for PostTenantTenantIdBackupSnapshotIdRestore for application/json ContentType.
type PostTenantTenantIdBackupSnapshotIdRestoreJSONRequestBody = PostTenantRestoreRequestBody

// PostTenantTenantIdComputeCacheJSONRequestBody defines body for PostTenantTenantIdComputeCache for application/json ContentType.
type PostTenantTenantIdComputeCacheJSONRequestBody = PostTenantComputeCacheRequestBody

// PutTenantTenantIdConfigEtcdTextRequestBody defines body for PutTenantTenantIdConfigEtcd for text/plain ContentType.
type PutTenantTenantIdConfigEtcdTextRequestBody = PutTenantTenantIdConfigEtcdTextBody

// PutTenantTenantIdConfigRisingwaveTextRequestBody defines body for PutTenantTenantIdConfigRisingwave for text/plain ContentType.
type PutTenantTenantIdConfigRisingwaveTextRequestBody = PutTenantTenantIdConfigRisingwaveTextBody

// PutTenantTenantIdConfigNoRestartRisingwaveTextRequestBody defines body for PutTenantTenantIdConfigNoRestartRisingwave for text/plain ContentType.
type PutTenantTenantIdConfigNoRestartRisingwaveTextRequestBody = PutTenantTenantIdConfigNoRestartRisingwaveTextBody

// PostTenantTenantIdExpireJSONRequestBody defines body for PostTenantTenantIdExpire for application/json ContentType.
type PostTenantTenantIdExpireJSONRequestBody = PostTenantExpireRequestBody

// PostTenantTenantIdExtensionsCompactionStatusJSONRequestBody defines body for PostTenantTenantIdExtensionsCompactionStatus for application/json ContentType.
type PostTenantTenantIdExtensionsCompactionStatusJSONRequestBody = PostTenantExtensionCompactionStatusRequestBody

// PostTenantTenantIdExtensionsCompactionUpdateJSONRequestBody defines body for PostTenantTenantIdExtensionsCompactionUpdate for application/json ContentType.
type PostTenantTenantIdExtensionsCompactionUpdateJSONRequestBody = PostTenantExtensionCompactionRequestBody

// PostTenantTenantIdExtensionsServerlessbackfillVersionTextRequestBody defines body for PostTenantTenantIdExtensionsServerlessbackfillVersion for text/plain ContentType.
type PostTenantTenantIdExtensionsServerlessbackfillVersionTextRequestBody = PostTenantTenantIdExtensionsServerlessbackfillVersionTextBody

// PostTenantTenantIdResourceJSONRequestBody defines body for PostTenantTenantIdResource for application/json ContentType.
type PostTenantTenantIdResourceJSONRequestBody = PostTenantResourcesRequestBody

// PostTenantTenantIdResourceGroupsJSONRequestBody defines body for PostTenantTenantIdResourceGroups for application/json ContentType.
type PostTenantTenantIdResourceGroupsJSONRequestBody = CreateResourceGroupsRequestBody

// PostTenantTenantIdResourceGroupsResourceGroupJSONRequestBody defines body for PostTenantTenantIdResourceGroupsResourceGroup for application/json ContentType.
type PostTenantTenantIdResourceGroupsResourceGroupJSONRequestBody = UpdateResourceGroupsRequestBody

// PostTenantTenantIdStatusJSONRequestBody defines body for PostTenantTenantIdStatus for application/json ContentType.
type PostTenantTenantIdStatusJSONRequestBody = PostTenantStatusRequestBody

// PostTenantTenantIdTierJSONRequestBody defines body for PostTenantTenantIdTier for application/json ContentType.
type PostTenantTenantIdTierJSONRequestBody = PostTenantTierRequestBody

// PostTenantTenantIdUpdateVersionJSONRequestBody defines body for PostTenantTenantIdUpdateVersion for application/json ContentType.
type PostTenantTenantIdUpdateVersionJSONRequestBody = PostTenantUpdateVersionRequestBody

// PostTenantsTenantIdExtensionsIcebergCompactionJSONRequestBody defines body for PostTenantsTenantIdExtensionsIcebergCompaction for application/json ContentType.
type PostTenantsTenantIdExtensionsIcebergCompactionJSONRequestBody PostTenantsTenantIdExtensionsIcebergCompactionJSONBody

// PutTenantsTenantIdExtensionsIcebergCompactionJSONRequestBody defines body for PutTenantsTenantIdExtensionsIcebergCompaction for application/json ContentType.
type PutTenantsTenantIdExtensionsIcebergCompactionJSONRequestBody PutTenantsTenantIdExtensionsIcebergCompactionJSONBody

// PostWorkflowIdRerunJSONRequestBody defines body for PostWorkflowIdRerun for application/json ContentType.
type PostWorkflowIdRerunJSONRequestBody = PostWorkflowRerunRequestBody

// PostWorkflowIdScheduleJSONRequestBody defines body for PostWorkflowIdSchedule for application/json ContentType.
type PostWorkflowIdScheduleJSONRequestBody = PostWorkflowScheduleRequestBody

// RequestEditorFn  is the function signature for the RequestEditor callback function
type RequestEditorFn func(ctx context.Context, req *http.Request) error

// Doer performs HTTP requests.
//
// The standard http.Client implements this interface.
type HttpRequestDoer interface {
	Do(req *http.Request) (*http.Response, error)
}

// Client which conforms to the OpenAPI3 specification for this service.
type Client struct {
	// The endpoint of the server conforming to this interface, with scheme,
	// https://api.deepmap.com for example. This can contain a path relative
	// to the server, such as https://api.deepmap.com/dev-test, and all the
	// paths in the swagger spec will be appended to the server.
	Server string

	// Doer for performing requests, typically a *http.Client with any
	// customized settings, such as certificate chains.
	Client HttpRequestDoer

	// A list of callbacks for modifying requests which are generated before sending over
	// the network.
	RequestEditors []RequestEditorFn
}

// ClientOption allows setting custom parameters during construction
type ClientOption func(*Client) error

// Creates a new Client, with reasonable defaults
func NewClient(server string, opts ...ClientOption) (*Client, error) {
	// create a client with sane default values
	client := Client{
		Server: server,
	}
	// mutate client and add all optional params
	for _, o := range opts {
		if err := o(&client); err != nil {
			return nil, err
		}
	}
	// ensure the server URL always has a trailing slash
	if !strings.HasSuffix(client.Server, "/") {
		client.Server += "/"
	}
	// create httpClient, if not already present
	if client.Client == nil {
		client.Client = &http.Client{}
	}
	return &client, nil
}

// WithHTTPClient allows overriding the default Doer, which is
// automatically created using http.Client. This is useful for tests.
func WithHTTPClient(doer HttpRequestDoer) ClientOption {
	return func(c *Client) error {
		c.Client = doer
		return nil
	}
}

// WithRequestEditorFn allows setting up a callback function, which will be
// called right before sending the request. This can be used to mutate the request.
func WithRequestEditorFn(fn RequestEditorFn) ClientOption {
	return func(c *Client) error {
		c.RequestEditors = append(c.RequestEditors, fn)
		return nil
	}
}

// The interface specification for the client above.
type ClientInterface interface {
	// Get request
	Get(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostAlertNotificationsWithBody request with any body
	PostAlertNotificationsWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostAlertNotifications(ctx context.Context, body PostAlertNotificationsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostByocClusterIdAccessWithBody request with any body
	PostByocClusterIdAccessWithBody(ctx context.Context, id uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostByocClusterIdAccess(ctx context.Context, id uint64, body PostByocClusterIdAccessJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostByocClusterIdUpdateWithBody request with any body
	PostByocClusterIdUpdateWithBody(ctx context.Context, id uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostByocClusterIdUpdate(ctx context.Context, id uint64, body PostByocClusterIdUpdateJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetByocClusters request
	GetByocClusters(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteClusterName request
	DeleteClusterName(ctx context.Context, name string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetClusterName request
	GetClusterName(ctx context.Context, name string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostClusterNameWithBody request with any body
	PostClusterNameWithBody(ctx context.Context, name string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostClusterName(ctx context.Context, name string, body PostClusterNameJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetClusters request
	GetClusters(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostClustersWithBody request with any body
	PostClustersWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostClusters(ctx context.Context, body PostClustersJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetClustersUuidPrometheusApiV1LabelLabelNameValues request
	GetClustersUuidPrometheusApiV1LabelLabelNameValues(ctx context.Context, uuid openapi_types.UUID, labelName string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostClustersUuidPrometheusApiV1LabelLabelNameValues request
	PostClustersUuidPrometheusApiV1LabelLabelNameValues(ctx context.Context, uuid openapi_types.UUID, labelName string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetClustersUuidPrometheusApiV1Query request
	GetClustersUuidPrometheusApiV1Query(ctx context.Context, uuid openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostClustersUuidPrometheusApiV1Query request
	PostClustersUuidPrometheusApiV1Query(ctx context.Context, uuid openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetClustersUuidPrometheusApiV1QueryRange request
	GetClustersUuidPrometheusApiV1QueryRange(ctx context.Context, uuid openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostClustersUuidPrometheusApiV1QueryRange request
	PostClustersUuidPrometheusApiV1QueryRange(ctx context.Context, uuid openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetClustersUuidPrometheusApiV1Series request
	GetClustersUuidPrometheusApiV1Series(ctx context.Context, uuid openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostClustersUuidPrometheusApiV1Series request
	PostClustersUuidPrometheusApiV1Series(ctx context.Context, uuid openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteTenantTenantId request
	DeleteTenantTenantId(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetTenantTenantId request
	GetTenantTenantId(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdBackup request
	PostTenantTenantIdBackup(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteTenantTenantIdBackupSnapshotId request
	DeleteTenantTenantIdBackupSnapshotId(ctx context.Context, tenantId uint64, snapshotId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdBackupSnapshotIdInPlaceRestore request
	PostTenantTenantIdBackupSnapshotIdInPlaceRestore(ctx context.Context, tenantId uint64, snapshotId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdBackupSnapshotIdRestoreWithBody request with any body
	PostTenantTenantIdBackupSnapshotIdRestoreWithBody(ctx context.Context, tenantId uint64, snapshotId openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostTenantTenantIdBackupSnapshotIdRestore(ctx context.Context, tenantId uint64, snapshotId openapi_types.UUID, body PostTenantTenantIdBackupSnapshotIdRestoreJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdComputeCacheWithBody request with any body
	PostTenantTenantIdComputeCacheWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostTenantTenantIdComputeCache(ctx context.Context, tenantId uint64, body PostTenantTenantIdComputeCacheJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutTenantTenantIdConfigEtcdWithBody request with any body
	PutTenantTenantIdConfigEtcdWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PutTenantTenantIdConfigEtcdWithTextBody(ctx context.Context, tenantId uint64, body PutTenantTenantIdConfigEtcdTextRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutTenantTenantIdConfigRisingwaveWithBody request with any body
	PutTenantTenantIdConfigRisingwaveWithBody(ctx context.Context, tenantId uint64, params *PutTenantTenantIdConfigRisingwaveParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PutTenantTenantIdConfigRisingwaveWithTextBody(ctx context.Context, tenantId uint64, params *PutTenantTenantIdConfigRisingwaveParams, body PutTenantTenantIdConfigRisingwaveTextRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutTenantTenantIdConfigNoRestartRisingwaveWithBody request with any body
	PutTenantTenantIdConfigNoRestartRisingwaveWithBody(ctx context.Context, tenantId uint64, params *PutTenantTenantIdConfigNoRestartRisingwaveParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PutTenantTenantIdConfigNoRestartRisingwaveWithTextBody(ctx context.Context, tenantId uint64, params *PutTenantTenantIdConfigNoRestartRisingwaveParams, body PutTenantTenantIdConfigNoRestartRisingwaveTextRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GenDiagReport request
	GenDiagReport(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// ListDiagReportTimeRange request
	ListDiagReportTimeRange(ctx context.Context, tenantId uint64, params *ListDiagReportTimeRangeParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetDiagReport request
	GetDiagReport(ctx context.Context, tenantId uint64, reportId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetTenantTenantIdEndpoint request
	GetTenantTenantIdEndpoint(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteTenantTenantIdExpire request
	DeleteTenantTenantIdExpire(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetTenantTenantIdExpire request
	GetTenantTenantIdExpire(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdExpireWithBody request with any body
	PostTenantTenantIdExpireWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostTenantTenantIdExpire(ctx context.Context, tenantId uint64, body PostTenantTenantIdExpireJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdExtensionsCompactionDisable request
	PostTenantTenantIdExtensionsCompactionDisable(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdExtensionsCompactionEnable request
	PostTenantTenantIdExtensionsCompactionEnable(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetTenantTenantIdExtensionsCompactionParameters request
	GetTenantTenantIdExtensionsCompactionParameters(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetTenantTenantIdExtensionsCompactionStatus request
	GetTenantTenantIdExtensionsCompactionStatus(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdExtensionsCompactionStatusWithBody request with any body
	PostTenantTenantIdExtensionsCompactionStatusWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostTenantTenantIdExtensionsCompactionStatus(ctx context.Context, tenantId uint64, body PostTenantTenantIdExtensionsCompactionStatusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdExtensionsCompactionUpdateWithBody request with any body
	PostTenantTenantIdExtensionsCompactionUpdateWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostTenantTenantIdExtensionsCompactionUpdate(ctx context.Context, tenantId uint64, body PostTenantTenantIdExtensionsCompactionUpdateJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdExtensionsServerlessbackfillDisable request
	PostTenantTenantIdExtensionsServerlessbackfillDisable(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdExtensionsServerlessbackfillEnable request
	PostTenantTenantIdExtensionsServerlessbackfillEnable(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdExtensionsServerlessbackfillVersionWithBody request with any body
	PostTenantTenantIdExtensionsServerlessbackfillVersionWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostTenantTenantIdExtensionsServerlessbackfillVersionWithTextBody(ctx context.Context, tenantId uint64, body PostTenantTenantIdExtensionsServerlessbackfillVersionTextRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdMigrateMetaStore request
	PostTenantTenantIdMigrateMetaStore(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteTenantTenantIdPrivatelinkPrivateLinkId request
	DeleteTenantTenantIdPrivatelinkPrivateLinkId(ctx context.Context, tenantId uint64, privateLinkId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdResourceWithBody request with any body
	PostTenantTenantIdResourceWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostTenantTenantIdResource(ctx context.Context, tenantId uint64, body PostTenantTenantIdResourceJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdResourceGroupsWithBody request with any body
	PostTenantTenantIdResourceGroupsWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostTenantTenantIdResourceGroups(ctx context.Context, tenantId uint64, body PostTenantTenantIdResourceGroupsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteTenantTenantIdResourceGroupsResourceGroup request
	DeleteTenantTenantIdResourceGroupsResourceGroup(ctx context.Context, tenantId uint64, resourceGroup string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdResourceGroupsResourceGroupWithBody request with any body
	PostTenantTenantIdResourceGroupsResourceGroupWithBody(ctx context.Context, tenantId uint64, resourceGroup string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostTenantTenantIdResourceGroupsResourceGroup(ctx context.Context, tenantId uint64, resourceGroup string, body PostTenantTenantIdResourceGroupsResourceGroupJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdRestart request
	PostTenantTenantIdRestart(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdStart request
	PostTenantTenantIdStart(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdStatusWithBody request with any body
	PostTenantTenantIdStatusWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostTenantTenantIdStatus(ctx context.Context, tenantId uint64, body PostTenantTenantIdStatusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdStop request
	PostTenantTenantIdStop(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdTierWithBody request with any body
	PostTenantTenantIdTierWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostTenantTenantIdTier(ctx context.Context, tenantId uint64, body PostTenantTenantIdTierJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdUpdateVersionWithBody request with any body
	PostTenantTenantIdUpdateVersionWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostTenantTenantIdUpdateVersion(ctx context.Context, tenantId uint64, body PostTenantTenantIdUpdateVersionJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdUpgradeMetaStore request
	PostTenantTenantIdUpgradeMetaStore(ctx context.Context, tenantId uint64, params *PostTenantTenantIdUpgradeMetaStoreParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetTenants request
	GetTenants(ctx context.Context, params *GetTenantsParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteTenantsTenantIdExtensionsIcebergCompaction request
	DeleteTenantsTenantIdExtensionsIcebergCompaction(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetTenantsTenantIdExtensionsIcebergCompaction request
	GetTenantsTenantIdExtensionsIcebergCompaction(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantsTenantIdExtensionsIcebergCompactionWithBody request with any body
	PostTenantsTenantIdExtensionsIcebergCompactionWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostTenantsTenantIdExtensionsIcebergCompaction(ctx context.Context, tenantId uint64, body PostTenantsTenantIdExtensionsIcebergCompactionJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutTenantsTenantIdExtensionsIcebergCompactionWithBody request with any body
	PutTenantsTenantIdExtensionsIcebergCompactionWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PutTenantsTenantIdExtensionsIcebergCompaction(ctx context.Context, tenantId uint64, body PutTenantsTenantIdExtensionsIcebergCompactionJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetVersion request
	GetVersion(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostWebhooksAlert request
	PostWebhooksAlert(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetWorkflowId request
	GetWorkflowId(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostWorkflowIdCancel request
	PostWorkflowIdCancel(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostWorkflowIdRerunWithBody request with any body
	PostWorkflowIdRerunWithBody(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostWorkflowIdRerun(ctx context.Context, id openapi_types.UUID, body PostWorkflowIdRerunJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostWorkflowIdResume request
	PostWorkflowIdResume(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostWorkflowIdScheduleWithBody request with any body
	PostWorkflowIdScheduleWithBody(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostWorkflowIdSchedule(ctx context.Context, id openapi_types.UUID, body PostWorkflowIdScheduleJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetWorkflows request
	GetWorkflows(ctx context.Context, params *GetWorkflowsParams, reqEditors ...RequestEditorFn) (*http.Response, error)
}

func (c *Client) Get(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostAlertNotificationsWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostAlertNotificationsRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostAlertNotifications(ctx context.Context, body PostAlertNotificationsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostAlertNotificationsRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostByocClusterIdAccessWithBody(ctx context.Context, id uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostByocClusterIdAccessRequestWithBody(c.Server, id, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostByocClusterIdAccess(ctx context.Context, id uint64, body PostByocClusterIdAccessJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostByocClusterIdAccessRequest(c.Server, id, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostByocClusterIdUpdateWithBody(ctx context.Context, id uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostByocClusterIdUpdateRequestWithBody(c.Server, id, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostByocClusterIdUpdate(ctx context.Context, id uint64, body PostByocClusterIdUpdateJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostByocClusterIdUpdateRequest(c.Server, id, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetByocClusters(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetByocClustersRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteClusterName(ctx context.Context, name string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteClusterNameRequest(c.Server, name)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetClusterName(ctx context.Context, name string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetClusterNameRequest(c.Server, name)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostClusterNameWithBody(ctx context.Context, name string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostClusterNameRequestWithBody(c.Server, name, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostClusterName(ctx context.Context, name string, body PostClusterNameJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostClusterNameRequest(c.Server, name, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetClusters(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetClustersRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostClustersWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostClustersRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostClusters(ctx context.Context, body PostClustersJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostClustersRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetClustersUuidPrometheusApiV1LabelLabelNameValues(ctx context.Context, uuid openapi_types.UUID, labelName string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetClustersUuidPrometheusApiV1LabelLabelNameValuesRequest(c.Server, uuid, labelName)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostClustersUuidPrometheusApiV1LabelLabelNameValues(ctx context.Context, uuid openapi_types.UUID, labelName string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostClustersUuidPrometheusApiV1LabelLabelNameValuesRequest(c.Server, uuid, labelName)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetClustersUuidPrometheusApiV1Query(ctx context.Context, uuid openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetClustersUuidPrometheusApiV1QueryRequest(c.Server, uuid)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostClustersUuidPrometheusApiV1Query(ctx context.Context, uuid openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostClustersUuidPrometheusApiV1QueryRequest(c.Server, uuid)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetClustersUuidPrometheusApiV1QueryRange(ctx context.Context, uuid openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetClustersUuidPrometheusApiV1QueryRangeRequest(c.Server, uuid)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostClustersUuidPrometheusApiV1QueryRange(ctx context.Context, uuid openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostClustersUuidPrometheusApiV1QueryRangeRequest(c.Server, uuid)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetClustersUuidPrometheusApiV1Series(ctx context.Context, uuid openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetClustersUuidPrometheusApiV1SeriesRequest(c.Server, uuid)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostClustersUuidPrometheusApiV1Series(ctx context.Context, uuid openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostClustersUuidPrometheusApiV1SeriesRequest(c.Server, uuid)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteTenantTenantId(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteTenantTenantIdRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetTenantTenantId(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetTenantTenantIdRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdBackup(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdBackupRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteTenantTenantIdBackupSnapshotId(ctx context.Context, tenantId uint64, snapshotId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteTenantTenantIdBackupSnapshotIdRequest(c.Server, tenantId, snapshotId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdBackupSnapshotIdInPlaceRestore(ctx context.Context, tenantId uint64, snapshotId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdBackupSnapshotIdInPlaceRestoreRequest(c.Server, tenantId, snapshotId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdBackupSnapshotIdRestoreWithBody(ctx context.Context, tenantId uint64, snapshotId openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdBackupSnapshotIdRestoreRequestWithBody(c.Server, tenantId, snapshotId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdBackupSnapshotIdRestore(ctx context.Context, tenantId uint64, snapshotId openapi_types.UUID, body PostTenantTenantIdBackupSnapshotIdRestoreJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdBackupSnapshotIdRestoreRequest(c.Server, tenantId, snapshotId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdComputeCacheWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdComputeCacheRequestWithBody(c.Server, tenantId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdComputeCache(ctx context.Context, tenantId uint64, body PostTenantTenantIdComputeCacheJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdComputeCacheRequest(c.Server, tenantId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutTenantTenantIdConfigEtcdWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutTenantTenantIdConfigEtcdRequestWithBody(c.Server, tenantId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutTenantTenantIdConfigEtcdWithTextBody(ctx context.Context, tenantId uint64, body PutTenantTenantIdConfigEtcdTextRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutTenantTenantIdConfigEtcdRequestWithTextBody(c.Server, tenantId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutTenantTenantIdConfigRisingwaveWithBody(ctx context.Context, tenantId uint64, params *PutTenantTenantIdConfigRisingwaveParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutTenantTenantIdConfigRisingwaveRequestWithBody(c.Server, tenantId, params, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutTenantTenantIdConfigRisingwaveWithTextBody(ctx context.Context, tenantId uint64, params *PutTenantTenantIdConfigRisingwaveParams, body PutTenantTenantIdConfigRisingwaveTextRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutTenantTenantIdConfigRisingwaveRequestWithTextBody(c.Server, tenantId, params, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutTenantTenantIdConfigNoRestartRisingwaveWithBody(ctx context.Context, tenantId uint64, params *PutTenantTenantIdConfigNoRestartRisingwaveParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutTenantTenantIdConfigNoRestartRisingwaveRequestWithBody(c.Server, tenantId, params, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutTenantTenantIdConfigNoRestartRisingwaveWithTextBody(ctx context.Context, tenantId uint64, params *PutTenantTenantIdConfigNoRestartRisingwaveParams, body PutTenantTenantIdConfigNoRestartRisingwaveTextRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutTenantTenantIdConfigNoRestartRisingwaveRequestWithTextBody(c.Server, tenantId, params, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GenDiagReport(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGenDiagReportRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) ListDiagReportTimeRange(ctx context.Context, tenantId uint64, params *ListDiagReportTimeRangeParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewListDiagReportTimeRangeRequest(c.Server, tenantId, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetDiagReport(ctx context.Context, tenantId uint64, reportId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetDiagReportRequest(c.Server, tenantId, reportId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetTenantTenantIdEndpoint(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetTenantTenantIdEndpointRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteTenantTenantIdExpire(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteTenantTenantIdExpireRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetTenantTenantIdExpire(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetTenantTenantIdExpireRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdExpireWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdExpireRequestWithBody(c.Server, tenantId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdExpire(ctx context.Context, tenantId uint64, body PostTenantTenantIdExpireJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdExpireRequest(c.Server, tenantId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdExtensionsCompactionDisable(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdExtensionsCompactionDisableRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdExtensionsCompactionEnable(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdExtensionsCompactionEnableRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetTenantTenantIdExtensionsCompactionParameters(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetTenantTenantIdExtensionsCompactionParametersRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetTenantTenantIdExtensionsCompactionStatus(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetTenantTenantIdExtensionsCompactionStatusRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdExtensionsCompactionStatusWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdExtensionsCompactionStatusRequestWithBody(c.Server, tenantId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdExtensionsCompactionStatus(ctx context.Context, tenantId uint64, body PostTenantTenantIdExtensionsCompactionStatusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdExtensionsCompactionStatusRequest(c.Server, tenantId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdExtensionsCompactionUpdateWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdExtensionsCompactionUpdateRequestWithBody(c.Server, tenantId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdExtensionsCompactionUpdate(ctx context.Context, tenantId uint64, body PostTenantTenantIdExtensionsCompactionUpdateJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdExtensionsCompactionUpdateRequest(c.Server, tenantId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdExtensionsServerlessbackfillDisable(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdExtensionsServerlessbackfillDisableRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdExtensionsServerlessbackfillEnable(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdExtensionsServerlessbackfillEnableRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdExtensionsServerlessbackfillVersionWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdExtensionsServerlessbackfillVersionRequestWithBody(c.Server, tenantId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdExtensionsServerlessbackfillVersionWithTextBody(ctx context.Context, tenantId uint64, body PostTenantTenantIdExtensionsServerlessbackfillVersionTextRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdExtensionsServerlessbackfillVersionRequestWithTextBody(c.Server, tenantId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdMigrateMetaStore(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdMigrateMetaStoreRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteTenantTenantIdPrivatelinkPrivateLinkId(ctx context.Context, tenantId uint64, privateLinkId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteTenantTenantIdPrivatelinkPrivateLinkIdRequest(c.Server, tenantId, privateLinkId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdResourceWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdResourceRequestWithBody(c.Server, tenantId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdResource(ctx context.Context, tenantId uint64, body PostTenantTenantIdResourceJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdResourceRequest(c.Server, tenantId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdResourceGroupsWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdResourceGroupsRequestWithBody(c.Server, tenantId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdResourceGroups(ctx context.Context, tenantId uint64, body PostTenantTenantIdResourceGroupsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdResourceGroupsRequest(c.Server, tenantId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteTenantTenantIdResourceGroupsResourceGroup(ctx context.Context, tenantId uint64, resourceGroup string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteTenantTenantIdResourceGroupsResourceGroupRequest(c.Server, tenantId, resourceGroup)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdResourceGroupsResourceGroupWithBody(ctx context.Context, tenantId uint64, resourceGroup string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdResourceGroupsResourceGroupRequestWithBody(c.Server, tenantId, resourceGroup, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdResourceGroupsResourceGroup(ctx context.Context, tenantId uint64, resourceGroup string, body PostTenantTenantIdResourceGroupsResourceGroupJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdResourceGroupsResourceGroupRequest(c.Server, tenantId, resourceGroup, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdRestart(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdRestartRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdStart(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdStartRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdStatusWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdStatusRequestWithBody(c.Server, tenantId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdStatus(ctx context.Context, tenantId uint64, body PostTenantTenantIdStatusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdStatusRequest(c.Server, tenantId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdStop(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdStopRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdTierWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdTierRequestWithBody(c.Server, tenantId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdTier(ctx context.Context, tenantId uint64, body PostTenantTenantIdTierJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdTierRequest(c.Server, tenantId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdUpdateVersionWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdUpdateVersionRequestWithBody(c.Server, tenantId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdUpdateVersion(ctx context.Context, tenantId uint64, body PostTenantTenantIdUpdateVersionJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdUpdateVersionRequest(c.Server, tenantId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdUpgradeMetaStore(ctx context.Context, tenantId uint64, params *PostTenantTenantIdUpgradeMetaStoreParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdUpgradeMetaStoreRequest(c.Server, tenantId, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetTenants(ctx context.Context, params *GetTenantsParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetTenantsRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteTenantsTenantIdExtensionsIcebergCompaction(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteTenantsTenantIdExtensionsIcebergCompactionRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetTenantsTenantIdExtensionsIcebergCompaction(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetTenantsTenantIdExtensionsIcebergCompactionRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantsTenantIdExtensionsIcebergCompactionWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantsTenantIdExtensionsIcebergCompactionRequestWithBody(c.Server, tenantId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantsTenantIdExtensionsIcebergCompaction(ctx context.Context, tenantId uint64, body PostTenantsTenantIdExtensionsIcebergCompactionJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantsTenantIdExtensionsIcebergCompactionRequest(c.Server, tenantId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutTenantsTenantIdExtensionsIcebergCompactionWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutTenantsTenantIdExtensionsIcebergCompactionRequestWithBody(c.Server, tenantId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutTenantsTenantIdExtensionsIcebergCompaction(ctx context.Context, tenantId uint64, body PutTenantsTenantIdExtensionsIcebergCompactionJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutTenantsTenantIdExtensionsIcebergCompactionRequest(c.Server, tenantId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetVersion(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetVersionRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostWebhooksAlert(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostWebhooksAlertRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetWorkflowId(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetWorkflowIdRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostWorkflowIdCancel(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostWorkflowIdCancelRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostWorkflowIdRerunWithBody(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostWorkflowIdRerunRequestWithBody(c.Server, id, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostWorkflowIdRerun(ctx context.Context, id openapi_types.UUID, body PostWorkflowIdRerunJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostWorkflowIdRerunRequest(c.Server, id, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostWorkflowIdResume(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostWorkflowIdResumeRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostWorkflowIdScheduleWithBody(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostWorkflowIdScheduleRequestWithBody(c.Server, id, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostWorkflowIdSchedule(ctx context.Context, id openapi_types.UUID, body PostWorkflowIdScheduleJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostWorkflowIdScheduleRequest(c.Server, id, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetWorkflows(ctx context.Context, params *GetWorkflowsParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetWorkflowsRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

// NewGetRequest generates requests for Get
func NewGetRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostAlertNotificationsRequest calls the generic PostAlertNotifications builder with application/json body
func NewPostAlertNotificationsRequest(server string, body PostAlertNotificationsJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostAlertNotificationsRequestWithBody(server, "application/json", bodyReader)
}

// NewPostAlertNotificationsRequestWithBody generates requests for PostAlertNotifications with any type of body
func NewPostAlertNotificationsRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/alertNotifications")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostByocClusterIdAccessRequest calls the generic PostByocClusterIdAccess builder with application/json body
func NewPostByocClusterIdAccessRequest(server string, id uint64, body PostByocClusterIdAccessJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostByocClusterIdAccessRequestWithBody(server, id, "application/json", bodyReader)
}

// NewPostByocClusterIdAccessRequestWithBody generates requests for PostByocClusterIdAccess with any type of body
func NewPostByocClusterIdAccessRequestWithBody(server string, id uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/byoc-cluster/%s/access", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostByocClusterIdUpdateRequest calls the generic PostByocClusterIdUpdate builder with application/json body
func NewPostByocClusterIdUpdateRequest(server string, id uint64, body PostByocClusterIdUpdateJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostByocClusterIdUpdateRequestWithBody(server, id, "application/json", bodyReader)
}

// NewPostByocClusterIdUpdateRequestWithBody generates requests for PostByocClusterIdUpdate with any type of body
func NewPostByocClusterIdUpdateRequestWithBody(server string, id uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/byoc-cluster/%s/update", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetByocClustersRequest generates requests for GetByocClusters
func NewGetByocClustersRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/byoc-clusters")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewDeleteClusterNameRequest generates requests for DeleteClusterName
func NewDeleteClusterNameRequest(server string, name string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "name", runtime.ParamLocationPath, name)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/cluster/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetClusterNameRequest generates requests for GetClusterName
func NewGetClusterNameRequest(server string, name string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "name", runtime.ParamLocationPath, name)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/cluster/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostClusterNameRequest calls the generic PostClusterName builder with application/json body
func NewPostClusterNameRequest(server string, name string, body PostClusterNameJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostClusterNameRequestWithBody(server, name, "application/json", bodyReader)
}

// NewPostClusterNameRequestWithBody generates requests for PostClusterName with any type of body
func NewPostClusterNameRequestWithBody(server string, name string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "name", runtime.ParamLocationPath, name)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/cluster/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetClustersRequest generates requests for GetClusters
func NewGetClustersRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/clusters")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostClustersRequest calls the generic PostClusters builder with application/json body
func NewPostClustersRequest(server string, body PostClustersJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostClustersRequestWithBody(server, "application/json", bodyReader)
}

// NewPostClustersRequestWithBody generates requests for PostClusters with any type of body
func NewPostClustersRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/clusters")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetClustersUuidPrometheusApiV1LabelLabelNameValuesRequest generates requests for GetClustersUuidPrometheusApiV1LabelLabelNameValues
func NewGetClustersUuidPrometheusApiV1LabelLabelNameValuesRequest(server string, uuid openapi_types.UUID, labelName string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "uuid", runtime.ParamLocationPath, uuid)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "labelName", runtime.ParamLocationPath, labelName)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/clusters/%s/prometheus/api/v1/label/%s/values", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostClustersUuidPrometheusApiV1LabelLabelNameValuesRequest generates requests for PostClustersUuidPrometheusApiV1LabelLabelNameValues
func NewPostClustersUuidPrometheusApiV1LabelLabelNameValuesRequest(server string, uuid openapi_types.UUID, labelName string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "uuid", runtime.ParamLocationPath, uuid)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "labelName", runtime.ParamLocationPath, labelName)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/clusters/%s/prometheus/api/v1/label/%s/values", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetClustersUuidPrometheusApiV1QueryRequest generates requests for GetClustersUuidPrometheusApiV1Query
func NewGetClustersUuidPrometheusApiV1QueryRequest(server string, uuid openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "uuid", runtime.ParamLocationPath, uuid)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/clusters/%s/prometheus/api/v1/query", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostClustersUuidPrometheusApiV1QueryRequest generates requests for PostClustersUuidPrometheusApiV1Query
func NewPostClustersUuidPrometheusApiV1QueryRequest(server string, uuid openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "uuid", runtime.ParamLocationPath, uuid)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/clusters/%s/prometheus/api/v1/query", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetClustersUuidPrometheusApiV1QueryRangeRequest generates requests for GetClustersUuidPrometheusApiV1QueryRange
func NewGetClustersUuidPrometheusApiV1QueryRangeRequest(server string, uuid openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "uuid", runtime.ParamLocationPath, uuid)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/clusters/%s/prometheus/api/v1/query_range", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostClustersUuidPrometheusApiV1QueryRangeRequest generates requests for PostClustersUuidPrometheusApiV1QueryRange
func NewPostClustersUuidPrometheusApiV1QueryRangeRequest(server string, uuid openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "uuid", runtime.ParamLocationPath, uuid)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/clusters/%s/prometheus/api/v1/query_range", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetClustersUuidPrometheusApiV1SeriesRequest generates requests for GetClustersUuidPrometheusApiV1Series
func NewGetClustersUuidPrometheusApiV1SeriesRequest(server string, uuid openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "uuid", runtime.ParamLocationPath, uuid)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/clusters/%s/prometheus/api/v1/series", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostClustersUuidPrometheusApiV1SeriesRequest generates requests for PostClustersUuidPrometheusApiV1Series
func NewPostClustersUuidPrometheusApiV1SeriesRequest(server string, uuid openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "uuid", runtime.ParamLocationPath, uuid)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/clusters/%s/prometheus/api/v1/series", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewDeleteTenantTenantIdRequest generates requests for DeleteTenantTenantId
func NewDeleteTenantTenantIdRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetTenantTenantIdRequest generates requests for GetTenantTenantId
func NewGetTenantTenantIdRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantTenantIdBackupRequest generates requests for PostTenantTenantIdBackup
func NewPostTenantTenantIdBackupRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/backup", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewDeleteTenantTenantIdBackupSnapshotIdRequest generates requests for DeleteTenantTenantIdBackupSnapshotId
func NewDeleteTenantTenantIdBackupSnapshotIdRequest(server string, tenantId uint64, snapshotId openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "snapshotId", runtime.ParamLocationPath, snapshotId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/backup/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantTenantIdBackupSnapshotIdInPlaceRestoreRequest generates requests for PostTenantTenantIdBackupSnapshotIdInPlaceRestore
func NewPostTenantTenantIdBackupSnapshotIdInPlaceRestoreRequest(server string, tenantId uint64, snapshotId openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "snapshotId", runtime.ParamLocationPath, snapshotId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/backup/%s/in-place-restore", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantTenantIdBackupSnapshotIdRestoreRequest calls the generic PostTenantTenantIdBackupSnapshotIdRestore builder with application/json body
func NewPostTenantTenantIdBackupSnapshotIdRestoreRequest(server string, tenantId uint64, snapshotId openapi_types.UUID, body PostTenantTenantIdBackupSnapshotIdRestoreJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostTenantTenantIdBackupSnapshotIdRestoreRequestWithBody(server, tenantId, snapshotId, "application/json", bodyReader)
}

// NewPostTenantTenantIdBackupSnapshotIdRestoreRequestWithBody generates requests for PostTenantTenantIdBackupSnapshotIdRestore with any type of body
func NewPostTenantTenantIdBackupSnapshotIdRestoreRequestWithBody(server string, tenantId uint64, snapshotId openapi_types.UUID, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "snapshotId", runtime.ParamLocationPath, snapshotId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/backup/%s/restore", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostTenantTenantIdComputeCacheRequest calls the generic PostTenantTenantIdComputeCache builder with application/json body
func NewPostTenantTenantIdComputeCacheRequest(server string, tenantId uint64, body PostTenantTenantIdComputeCacheJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostTenantTenantIdComputeCacheRequestWithBody(server, tenantId, "application/json", bodyReader)
}

// NewPostTenantTenantIdComputeCacheRequestWithBody generates requests for PostTenantTenantIdComputeCache with any type of body
func NewPostTenantTenantIdComputeCacheRequestWithBody(server string, tenantId uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/computeCache", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPutTenantTenantIdConfigEtcdRequestWithTextBody calls the generic PutTenantTenantIdConfigEtcd builder with text/plain body
func NewPutTenantTenantIdConfigEtcdRequestWithTextBody(server string, tenantId uint64, body PutTenantTenantIdConfigEtcdTextRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	bodyReader = strings.NewReader(string(body))
	return NewPutTenantTenantIdConfigEtcdRequestWithBody(server, tenantId, "text/plain", bodyReader)
}

// NewPutTenantTenantIdConfigEtcdRequestWithBody generates requests for PutTenantTenantIdConfigEtcd with any type of body
func NewPutTenantTenantIdConfigEtcdRequestWithBody(server string, tenantId uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/config/etcd", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPutTenantTenantIdConfigRisingwaveRequestWithTextBody calls the generic PutTenantTenantIdConfigRisingwave builder with text/plain body
func NewPutTenantTenantIdConfigRisingwaveRequestWithTextBody(server string, tenantId uint64, params *PutTenantTenantIdConfigRisingwaveParams, body PutTenantTenantIdConfigRisingwaveTextRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	bodyReader = strings.NewReader(string(body))
	return NewPutTenantTenantIdConfigRisingwaveRequestWithBody(server, tenantId, params, "text/plain", bodyReader)
}

// NewPutTenantTenantIdConfigRisingwaveRequestWithBody generates requests for PutTenantTenantIdConfigRisingwave with any type of body
func NewPutTenantTenantIdConfigRisingwaveRequestWithBody(server string, tenantId uint64, params *PutTenantTenantIdConfigRisingwaveParams, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/config/risingwave", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Component != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "component", runtime.ParamLocationQuery, *params.Component); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.NodeGroup != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "nodeGroup", runtime.ParamLocationQuery, *params.NodeGroup); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPutTenantTenantIdConfigNoRestartRisingwaveRequestWithTextBody calls the generic PutTenantTenantIdConfigNoRestartRisingwave builder with text/plain body
func NewPutTenantTenantIdConfigNoRestartRisingwaveRequestWithTextBody(server string, tenantId uint64, params *PutTenantTenantIdConfigNoRestartRisingwaveParams, body PutTenantTenantIdConfigNoRestartRisingwaveTextRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	bodyReader = strings.NewReader(string(body))
	return NewPutTenantTenantIdConfigNoRestartRisingwaveRequestWithBody(server, tenantId, params, "text/plain", bodyReader)
}

// NewPutTenantTenantIdConfigNoRestartRisingwaveRequestWithBody generates requests for PutTenantTenantIdConfigNoRestartRisingwave with any type of body
func NewPutTenantTenantIdConfigNoRestartRisingwaveRequestWithBody(server string, tenantId uint64, params *PutTenantTenantIdConfigNoRestartRisingwaveParams, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/configNoRestart/risingwave", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Component != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "component", runtime.ParamLocationQuery, *params.Component); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.NodeGroup != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "nodeGroup", runtime.ParamLocationQuery, *params.NodeGroup); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGenDiagReportRequest generates requests for GenDiagReport
func NewGenDiagReportRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/diagnosis/report/generate", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewListDiagReportTimeRangeRequest generates requests for ListDiagReportTimeRange
func NewListDiagReportTimeRangeRequest(server string, tenantId uint64, params *ListDiagReportTimeRangeParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/diagnosis/report/range", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.StartTime != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "startTime", runtime.ParamLocationQuery, *params.StartTime); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.EndTime != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "endTime", runtime.ParamLocationQuery, *params.EndTime); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Limit != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "limit", runtime.ParamLocationQuery, *params.Limit); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Offset != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "offset", runtime.ParamLocationQuery, *params.Offset); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetDiagReportRequest generates requests for GetDiagReport
func NewGetDiagReportRequest(server string, tenantId uint64, reportId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "reportId", runtime.ParamLocationPath, reportId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/diagnosis/report/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetTenantTenantIdEndpointRequest generates requests for GetTenantTenantIdEndpoint
func NewGetTenantTenantIdEndpointRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/endpoint", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewDeleteTenantTenantIdExpireRequest generates requests for DeleteTenantTenantIdExpire
func NewDeleteTenantTenantIdExpireRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/expire", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetTenantTenantIdExpireRequest generates requests for GetTenantTenantIdExpire
func NewGetTenantTenantIdExpireRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/expire", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantTenantIdExpireRequest calls the generic PostTenantTenantIdExpire builder with application/json body
func NewPostTenantTenantIdExpireRequest(server string, tenantId uint64, body PostTenantTenantIdExpireJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostTenantTenantIdExpireRequestWithBody(server, tenantId, "application/json", bodyReader)
}

// NewPostTenantTenantIdExpireRequestWithBody generates requests for PostTenantTenantIdExpire with any type of body
func NewPostTenantTenantIdExpireRequestWithBody(server string, tenantId uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/expire", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostTenantTenantIdExtensionsCompactionDisableRequest generates requests for PostTenantTenantIdExtensionsCompactionDisable
func NewPostTenantTenantIdExtensionsCompactionDisableRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/extensions/compaction/disable", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantTenantIdExtensionsCompactionEnableRequest generates requests for PostTenantTenantIdExtensionsCompactionEnable
func NewPostTenantTenantIdExtensionsCompactionEnableRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/extensions/compaction/enable", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetTenantTenantIdExtensionsCompactionParametersRequest generates requests for GetTenantTenantIdExtensionsCompactionParameters
func NewGetTenantTenantIdExtensionsCompactionParametersRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/extensions/compaction/parameters", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetTenantTenantIdExtensionsCompactionStatusRequest generates requests for GetTenantTenantIdExtensionsCompactionStatus
func NewGetTenantTenantIdExtensionsCompactionStatusRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/extensions/compaction/status", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantTenantIdExtensionsCompactionStatusRequest calls the generic PostTenantTenantIdExtensionsCompactionStatus builder with application/json body
func NewPostTenantTenantIdExtensionsCompactionStatusRequest(server string, tenantId uint64, body PostTenantTenantIdExtensionsCompactionStatusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostTenantTenantIdExtensionsCompactionStatusRequestWithBody(server, tenantId, "application/json", bodyReader)
}

// NewPostTenantTenantIdExtensionsCompactionStatusRequestWithBody generates requests for PostTenantTenantIdExtensionsCompactionStatus with any type of body
func NewPostTenantTenantIdExtensionsCompactionStatusRequestWithBody(server string, tenantId uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/extensions/compaction/status", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostTenantTenantIdExtensionsCompactionUpdateRequest calls the generic PostTenantTenantIdExtensionsCompactionUpdate builder with application/json body
func NewPostTenantTenantIdExtensionsCompactionUpdateRequest(server string, tenantId uint64, body PostTenantTenantIdExtensionsCompactionUpdateJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostTenantTenantIdExtensionsCompactionUpdateRequestWithBody(server, tenantId, "application/json", bodyReader)
}

// NewPostTenantTenantIdExtensionsCompactionUpdateRequestWithBody generates requests for PostTenantTenantIdExtensionsCompactionUpdate with any type of body
func NewPostTenantTenantIdExtensionsCompactionUpdateRequestWithBody(server string, tenantId uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/extensions/compaction/update", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostTenantTenantIdExtensionsServerlessbackfillDisableRequest generates requests for PostTenantTenantIdExtensionsServerlessbackfillDisable
func NewPostTenantTenantIdExtensionsServerlessbackfillDisableRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/extensions/serverlessbackfill/disable", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantTenantIdExtensionsServerlessbackfillEnableRequest generates requests for PostTenantTenantIdExtensionsServerlessbackfillEnable
func NewPostTenantTenantIdExtensionsServerlessbackfillEnableRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/extensions/serverlessbackfill/enable", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantTenantIdExtensionsServerlessbackfillVersionRequestWithTextBody calls the generic PostTenantTenantIdExtensionsServerlessbackfillVersion builder with text/plain body
func NewPostTenantTenantIdExtensionsServerlessbackfillVersionRequestWithTextBody(server string, tenantId uint64, body PostTenantTenantIdExtensionsServerlessbackfillVersionTextRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	bodyReader = strings.NewReader(string(body))
	return NewPostTenantTenantIdExtensionsServerlessbackfillVersionRequestWithBody(server, tenantId, "text/plain", bodyReader)
}

// NewPostTenantTenantIdExtensionsServerlessbackfillVersionRequestWithBody generates requests for PostTenantTenantIdExtensionsServerlessbackfillVersion with any type of body
func NewPostTenantTenantIdExtensionsServerlessbackfillVersionRequestWithBody(server string, tenantId uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/extensions/serverlessbackfill/version", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostTenantTenantIdMigrateMetaStoreRequest generates requests for PostTenantTenantIdMigrateMetaStore
func NewPostTenantTenantIdMigrateMetaStoreRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/migrateMetaStore", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewDeleteTenantTenantIdPrivatelinkPrivateLinkIdRequest generates requests for DeleteTenantTenantIdPrivatelinkPrivateLinkId
func NewDeleteTenantTenantIdPrivatelinkPrivateLinkIdRequest(server string, tenantId uint64, privateLinkId openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "privateLinkId", runtime.ParamLocationPath, privateLinkId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/privatelink/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantTenantIdResourceRequest calls the generic PostTenantTenantIdResource builder with application/json body
func NewPostTenantTenantIdResourceRequest(server string, tenantId uint64, body PostTenantTenantIdResourceJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostTenantTenantIdResourceRequestWithBody(server, tenantId, "application/json", bodyReader)
}

// NewPostTenantTenantIdResourceRequestWithBody generates requests for PostTenantTenantIdResource with any type of body
func NewPostTenantTenantIdResourceRequestWithBody(server string, tenantId uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/resource", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostTenantTenantIdResourceGroupsRequest calls the generic PostTenantTenantIdResourceGroups builder with application/json body
func NewPostTenantTenantIdResourceGroupsRequest(server string, tenantId uint64, body PostTenantTenantIdResourceGroupsJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostTenantTenantIdResourceGroupsRequestWithBody(server, tenantId, "application/json", bodyReader)
}

// NewPostTenantTenantIdResourceGroupsRequestWithBody generates requests for PostTenantTenantIdResourceGroups with any type of body
func NewPostTenantTenantIdResourceGroupsRequestWithBody(server string, tenantId uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/resourceGroups", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewDeleteTenantTenantIdResourceGroupsResourceGroupRequest generates requests for DeleteTenantTenantIdResourceGroupsResourceGroup
func NewDeleteTenantTenantIdResourceGroupsResourceGroupRequest(server string, tenantId uint64, resourceGroup string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "resourceGroup", runtime.ParamLocationPath, resourceGroup)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/resourceGroups/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantTenantIdResourceGroupsResourceGroupRequest calls the generic PostTenantTenantIdResourceGroupsResourceGroup builder with application/json body
func NewPostTenantTenantIdResourceGroupsResourceGroupRequest(server string, tenantId uint64, resourceGroup string, body PostTenantTenantIdResourceGroupsResourceGroupJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostTenantTenantIdResourceGroupsResourceGroupRequestWithBody(server, tenantId, resourceGroup, "application/json", bodyReader)
}

// NewPostTenantTenantIdResourceGroupsResourceGroupRequestWithBody generates requests for PostTenantTenantIdResourceGroupsResourceGroup with any type of body
func NewPostTenantTenantIdResourceGroupsResourceGroupRequestWithBody(server string, tenantId uint64, resourceGroup string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "resourceGroup", runtime.ParamLocationPath, resourceGroup)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/resourceGroups/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostTenantTenantIdRestartRequest generates requests for PostTenantTenantIdRestart
func NewPostTenantTenantIdRestartRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/restart", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantTenantIdStartRequest generates requests for PostTenantTenantIdStart
func NewPostTenantTenantIdStartRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/start", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantTenantIdStatusRequest calls the generic PostTenantTenantIdStatus builder with application/json body
func NewPostTenantTenantIdStatusRequest(server string, tenantId uint64, body PostTenantTenantIdStatusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostTenantTenantIdStatusRequestWithBody(server, tenantId, "application/json", bodyReader)
}

// NewPostTenantTenantIdStatusRequestWithBody generates requests for PostTenantTenantIdStatus with any type of body
func NewPostTenantTenantIdStatusRequestWithBody(server string, tenantId uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/status", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostTenantTenantIdStopRequest generates requests for PostTenantTenantIdStop
func NewPostTenantTenantIdStopRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/stop", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantTenantIdTierRequest calls the generic PostTenantTenantIdTier builder with application/json body
func NewPostTenantTenantIdTierRequest(server string, tenantId uint64, body PostTenantTenantIdTierJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostTenantTenantIdTierRequestWithBody(server, tenantId, "application/json", bodyReader)
}

// NewPostTenantTenantIdTierRequestWithBody generates requests for PostTenantTenantIdTier with any type of body
func NewPostTenantTenantIdTierRequestWithBody(server string, tenantId uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/tier", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostTenantTenantIdUpdateVersionRequest calls the generic PostTenantTenantIdUpdateVersion builder with application/json body
func NewPostTenantTenantIdUpdateVersionRequest(server string, tenantId uint64, body PostTenantTenantIdUpdateVersionJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostTenantTenantIdUpdateVersionRequestWithBody(server, tenantId, "application/json", bodyReader)
}

// NewPostTenantTenantIdUpdateVersionRequestWithBody generates requests for PostTenantTenantIdUpdateVersion with any type of body
func NewPostTenantTenantIdUpdateVersionRequestWithBody(server string, tenantId uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/updateVersion", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostTenantTenantIdUpgradeMetaStoreRequest generates requests for PostTenantTenantIdUpgradeMetaStore
func NewPostTenantTenantIdUpgradeMetaStoreRequest(server string, tenantId uint64, params *PostTenantTenantIdUpgradeMetaStoreParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/upgradeMetaStore", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "metaStore", runtime.ParamLocationQuery, params.MetaStore); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetTenantsRequest generates requests for GetTenants
func NewGetTenantsRequest(server string, params *GetTenantsParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenants")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Offset != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "offset", runtime.ParamLocationQuery, *params.Offset); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Limit != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "limit", runtime.ParamLocationQuery, *params.Limit); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewDeleteTenantsTenantIdExtensionsIcebergCompactionRequest generates requests for DeleteTenantsTenantIdExtensionsIcebergCompaction
func NewDeleteTenantsTenantIdExtensionsIcebergCompactionRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenants/%s/extensions/iceberg-compaction", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetTenantsTenantIdExtensionsIcebergCompactionRequest generates requests for GetTenantsTenantIdExtensionsIcebergCompaction
func NewGetTenantsTenantIdExtensionsIcebergCompactionRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenants/%s/extensions/iceberg-compaction", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantsTenantIdExtensionsIcebergCompactionRequest calls the generic PostTenantsTenantIdExtensionsIcebergCompaction builder with application/json body
func NewPostTenantsTenantIdExtensionsIcebergCompactionRequest(server string, tenantId uint64, body PostTenantsTenantIdExtensionsIcebergCompactionJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostTenantsTenantIdExtensionsIcebergCompactionRequestWithBody(server, tenantId, "application/json", bodyReader)
}

// NewPostTenantsTenantIdExtensionsIcebergCompactionRequestWithBody generates requests for PostTenantsTenantIdExtensionsIcebergCompaction with any type of body
func NewPostTenantsTenantIdExtensionsIcebergCompactionRequestWithBody(server string, tenantId uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenants/%s/extensions/iceberg-compaction", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPutTenantsTenantIdExtensionsIcebergCompactionRequest calls the generic PutTenantsTenantIdExtensionsIcebergCompaction builder with application/json body
func NewPutTenantsTenantIdExtensionsIcebergCompactionRequest(server string, tenantId uint64, body PutTenantsTenantIdExtensionsIcebergCompactionJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPutTenantsTenantIdExtensionsIcebergCompactionRequestWithBody(server, tenantId, "application/json", bodyReader)
}

// NewPutTenantsTenantIdExtensionsIcebergCompactionRequestWithBody generates requests for PutTenantsTenantIdExtensionsIcebergCompaction with any type of body
func NewPutTenantsTenantIdExtensionsIcebergCompactionRequestWithBody(server string, tenantId uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenants/%s/extensions/iceberg-compaction", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetVersionRequest generates requests for GetVersion
func NewGetVersionRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/version")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostWebhooksAlertRequest generates requests for PostWebhooksAlert
func NewPostWebhooksAlertRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/webhooks/alert")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetWorkflowIdRequest generates requests for GetWorkflowId
func NewGetWorkflowIdRequest(server string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/workflow/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostWorkflowIdCancelRequest generates requests for PostWorkflowIdCancel
func NewPostWorkflowIdCancelRequest(server string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/workflow/%s/cancel", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostWorkflowIdRerunRequest calls the generic PostWorkflowIdRerun builder with application/json body
func NewPostWorkflowIdRerunRequest(server string, id openapi_types.UUID, body PostWorkflowIdRerunJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostWorkflowIdRerunRequestWithBody(server, id, "application/json", bodyReader)
}

// NewPostWorkflowIdRerunRequestWithBody generates requests for PostWorkflowIdRerun with any type of body
func NewPostWorkflowIdRerunRequestWithBody(server string, id openapi_types.UUID, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/workflow/%s/rerun", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostWorkflowIdResumeRequest generates requests for PostWorkflowIdResume
func NewPostWorkflowIdResumeRequest(server string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/workflow/%s/resume", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostWorkflowIdScheduleRequest calls the generic PostWorkflowIdSchedule builder with application/json body
func NewPostWorkflowIdScheduleRequest(server string, id openapi_types.UUID, body PostWorkflowIdScheduleJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostWorkflowIdScheduleRequestWithBody(server, id, "application/json", bodyReader)
}

// NewPostWorkflowIdScheduleRequestWithBody generates requests for PostWorkflowIdSchedule with any type of body
func NewPostWorkflowIdScheduleRequestWithBody(server string, id openapi_types.UUID, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/workflow/%s/schedule", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetWorkflowsRequest generates requests for GetWorkflows
func NewGetWorkflowsRequest(server string, params *GetWorkflowsParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/workflows")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Offset != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "offset", runtime.ParamLocationQuery, *params.Offset); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Limit != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "limit", runtime.ParamLocationQuery, *params.Limit); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

func (c *Client) applyEditors(ctx context.Context, req *http.Request, additionalEditors []RequestEditorFn) error {
	for _, r := range c.RequestEditors {
		if err := r(ctx, req); err != nil {
			return err
		}
	}
	for _, r := range additionalEditors {
		if err := r(ctx, req); err != nil {
			return err
		}
	}
	return nil
}

// ClientWithResponses builds on ClientInterface to offer response payloads
type ClientWithResponses struct {
	ClientInterface
}

// NewClientWithResponses creates a new ClientWithResponses, which wraps
// Client with return type handling
func NewClientWithResponses(server string, opts ...ClientOption) (*ClientWithResponses, error) {
	client, err := NewClient(server, opts...)
	if err != nil {
		return nil, err
	}
	return &ClientWithResponses{client}, nil
}

// WithBaseURL overrides the baseURL.
func WithBaseURL(baseURL string) ClientOption {
	return func(c *Client) error {
		newBaseURL, err := url.Parse(baseURL)
		if err != nil {
			return err
		}
		c.Server = newBaseURL.String()
		return nil
	}
}

// ClientWithResponsesInterface is the interface specification for the client with responses above.
type ClientWithResponsesInterface interface {
	// GetWithResponse request
	GetWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetResponse, error)

	// PostAlertNotificationsWithBodyWithResponse request with any body
	PostAlertNotificationsWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostAlertNotificationsResponse, error)

	PostAlertNotificationsWithResponse(ctx context.Context, body PostAlertNotificationsJSONRequestBody, reqEditors ...RequestEditorFn) (*PostAlertNotificationsResponse, error)

	// PostByocClusterIdAccessWithBodyWithResponse request with any body
	PostByocClusterIdAccessWithBodyWithResponse(ctx context.Context, id uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostByocClusterIdAccessResponse, error)

	PostByocClusterIdAccessWithResponse(ctx context.Context, id uint64, body PostByocClusterIdAccessJSONRequestBody, reqEditors ...RequestEditorFn) (*PostByocClusterIdAccessResponse, error)

	// PostByocClusterIdUpdateWithBodyWithResponse request with any body
	PostByocClusterIdUpdateWithBodyWithResponse(ctx context.Context, id uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostByocClusterIdUpdateResponse, error)

	PostByocClusterIdUpdateWithResponse(ctx context.Context, id uint64, body PostByocClusterIdUpdateJSONRequestBody, reqEditors ...RequestEditorFn) (*PostByocClusterIdUpdateResponse, error)

	// GetByocClustersWithResponse request
	GetByocClustersWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetByocClustersResponse, error)

	// DeleteClusterNameWithResponse request
	DeleteClusterNameWithResponse(ctx context.Context, name string, reqEditors ...RequestEditorFn) (*DeleteClusterNameResponse, error)

	// GetClusterNameWithResponse request
	GetClusterNameWithResponse(ctx context.Context, name string, reqEditors ...RequestEditorFn) (*GetClusterNameResponse, error)

	// PostClusterNameWithBodyWithResponse request with any body
	PostClusterNameWithBodyWithResponse(ctx context.Context, name string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostClusterNameResponse, error)

	PostClusterNameWithResponse(ctx context.Context, name string, body PostClusterNameJSONRequestBody, reqEditors ...RequestEditorFn) (*PostClusterNameResponse, error)

	// GetClustersWithResponse request
	GetClustersWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetClustersResponse, error)

	// PostClustersWithBodyWithResponse request with any body
	PostClustersWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostClustersResponse, error)

	PostClustersWithResponse(ctx context.Context, body PostClustersJSONRequestBody, reqEditors ...RequestEditorFn) (*PostClustersResponse, error)

	// GetClustersUuidPrometheusApiV1LabelLabelNameValuesWithResponse request
	GetClustersUuidPrometheusApiV1LabelLabelNameValuesWithResponse(ctx context.Context, uuid openapi_types.UUID, labelName string, reqEditors ...RequestEditorFn) (*GetClustersUuidPrometheusApiV1LabelLabelNameValuesResponse, error)

	// PostClustersUuidPrometheusApiV1LabelLabelNameValuesWithResponse request
	PostClustersUuidPrometheusApiV1LabelLabelNameValuesWithResponse(ctx context.Context, uuid openapi_types.UUID, labelName string, reqEditors ...RequestEditorFn) (*PostClustersUuidPrometheusApiV1LabelLabelNameValuesResponse, error)

	// GetClustersUuidPrometheusApiV1QueryWithResponse request
	GetClustersUuidPrometheusApiV1QueryWithResponse(ctx context.Context, uuid openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetClustersUuidPrometheusApiV1QueryResponse, error)

	// PostClustersUuidPrometheusApiV1QueryWithResponse request
	PostClustersUuidPrometheusApiV1QueryWithResponse(ctx context.Context, uuid openapi_types.UUID, reqEditors ...RequestEditorFn) (*PostClustersUuidPrometheusApiV1QueryResponse, error)

	// GetClustersUuidPrometheusApiV1QueryRangeWithResponse request
	GetClustersUuidPrometheusApiV1QueryRangeWithResponse(ctx context.Context, uuid openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetClustersUuidPrometheusApiV1QueryRangeResponse, error)

	// PostClustersUuidPrometheusApiV1QueryRangeWithResponse request
	PostClustersUuidPrometheusApiV1QueryRangeWithResponse(ctx context.Context, uuid openapi_types.UUID, reqEditors ...RequestEditorFn) (*PostClustersUuidPrometheusApiV1QueryRangeResponse, error)

	// GetClustersUuidPrometheusApiV1SeriesWithResponse request
	GetClustersUuidPrometheusApiV1SeriesWithResponse(ctx context.Context, uuid openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetClustersUuidPrometheusApiV1SeriesResponse, error)

	// PostClustersUuidPrometheusApiV1SeriesWithResponse request
	PostClustersUuidPrometheusApiV1SeriesWithResponse(ctx context.Context, uuid openapi_types.UUID, reqEditors ...RequestEditorFn) (*PostClustersUuidPrometheusApiV1SeriesResponse, error)

	// DeleteTenantTenantIdWithResponse request
	DeleteTenantTenantIdWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*DeleteTenantTenantIdResponse, error)

	// GetTenantTenantIdWithResponse request
	GetTenantTenantIdWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*GetTenantTenantIdResponse, error)

	// PostTenantTenantIdBackupWithResponse request
	PostTenantTenantIdBackupWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PostTenantTenantIdBackupResponse, error)

	// DeleteTenantTenantIdBackupSnapshotIdWithResponse request
	DeleteTenantTenantIdBackupSnapshotIdWithResponse(ctx context.Context, tenantId uint64, snapshotId openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteTenantTenantIdBackupSnapshotIdResponse, error)

	// PostTenantTenantIdBackupSnapshotIdInPlaceRestoreWithResponse request
	PostTenantTenantIdBackupSnapshotIdInPlaceRestoreWithResponse(ctx context.Context, tenantId uint64, snapshotId openapi_types.UUID, reqEditors ...RequestEditorFn) (*PostTenantTenantIdBackupSnapshotIdInPlaceRestoreResponse, error)

	// PostTenantTenantIdBackupSnapshotIdRestoreWithBodyWithResponse request with any body
	PostTenantTenantIdBackupSnapshotIdRestoreWithBodyWithResponse(ctx context.Context, tenantId uint64, snapshotId openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdBackupSnapshotIdRestoreResponse, error)

	PostTenantTenantIdBackupSnapshotIdRestoreWithResponse(ctx context.Context, tenantId uint64, snapshotId openapi_types.UUID, body PostTenantTenantIdBackupSnapshotIdRestoreJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdBackupSnapshotIdRestoreResponse, error)

	// PostTenantTenantIdComputeCacheWithBodyWithResponse request with any body
	PostTenantTenantIdComputeCacheWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdComputeCacheResponse, error)

	PostTenantTenantIdComputeCacheWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdComputeCacheJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdComputeCacheResponse, error)

	// PutTenantTenantIdConfigEtcdWithBodyWithResponse request with any body
	PutTenantTenantIdConfigEtcdWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutTenantTenantIdConfigEtcdResponse, error)

	PutTenantTenantIdConfigEtcdWithTextBodyWithResponse(ctx context.Context, tenantId uint64, body PutTenantTenantIdConfigEtcdTextRequestBody, reqEditors ...RequestEditorFn) (*PutTenantTenantIdConfigEtcdResponse, error)

	// PutTenantTenantIdConfigRisingwaveWithBodyWithResponse request with any body
	PutTenantTenantIdConfigRisingwaveWithBodyWithResponse(ctx context.Context, tenantId uint64, params *PutTenantTenantIdConfigRisingwaveParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutTenantTenantIdConfigRisingwaveResponse, error)

	PutTenantTenantIdConfigRisingwaveWithTextBodyWithResponse(ctx context.Context, tenantId uint64, params *PutTenantTenantIdConfigRisingwaveParams, body PutTenantTenantIdConfigRisingwaveTextRequestBody, reqEditors ...RequestEditorFn) (*PutTenantTenantIdConfigRisingwaveResponse, error)

	// PutTenantTenantIdConfigNoRestartRisingwaveWithBodyWithResponse request with any body
	PutTenantTenantIdConfigNoRestartRisingwaveWithBodyWithResponse(ctx context.Context, tenantId uint64, params *PutTenantTenantIdConfigNoRestartRisingwaveParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutTenantTenantIdConfigNoRestartRisingwaveResponse, error)

	PutTenantTenantIdConfigNoRestartRisingwaveWithTextBodyWithResponse(ctx context.Context, tenantId uint64, params *PutTenantTenantIdConfigNoRestartRisingwaveParams, body PutTenantTenantIdConfigNoRestartRisingwaveTextRequestBody, reqEditors ...RequestEditorFn) (*PutTenantTenantIdConfigNoRestartRisingwaveResponse, error)

	// GenDiagReportWithResponse request
	GenDiagReportWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*GenDiagReportResponse, error)

	// ListDiagReportTimeRangeWithResponse request
	ListDiagReportTimeRangeWithResponse(ctx context.Context, tenantId uint64, params *ListDiagReportTimeRangeParams, reqEditors ...RequestEditorFn) (*ListDiagReportTimeRangeResponse, error)

	// GetDiagReportWithResponse request
	GetDiagReportWithResponse(ctx context.Context, tenantId uint64, reportId uint64, reqEditors ...RequestEditorFn) (*GetDiagReportResponse, error)

	// GetTenantTenantIdEndpointWithResponse request
	GetTenantTenantIdEndpointWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*GetTenantTenantIdEndpointResponse, error)

	// DeleteTenantTenantIdExpireWithResponse request
	DeleteTenantTenantIdExpireWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*DeleteTenantTenantIdExpireResponse, error)

	// GetTenantTenantIdExpireWithResponse request
	GetTenantTenantIdExpireWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*GetTenantTenantIdExpireResponse, error)

	// PostTenantTenantIdExpireWithBodyWithResponse request with any body
	PostTenantTenantIdExpireWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExpireResponse, error)

	PostTenantTenantIdExpireWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdExpireJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExpireResponse, error)

	// PostTenantTenantIdExtensionsCompactionDisableWithResponse request
	PostTenantTenantIdExtensionsCompactionDisableWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsCompactionDisableResponse, error)

	// PostTenantTenantIdExtensionsCompactionEnableWithResponse request
	PostTenantTenantIdExtensionsCompactionEnableWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsCompactionEnableResponse, error)

	// GetTenantTenantIdExtensionsCompactionParametersWithResponse request
	GetTenantTenantIdExtensionsCompactionParametersWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*GetTenantTenantIdExtensionsCompactionParametersResponse, error)

	// GetTenantTenantIdExtensionsCompactionStatusWithResponse request
	GetTenantTenantIdExtensionsCompactionStatusWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*GetTenantTenantIdExtensionsCompactionStatusResponse, error)

	// PostTenantTenantIdExtensionsCompactionStatusWithBodyWithResponse request with any body
	PostTenantTenantIdExtensionsCompactionStatusWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsCompactionStatusResponse, error)

	PostTenantTenantIdExtensionsCompactionStatusWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdExtensionsCompactionStatusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsCompactionStatusResponse, error)

	// PostTenantTenantIdExtensionsCompactionUpdateWithBodyWithResponse request with any body
	PostTenantTenantIdExtensionsCompactionUpdateWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsCompactionUpdateResponse, error)

	PostTenantTenantIdExtensionsCompactionUpdateWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdExtensionsCompactionUpdateJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsCompactionUpdateResponse, error)

	// PostTenantTenantIdExtensionsServerlessbackfillDisableWithResponse request
	PostTenantTenantIdExtensionsServerlessbackfillDisableWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsServerlessbackfillDisableResponse, error)

	// PostTenantTenantIdExtensionsServerlessbackfillEnableWithResponse request
	PostTenantTenantIdExtensionsServerlessbackfillEnableWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsServerlessbackfillEnableResponse, error)

	// PostTenantTenantIdExtensionsServerlessbackfillVersionWithBodyWithResponse request with any body
	PostTenantTenantIdExtensionsServerlessbackfillVersionWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsServerlessbackfillVersionResponse, error)

	PostTenantTenantIdExtensionsServerlessbackfillVersionWithTextBodyWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdExtensionsServerlessbackfillVersionTextRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsServerlessbackfillVersionResponse, error)

	// PostTenantTenantIdMigrateMetaStoreWithResponse request
	PostTenantTenantIdMigrateMetaStoreWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PostTenantTenantIdMigrateMetaStoreResponse, error)

	// DeleteTenantTenantIdPrivatelinkPrivateLinkIdWithResponse request
	DeleteTenantTenantIdPrivatelinkPrivateLinkIdWithResponse(ctx context.Context, tenantId uint64, privateLinkId openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteTenantTenantIdPrivatelinkPrivateLinkIdResponse, error)

	// PostTenantTenantIdResourceWithBodyWithResponse request with any body
	PostTenantTenantIdResourceWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdResourceResponse, error)

	PostTenantTenantIdResourceWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdResourceJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdResourceResponse, error)

	// PostTenantTenantIdResourceGroupsWithBodyWithResponse request with any body
	PostTenantTenantIdResourceGroupsWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdResourceGroupsResponse, error)

	PostTenantTenantIdResourceGroupsWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdResourceGroupsJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdResourceGroupsResponse, error)

	// DeleteTenantTenantIdResourceGroupsResourceGroupWithResponse request
	DeleteTenantTenantIdResourceGroupsResourceGroupWithResponse(ctx context.Context, tenantId uint64, resourceGroup string, reqEditors ...RequestEditorFn) (*DeleteTenantTenantIdResourceGroupsResourceGroupResponse, error)

	// PostTenantTenantIdResourceGroupsResourceGroupWithBodyWithResponse request with any body
	PostTenantTenantIdResourceGroupsResourceGroupWithBodyWithResponse(ctx context.Context, tenantId uint64, resourceGroup string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdResourceGroupsResourceGroupResponse, error)

	PostTenantTenantIdResourceGroupsResourceGroupWithResponse(ctx context.Context, tenantId uint64, resourceGroup string, body PostTenantTenantIdResourceGroupsResourceGroupJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdResourceGroupsResourceGroupResponse, error)

	// PostTenantTenantIdRestartWithResponse request
	PostTenantTenantIdRestartWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PostTenantTenantIdRestartResponse, error)

	// PostTenantTenantIdStartWithResponse request
	PostTenantTenantIdStartWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PostTenantTenantIdStartResponse, error)

	// PostTenantTenantIdStatusWithBodyWithResponse request with any body
	PostTenantTenantIdStatusWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdStatusResponse, error)

	PostTenantTenantIdStatusWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdStatusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdStatusResponse, error)

	// PostTenantTenantIdStopWithResponse request
	PostTenantTenantIdStopWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PostTenantTenantIdStopResponse, error)

	// PostTenantTenantIdTierWithBodyWithResponse request with any body
	PostTenantTenantIdTierWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdTierResponse, error)

	PostTenantTenantIdTierWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdTierJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdTierResponse, error)

	// PostTenantTenantIdUpdateVersionWithBodyWithResponse request with any body
	PostTenantTenantIdUpdateVersionWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdUpdateVersionResponse, error)

	PostTenantTenantIdUpdateVersionWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdUpdateVersionJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdUpdateVersionResponse, error)

	// PostTenantTenantIdUpgradeMetaStoreWithResponse request
	PostTenantTenantIdUpgradeMetaStoreWithResponse(ctx context.Context, tenantId uint64, params *PostTenantTenantIdUpgradeMetaStoreParams, reqEditors ...RequestEditorFn) (*PostTenantTenantIdUpgradeMetaStoreResponse, error)

	// GetTenantsWithResponse request
	GetTenantsWithResponse(ctx context.Context, params *GetTenantsParams, reqEditors ...RequestEditorFn) (*GetTenantsResponse, error)

	// DeleteTenantsTenantIdExtensionsIcebergCompactionWithResponse request
	DeleteTenantsTenantIdExtensionsIcebergCompactionWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*DeleteTenantsTenantIdExtensionsIcebergCompactionResponse, error)

	// GetTenantsTenantIdExtensionsIcebergCompactionWithResponse request
	GetTenantsTenantIdExtensionsIcebergCompactionWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*GetTenantsTenantIdExtensionsIcebergCompactionResponse, error)

	// PostTenantsTenantIdExtensionsIcebergCompactionWithBodyWithResponse request with any body
	PostTenantsTenantIdExtensionsIcebergCompactionWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantsTenantIdExtensionsIcebergCompactionResponse, error)

	PostTenantsTenantIdExtensionsIcebergCompactionWithResponse(ctx context.Context, tenantId uint64, body PostTenantsTenantIdExtensionsIcebergCompactionJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantsTenantIdExtensionsIcebergCompactionResponse, error)

	// PutTenantsTenantIdExtensionsIcebergCompactionWithBodyWithResponse request with any body
	PutTenantsTenantIdExtensionsIcebergCompactionWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutTenantsTenantIdExtensionsIcebergCompactionResponse, error)

	PutTenantsTenantIdExtensionsIcebergCompactionWithResponse(ctx context.Context, tenantId uint64, body PutTenantsTenantIdExtensionsIcebergCompactionJSONRequestBody, reqEditors ...RequestEditorFn) (*PutTenantsTenantIdExtensionsIcebergCompactionResponse, error)

	// GetVersionWithResponse request
	GetVersionWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetVersionResponse, error)

	// PostWebhooksAlertWithResponse request
	PostWebhooksAlertWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*PostWebhooksAlertResponse, error)

	// GetWorkflowIdWithResponse request
	GetWorkflowIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetWorkflowIdResponse, error)

	// PostWorkflowIdCancelWithResponse request
	PostWorkflowIdCancelWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*PostWorkflowIdCancelResponse, error)

	// PostWorkflowIdRerunWithBodyWithResponse request with any body
	PostWorkflowIdRerunWithBodyWithResponse(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostWorkflowIdRerunResponse, error)

	PostWorkflowIdRerunWithResponse(ctx context.Context, id openapi_types.UUID, body PostWorkflowIdRerunJSONRequestBody, reqEditors ...RequestEditorFn) (*PostWorkflowIdRerunResponse, error)

	// PostWorkflowIdResumeWithResponse request
	PostWorkflowIdResumeWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*PostWorkflowIdResumeResponse, error)

	// PostWorkflowIdScheduleWithBodyWithResponse request with any body
	PostWorkflowIdScheduleWithBodyWithResponse(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostWorkflowIdScheduleResponse, error)

	PostWorkflowIdScheduleWithResponse(ctx context.Context, id openapi_types.UUID, body PostWorkflowIdScheduleJSONRequestBody, reqEditors ...RequestEditorFn) (*PostWorkflowIdScheduleResponse, error)

	// GetWorkflowsWithResponse request
	GetWorkflowsWithResponse(ctx context.Context, params *GetWorkflowsParams, reqEditors ...RequestEditorFn) (*GetWorkflowsResponse, error)
}

type GetResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r GetResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostAlertNotificationsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r PostAlertNotificationsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostAlertNotificationsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostByocClusterIdAccessResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ClusterAccessInfo
	JSON400      *BadRequestResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PostByocClusterIdAccessResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostByocClusterIdAccessResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostByocClusterIdUpdateResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *WorkflowIdResponseBody
	JSON400      *BadRequestResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PostByocClusterIdUpdateResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostByocClusterIdUpdateResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetByocClustersResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ManagedClusters
}

// Status returns HTTPResponse.Status
func (r GetByocClustersResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetByocClustersResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteClusterNameResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
	JSON400      *BadRequestResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r DeleteClusterNameResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteClusterNameResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetClusterNameResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ManagedCluster
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r GetClusterNameResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetClusterNameResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostClusterNameResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r PostClusterNameResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostClusterNameResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetClustersResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ManagedClusters
}

// Status returns HTTPResponse.Status
func (r GetClustersResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetClustersResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostClustersResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r PostClustersResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostClustersResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetClustersUuidPrometheusApiV1LabelLabelNameValuesResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON412      *ClusterStatusResponse
}

// Status returns HTTPResponse.Status
func (r GetClustersUuidPrometheusApiV1LabelLabelNameValuesResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetClustersUuidPrometheusApiV1LabelLabelNameValuesResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostClustersUuidPrometheusApiV1LabelLabelNameValuesResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON412      *ClusterStatusResponse
}

// Status returns HTTPResponse.Status
func (r PostClustersUuidPrometheusApiV1LabelLabelNameValuesResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostClustersUuidPrometheusApiV1LabelLabelNameValuesResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetClustersUuidPrometheusApiV1QueryResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON412      *ClusterStatusResponse
}

// Status returns HTTPResponse.Status
func (r GetClustersUuidPrometheusApiV1QueryResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetClustersUuidPrometheusApiV1QueryResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostClustersUuidPrometheusApiV1QueryResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON412      *ClusterStatusResponse
}

// Status returns HTTPResponse.Status
func (r PostClustersUuidPrometheusApiV1QueryResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostClustersUuidPrometheusApiV1QueryResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetClustersUuidPrometheusApiV1QueryRangeResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON412      *ClusterStatusResponse
}

// Status returns HTTPResponse.Status
func (r GetClustersUuidPrometheusApiV1QueryRangeResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetClustersUuidPrometheusApiV1QueryRangeResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostClustersUuidPrometheusApiV1QueryRangeResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON412      *ClusterStatusResponse
}

// Status returns HTTPResponse.Status
func (r PostClustersUuidPrometheusApiV1QueryRangeResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostClustersUuidPrometheusApiV1QueryRangeResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetClustersUuidPrometheusApiV1SeriesResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON412      *ClusterStatusResponse
}

// Status returns HTTPResponse.Status
func (r GetClustersUuidPrometheusApiV1SeriesResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetClustersUuidPrometheusApiV1SeriesResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostClustersUuidPrometheusApiV1SeriesResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON412      *ClusterStatusResponse
}

// Status returns HTTPResponse.Status
func (r PostClustersUuidPrometheusApiV1SeriesResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostClustersUuidPrometheusApiV1SeriesResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteTenantTenantIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *WorkflowIdResponseBody
	JSON400      *BadRequestResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r DeleteTenantTenantIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteTenantTenantIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetTenantTenantIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *Tenant
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r GetTenantTenantIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetTenantTenantIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdBackupResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *PostSnapshotResponseBody
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdBackupResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdBackupResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteTenantTenantIdBackupSnapshotIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *OptionalWorkflowIdResponseBody
}

// Status returns HTTPResponse.Status
func (r DeleteTenantTenantIdBackupSnapshotIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteTenantTenantIdBackupSnapshotIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdBackupSnapshotIdInPlaceRestoreResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *WorkflowIdResponseBody
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdBackupSnapshotIdInPlaceRestoreResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdBackupSnapshotIdInPlaceRestoreResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdBackupSnapshotIdRestoreResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *Tenant
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdBackupSnapshotIdRestoreResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdBackupSnapshotIdRestoreResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdComputeCacheResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *WorkflowIdResponseBody
	JSON400      *BadRequestResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdComputeCacheResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdComputeCacheResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutTenantTenantIdConfigEtcdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *WorkflowIdResponseBody
	JSON400      *BadRequestResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PutTenantTenantIdConfigEtcdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutTenantTenantIdConfigEtcdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutTenantTenantIdConfigRisingwaveResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *WorkflowIdResponseBody
	JSON400      *BadRequestResponse
	JSON404      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r PutTenantTenantIdConfigRisingwaveResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutTenantTenantIdConfigRisingwaveResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutTenantTenantIdConfigNoRestartRisingwaveResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *WorkflowIdResponseBody
	JSON400      *BadRequestResponse
	JSON404      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r PutTenantTenantIdConfigNoRestartRisingwaveResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutTenantTenantIdConfigNoRestartRisingwaveResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GenDiagReportResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *RwDiagReport
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r GenDiagReportResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GenDiagReportResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type ListDiagReportTimeRangeResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *RwDiagReportMetaPage
}

// Status returns HTTPResponse.Status
func (r ListDiagReportTimeRangeResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r ListDiagReportTimeRangeResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetDiagReportResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *RwDiagReport
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r GetDiagReportResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetDiagReportResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetTenantTenantIdEndpointResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DatabaseConnectionUrl
	JSON401      *DefaultResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r GetTenantTenantIdEndpointResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetTenantTenantIdEndpointResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteTenantTenantIdExpireResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r DeleteTenantTenantIdExpireResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteTenantTenantIdExpireResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetTenantTenantIdExpireResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *TenantExpireInfo
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r GetTenantTenantIdExpireResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetTenantTenantIdExpireResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdExpireResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdExpireResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdExpireResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdExtensionsCompactionDisableResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *WorkflowIdResponseBody
	JSON400      *BadRequestResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdExtensionsCompactionDisableResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdExtensionsCompactionDisableResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdExtensionsCompactionEnableResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *WorkflowIdResponseBody
	JSON400      *FailedPreconditionResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdExtensionsCompactionEnableResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdExtensionsCompactionEnableResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetTenantTenantIdExtensionsCompactionParametersResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *GetTenantExtensionCompactionParametersResponseBody
	JSON400      *FailedPreconditionResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r GetTenantTenantIdExtensionsCompactionParametersResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetTenantTenantIdExtensionsCompactionParametersResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetTenantTenantIdExtensionsCompactionStatusResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *GetTenantExtensionCompactionStatusResponseBody
	JSON400      *BadRequestResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r GetTenantTenantIdExtensionsCompactionStatusResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetTenantTenantIdExtensionsCompactionStatusResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdExtensionsCompactionStatusResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *GetTenantExtensionCompactionStatusResponseBody
	JSON400      *BadRequestResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdExtensionsCompactionStatusResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdExtensionsCompactionStatusResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdExtensionsCompactionUpdateResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *WorkflowIdResponseBody
	JSON400      *FailedPreconditionResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdExtensionsCompactionUpdateResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdExtensionsCompactionUpdateResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdExtensionsServerlessbackfillDisableResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *WorkflowIdResponseBody
	JSON400      *FailedPreconditionResponse
	JSON404      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdExtensionsServerlessbackfillDisableResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdExtensionsServerlessbackfillDisableResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdExtensionsServerlessbackfillEnableResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *WorkflowIdResponseBody
	JSON400      *FailedPreconditionResponse
	JSON404      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdExtensionsServerlessbackfillEnableResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdExtensionsServerlessbackfillEnableResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdExtensionsServerlessbackfillVersionResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *WorkflowIdResponseBody
	JSON400      *FailedPreconditionResponse
	JSON404      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdExtensionsServerlessbackfillVersionResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdExtensionsServerlessbackfillVersionResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdMigrateMetaStoreResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *WorkflowIdResponse
	JSON400      *FailedPreconditionResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdMigrateMetaStoreResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdMigrateMetaStoreResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteTenantTenantIdPrivatelinkPrivateLinkIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *WorkflowIdResponseBody
	JSON400      *BadRequestResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r DeleteTenantTenantIdPrivatelinkPrivateLinkIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteTenantTenantIdPrivatelinkPrivateLinkIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdResourceResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *WorkflowIdResponseBody
	JSON400      *BadRequestResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdResourceResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdResourceResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdResourceGroupsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *WorkflowIdResponseBody
	JSON400      *FailedPreconditionResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdResourceGroupsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdResourceGroupsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteTenantTenantIdResourceGroupsResourceGroupResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *WorkflowIdResponseBody
	JSON400      *FailedPreconditionResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r DeleteTenantTenantIdResourceGroupsResourceGroupResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteTenantTenantIdResourceGroupsResourceGroupResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdResourceGroupsResourceGroupResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *WorkflowIdResponseBody
	JSON400      *FailedPreconditionResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdResourceGroupsResourceGroupResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdResourceGroupsResourceGroupResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdRestartResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *WorkflowIdResponseBody
	JSON400      *BadRequestResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdRestartResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdRestartResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdStartResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *WorkflowIdResponseBody
	JSON400      *BadRequestResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdStartResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdStartResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdStatusResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
	JSON400      *BadRequestResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdStatusResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdStatusResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdStopResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *WorkflowIdResponseBody
	JSON400      *BadRequestResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdStopResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdStopResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdTierResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
	JSON400      *BadRequestResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdTierResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdTierResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdUpdateVersionResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *WorkflowIdResponseBody
	JSON400      *BadRequestResponse
	JSON404      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdUpdateVersionResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdUpdateVersionResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdUpgradeMetaStoreResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *WorkflowIdResponse
	JSON400      *FailedPreconditionResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdUpgradeMetaStoreResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdUpgradeMetaStoreResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetTenantsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *TenantPage
}

// Status returns HTTPResponse.Status
func (r GetTenantsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetTenantsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteTenantsTenantIdExtensionsIcebergCompactionResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *WorkflowIdResponseBody
	JSON400      *FailedPreconditionResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r DeleteTenantsTenantIdExtensionsIcebergCompactionResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteTenantsTenantIdExtensionsIcebergCompactionResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetTenantsTenantIdExtensionsIcebergCompactionResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *IcebergCompaction
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r GetTenantsTenantIdExtensionsIcebergCompactionResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetTenantsTenantIdExtensionsIcebergCompactionResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantsTenantIdExtensionsIcebergCompactionResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *WorkflowIdResponseBody
	JSON400      *FailedPreconditionResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantsTenantIdExtensionsIcebergCompactionResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantsTenantIdExtensionsIcebergCompactionResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutTenantsTenantIdExtensionsIcebergCompactionResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *WorkflowIdResponseBody
	JSON400      *FailedPreconditionResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PutTenantsTenantIdExtensionsIcebergCompactionResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutTenantsTenantIdExtensionsIcebergCompactionResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetVersionResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r GetVersionResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetVersionResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostWebhooksAlertResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r PostWebhooksAlertResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostWebhooksAlertResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetWorkflowIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *WorkflowWithEvents
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r GetWorkflowIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetWorkflowIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostWorkflowIdCancelResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON400      *BadRequestResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PostWorkflowIdCancelResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostWorkflowIdCancelResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostWorkflowIdRerunResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *WorkflowIdResponseBody
	JSON400      *BadRequestResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PostWorkflowIdRerunResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostWorkflowIdRerunResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostWorkflowIdResumeResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON400      *BadRequestResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PostWorkflowIdResumeResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostWorkflowIdResumeResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostWorkflowIdScheduleResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON400      *BadRequestResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PostWorkflowIdScheduleResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostWorkflowIdScheduleResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetWorkflowsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *WorkflowPage
}

// Status returns HTTPResponse.Status
func (r GetWorkflowsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetWorkflowsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

// GetWithResponse request returning *GetResponse
func (c *ClientWithResponses) GetWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetResponse, error) {
	rsp, err := c.Get(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetResponse(rsp)
}

// PostAlertNotificationsWithBodyWithResponse request with arbitrary body returning *PostAlertNotificationsResponse
func (c *ClientWithResponses) PostAlertNotificationsWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostAlertNotificationsResponse, error) {
	rsp, err := c.PostAlertNotificationsWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostAlertNotificationsResponse(rsp)
}

func (c *ClientWithResponses) PostAlertNotificationsWithResponse(ctx context.Context, body PostAlertNotificationsJSONRequestBody, reqEditors ...RequestEditorFn) (*PostAlertNotificationsResponse, error) {
	rsp, err := c.PostAlertNotifications(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostAlertNotificationsResponse(rsp)
}

// PostByocClusterIdAccessWithBodyWithResponse request with arbitrary body returning *PostByocClusterIdAccessResponse
func (c *ClientWithResponses) PostByocClusterIdAccessWithBodyWithResponse(ctx context.Context, id uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostByocClusterIdAccessResponse, error) {
	rsp, err := c.PostByocClusterIdAccessWithBody(ctx, id, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostByocClusterIdAccessResponse(rsp)
}

func (c *ClientWithResponses) PostByocClusterIdAccessWithResponse(ctx context.Context, id uint64, body PostByocClusterIdAccessJSONRequestBody, reqEditors ...RequestEditorFn) (*PostByocClusterIdAccessResponse, error) {
	rsp, err := c.PostByocClusterIdAccess(ctx, id, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostByocClusterIdAccessResponse(rsp)
}

// PostByocClusterIdUpdateWithBodyWithResponse request with arbitrary body returning *PostByocClusterIdUpdateResponse
func (c *ClientWithResponses) PostByocClusterIdUpdateWithBodyWithResponse(ctx context.Context, id uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostByocClusterIdUpdateResponse, error) {
	rsp, err := c.PostByocClusterIdUpdateWithBody(ctx, id, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostByocClusterIdUpdateResponse(rsp)
}

func (c *ClientWithResponses) PostByocClusterIdUpdateWithResponse(ctx context.Context, id uint64, body PostByocClusterIdUpdateJSONRequestBody, reqEditors ...RequestEditorFn) (*PostByocClusterIdUpdateResponse, error) {
	rsp, err := c.PostByocClusterIdUpdate(ctx, id, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostByocClusterIdUpdateResponse(rsp)
}

// GetByocClustersWithResponse request returning *GetByocClustersResponse
func (c *ClientWithResponses) GetByocClustersWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetByocClustersResponse, error) {
	rsp, err := c.GetByocClusters(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetByocClustersResponse(rsp)
}

// DeleteClusterNameWithResponse request returning *DeleteClusterNameResponse
func (c *ClientWithResponses) DeleteClusterNameWithResponse(ctx context.Context, name string, reqEditors ...RequestEditorFn) (*DeleteClusterNameResponse, error) {
	rsp, err := c.DeleteClusterName(ctx, name, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteClusterNameResponse(rsp)
}

// GetClusterNameWithResponse request returning *GetClusterNameResponse
func (c *ClientWithResponses) GetClusterNameWithResponse(ctx context.Context, name string, reqEditors ...RequestEditorFn) (*GetClusterNameResponse, error) {
	rsp, err := c.GetClusterName(ctx, name, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetClusterNameResponse(rsp)
}

// PostClusterNameWithBodyWithResponse request with arbitrary body returning *PostClusterNameResponse
func (c *ClientWithResponses) PostClusterNameWithBodyWithResponse(ctx context.Context, name string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostClusterNameResponse, error) {
	rsp, err := c.PostClusterNameWithBody(ctx, name, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostClusterNameResponse(rsp)
}

func (c *ClientWithResponses) PostClusterNameWithResponse(ctx context.Context, name string, body PostClusterNameJSONRequestBody, reqEditors ...RequestEditorFn) (*PostClusterNameResponse, error) {
	rsp, err := c.PostClusterName(ctx, name, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostClusterNameResponse(rsp)
}

// GetClustersWithResponse request returning *GetClustersResponse
func (c *ClientWithResponses) GetClustersWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetClustersResponse, error) {
	rsp, err := c.GetClusters(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetClustersResponse(rsp)
}

// PostClustersWithBodyWithResponse request with arbitrary body returning *PostClustersResponse
func (c *ClientWithResponses) PostClustersWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostClustersResponse, error) {
	rsp, err := c.PostClustersWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostClustersResponse(rsp)
}

func (c *ClientWithResponses) PostClustersWithResponse(ctx context.Context, body PostClustersJSONRequestBody, reqEditors ...RequestEditorFn) (*PostClustersResponse, error) {
	rsp, err := c.PostClusters(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostClustersResponse(rsp)
}

// GetClustersUuidPrometheusApiV1LabelLabelNameValuesWithResponse request returning *GetClustersUuidPrometheusApiV1LabelLabelNameValuesResponse
func (c *ClientWithResponses) GetClustersUuidPrometheusApiV1LabelLabelNameValuesWithResponse(ctx context.Context, uuid openapi_types.UUID, labelName string, reqEditors ...RequestEditorFn) (*GetClustersUuidPrometheusApiV1LabelLabelNameValuesResponse, error) {
	rsp, err := c.GetClustersUuidPrometheusApiV1LabelLabelNameValues(ctx, uuid, labelName, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetClustersUuidPrometheusApiV1LabelLabelNameValuesResponse(rsp)
}

// PostClustersUuidPrometheusApiV1LabelLabelNameValuesWithResponse request returning *PostClustersUuidPrometheusApiV1LabelLabelNameValuesResponse
func (c *ClientWithResponses) PostClustersUuidPrometheusApiV1LabelLabelNameValuesWithResponse(ctx context.Context, uuid openapi_types.UUID, labelName string, reqEditors ...RequestEditorFn) (*PostClustersUuidPrometheusApiV1LabelLabelNameValuesResponse, error) {
	rsp, err := c.PostClustersUuidPrometheusApiV1LabelLabelNameValues(ctx, uuid, labelName, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostClustersUuidPrometheusApiV1LabelLabelNameValuesResponse(rsp)
}

// GetClustersUuidPrometheusApiV1QueryWithResponse request returning *GetClustersUuidPrometheusApiV1QueryResponse
func (c *ClientWithResponses) GetClustersUuidPrometheusApiV1QueryWithResponse(ctx context.Context, uuid openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetClustersUuidPrometheusApiV1QueryResponse, error) {
	rsp, err := c.GetClustersUuidPrometheusApiV1Query(ctx, uuid, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetClustersUuidPrometheusApiV1QueryResponse(rsp)
}

// PostClustersUuidPrometheusApiV1QueryWithResponse request returning *PostClustersUuidPrometheusApiV1QueryResponse
func (c *ClientWithResponses) PostClustersUuidPrometheusApiV1QueryWithResponse(ctx context.Context, uuid openapi_types.UUID, reqEditors ...RequestEditorFn) (*PostClustersUuidPrometheusApiV1QueryResponse, error) {
	rsp, err := c.PostClustersUuidPrometheusApiV1Query(ctx, uuid, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostClustersUuidPrometheusApiV1QueryResponse(rsp)
}

// GetClustersUuidPrometheusApiV1QueryRangeWithResponse request returning *GetClustersUuidPrometheusApiV1QueryRangeResponse
func (c *ClientWithResponses) GetClustersUuidPrometheusApiV1QueryRangeWithResponse(ctx context.Context, uuid openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetClustersUuidPrometheusApiV1QueryRangeResponse, error) {
	rsp, err := c.GetClustersUuidPrometheusApiV1QueryRange(ctx, uuid, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetClustersUuidPrometheusApiV1QueryRangeResponse(rsp)
}

// PostClustersUuidPrometheusApiV1QueryRangeWithResponse request returning *PostClustersUuidPrometheusApiV1QueryRangeResponse
func (c *ClientWithResponses) PostClustersUuidPrometheusApiV1QueryRangeWithResponse(ctx context.Context, uuid openapi_types.UUID, reqEditors ...RequestEditorFn) (*PostClustersUuidPrometheusApiV1QueryRangeResponse, error) {
	rsp, err := c.PostClustersUuidPrometheusApiV1QueryRange(ctx, uuid, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostClustersUuidPrometheusApiV1QueryRangeResponse(rsp)
}

// GetClustersUuidPrometheusApiV1SeriesWithResponse request returning *GetClustersUuidPrometheusApiV1SeriesResponse
func (c *ClientWithResponses) GetClustersUuidPrometheusApiV1SeriesWithResponse(ctx context.Context, uuid openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetClustersUuidPrometheusApiV1SeriesResponse, error) {
	rsp, err := c.GetClustersUuidPrometheusApiV1Series(ctx, uuid, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetClustersUuidPrometheusApiV1SeriesResponse(rsp)
}

// PostClustersUuidPrometheusApiV1SeriesWithResponse request returning *PostClustersUuidPrometheusApiV1SeriesResponse
func (c *ClientWithResponses) PostClustersUuidPrometheusApiV1SeriesWithResponse(ctx context.Context, uuid openapi_types.UUID, reqEditors ...RequestEditorFn) (*PostClustersUuidPrometheusApiV1SeriesResponse, error) {
	rsp, err := c.PostClustersUuidPrometheusApiV1Series(ctx, uuid, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostClustersUuidPrometheusApiV1SeriesResponse(rsp)
}

// DeleteTenantTenantIdWithResponse request returning *DeleteTenantTenantIdResponse
func (c *ClientWithResponses) DeleteTenantTenantIdWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*DeleteTenantTenantIdResponse, error) {
	rsp, err := c.DeleteTenantTenantId(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteTenantTenantIdResponse(rsp)
}

// GetTenantTenantIdWithResponse request returning *GetTenantTenantIdResponse
func (c *ClientWithResponses) GetTenantTenantIdWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*GetTenantTenantIdResponse, error) {
	rsp, err := c.GetTenantTenantId(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetTenantTenantIdResponse(rsp)
}

// PostTenantTenantIdBackupWithResponse request returning *PostTenantTenantIdBackupResponse
func (c *ClientWithResponses) PostTenantTenantIdBackupWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PostTenantTenantIdBackupResponse, error) {
	rsp, err := c.PostTenantTenantIdBackup(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdBackupResponse(rsp)
}

// DeleteTenantTenantIdBackupSnapshotIdWithResponse request returning *DeleteTenantTenantIdBackupSnapshotIdResponse
func (c *ClientWithResponses) DeleteTenantTenantIdBackupSnapshotIdWithResponse(ctx context.Context, tenantId uint64, snapshotId openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteTenantTenantIdBackupSnapshotIdResponse, error) {
	rsp, err := c.DeleteTenantTenantIdBackupSnapshotId(ctx, tenantId, snapshotId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteTenantTenantIdBackupSnapshotIdResponse(rsp)
}

// PostTenantTenantIdBackupSnapshotIdInPlaceRestoreWithResponse request returning *PostTenantTenantIdBackupSnapshotIdInPlaceRestoreResponse
func (c *ClientWithResponses) PostTenantTenantIdBackupSnapshotIdInPlaceRestoreWithResponse(ctx context.Context, tenantId uint64, snapshotId openapi_types.UUID, reqEditors ...RequestEditorFn) (*PostTenantTenantIdBackupSnapshotIdInPlaceRestoreResponse, error) {
	rsp, err := c.PostTenantTenantIdBackupSnapshotIdInPlaceRestore(ctx, tenantId, snapshotId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdBackupSnapshotIdInPlaceRestoreResponse(rsp)
}

// PostTenantTenantIdBackupSnapshotIdRestoreWithBodyWithResponse request with arbitrary body returning *PostTenantTenantIdBackupSnapshotIdRestoreResponse
func (c *ClientWithResponses) PostTenantTenantIdBackupSnapshotIdRestoreWithBodyWithResponse(ctx context.Context, tenantId uint64, snapshotId openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdBackupSnapshotIdRestoreResponse, error) {
	rsp, err := c.PostTenantTenantIdBackupSnapshotIdRestoreWithBody(ctx, tenantId, snapshotId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdBackupSnapshotIdRestoreResponse(rsp)
}

func (c *ClientWithResponses) PostTenantTenantIdBackupSnapshotIdRestoreWithResponse(ctx context.Context, tenantId uint64, snapshotId openapi_types.UUID, body PostTenantTenantIdBackupSnapshotIdRestoreJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdBackupSnapshotIdRestoreResponse, error) {
	rsp, err := c.PostTenantTenantIdBackupSnapshotIdRestore(ctx, tenantId, snapshotId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdBackupSnapshotIdRestoreResponse(rsp)
}

// PostTenantTenantIdComputeCacheWithBodyWithResponse request with arbitrary body returning *PostTenantTenantIdComputeCacheResponse
func (c *ClientWithResponses) PostTenantTenantIdComputeCacheWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdComputeCacheResponse, error) {
	rsp, err := c.PostTenantTenantIdComputeCacheWithBody(ctx, tenantId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdComputeCacheResponse(rsp)
}

func (c *ClientWithResponses) PostTenantTenantIdComputeCacheWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdComputeCacheJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdComputeCacheResponse, error) {
	rsp, err := c.PostTenantTenantIdComputeCache(ctx, tenantId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdComputeCacheResponse(rsp)
}

// PutTenantTenantIdConfigEtcdWithBodyWithResponse request with arbitrary body returning *PutTenantTenantIdConfigEtcdResponse
func (c *ClientWithResponses) PutTenantTenantIdConfigEtcdWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutTenantTenantIdConfigEtcdResponse, error) {
	rsp, err := c.PutTenantTenantIdConfigEtcdWithBody(ctx, tenantId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutTenantTenantIdConfigEtcdResponse(rsp)
}

func (c *ClientWithResponses) PutTenantTenantIdConfigEtcdWithTextBodyWithResponse(ctx context.Context, tenantId uint64, body PutTenantTenantIdConfigEtcdTextRequestBody, reqEditors ...RequestEditorFn) (*PutTenantTenantIdConfigEtcdResponse, error) {
	rsp, err := c.PutTenantTenantIdConfigEtcdWithTextBody(ctx, tenantId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutTenantTenantIdConfigEtcdResponse(rsp)
}

// PutTenantTenantIdConfigRisingwaveWithBodyWithResponse request with arbitrary body returning *PutTenantTenantIdConfigRisingwaveResponse
func (c *ClientWithResponses) PutTenantTenantIdConfigRisingwaveWithBodyWithResponse(ctx context.Context, tenantId uint64, params *PutTenantTenantIdConfigRisingwaveParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutTenantTenantIdConfigRisingwaveResponse, error) {
	rsp, err := c.PutTenantTenantIdConfigRisingwaveWithBody(ctx, tenantId, params, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutTenantTenantIdConfigRisingwaveResponse(rsp)
}

func (c *ClientWithResponses) PutTenantTenantIdConfigRisingwaveWithTextBodyWithResponse(ctx context.Context, tenantId uint64, params *PutTenantTenantIdConfigRisingwaveParams, body PutTenantTenantIdConfigRisingwaveTextRequestBody, reqEditors ...RequestEditorFn) (*PutTenantTenantIdConfigRisingwaveResponse, error) {
	rsp, err := c.PutTenantTenantIdConfigRisingwaveWithTextBody(ctx, tenantId, params, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutTenantTenantIdConfigRisingwaveResponse(rsp)
}

// PutTenantTenantIdConfigNoRestartRisingwaveWithBodyWithResponse request with arbitrary body returning *PutTenantTenantIdConfigNoRestartRisingwaveResponse
func (c *ClientWithResponses) PutTenantTenantIdConfigNoRestartRisingwaveWithBodyWithResponse(ctx context.Context, tenantId uint64, params *PutTenantTenantIdConfigNoRestartRisingwaveParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutTenantTenantIdConfigNoRestartRisingwaveResponse, error) {
	rsp, err := c.PutTenantTenantIdConfigNoRestartRisingwaveWithBody(ctx, tenantId, params, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutTenantTenantIdConfigNoRestartRisingwaveResponse(rsp)
}

func (c *ClientWithResponses) PutTenantTenantIdConfigNoRestartRisingwaveWithTextBodyWithResponse(ctx context.Context, tenantId uint64, params *PutTenantTenantIdConfigNoRestartRisingwaveParams, body PutTenantTenantIdConfigNoRestartRisingwaveTextRequestBody, reqEditors ...RequestEditorFn) (*PutTenantTenantIdConfigNoRestartRisingwaveResponse, error) {
	rsp, err := c.PutTenantTenantIdConfigNoRestartRisingwaveWithTextBody(ctx, tenantId, params, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutTenantTenantIdConfigNoRestartRisingwaveResponse(rsp)
}

// GenDiagReportWithResponse request returning *GenDiagReportResponse
func (c *ClientWithResponses) GenDiagReportWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*GenDiagReportResponse, error) {
	rsp, err := c.GenDiagReport(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGenDiagReportResponse(rsp)
}

// ListDiagReportTimeRangeWithResponse request returning *ListDiagReportTimeRangeResponse
func (c *ClientWithResponses) ListDiagReportTimeRangeWithResponse(ctx context.Context, tenantId uint64, params *ListDiagReportTimeRangeParams, reqEditors ...RequestEditorFn) (*ListDiagReportTimeRangeResponse, error) {
	rsp, err := c.ListDiagReportTimeRange(ctx, tenantId, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseListDiagReportTimeRangeResponse(rsp)
}

// GetDiagReportWithResponse request returning *GetDiagReportResponse
func (c *ClientWithResponses) GetDiagReportWithResponse(ctx context.Context, tenantId uint64, reportId uint64, reqEditors ...RequestEditorFn) (*GetDiagReportResponse, error) {
	rsp, err := c.GetDiagReport(ctx, tenantId, reportId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetDiagReportResponse(rsp)
}

// GetTenantTenantIdEndpointWithResponse request returning *GetTenantTenantIdEndpointResponse
func (c *ClientWithResponses) GetTenantTenantIdEndpointWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*GetTenantTenantIdEndpointResponse, error) {
	rsp, err := c.GetTenantTenantIdEndpoint(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetTenantTenantIdEndpointResponse(rsp)
}

// DeleteTenantTenantIdExpireWithResponse request returning *DeleteTenantTenantIdExpireResponse
func (c *ClientWithResponses) DeleteTenantTenantIdExpireWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*DeleteTenantTenantIdExpireResponse, error) {
	rsp, err := c.DeleteTenantTenantIdExpire(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteTenantTenantIdExpireResponse(rsp)
}

// GetTenantTenantIdExpireWithResponse request returning *GetTenantTenantIdExpireResponse
func (c *ClientWithResponses) GetTenantTenantIdExpireWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*GetTenantTenantIdExpireResponse, error) {
	rsp, err := c.GetTenantTenantIdExpire(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetTenantTenantIdExpireResponse(rsp)
}

// PostTenantTenantIdExpireWithBodyWithResponse request with arbitrary body returning *PostTenantTenantIdExpireResponse
func (c *ClientWithResponses) PostTenantTenantIdExpireWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExpireResponse, error) {
	rsp, err := c.PostTenantTenantIdExpireWithBody(ctx, tenantId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdExpireResponse(rsp)
}

func (c *ClientWithResponses) PostTenantTenantIdExpireWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdExpireJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExpireResponse, error) {
	rsp, err := c.PostTenantTenantIdExpire(ctx, tenantId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdExpireResponse(rsp)
}

// PostTenantTenantIdExtensionsCompactionDisableWithResponse request returning *PostTenantTenantIdExtensionsCompactionDisableResponse
func (c *ClientWithResponses) PostTenantTenantIdExtensionsCompactionDisableWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsCompactionDisableResponse, error) {
	rsp, err := c.PostTenantTenantIdExtensionsCompactionDisable(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdExtensionsCompactionDisableResponse(rsp)
}

// PostTenantTenantIdExtensionsCompactionEnableWithResponse request returning *PostTenantTenantIdExtensionsCompactionEnableResponse
func (c *ClientWithResponses) PostTenantTenantIdExtensionsCompactionEnableWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsCompactionEnableResponse, error) {
	rsp, err := c.PostTenantTenantIdExtensionsCompactionEnable(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdExtensionsCompactionEnableResponse(rsp)
}

// GetTenantTenantIdExtensionsCompactionParametersWithResponse request returning *GetTenantTenantIdExtensionsCompactionParametersResponse
func (c *ClientWithResponses) GetTenantTenantIdExtensionsCompactionParametersWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*GetTenantTenantIdExtensionsCompactionParametersResponse, error) {
	rsp, err := c.GetTenantTenantIdExtensionsCompactionParameters(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetTenantTenantIdExtensionsCompactionParametersResponse(rsp)
}

// GetTenantTenantIdExtensionsCompactionStatusWithResponse request returning *GetTenantTenantIdExtensionsCompactionStatusResponse
func (c *ClientWithResponses) GetTenantTenantIdExtensionsCompactionStatusWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*GetTenantTenantIdExtensionsCompactionStatusResponse, error) {
	rsp, err := c.GetTenantTenantIdExtensionsCompactionStatus(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetTenantTenantIdExtensionsCompactionStatusResponse(rsp)
}

// PostTenantTenantIdExtensionsCompactionStatusWithBodyWithResponse request with arbitrary body returning *PostTenantTenantIdExtensionsCompactionStatusResponse
func (c *ClientWithResponses) PostTenantTenantIdExtensionsCompactionStatusWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsCompactionStatusResponse, error) {
	rsp, err := c.PostTenantTenantIdExtensionsCompactionStatusWithBody(ctx, tenantId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdExtensionsCompactionStatusResponse(rsp)
}

func (c *ClientWithResponses) PostTenantTenantIdExtensionsCompactionStatusWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdExtensionsCompactionStatusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsCompactionStatusResponse, error) {
	rsp, err := c.PostTenantTenantIdExtensionsCompactionStatus(ctx, tenantId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdExtensionsCompactionStatusResponse(rsp)
}

// PostTenantTenantIdExtensionsCompactionUpdateWithBodyWithResponse request with arbitrary body returning *PostTenantTenantIdExtensionsCompactionUpdateResponse
func (c *ClientWithResponses) PostTenantTenantIdExtensionsCompactionUpdateWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsCompactionUpdateResponse, error) {
	rsp, err := c.PostTenantTenantIdExtensionsCompactionUpdateWithBody(ctx, tenantId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdExtensionsCompactionUpdateResponse(rsp)
}

func (c *ClientWithResponses) PostTenantTenantIdExtensionsCompactionUpdateWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdExtensionsCompactionUpdateJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsCompactionUpdateResponse, error) {
	rsp, err := c.PostTenantTenantIdExtensionsCompactionUpdate(ctx, tenantId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdExtensionsCompactionUpdateResponse(rsp)
}

// PostTenantTenantIdExtensionsServerlessbackfillDisableWithResponse request returning *PostTenantTenantIdExtensionsServerlessbackfillDisableResponse
func (c *ClientWithResponses) PostTenantTenantIdExtensionsServerlessbackfillDisableWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsServerlessbackfillDisableResponse, error) {
	rsp, err := c.PostTenantTenantIdExtensionsServerlessbackfillDisable(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdExtensionsServerlessbackfillDisableResponse(rsp)
}

// PostTenantTenantIdExtensionsServerlessbackfillEnableWithResponse request returning *PostTenantTenantIdExtensionsServerlessbackfillEnableResponse
func (c *ClientWithResponses) PostTenantTenantIdExtensionsServerlessbackfillEnableWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsServerlessbackfillEnableResponse, error) {
	rsp, err := c.PostTenantTenantIdExtensionsServerlessbackfillEnable(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdExtensionsServerlessbackfillEnableResponse(rsp)
}

// PostTenantTenantIdExtensionsServerlessbackfillVersionWithBodyWithResponse request with arbitrary body returning *PostTenantTenantIdExtensionsServerlessbackfillVersionResponse
func (c *ClientWithResponses) PostTenantTenantIdExtensionsServerlessbackfillVersionWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsServerlessbackfillVersionResponse, error) {
	rsp, err := c.PostTenantTenantIdExtensionsServerlessbackfillVersionWithBody(ctx, tenantId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdExtensionsServerlessbackfillVersionResponse(rsp)
}

func (c *ClientWithResponses) PostTenantTenantIdExtensionsServerlessbackfillVersionWithTextBodyWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdExtensionsServerlessbackfillVersionTextRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsServerlessbackfillVersionResponse, error) {
	rsp, err := c.PostTenantTenantIdExtensionsServerlessbackfillVersionWithTextBody(ctx, tenantId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdExtensionsServerlessbackfillVersionResponse(rsp)
}

// PostTenantTenantIdMigrateMetaStoreWithResponse request returning *PostTenantTenantIdMigrateMetaStoreResponse
func (c *ClientWithResponses) PostTenantTenantIdMigrateMetaStoreWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PostTenantTenantIdMigrateMetaStoreResponse, error) {
	rsp, err := c.PostTenantTenantIdMigrateMetaStore(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdMigrateMetaStoreResponse(rsp)
}

// DeleteTenantTenantIdPrivatelinkPrivateLinkIdWithResponse request returning *DeleteTenantTenantIdPrivatelinkPrivateLinkIdResponse
func (c *ClientWithResponses) DeleteTenantTenantIdPrivatelinkPrivateLinkIdWithResponse(ctx context.Context, tenantId uint64, privateLinkId openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteTenantTenantIdPrivatelinkPrivateLinkIdResponse, error) {
	rsp, err := c.DeleteTenantTenantIdPrivatelinkPrivateLinkId(ctx, tenantId, privateLinkId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteTenantTenantIdPrivatelinkPrivateLinkIdResponse(rsp)
}

// PostTenantTenantIdResourceWithBodyWithResponse request with arbitrary body returning *PostTenantTenantIdResourceResponse
func (c *ClientWithResponses) PostTenantTenantIdResourceWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdResourceResponse, error) {
	rsp, err := c.PostTenantTenantIdResourceWithBody(ctx, tenantId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdResourceResponse(rsp)
}

func (c *ClientWithResponses) PostTenantTenantIdResourceWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdResourceJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdResourceResponse, error) {
	rsp, err := c.PostTenantTenantIdResource(ctx, tenantId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdResourceResponse(rsp)
}

// PostTenantTenantIdResourceGroupsWithBodyWithResponse request with arbitrary body returning *PostTenantTenantIdResourceGroupsResponse
func (c *ClientWithResponses) PostTenantTenantIdResourceGroupsWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdResourceGroupsResponse, error) {
	rsp, err := c.PostTenantTenantIdResourceGroupsWithBody(ctx, tenantId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdResourceGroupsResponse(rsp)
}

func (c *ClientWithResponses) PostTenantTenantIdResourceGroupsWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdResourceGroupsJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdResourceGroupsResponse, error) {
	rsp, err := c.PostTenantTenantIdResourceGroups(ctx, tenantId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdResourceGroupsResponse(rsp)
}

// DeleteTenantTenantIdResourceGroupsResourceGroupWithResponse request returning *DeleteTenantTenantIdResourceGroupsResourceGroupResponse
func (c *ClientWithResponses) DeleteTenantTenantIdResourceGroupsResourceGroupWithResponse(ctx context.Context, tenantId uint64, resourceGroup string, reqEditors ...RequestEditorFn) (*DeleteTenantTenantIdResourceGroupsResourceGroupResponse, error) {
	rsp, err := c.DeleteTenantTenantIdResourceGroupsResourceGroup(ctx, tenantId, resourceGroup, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteTenantTenantIdResourceGroupsResourceGroupResponse(rsp)
}

// PostTenantTenantIdResourceGroupsResourceGroupWithBodyWithResponse request with arbitrary body returning *PostTenantTenantIdResourceGroupsResourceGroupResponse
func (c *ClientWithResponses) PostTenantTenantIdResourceGroupsResourceGroupWithBodyWithResponse(ctx context.Context, tenantId uint64, resourceGroup string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdResourceGroupsResourceGroupResponse, error) {
	rsp, err := c.PostTenantTenantIdResourceGroupsResourceGroupWithBody(ctx, tenantId, resourceGroup, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdResourceGroupsResourceGroupResponse(rsp)
}

func (c *ClientWithResponses) PostTenantTenantIdResourceGroupsResourceGroupWithResponse(ctx context.Context, tenantId uint64, resourceGroup string, body PostTenantTenantIdResourceGroupsResourceGroupJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdResourceGroupsResourceGroupResponse, error) {
	rsp, err := c.PostTenantTenantIdResourceGroupsResourceGroup(ctx, tenantId, resourceGroup, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdResourceGroupsResourceGroupResponse(rsp)
}

// PostTenantTenantIdRestartWithResponse request returning *PostTenantTenantIdRestartResponse
func (c *ClientWithResponses) PostTenantTenantIdRestartWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PostTenantTenantIdRestartResponse, error) {
	rsp, err := c.PostTenantTenantIdRestart(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdRestartResponse(rsp)
}

// PostTenantTenantIdStartWithResponse request returning *PostTenantTenantIdStartResponse
func (c *ClientWithResponses) PostTenantTenantIdStartWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PostTenantTenantIdStartResponse, error) {
	rsp, err := c.PostTenantTenantIdStart(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdStartResponse(rsp)
}

// PostTenantTenantIdStatusWithBodyWithResponse request with arbitrary body returning *PostTenantTenantIdStatusResponse
func (c *ClientWithResponses) PostTenantTenantIdStatusWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdStatusResponse, error) {
	rsp, err := c.PostTenantTenantIdStatusWithBody(ctx, tenantId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdStatusResponse(rsp)
}

func (c *ClientWithResponses) PostTenantTenantIdStatusWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdStatusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdStatusResponse, error) {
	rsp, err := c.PostTenantTenantIdStatus(ctx, tenantId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdStatusResponse(rsp)
}

// PostTenantTenantIdStopWithResponse request returning *PostTenantTenantIdStopResponse
func (c *ClientWithResponses) PostTenantTenantIdStopWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PostTenantTenantIdStopResponse, error) {
	rsp, err := c.PostTenantTenantIdStop(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdStopResponse(rsp)
}

// PostTenantTenantIdTierWithBodyWithResponse request with arbitrary body returning *PostTenantTenantIdTierResponse
func (c *ClientWithResponses) PostTenantTenantIdTierWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdTierResponse, error) {
	rsp, err := c.PostTenantTenantIdTierWithBody(ctx, tenantId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdTierResponse(rsp)
}

func (c *ClientWithResponses) PostTenantTenantIdTierWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdTierJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdTierResponse, error) {
	rsp, err := c.PostTenantTenantIdTier(ctx, tenantId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdTierResponse(rsp)
}

// PostTenantTenantIdUpdateVersionWithBodyWithResponse request with arbitrary body returning *PostTenantTenantIdUpdateVersionResponse
func (c *ClientWithResponses) PostTenantTenantIdUpdateVersionWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdUpdateVersionResponse, error) {
	rsp, err := c.PostTenantTenantIdUpdateVersionWithBody(ctx, tenantId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdUpdateVersionResponse(rsp)
}

func (c *ClientWithResponses) PostTenantTenantIdUpdateVersionWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdUpdateVersionJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdUpdateVersionResponse, error) {
	rsp, err := c.PostTenantTenantIdUpdateVersion(ctx, tenantId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdUpdateVersionResponse(rsp)
}

// PostTenantTenantIdUpgradeMetaStoreWithResponse request returning *PostTenantTenantIdUpgradeMetaStoreResponse
func (c *ClientWithResponses) PostTenantTenantIdUpgradeMetaStoreWithResponse(ctx context.Context, tenantId uint64, params *PostTenantTenantIdUpgradeMetaStoreParams, reqEditors ...RequestEditorFn) (*PostTenantTenantIdUpgradeMetaStoreResponse, error) {
	rsp, err := c.PostTenantTenantIdUpgradeMetaStore(ctx, tenantId, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdUpgradeMetaStoreResponse(rsp)
}

// GetTenantsWithResponse request returning *GetTenantsResponse
func (c *ClientWithResponses) GetTenantsWithResponse(ctx context.Context, params *GetTenantsParams, reqEditors ...RequestEditorFn) (*GetTenantsResponse, error) {
	rsp, err := c.GetTenants(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetTenantsResponse(rsp)
}

// DeleteTenantsTenantIdExtensionsIcebergCompactionWithResponse request returning *DeleteTenantsTenantIdExtensionsIcebergCompactionResponse
func (c *ClientWithResponses) DeleteTenantsTenantIdExtensionsIcebergCompactionWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*DeleteTenantsTenantIdExtensionsIcebergCompactionResponse, error) {
	rsp, err := c.DeleteTenantsTenantIdExtensionsIcebergCompaction(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteTenantsTenantIdExtensionsIcebergCompactionResponse(rsp)
}

// GetTenantsTenantIdExtensionsIcebergCompactionWithResponse request returning *GetTenantsTenantIdExtensionsIcebergCompactionResponse
func (c *ClientWithResponses) GetTenantsTenantIdExtensionsIcebergCompactionWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*GetTenantsTenantIdExtensionsIcebergCompactionResponse, error) {
	rsp, err := c.GetTenantsTenantIdExtensionsIcebergCompaction(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetTenantsTenantIdExtensionsIcebergCompactionResponse(rsp)
}

// PostTenantsTenantIdExtensionsIcebergCompactionWithBodyWithResponse request with arbitrary body returning *PostTenantsTenantIdExtensionsIcebergCompactionResponse
func (c *ClientWithResponses) PostTenantsTenantIdExtensionsIcebergCompactionWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantsTenantIdExtensionsIcebergCompactionResponse, error) {
	rsp, err := c.PostTenantsTenantIdExtensionsIcebergCompactionWithBody(ctx, tenantId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantsTenantIdExtensionsIcebergCompactionResponse(rsp)
}

func (c *ClientWithResponses) PostTenantsTenantIdExtensionsIcebergCompactionWithResponse(ctx context.Context, tenantId uint64, body PostTenantsTenantIdExtensionsIcebergCompactionJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantsTenantIdExtensionsIcebergCompactionResponse, error) {
	rsp, err := c.PostTenantsTenantIdExtensionsIcebergCompaction(ctx, tenantId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantsTenantIdExtensionsIcebergCompactionResponse(rsp)
}

// PutTenantsTenantIdExtensionsIcebergCompactionWithBodyWithResponse request with arbitrary body returning *PutTenantsTenantIdExtensionsIcebergCompactionResponse
func (c *ClientWithResponses) PutTenantsTenantIdExtensionsIcebergCompactionWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutTenantsTenantIdExtensionsIcebergCompactionResponse, error) {
	rsp, err := c.PutTenantsTenantIdExtensionsIcebergCompactionWithBody(ctx, tenantId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutTenantsTenantIdExtensionsIcebergCompactionResponse(rsp)
}

func (c *ClientWithResponses) PutTenantsTenantIdExtensionsIcebergCompactionWithResponse(ctx context.Context, tenantId uint64, body PutTenantsTenantIdExtensionsIcebergCompactionJSONRequestBody, reqEditors ...RequestEditorFn) (*PutTenantsTenantIdExtensionsIcebergCompactionResponse, error) {
	rsp, err := c.PutTenantsTenantIdExtensionsIcebergCompaction(ctx, tenantId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutTenantsTenantIdExtensionsIcebergCompactionResponse(rsp)
}

// GetVersionWithResponse request returning *GetVersionResponse
func (c *ClientWithResponses) GetVersionWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetVersionResponse, error) {
	rsp, err := c.GetVersion(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetVersionResponse(rsp)
}

// PostWebhooksAlertWithResponse request returning *PostWebhooksAlertResponse
func (c *ClientWithResponses) PostWebhooksAlertWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*PostWebhooksAlertResponse, error) {
	rsp, err := c.PostWebhooksAlert(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostWebhooksAlertResponse(rsp)
}

// GetWorkflowIdWithResponse request returning *GetWorkflowIdResponse
func (c *ClientWithResponses) GetWorkflowIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetWorkflowIdResponse, error) {
	rsp, err := c.GetWorkflowId(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetWorkflowIdResponse(rsp)
}

// PostWorkflowIdCancelWithResponse request returning *PostWorkflowIdCancelResponse
func (c *ClientWithResponses) PostWorkflowIdCancelWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*PostWorkflowIdCancelResponse, error) {
	rsp, err := c.PostWorkflowIdCancel(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostWorkflowIdCancelResponse(rsp)
}

// PostWorkflowIdRerunWithBodyWithResponse request with arbitrary body returning *PostWorkflowIdRerunResponse
func (c *ClientWithResponses) PostWorkflowIdRerunWithBodyWithResponse(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostWorkflowIdRerunResponse, error) {
	rsp, err := c.PostWorkflowIdRerunWithBody(ctx, id, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostWorkflowIdRerunResponse(rsp)
}

func (c *ClientWithResponses) PostWorkflowIdRerunWithResponse(ctx context.Context, id openapi_types.UUID, body PostWorkflowIdRerunJSONRequestBody, reqEditors ...RequestEditorFn) (*PostWorkflowIdRerunResponse, error) {
	rsp, err := c.PostWorkflowIdRerun(ctx, id, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostWorkflowIdRerunResponse(rsp)
}

// PostWorkflowIdResumeWithResponse request returning *PostWorkflowIdResumeResponse
func (c *ClientWithResponses) PostWorkflowIdResumeWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*PostWorkflowIdResumeResponse, error) {
	rsp, err := c.PostWorkflowIdResume(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostWorkflowIdResumeResponse(rsp)
}

// PostWorkflowIdScheduleWithBodyWithResponse request with arbitrary body returning *PostWorkflowIdScheduleResponse
func (c *ClientWithResponses) PostWorkflowIdScheduleWithBodyWithResponse(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostWorkflowIdScheduleResponse, error) {
	rsp, err := c.PostWorkflowIdScheduleWithBody(ctx, id, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostWorkflowIdScheduleResponse(rsp)
}

func (c *ClientWithResponses) PostWorkflowIdScheduleWithResponse(ctx context.Context, id openapi_types.UUID, body PostWorkflowIdScheduleJSONRequestBody, reqEditors ...RequestEditorFn) (*PostWorkflowIdScheduleResponse, error) {
	rsp, err := c.PostWorkflowIdSchedule(ctx, id, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostWorkflowIdScheduleResponse(rsp)
}

// GetWorkflowsWithResponse request returning *GetWorkflowsResponse
func (c *ClientWithResponses) GetWorkflowsWithResponse(ctx context.Context, params *GetWorkflowsParams, reqEditors ...RequestEditorFn) (*GetWorkflowsResponse, error) {
	rsp, err := c.GetWorkflows(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetWorkflowsResponse(rsp)
}

// ParseGetResponse parses an HTTP response from a GetWithResponse call
func ParseGetResponse(rsp *http.Response) (*GetResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePostAlertNotificationsResponse parses an HTTP response from a PostAlertNotificationsWithResponse call
func ParsePostAlertNotificationsResponse(rsp *http.Response) (*PostAlertNotificationsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostAlertNotificationsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParsePostByocClusterIdAccessResponse parses an HTTP response from a PostByocClusterIdAccessWithResponse call
func ParsePostByocClusterIdAccessResponse(rsp *http.Response) (*PostByocClusterIdAccessResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostByocClusterIdAccessResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ClusterAccessInfo
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostByocClusterIdUpdateResponse parses an HTTP response from a PostByocClusterIdUpdateWithResponse call
func ParsePostByocClusterIdUpdateResponse(rsp *http.Response) (*PostByocClusterIdUpdateResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostByocClusterIdUpdateResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest WorkflowIdResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetByocClustersResponse parses an HTTP response from a GetByocClustersWithResponse call
func ParseGetByocClustersResponse(rsp *http.Response) (*GetByocClustersResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetByocClustersResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ManagedClusters
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseDeleteClusterNameResponse parses an HTTP response from a DeleteClusterNameWithResponse call
func ParseDeleteClusterNameResponse(rsp *http.Response) (*DeleteClusterNameResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteClusterNameResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetClusterNameResponse parses an HTTP response from a GetClusterNameWithResponse call
func ParseGetClusterNameResponse(rsp *http.Response) (*GetClusterNameResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetClusterNameResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ManagedCluster
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostClusterNameResponse parses an HTTP response from a PostClusterNameWithResponse call
func ParsePostClusterNameResponse(rsp *http.Response) (*PostClusterNameResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostClusterNameResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseGetClustersResponse parses an HTTP response from a GetClustersWithResponse call
func ParseGetClustersResponse(rsp *http.Response) (*GetClustersResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetClustersResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ManagedClusters
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePostClustersResponse parses an HTTP response from a PostClustersWithResponse call
func ParsePostClustersResponse(rsp *http.Response) (*PostClustersResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostClustersResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseGetClustersUuidPrometheusApiV1LabelLabelNameValuesResponse parses an HTTP response from a GetClustersUuidPrometheusApiV1LabelLabelNameValuesWithResponse call
func ParseGetClustersUuidPrometheusApiV1LabelLabelNameValuesResponse(rsp *http.Response) (*GetClustersUuidPrometheusApiV1LabelLabelNameValuesResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetClustersUuidPrometheusApiV1LabelLabelNameValuesResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 412:
		var dest ClusterStatusResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON412 = &dest

	}

	return response, nil
}

// ParsePostClustersUuidPrometheusApiV1LabelLabelNameValuesResponse parses an HTTP response from a PostClustersUuidPrometheusApiV1LabelLabelNameValuesWithResponse call
func ParsePostClustersUuidPrometheusApiV1LabelLabelNameValuesResponse(rsp *http.Response) (*PostClustersUuidPrometheusApiV1LabelLabelNameValuesResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostClustersUuidPrometheusApiV1LabelLabelNameValuesResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 412:
		var dest ClusterStatusResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON412 = &dest

	}

	return response, nil
}

// ParseGetClustersUuidPrometheusApiV1QueryResponse parses an HTTP response from a GetClustersUuidPrometheusApiV1QueryWithResponse call
func ParseGetClustersUuidPrometheusApiV1QueryResponse(rsp *http.Response) (*GetClustersUuidPrometheusApiV1QueryResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetClustersUuidPrometheusApiV1QueryResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 412:
		var dest ClusterStatusResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON412 = &dest

	}

	return response, nil
}

// ParsePostClustersUuidPrometheusApiV1QueryResponse parses an HTTP response from a PostClustersUuidPrometheusApiV1QueryWithResponse call
func ParsePostClustersUuidPrometheusApiV1QueryResponse(rsp *http.Response) (*PostClustersUuidPrometheusApiV1QueryResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostClustersUuidPrometheusApiV1QueryResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 412:
		var dest ClusterStatusResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON412 = &dest

	}

	return response, nil
}

// ParseGetClustersUuidPrometheusApiV1QueryRangeResponse parses an HTTP response from a GetClustersUuidPrometheusApiV1QueryRangeWithResponse call
func ParseGetClustersUuidPrometheusApiV1QueryRangeResponse(rsp *http.Response) (*GetClustersUuidPrometheusApiV1QueryRangeResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetClustersUuidPrometheusApiV1QueryRangeResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 412:
		var dest ClusterStatusResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON412 = &dest

	}

	return response, nil
}

// ParsePostClustersUuidPrometheusApiV1QueryRangeResponse parses an HTTP response from a PostClustersUuidPrometheusApiV1QueryRangeWithResponse call
func ParsePostClustersUuidPrometheusApiV1QueryRangeResponse(rsp *http.Response) (*PostClustersUuidPrometheusApiV1QueryRangeResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostClustersUuidPrometheusApiV1QueryRangeResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 412:
		var dest ClusterStatusResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON412 = &dest

	}

	return response, nil
}

// ParseGetClustersUuidPrometheusApiV1SeriesResponse parses an HTTP response from a GetClustersUuidPrometheusApiV1SeriesWithResponse call
func ParseGetClustersUuidPrometheusApiV1SeriesResponse(rsp *http.Response) (*GetClustersUuidPrometheusApiV1SeriesResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetClustersUuidPrometheusApiV1SeriesResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 412:
		var dest ClusterStatusResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON412 = &dest

	}

	return response, nil
}

// ParsePostClustersUuidPrometheusApiV1SeriesResponse parses an HTTP response from a PostClustersUuidPrometheusApiV1SeriesWithResponse call
func ParsePostClustersUuidPrometheusApiV1SeriesResponse(rsp *http.Response) (*PostClustersUuidPrometheusApiV1SeriesResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostClustersUuidPrometheusApiV1SeriesResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 412:
		var dest ClusterStatusResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON412 = &dest

	}

	return response, nil
}

// ParseDeleteTenantTenantIdResponse parses an HTTP response from a DeleteTenantTenantIdWithResponse call
func ParseDeleteTenantTenantIdResponse(rsp *http.Response) (*DeleteTenantTenantIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteTenantTenantIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest WorkflowIdResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetTenantTenantIdResponse parses an HTTP response from a GetTenantTenantIdWithResponse call
func ParseGetTenantTenantIdResponse(rsp *http.Response) (*GetTenantTenantIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetTenantTenantIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest Tenant
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdBackupResponse parses an HTTP response from a PostTenantTenantIdBackupWithResponse call
func ParsePostTenantTenantIdBackupResponse(rsp *http.Response) (*PostTenantTenantIdBackupResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdBackupResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest PostSnapshotResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	}

	return response, nil
}

// ParseDeleteTenantTenantIdBackupSnapshotIdResponse parses an HTTP response from a DeleteTenantTenantIdBackupSnapshotIdWithResponse call
func ParseDeleteTenantTenantIdBackupSnapshotIdResponse(rsp *http.Response) (*DeleteTenantTenantIdBackupSnapshotIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteTenantTenantIdBackupSnapshotIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest OptionalWorkflowIdResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdBackupSnapshotIdInPlaceRestoreResponse parses an HTTP response from a PostTenantTenantIdBackupSnapshotIdInPlaceRestoreWithResponse call
func ParsePostTenantTenantIdBackupSnapshotIdInPlaceRestoreResponse(rsp *http.Response) (*PostTenantTenantIdBackupSnapshotIdInPlaceRestoreResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdBackupSnapshotIdInPlaceRestoreResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest WorkflowIdResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdBackupSnapshotIdRestoreResponse parses an HTTP response from a PostTenantTenantIdBackupSnapshotIdRestoreWithResponse call
func ParsePostTenantTenantIdBackupSnapshotIdRestoreResponse(rsp *http.Response) (*PostTenantTenantIdBackupSnapshotIdRestoreResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdBackupSnapshotIdRestoreResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest Tenant
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdComputeCacheResponse parses an HTTP response from a PostTenantTenantIdComputeCacheWithResponse call
func ParsePostTenantTenantIdComputeCacheResponse(rsp *http.Response) (*PostTenantTenantIdComputeCacheResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdComputeCacheResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest WorkflowIdResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	}

	return response, nil
}

// ParsePutTenantTenantIdConfigEtcdResponse parses an HTTP response from a PutTenantTenantIdConfigEtcdWithResponse call
func ParsePutTenantTenantIdConfigEtcdResponse(rsp *http.Response) (*PutTenantTenantIdConfigEtcdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutTenantTenantIdConfigEtcdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest WorkflowIdResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePutTenantTenantIdConfigRisingwaveResponse parses an HTTP response from a PutTenantTenantIdConfigRisingwaveWithResponse call
func ParsePutTenantTenantIdConfigRisingwaveResponse(rsp *http.Response) (*PutTenantTenantIdConfigRisingwaveResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutTenantTenantIdConfigRisingwaveResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest WorkflowIdResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePutTenantTenantIdConfigNoRestartRisingwaveResponse parses an HTTP response from a PutTenantTenantIdConfigNoRestartRisingwaveWithResponse call
func ParsePutTenantTenantIdConfigNoRestartRisingwaveResponse(rsp *http.Response) (*PutTenantTenantIdConfigNoRestartRisingwaveResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutTenantTenantIdConfigNoRestartRisingwaveResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest WorkflowIdResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGenDiagReportResponse parses an HTTP response from a GenDiagReportWithResponse call
func ParseGenDiagReportResponse(rsp *http.Response) (*GenDiagReportResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GenDiagReportResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest RwDiagReport
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseListDiagReportTimeRangeResponse parses an HTTP response from a ListDiagReportTimeRangeWithResponse call
func ParseListDiagReportTimeRangeResponse(rsp *http.Response) (*ListDiagReportTimeRangeResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &ListDiagReportTimeRangeResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest RwDiagReportMetaPage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseGetDiagReportResponse parses an HTTP response from a GetDiagReportWithResponse call
func ParseGetDiagReportResponse(rsp *http.Response) (*GetDiagReportResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetDiagReportResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest RwDiagReport
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetTenantTenantIdEndpointResponse parses an HTTP response from a GetTenantTenantIdEndpointWithResponse call
func ParseGetTenantTenantIdEndpointResponse(rsp *http.Response) (*GetTenantTenantIdEndpointResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetTenantTenantIdEndpointResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DatabaseConnectionUrl
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseDeleteTenantTenantIdExpireResponse parses an HTTP response from a DeleteTenantTenantIdExpireWithResponse call
func ParseDeleteTenantTenantIdExpireResponse(rsp *http.Response) (*DeleteTenantTenantIdExpireResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteTenantTenantIdExpireResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetTenantTenantIdExpireResponse parses an HTTP response from a GetTenantTenantIdExpireWithResponse call
func ParseGetTenantTenantIdExpireResponse(rsp *http.Response) (*GetTenantTenantIdExpireResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetTenantTenantIdExpireResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest TenantExpireInfo
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdExpireResponse parses an HTTP response from a PostTenantTenantIdExpireWithResponse call
func ParsePostTenantTenantIdExpireResponse(rsp *http.Response) (*PostTenantTenantIdExpireResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdExpireResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdExtensionsCompactionDisableResponse parses an HTTP response from a PostTenantTenantIdExtensionsCompactionDisableWithResponse call
func ParsePostTenantTenantIdExtensionsCompactionDisableResponse(rsp *http.Response) (*PostTenantTenantIdExtensionsCompactionDisableResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdExtensionsCompactionDisableResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest WorkflowIdResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdExtensionsCompactionEnableResponse parses an HTTP response from a PostTenantTenantIdExtensionsCompactionEnableWithResponse call
func ParsePostTenantTenantIdExtensionsCompactionEnableResponse(rsp *http.Response) (*PostTenantTenantIdExtensionsCompactionEnableResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdExtensionsCompactionEnableResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest WorkflowIdResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest FailedPreconditionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetTenantTenantIdExtensionsCompactionParametersResponse parses an HTTP response from a GetTenantTenantIdExtensionsCompactionParametersWithResponse call
func ParseGetTenantTenantIdExtensionsCompactionParametersResponse(rsp *http.Response) (*GetTenantTenantIdExtensionsCompactionParametersResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetTenantTenantIdExtensionsCompactionParametersResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest GetTenantExtensionCompactionParametersResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest FailedPreconditionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetTenantTenantIdExtensionsCompactionStatusResponse parses an HTTP response from a GetTenantTenantIdExtensionsCompactionStatusWithResponse call
func ParseGetTenantTenantIdExtensionsCompactionStatusResponse(rsp *http.Response) (*GetTenantTenantIdExtensionsCompactionStatusResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetTenantTenantIdExtensionsCompactionStatusResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest GetTenantExtensionCompactionStatusResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdExtensionsCompactionStatusResponse parses an HTTP response from a PostTenantTenantIdExtensionsCompactionStatusWithResponse call
func ParsePostTenantTenantIdExtensionsCompactionStatusResponse(rsp *http.Response) (*PostTenantTenantIdExtensionsCompactionStatusResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdExtensionsCompactionStatusResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest GetTenantExtensionCompactionStatusResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdExtensionsCompactionUpdateResponse parses an HTTP response from a PostTenantTenantIdExtensionsCompactionUpdateWithResponse call
func ParsePostTenantTenantIdExtensionsCompactionUpdateResponse(rsp *http.Response) (*PostTenantTenantIdExtensionsCompactionUpdateResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdExtensionsCompactionUpdateResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest WorkflowIdResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest FailedPreconditionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdExtensionsServerlessbackfillDisableResponse parses an HTTP response from a PostTenantTenantIdExtensionsServerlessbackfillDisableWithResponse call
func ParsePostTenantTenantIdExtensionsServerlessbackfillDisableResponse(rsp *http.Response) (*PostTenantTenantIdExtensionsServerlessbackfillDisableResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdExtensionsServerlessbackfillDisableResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest WorkflowIdResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest FailedPreconditionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdExtensionsServerlessbackfillEnableResponse parses an HTTP response from a PostTenantTenantIdExtensionsServerlessbackfillEnableWithResponse call
func ParsePostTenantTenantIdExtensionsServerlessbackfillEnableResponse(rsp *http.Response) (*PostTenantTenantIdExtensionsServerlessbackfillEnableResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdExtensionsServerlessbackfillEnableResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest WorkflowIdResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest FailedPreconditionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdExtensionsServerlessbackfillVersionResponse parses an HTTP response from a PostTenantTenantIdExtensionsServerlessbackfillVersionWithResponse call
func ParsePostTenantTenantIdExtensionsServerlessbackfillVersionResponse(rsp *http.Response) (*PostTenantTenantIdExtensionsServerlessbackfillVersionResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdExtensionsServerlessbackfillVersionResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest WorkflowIdResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest FailedPreconditionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdMigrateMetaStoreResponse parses an HTTP response from a PostTenantTenantIdMigrateMetaStoreWithResponse call
func ParsePostTenantTenantIdMigrateMetaStoreResponse(rsp *http.Response) (*PostTenantTenantIdMigrateMetaStoreResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdMigrateMetaStoreResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest WorkflowIdResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest FailedPreconditionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseDeleteTenantTenantIdPrivatelinkPrivateLinkIdResponse parses an HTTP response from a DeleteTenantTenantIdPrivatelinkPrivateLinkIdWithResponse call
func ParseDeleteTenantTenantIdPrivatelinkPrivateLinkIdResponse(rsp *http.Response) (*DeleteTenantTenantIdPrivatelinkPrivateLinkIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteTenantTenantIdPrivatelinkPrivateLinkIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest WorkflowIdResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdResourceResponse parses an HTTP response from a PostTenantTenantIdResourceWithResponse call
func ParsePostTenantTenantIdResourceResponse(rsp *http.Response) (*PostTenantTenantIdResourceResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdResourceResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest WorkflowIdResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdResourceGroupsResponse parses an HTTP response from a PostTenantTenantIdResourceGroupsWithResponse call
func ParsePostTenantTenantIdResourceGroupsResponse(rsp *http.Response) (*PostTenantTenantIdResourceGroupsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdResourceGroupsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest WorkflowIdResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest FailedPreconditionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseDeleteTenantTenantIdResourceGroupsResourceGroupResponse parses an HTTP response from a DeleteTenantTenantIdResourceGroupsResourceGroupWithResponse call
func ParseDeleteTenantTenantIdResourceGroupsResourceGroupResponse(rsp *http.Response) (*DeleteTenantTenantIdResourceGroupsResourceGroupResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteTenantTenantIdResourceGroupsResourceGroupResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest WorkflowIdResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest FailedPreconditionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdResourceGroupsResourceGroupResponse parses an HTTP response from a PostTenantTenantIdResourceGroupsResourceGroupWithResponse call
func ParsePostTenantTenantIdResourceGroupsResourceGroupResponse(rsp *http.Response) (*PostTenantTenantIdResourceGroupsResourceGroupResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdResourceGroupsResourceGroupResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest WorkflowIdResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest FailedPreconditionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdRestartResponse parses an HTTP response from a PostTenantTenantIdRestartWithResponse call
func ParsePostTenantTenantIdRestartResponse(rsp *http.Response) (*PostTenantTenantIdRestartResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdRestartResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest WorkflowIdResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdStartResponse parses an HTTP response from a PostTenantTenantIdStartWithResponse call
func ParsePostTenantTenantIdStartResponse(rsp *http.Response) (*PostTenantTenantIdStartResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdStartResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest WorkflowIdResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdStatusResponse parses an HTTP response from a PostTenantTenantIdStatusWithResponse call
func ParsePostTenantTenantIdStatusResponse(rsp *http.Response) (*PostTenantTenantIdStatusResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdStatusResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdStopResponse parses an HTTP response from a PostTenantTenantIdStopWithResponse call
func ParsePostTenantTenantIdStopResponse(rsp *http.Response) (*PostTenantTenantIdStopResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdStopResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest WorkflowIdResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdTierResponse parses an HTTP response from a PostTenantTenantIdTierWithResponse call
func ParsePostTenantTenantIdTierResponse(rsp *http.Response) (*PostTenantTenantIdTierResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdTierResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdUpdateVersionResponse parses an HTTP response from a PostTenantTenantIdUpdateVersionWithResponse call
func ParsePostTenantTenantIdUpdateVersionResponse(rsp *http.Response) (*PostTenantTenantIdUpdateVersionResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdUpdateVersionResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest WorkflowIdResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdUpgradeMetaStoreResponse parses an HTTP response from a PostTenantTenantIdUpgradeMetaStoreWithResponse call
func ParsePostTenantTenantIdUpgradeMetaStoreResponse(rsp *http.Response) (*PostTenantTenantIdUpgradeMetaStoreResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdUpgradeMetaStoreResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest WorkflowIdResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest FailedPreconditionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetTenantsResponse parses an HTTP response from a GetTenantsWithResponse call
func ParseGetTenantsResponse(rsp *http.Response) (*GetTenantsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetTenantsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest TenantPage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseDeleteTenantsTenantIdExtensionsIcebergCompactionResponse parses an HTTP response from a DeleteTenantsTenantIdExtensionsIcebergCompactionWithResponse call
func ParseDeleteTenantsTenantIdExtensionsIcebergCompactionResponse(rsp *http.Response) (*DeleteTenantsTenantIdExtensionsIcebergCompactionResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteTenantsTenantIdExtensionsIcebergCompactionResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest WorkflowIdResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest FailedPreconditionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetTenantsTenantIdExtensionsIcebergCompactionResponse parses an HTTP response from a GetTenantsTenantIdExtensionsIcebergCompactionWithResponse call
func ParseGetTenantsTenantIdExtensionsIcebergCompactionResponse(rsp *http.Response) (*GetTenantsTenantIdExtensionsIcebergCompactionResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetTenantsTenantIdExtensionsIcebergCompactionResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest IcebergCompaction
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostTenantsTenantIdExtensionsIcebergCompactionResponse parses an HTTP response from a PostTenantsTenantIdExtensionsIcebergCompactionWithResponse call
func ParsePostTenantsTenantIdExtensionsIcebergCompactionResponse(rsp *http.Response) (*PostTenantsTenantIdExtensionsIcebergCompactionResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantsTenantIdExtensionsIcebergCompactionResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest WorkflowIdResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest FailedPreconditionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePutTenantsTenantIdExtensionsIcebergCompactionResponse parses an HTTP response from a PutTenantsTenantIdExtensionsIcebergCompactionWithResponse call
func ParsePutTenantsTenantIdExtensionsIcebergCompactionResponse(rsp *http.Response) (*PutTenantsTenantIdExtensionsIcebergCompactionResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutTenantsTenantIdExtensionsIcebergCompactionResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest WorkflowIdResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest FailedPreconditionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetVersionResponse parses an HTTP response from a GetVersionWithResponse call
func ParseGetVersionResponse(rsp *http.Response) (*GetVersionResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetVersionResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePostWebhooksAlertResponse parses an HTTP response from a PostWebhooksAlertWithResponse call
func ParsePostWebhooksAlertResponse(rsp *http.Response) (*PostWebhooksAlertResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostWebhooksAlertResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseGetWorkflowIdResponse parses an HTTP response from a GetWorkflowIdWithResponse call
func ParseGetWorkflowIdResponse(rsp *http.Response) (*GetWorkflowIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetWorkflowIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest WorkflowWithEvents
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostWorkflowIdCancelResponse parses an HTTP response from a PostWorkflowIdCancelWithResponse call
func ParsePostWorkflowIdCancelResponse(rsp *http.Response) (*PostWorkflowIdCancelResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostWorkflowIdCancelResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostWorkflowIdRerunResponse parses an HTTP response from a PostWorkflowIdRerunWithResponse call
func ParsePostWorkflowIdRerunResponse(rsp *http.Response) (*PostWorkflowIdRerunResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostWorkflowIdRerunResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest WorkflowIdResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostWorkflowIdResumeResponse parses an HTTP response from a PostWorkflowIdResumeWithResponse call
func ParsePostWorkflowIdResumeResponse(rsp *http.Response) (*PostWorkflowIdResumeResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostWorkflowIdResumeResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostWorkflowIdScheduleResponse parses an HTTP response from a PostWorkflowIdScheduleWithResponse call
func ParsePostWorkflowIdScheduleResponse(rsp *http.Response) (*PostWorkflowIdScheduleResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostWorkflowIdScheduleResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetWorkflowsResponse parses an HTTP response from a GetWorkflowsWithResponse call
func ParseGetWorkflowsResponse(rsp *http.Response) (*GetWorkflowsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetWorkflowsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest WorkflowPage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ServerInterface represents all server handlers.
type ServerInterface interface {

	// (GET /)
	Get(c *gin.Context)
	// create alert notification
	// (POST /alertNotifications)
	PostAlertNotifications(c *gin.Context)
	// Get temporary access token for the requested cluster
	// (POST /byoc-cluster/{id}/access)
	PostByocClusterIdAccess(c *gin.Context, id uint64)
	// update the BYOC cluster version, if version is not specified, update it to the default version
	// (POST /byoc-cluster/{id}/update)
	PostByocClusterIdUpdate(c *gin.Context, id uint64)
	// Get all clusters
	// (GET /byoc-clusters)
	GetByocClusters(c *gin.Context)
	// delete cluster
	// (DELETE /cluster/{name})
	DeleteClusterName(c *gin.Context, name string)
	// Get a cluster
	// (GET /cluster/{name})
	GetClusterName(c *gin.Context, name string)
	// Create or update cluster
	// (POST /cluster/{name})
	PostClusterName(c *gin.Context, name string)
	// Get all clusters
	// (GET /clusters)
	GetClusters(c *gin.Context)
	// Create or update cluster by name
	// (POST /clusters)
	PostClusters(c *gin.Context)

	// (GET /clusters/{uuid}/prometheus/api/v1/label/{labelName}/values)
	GetClustersUuidPrometheusApiV1LabelLabelNameValues(c *gin.Context, uuid openapi_types.UUID, labelName string)

	// (POST /clusters/{uuid}/prometheus/api/v1/label/{labelName}/values)
	PostClustersUuidPrometheusApiV1LabelLabelNameValues(c *gin.Context, uuid openapi_types.UUID, labelName string)

	// (GET /clusters/{uuid}/prometheus/api/v1/query)
	GetClustersUuidPrometheusApiV1Query(c *gin.Context, uuid openapi_types.UUID)

	// (POST /clusters/{uuid}/prometheus/api/v1/query)
	PostClustersUuidPrometheusApiV1Query(c *gin.Context, uuid openapi_types.UUID)

	// (GET /clusters/{uuid}/prometheus/api/v1/query_range)
	GetClustersUuidPrometheusApiV1QueryRange(c *gin.Context, uuid openapi_types.UUID)

	// (POST /clusters/{uuid}/prometheus/api/v1/query_range)
	PostClustersUuidPrometheusApiV1QueryRange(c *gin.Context, uuid openapi_types.UUID)

	// (GET /clusters/{uuid}/prometheus/api/v1/series)
	GetClustersUuidPrometheusApiV1Series(c *gin.Context, uuid openapi_types.UUID)

	// (POST /clusters/{uuid}/prometheus/api/v1/series)
	PostClustersUuidPrometheusApiV1Series(c *gin.Context, uuid openapi_types.UUID)
	// Delete a tenant
	// (DELETE /tenant/{tenantId})
	DeleteTenantTenantId(c *gin.Context, tenantId uint64)
	// Get a tenant
	// (GET /tenant/{tenantId})
	GetTenantTenantId(c *gin.Context, tenantId uint64)
	// create a backup snapshot for the meta store of the tenant
	// (POST /tenant/{tenantId}/backup)
	PostTenantTenantIdBackup(c *gin.Context, tenantId uint64)
	// delete a backup snapshot for the meta store of the tenant
	// (DELETE /tenant/{tenantId}/backup/{snapshotId})
	DeleteTenantTenantIdBackupSnapshotId(c *gin.Context, tenantId uint64, snapshotId openapi_types.UUID)
	// Restore to a new cluster based on the snapshot id
	// (POST /tenant/{tenantId}/backup/{snapshotId}/in-place-restore)
	PostTenantTenantIdBackupSnapshotIdInPlaceRestore(c *gin.Context, tenantId uint64, snapshotId openapi_types.UUID)
	// Restore to a new cluster based on the snapshot id
	// (POST /tenant/{tenantId}/backup/{snapshotId}/restore)
	PostTenantTenantIdBackupSnapshotIdRestore(c *gin.Context, tenantId uint64, snapshotId openapi_types.UUID)
	// update compute cache for tenant
	// (POST /tenant/{tenantId}/computeCache)
	PostTenantTenantIdComputeCache(c *gin.Context, tenantId uint64)
	// Update the etcd config
	// (PUT /tenant/{tenantId}/config/etcd)
	PutTenantTenantIdConfigEtcd(c *gin.Context, tenantId uint64)
	// Update the tenant config
	// (PUT /tenant/{tenantId}/config/risingwave)
	PutTenantTenantIdConfigRisingwave(c *gin.Context, tenantId uint64, params PutTenantTenantIdConfigRisingwaveParams)
	// Update the tenant config
	// (PUT /tenant/{tenantId}/configNoRestart/risingwave)
	PutTenantTenantIdConfigNoRestartRisingwave(c *gin.Context, tenantId uint64, params PutTenantTenantIdConfigNoRestartRisingwaveParams)
	// generate a RW Diagnosis Report
	// (POST /tenant/{tenantId}/diagnosis/report/generate)
	GenDiagReport(c *gin.Context, tenantId uint64)
	// list a set of RW Diagnosis Reports within a specified range of time
	// (GET /tenant/{tenantId}/diagnosis/report/range)
	ListDiagReportTimeRange(c *gin.Context, tenantId uint64, params ListDiagReportTimeRangeParams)
	// fetch the content of the RW Diagnosis Report
	// (GET /tenant/{tenantId}/diagnosis/report/{reportId})
	GetDiagReport(c *gin.Context, tenantId uint64, reportId uint64)
	// Get the root endpoint of the specified tenant
	// (GET /tenant/{tenantId}/endpoint)
	GetTenantTenantIdEndpoint(c *gin.Context, tenantId uint64)
	// Cancel expiration workflow
	// (DELETE /tenant/{tenantId}/expire)
	DeleteTenantTenantIdExpire(c *gin.Context, tenantId uint64)
	// Get the expire information
	// (GET /tenant/{tenantId}/expire)
	GetTenantTenantIdExpire(c *gin.Context, tenantId uint64)
	// Update expiration lifecycle
	// (POST /tenant/{tenantId}/expire)
	PostTenantTenantIdExpire(c *gin.Context, tenantId uint64)
	// Disable the risingwave extensions for serverless compaction
	// (POST /tenant/{tenantId}/extensions/compaction/disable)
	PostTenantTenantIdExtensionsCompactionDisable(c *gin.Context, tenantId uint64)
	// Enable the risingwave extensions for serverless compaction
	// (POST /tenant/{tenantId}/extensions/compaction/enable)
	PostTenantTenantIdExtensionsCompactionEnable(c *gin.Context, tenantId uint64)
	// Get the parameters of the risingwave extensions for serverless compaction
	// (GET /tenant/{tenantId}/extensions/compaction/parameters)
	GetTenantTenantIdExtensionsCompactionParameters(c *gin.Context, tenantId uint64)
	// Get the status of the risingwave extensions for serverless compaction
	// (GET /tenant/{tenantId}/extensions/compaction/status)
	GetTenantTenantIdExtensionsCompactionStatus(c *gin.Context, tenantId uint64)
	// Get the status of the risingwave extensions for serverless compaction
	// (POST /tenant/{tenantId}/extensions/compaction/status)
	PostTenantTenantIdExtensionsCompactionStatus(c *gin.Context, tenantId uint64)
	// Update the risingwave extensions for serverless compaction
	// (POST /tenant/{tenantId}/extensions/compaction/update)
	PostTenantTenantIdExtensionsCompactionUpdate(c *gin.Context, tenantId uint64)
	// Disable the risingwave extensions for serverless backfilling
	// (POST /tenant/{tenantId}/extensions/serverlessbackfill/disable)
	PostTenantTenantIdExtensionsServerlessbackfillDisable(c *gin.Context, tenantId uint64)
	// Enable the risingwave extensions for serverless backfilling
	// (POST /tenant/{tenantId}/extensions/serverlessbackfill/enable)
	PostTenantTenantIdExtensionsServerlessbackfillEnable(c *gin.Context, tenantId uint64)
	// Update the risingwave extensions for serverless backfilling to version. Version format like 1.0.0.
	// (POST /tenant/{tenantId}/extensions/serverlessbackfill/version)
	PostTenantTenantIdExtensionsServerlessbackfillVersion(c *gin.Context, tenantId uint64)
	// Migrate the risingwave meta store
	// (POST /tenant/{tenantId}/migrateMetaStore)
	PostTenantTenantIdMigrateMetaStore(c *gin.Context, tenantId uint64)
	// Delete a tenant's private link by id
	// (DELETE /tenant/{tenantId}/privatelink/{privateLinkId})
	DeleteTenantTenantIdPrivatelinkPrivateLinkId(c *gin.Context, tenantId uint64, privateLinkId openapi_types.UUID)
	// Update a tenant resource
	// (POST /tenant/{tenantId}/resource)
	PostTenantTenantIdResource(c *gin.Context, tenantId uint64)
	// Create resource group
	// (POST /tenant/{tenantId}/resourceGroups)
	PostTenantTenantIdResourceGroups(c *gin.Context, tenantId uint64)
	// Delete resource group
	// (DELETE /tenant/{tenantId}/resourceGroups/{resourceGroup})
	DeleteTenantTenantIdResourceGroupsResourceGroup(c *gin.Context, tenantId uint64, resourceGroup string)
	// Update resource group
	// (POST /tenant/{tenantId}/resourceGroups/{resourceGroup})
	PostTenantTenantIdResourceGroupsResourceGroup(c *gin.Context, tenantId uint64, resourceGroup string)
	// Restart the tenant
	// (POST /tenant/{tenantId}/restart)
	PostTenantTenantIdRestart(c *gin.Context, tenantId uint64)
	// Start the tenant
	// (POST /tenant/{tenantId}/start)
	PostTenantTenantIdStart(c *gin.Context, tenantId uint64)
	// Reset tenant status
	// (POST /tenant/{tenantId}/status)
	PostTenantTenantIdStatus(c *gin.Context, tenantId uint64)
	// Stop the tenant
	// (POST /tenant/{tenantId}/stop)
	PostTenantTenantIdStop(c *gin.Context, tenantId uint64)
	// Update tenant tier
	// (POST /tenant/{tenantId}/tier)
	PostTenantTenantIdTier(c *gin.Context, tenantId uint64)
	// Update the tenant version
	// (POST /tenant/{tenantId}/updateVersion)
	PostTenantTenantIdUpdateVersion(c *gin.Context, tenantId uint64)

	// (POST /tenant/{tenantId}/upgradeMetaStore)
	PostTenantTenantIdUpgradeMetaStore(c *gin.Context, tenantId uint64, params PostTenantTenantIdUpgradeMetaStoreParams)
	// Get all the tenants in the Management System
	// (GET /tenants)
	GetTenants(c *gin.Context, params GetTenantsParams)
	// Disable iceberg compaction
	// (DELETE /tenants/{tenantId}/extensions/iceberg-compaction)
	DeleteTenantsTenantIdExtensionsIcebergCompaction(c *gin.Context, tenantId uint64)
	// Get iceberg compaction status
	// (GET /tenants/{tenantId}/extensions/iceberg-compaction)
	GetTenantsTenantIdExtensionsIcebergCompaction(c *gin.Context, tenantId uint64)
	// Enable iceberg compaction
	// (POST /tenants/{tenantId}/extensions/iceberg-compaction)
	PostTenantsTenantIdExtensionsIcebergCompaction(c *gin.Context, tenantId uint64)
	// Update iceberg compaction
	// (PUT /tenants/{tenantId}/extensions/iceberg-compaction)
	PutTenantsTenantIdExtensionsIcebergCompaction(c *gin.Context, tenantId uint64)

	// (GET /version)
	GetVersion(c *gin.Context)
	// receive alerts reported from alert manager
	// (POST /webhooks/alert)
	PostWebhooksAlert(c *gin.Context)

	// (GET /workflow/{id})
	GetWorkflowId(c *gin.Context, id openapi_types.UUID)

	// (POST /workflow/{id}/cancel)
	PostWorkflowIdCancel(c *gin.Context, id openapi_types.UUID)

	// (POST /workflow/{id}/rerun)
	PostWorkflowIdRerun(c *gin.Context, id openapi_types.UUID)

	// (POST /workflow/{id}/resume)
	PostWorkflowIdResume(c *gin.Context, id openapi_types.UUID)

	// (POST /workflow/{id}/schedule)
	PostWorkflowIdSchedule(c *gin.Context, id openapi_types.UUID)

	// (GET /workflows)
	GetWorkflows(c *gin.Context, params GetWorkflowsParams)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandler       func(*gin.Context, error, int)
}

type MiddlewareFunc func(c *gin.Context)

// Get operation middleware
func (siw *ServerInterfaceWrapper) Get(c *gin.Context) {

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.Get(c)
}

// PostAlertNotifications operation middleware
func (siw *ServerInterfaceWrapper) PostAlertNotifications(c *gin.Context) {

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostAlertNotifications(c)
}

// PostByocClusterIdAccess operation middleware
func (siw *ServerInterfaceWrapper) PostByocClusterIdAccess(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id uint64

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostByocClusterIdAccess(c, id)
}

// PostByocClusterIdUpdate operation middleware
func (siw *ServerInterfaceWrapper) PostByocClusterIdUpdate(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id uint64

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostByocClusterIdUpdate(c, id)
}

// GetByocClusters operation middleware
func (siw *ServerInterfaceWrapper) GetByocClusters(c *gin.Context) {

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetByocClusters(c)
}

// DeleteClusterName operation middleware
func (siw *ServerInterfaceWrapper) DeleteClusterName(c *gin.Context) {

	var err error

	// ------------- Path parameter "name" -------------
	var name string

	err = runtime.BindStyledParameterWithOptions("simple", "name", c.Param("name"), &name, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter name: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteClusterName(c, name)
}

// GetClusterName operation middleware
func (siw *ServerInterfaceWrapper) GetClusterName(c *gin.Context) {

	var err error

	// ------------- Path parameter "name" -------------
	var name string

	err = runtime.BindStyledParameterWithOptions("simple", "name", c.Param("name"), &name, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter name: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetClusterName(c, name)
}

// PostClusterName operation middleware
func (siw *ServerInterfaceWrapper) PostClusterName(c *gin.Context) {

	var err error

	// ------------- Path parameter "name" -------------
	var name string

	err = runtime.BindStyledParameterWithOptions("simple", "name", c.Param("name"), &name, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter name: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostClusterName(c, name)
}

// GetClusters operation middleware
func (siw *ServerInterfaceWrapper) GetClusters(c *gin.Context) {

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetClusters(c)
}

// PostClusters operation middleware
func (siw *ServerInterfaceWrapper) PostClusters(c *gin.Context) {

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostClusters(c)
}

// GetClustersUuidPrometheusApiV1LabelLabelNameValues operation middleware
func (siw *ServerInterfaceWrapper) GetClustersUuidPrometheusApiV1LabelLabelNameValues(c *gin.Context) {

	var err error

	// ------------- Path parameter "uuid" -------------
	var uuid openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "uuid", c.Param("uuid"), &uuid, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter uuid: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "labelName" -------------
	var labelName string

	err = runtime.BindStyledParameterWithOptions("simple", "labelName", c.Param("labelName"), &labelName, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter labelName: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetClustersUuidPrometheusApiV1LabelLabelNameValues(c, uuid, labelName)
}

// PostClustersUuidPrometheusApiV1LabelLabelNameValues operation middleware
func (siw *ServerInterfaceWrapper) PostClustersUuidPrometheusApiV1LabelLabelNameValues(c *gin.Context) {

	var err error

	// ------------- Path parameter "uuid" -------------
	var uuid openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "uuid", c.Param("uuid"), &uuid, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter uuid: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "labelName" -------------
	var labelName string

	err = runtime.BindStyledParameterWithOptions("simple", "labelName", c.Param("labelName"), &labelName, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter labelName: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostClustersUuidPrometheusApiV1LabelLabelNameValues(c, uuid, labelName)
}

// GetClustersUuidPrometheusApiV1Query operation middleware
func (siw *ServerInterfaceWrapper) GetClustersUuidPrometheusApiV1Query(c *gin.Context) {

	var err error

	// ------------- Path parameter "uuid" -------------
	var uuid openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "uuid", c.Param("uuid"), &uuid, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter uuid: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetClustersUuidPrometheusApiV1Query(c, uuid)
}

// PostClustersUuidPrometheusApiV1Query operation middleware
func (siw *ServerInterfaceWrapper) PostClustersUuidPrometheusApiV1Query(c *gin.Context) {

	var err error

	// ------------- Path parameter "uuid" -------------
	var uuid openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "uuid", c.Param("uuid"), &uuid, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter uuid: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostClustersUuidPrometheusApiV1Query(c, uuid)
}

// GetClustersUuidPrometheusApiV1QueryRange operation middleware
func (siw *ServerInterfaceWrapper) GetClustersUuidPrometheusApiV1QueryRange(c *gin.Context) {

	var err error

	// ------------- Path parameter "uuid" -------------
	var uuid openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "uuid", c.Param("uuid"), &uuid, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter uuid: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetClustersUuidPrometheusApiV1QueryRange(c, uuid)
}

// PostClustersUuidPrometheusApiV1QueryRange operation middleware
func (siw *ServerInterfaceWrapper) PostClustersUuidPrometheusApiV1QueryRange(c *gin.Context) {

	var err error

	// ------------- Path parameter "uuid" -------------
	var uuid openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "uuid", c.Param("uuid"), &uuid, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter uuid: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostClustersUuidPrometheusApiV1QueryRange(c, uuid)
}

// GetClustersUuidPrometheusApiV1Series operation middleware
func (siw *ServerInterfaceWrapper) GetClustersUuidPrometheusApiV1Series(c *gin.Context) {

	var err error

	// ------------- Path parameter "uuid" -------------
	var uuid openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "uuid", c.Param("uuid"), &uuid, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter uuid: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetClustersUuidPrometheusApiV1Series(c, uuid)
}

// PostClustersUuidPrometheusApiV1Series operation middleware
func (siw *ServerInterfaceWrapper) PostClustersUuidPrometheusApiV1Series(c *gin.Context) {

	var err error

	// ------------- Path parameter "uuid" -------------
	var uuid openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "uuid", c.Param("uuid"), &uuid, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter uuid: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostClustersUuidPrometheusApiV1Series(c, uuid)
}

// DeleteTenantTenantId operation middleware
func (siw *ServerInterfaceWrapper) DeleteTenantTenantId(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteTenantTenantId(c, tenantId)
}

// GetTenantTenantId operation middleware
func (siw *ServerInterfaceWrapper) GetTenantTenantId(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetTenantTenantId(c, tenantId)
}

// PostTenantTenantIdBackup operation middleware
func (siw *ServerInterfaceWrapper) PostTenantTenantIdBackup(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantTenantIdBackup(c, tenantId)
}

// DeleteTenantTenantIdBackupSnapshotId operation middleware
func (siw *ServerInterfaceWrapper) DeleteTenantTenantIdBackupSnapshotId(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "snapshotId" -------------
	var snapshotId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "snapshotId", c.Param("snapshotId"), &snapshotId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter snapshotId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteTenantTenantIdBackupSnapshotId(c, tenantId, snapshotId)
}

// PostTenantTenantIdBackupSnapshotIdInPlaceRestore operation middleware
func (siw *ServerInterfaceWrapper) PostTenantTenantIdBackupSnapshotIdInPlaceRestore(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "snapshotId" -------------
	var snapshotId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "snapshotId", c.Param("snapshotId"), &snapshotId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter snapshotId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantTenantIdBackupSnapshotIdInPlaceRestore(c, tenantId, snapshotId)
}

// PostTenantTenantIdBackupSnapshotIdRestore operation middleware
func (siw *ServerInterfaceWrapper) PostTenantTenantIdBackupSnapshotIdRestore(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "snapshotId" -------------
	var snapshotId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "snapshotId", c.Param("snapshotId"), &snapshotId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter snapshotId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantTenantIdBackupSnapshotIdRestore(c, tenantId, snapshotId)
}

// PostTenantTenantIdComputeCache operation middleware
func (siw *ServerInterfaceWrapper) PostTenantTenantIdComputeCache(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantTenantIdComputeCache(c, tenantId)
}

// PutTenantTenantIdConfigEtcd operation middleware
func (siw *ServerInterfaceWrapper) PutTenantTenantIdConfigEtcd(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PutTenantTenantIdConfigEtcd(c, tenantId)
}

// PutTenantTenantIdConfigRisingwave operation middleware
func (siw *ServerInterfaceWrapper) PutTenantTenantIdConfigRisingwave(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	// Parameter object where we will unmarshal all parameters from the context
	var params PutTenantTenantIdConfigRisingwaveParams

	// ------------- Optional query parameter "component" -------------

	err = runtime.BindQueryParameter("form", true, false, "component", c.Request.URL.Query(), &params.Component)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter component: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "nodeGroup" -------------

	err = runtime.BindQueryParameter("form", true, false, "nodeGroup", c.Request.URL.Query(), &params.NodeGroup)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter nodeGroup: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PutTenantTenantIdConfigRisingwave(c, tenantId, params)
}

// PutTenantTenantIdConfigNoRestartRisingwave operation middleware
func (siw *ServerInterfaceWrapper) PutTenantTenantIdConfigNoRestartRisingwave(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	// Parameter object where we will unmarshal all parameters from the context
	var params PutTenantTenantIdConfigNoRestartRisingwaveParams

	// ------------- Optional query parameter "component" -------------

	err = runtime.BindQueryParameter("form", true, false, "component", c.Request.URL.Query(), &params.Component)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter component: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "nodeGroup" -------------

	err = runtime.BindQueryParameter("form", true, false, "nodeGroup", c.Request.URL.Query(), &params.NodeGroup)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter nodeGroup: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PutTenantTenantIdConfigNoRestartRisingwave(c, tenantId, params)
}

// GenDiagReport operation middleware
func (siw *ServerInterfaceWrapper) GenDiagReport(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GenDiagReport(c, tenantId)
}

// ListDiagReportTimeRange operation middleware
func (siw *ServerInterfaceWrapper) ListDiagReportTimeRange(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	// Parameter object where we will unmarshal all parameters from the context
	var params ListDiagReportTimeRangeParams

	// ------------- Optional query parameter "startTime" -------------

	err = runtime.BindQueryParameter("form", true, false, "startTime", c.Request.URL.Query(), &params.StartTime)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter startTime: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "endTime" -------------

	err = runtime.BindQueryParameter("form", true, false, "endTime", c.Request.URL.Query(), &params.EndTime)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter endTime: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", c.Request.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter limit: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", c.Request.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter offset: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.ListDiagReportTimeRange(c, tenantId, params)
}

// GetDiagReport operation middleware
func (siw *ServerInterfaceWrapper) GetDiagReport(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "reportId" -------------
	var reportId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "reportId", c.Param("reportId"), &reportId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter reportId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetDiagReport(c, tenantId, reportId)
}

// GetTenantTenantIdEndpoint operation middleware
func (siw *ServerInterfaceWrapper) GetTenantTenantIdEndpoint(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetTenantTenantIdEndpoint(c, tenantId)
}

// DeleteTenantTenantIdExpire operation middleware
func (siw *ServerInterfaceWrapper) DeleteTenantTenantIdExpire(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteTenantTenantIdExpire(c, tenantId)
}

// GetTenantTenantIdExpire operation middleware
func (siw *ServerInterfaceWrapper) GetTenantTenantIdExpire(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetTenantTenantIdExpire(c, tenantId)
}

// PostTenantTenantIdExpire operation middleware
func (siw *ServerInterfaceWrapper) PostTenantTenantIdExpire(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantTenantIdExpire(c, tenantId)
}

// PostTenantTenantIdExtensionsCompactionDisable operation middleware
func (siw *ServerInterfaceWrapper) PostTenantTenantIdExtensionsCompactionDisable(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantTenantIdExtensionsCompactionDisable(c, tenantId)
}

// PostTenantTenantIdExtensionsCompactionEnable operation middleware
func (siw *ServerInterfaceWrapper) PostTenantTenantIdExtensionsCompactionEnable(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantTenantIdExtensionsCompactionEnable(c, tenantId)
}

// GetTenantTenantIdExtensionsCompactionParameters operation middleware
func (siw *ServerInterfaceWrapper) GetTenantTenantIdExtensionsCompactionParameters(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetTenantTenantIdExtensionsCompactionParameters(c, tenantId)
}

// GetTenantTenantIdExtensionsCompactionStatus operation middleware
func (siw *ServerInterfaceWrapper) GetTenantTenantIdExtensionsCompactionStatus(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetTenantTenantIdExtensionsCompactionStatus(c, tenantId)
}

// PostTenantTenantIdExtensionsCompactionStatus operation middleware
func (siw *ServerInterfaceWrapper) PostTenantTenantIdExtensionsCompactionStatus(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantTenantIdExtensionsCompactionStatus(c, tenantId)
}

// PostTenantTenantIdExtensionsCompactionUpdate operation middleware
func (siw *ServerInterfaceWrapper) PostTenantTenantIdExtensionsCompactionUpdate(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantTenantIdExtensionsCompactionUpdate(c, tenantId)
}

// PostTenantTenantIdExtensionsServerlessbackfillDisable operation middleware
func (siw *ServerInterfaceWrapper) PostTenantTenantIdExtensionsServerlessbackfillDisable(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantTenantIdExtensionsServerlessbackfillDisable(c, tenantId)
}

// PostTenantTenantIdExtensionsServerlessbackfillEnable operation middleware
func (siw *ServerInterfaceWrapper) PostTenantTenantIdExtensionsServerlessbackfillEnable(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantTenantIdExtensionsServerlessbackfillEnable(c, tenantId)
}

// PostTenantTenantIdExtensionsServerlessbackfillVersion operation middleware
func (siw *ServerInterfaceWrapper) PostTenantTenantIdExtensionsServerlessbackfillVersion(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantTenantIdExtensionsServerlessbackfillVersion(c, tenantId)
}

// PostTenantTenantIdMigrateMetaStore operation middleware
func (siw *ServerInterfaceWrapper) PostTenantTenantIdMigrateMetaStore(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantTenantIdMigrateMetaStore(c, tenantId)
}

// DeleteTenantTenantIdPrivatelinkPrivateLinkId operation middleware
func (siw *ServerInterfaceWrapper) DeleteTenantTenantIdPrivatelinkPrivateLinkId(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "privateLinkId" -------------
	var privateLinkId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "privateLinkId", c.Param("privateLinkId"), &privateLinkId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter privateLinkId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteTenantTenantIdPrivatelinkPrivateLinkId(c, tenantId, privateLinkId)
}

// PostTenantTenantIdResource operation middleware
func (siw *ServerInterfaceWrapper) PostTenantTenantIdResource(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantTenantIdResource(c, tenantId)
}

// PostTenantTenantIdResourceGroups operation middleware
func (siw *ServerInterfaceWrapper) PostTenantTenantIdResourceGroups(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantTenantIdResourceGroups(c, tenantId)
}

// DeleteTenantTenantIdResourceGroupsResourceGroup operation middleware
func (siw *ServerInterfaceWrapper) DeleteTenantTenantIdResourceGroupsResourceGroup(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "resourceGroup" -------------
	var resourceGroup string

	err = runtime.BindStyledParameterWithOptions("simple", "resourceGroup", c.Param("resourceGroup"), &resourceGroup, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter resourceGroup: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteTenantTenantIdResourceGroupsResourceGroup(c, tenantId, resourceGroup)
}

// PostTenantTenantIdResourceGroupsResourceGroup operation middleware
func (siw *ServerInterfaceWrapper) PostTenantTenantIdResourceGroupsResourceGroup(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "resourceGroup" -------------
	var resourceGroup string

	err = runtime.BindStyledParameterWithOptions("simple", "resourceGroup", c.Param("resourceGroup"), &resourceGroup, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter resourceGroup: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantTenantIdResourceGroupsResourceGroup(c, tenantId, resourceGroup)
}

// PostTenantTenantIdRestart operation middleware
func (siw *ServerInterfaceWrapper) PostTenantTenantIdRestart(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantTenantIdRestart(c, tenantId)
}

// PostTenantTenantIdStart operation middleware
func (siw *ServerInterfaceWrapper) PostTenantTenantIdStart(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantTenantIdStart(c, tenantId)
}

// PostTenantTenantIdStatus operation middleware
func (siw *ServerInterfaceWrapper) PostTenantTenantIdStatus(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantTenantIdStatus(c, tenantId)
}

// PostTenantTenantIdStop operation middleware
func (siw *ServerInterfaceWrapper) PostTenantTenantIdStop(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantTenantIdStop(c, tenantId)
}

// PostTenantTenantIdTier operation middleware
func (siw *ServerInterfaceWrapper) PostTenantTenantIdTier(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantTenantIdTier(c, tenantId)
}

// PostTenantTenantIdUpdateVersion operation middleware
func (siw *ServerInterfaceWrapper) PostTenantTenantIdUpdateVersion(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantTenantIdUpdateVersion(c, tenantId)
}

// PostTenantTenantIdUpgradeMetaStore operation middleware
func (siw *ServerInterfaceWrapper) PostTenantTenantIdUpgradeMetaStore(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	// Parameter object where we will unmarshal all parameters from the context
	var params PostTenantTenantIdUpgradeMetaStoreParams

	// ------------- Required query parameter "metaStore" -------------

	if paramValue := c.Query("metaStore"); paramValue != "" {

	} else {
		siw.ErrorHandler(c, fmt.Errorf("Query argument metaStore is required, but not found"), http.StatusBadRequest)
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "metaStore", c.Request.URL.Query(), &params.MetaStore)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter metaStore: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantTenantIdUpgradeMetaStore(c, tenantId, params)
}

// GetTenants operation middleware
func (siw *ServerInterfaceWrapper) GetTenants(c *gin.Context) {

	var err error

	// Parameter object where we will unmarshal all parameters from the context
	var params GetTenantsParams

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", c.Request.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter offset: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", c.Request.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter limit: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetTenants(c, params)
}

// DeleteTenantsTenantIdExtensionsIcebergCompaction operation middleware
func (siw *ServerInterfaceWrapper) DeleteTenantsTenantIdExtensionsIcebergCompaction(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteTenantsTenantIdExtensionsIcebergCompaction(c, tenantId)
}

// GetTenantsTenantIdExtensionsIcebergCompaction operation middleware
func (siw *ServerInterfaceWrapper) GetTenantsTenantIdExtensionsIcebergCompaction(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetTenantsTenantIdExtensionsIcebergCompaction(c, tenantId)
}

// PostTenantsTenantIdExtensionsIcebergCompaction operation middleware
func (siw *ServerInterfaceWrapper) PostTenantsTenantIdExtensionsIcebergCompaction(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantsTenantIdExtensionsIcebergCompaction(c, tenantId)
}

// PutTenantsTenantIdExtensionsIcebergCompaction operation middleware
func (siw *ServerInterfaceWrapper) PutTenantsTenantIdExtensionsIcebergCompaction(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PutTenantsTenantIdExtensionsIcebergCompaction(c, tenantId)
}

// GetVersion operation middleware
func (siw *ServerInterfaceWrapper) GetVersion(c *gin.Context) {

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetVersion(c)
}

// PostWebhooksAlert operation middleware
func (siw *ServerInterfaceWrapper) PostWebhooksAlert(c *gin.Context) {

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostWebhooksAlert(c)
}

// GetWorkflowId operation middleware
func (siw *ServerInterfaceWrapper) GetWorkflowId(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetWorkflowId(c, id)
}

// PostWorkflowIdCancel operation middleware
func (siw *ServerInterfaceWrapper) PostWorkflowIdCancel(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostWorkflowIdCancel(c, id)
}

// PostWorkflowIdRerun operation middleware
func (siw *ServerInterfaceWrapper) PostWorkflowIdRerun(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostWorkflowIdRerun(c, id)
}

// PostWorkflowIdResume operation middleware
func (siw *ServerInterfaceWrapper) PostWorkflowIdResume(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostWorkflowIdResume(c, id)
}

// PostWorkflowIdSchedule operation middleware
func (siw *ServerInterfaceWrapper) PostWorkflowIdSchedule(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostWorkflowIdSchedule(c, id)
}

// GetWorkflows operation middleware
func (siw *ServerInterfaceWrapper) GetWorkflows(c *gin.Context) {

	var err error

	// Parameter object where we will unmarshal all parameters from the context
	var params GetWorkflowsParams

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", c.Request.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter offset: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", c.Request.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter limit: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetWorkflows(c, params)
}

// GinServerOptions provides options for the Gin server.
type GinServerOptions struct {
	BaseURL      string
	Middlewares  []MiddlewareFunc
	ErrorHandler func(*gin.Context, error, int)
}

// RegisterHandlers creates http.Handler with routing matching OpenAPI spec.
func RegisterHandlers(router gin.IRouter, si ServerInterface) {
	RegisterHandlersWithOptions(router, si, GinServerOptions{})
}

// RegisterHandlersWithOptions creates http.Handler with additional options
func RegisterHandlersWithOptions(router gin.IRouter, si ServerInterface, options GinServerOptions) {
	errorHandler := options.ErrorHandler
	if errorHandler == nil {
		errorHandler = func(c *gin.Context, err error, statusCode int) {
			c.JSON(statusCode, gin.H{"msg": err.Error()})
		}
	}

	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandler:       errorHandler,
	}

	router.GET(options.BaseURL+"/", wrapper.Get)
	router.POST(options.BaseURL+"/alertNotifications", wrapper.PostAlertNotifications)
	router.POST(options.BaseURL+"/byoc-cluster/:id/access", wrapper.PostByocClusterIdAccess)
	router.POST(options.BaseURL+"/byoc-cluster/:id/update", wrapper.PostByocClusterIdUpdate)
	router.GET(options.BaseURL+"/byoc-clusters", wrapper.GetByocClusters)
	router.DELETE(options.BaseURL+"/cluster/:name", wrapper.DeleteClusterName)
	router.GET(options.BaseURL+"/cluster/:name", wrapper.GetClusterName)
	router.POST(options.BaseURL+"/cluster/:name", wrapper.PostClusterName)
	router.GET(options.BaseURL+"/clusters", wrapper.GetClusters)
	router.POST(options.BaseURL+"/clusters", wrapper.PostClusters)
	router.GET(options.BaseURL+"/clusters/:uuid/prometheus/api/v1/label/:labelName/values", wrapper.GetClustersUuidPrometheusApiV1LabelLabelNameValues)
	router.POST(options.BaseURL+"/clusters/:uuid/prometheus/api/v1/label/:labelName/values", wrapper.PostClustersUuidPrometheusApiV1LabelLabelNameValues)
	router.GET(options.BaseURL+"/clusters/:uuid/prometheus/api/v1/query", wrapper.GetClustersUuidPrometheusApiV1Query)
	router.POST(options.BaseURL+"/clusters/:uuid/prometheus/api/v1/query", wrapper.PostClustersUuidPrometheusApiV1Query)
	router.GET(options.BaseURL+"/clusters/:uuid/prometheus/api/v1/query_range", wrapper.GetClustersUuidPrometheusApiV1QueryRange)
	router.POST(options.BaseURL+"/clusters/:uuid/prometheus/api/v1/query_range", wrapper.PostClustersUuidPrometheusApiV1QueryRange)
	router.GET(options.BaseURL+"/clusters/:uuid/prometheus/api/v1/series", wrapper.GetClustersUuidPrometheusApiV1Series)
	router.POST(options.BaseURL+"/clusters/:uuid/prometheus/api/v1/series", wrapper.PostClustersUuidPrometheusApiV1Series)
	router.DELETE(options.BaseURL+"/tenant/:tenantId", wrapper.DeleteTenantTenantId)
	router.GET(options.BaseURL+"/tenant/:tenantId", wrapper.GetTenantTenantId)
	router.POST(options.BaseURL+"/tenant/:tenantId/backup", wrapper.PostTenantTenantIdBackup)
	router.DELETE(options.BaseURL+"/tenant/:tenantId/backup/:snapshotId", wrapper.DeleteTenantTenantIdBackupSnapshotId)
	router.POST(options.BaseURL+"/tenant/:tenantId/backup/:snapshotId/in-place-restore", wrapper.PostTenantTenantIdBackupSnapshotIdInPlaceRestore)
	router.POST(options.BaseURL+"/tenant/:tenantId/backup/:snapshotId/restore", wrapper.PostTenantTenantIdBackupSnapshotIdRestore)
	router.POST(options.BaseURL+"/tenant/:tenantId/computeCache", wrapper.PostTenantTenantIdComputeCache)
	router.PUT(options.BaseURL+"/tenant/:tenantId/config/etcd", wrapper.PutTenantTenantIdConfigEtcd)
	router.PUT(options.BaseURL+"/tenant/:tenantId/config/risingwave", wrapper.PutTenantTenantIdConfigRisingwave)
	router.PUT(options.BaseURL+"/tenant/:tenantId/configNoRestart/risingwave", wrapper.PutTenantTenantIdConfigNoRestartRisingwave)
	router.POST(options.BaseURL+"/tenant/:tenantId/diagnosis/report/generate", wrapper.GenDiagReport)
	router.GET(options.BaseURL+"/tenant/:tenantId/diagnosis/report/range", wrapper.ListDiagReportTimeRange)
	router.GET(options.BaseURL+"/tenant/:tenantId/diagnosis/report/:reportId", wrapper.GetDiagReport)
	router.GET(options.BaseURL+"/tenant/:tenantId/endpoint", wrapper.GetTenantTenantIdEndpoint)
	router.DELETE(options.BaseURL+"/tenant/:tenantId/expire", wrapper.DeleteTenantTenantIdExpire)
	router.GET(options.BaseURL+"/tenant/:tenantId/expire", wrapper.GetTenantTenantIdExpire)
	router.POST(options.BaseURL+"/tenant/:tenantId/expire", wrapper.PostTenantTenantIdExpire)
	router.POST(options.BaseURL+"/tenant/:tenantId/extensions/compaction/disable", wrapper.PostTenantTenantIdExtensionsCompactionDisable)
	router.POST(options.BaseURL+"/tenant/:tenantId/extensions/compaction/enable", wrapper.PostTenantTenantIdExtensionsCompactionEnable)
	router.GET(options.BaseURL+"/tenant/:tenantId/extensions/compaction/parameters", wrapper.GetTenantTenantIdExtensionsCompactionParameters)
	router.GET(options.BaseURL+"/tenant/:tenantId/extensions/compaction/status", wrapper.GetTenantTenantIdExtensionsCompactionStatus)
	router.POST(options.BaseURL+"/tenant/:tenantId/extensions/compaction/status", wrapper.PostTenantTenantIdExtensionsCompactionStatus)
	router.POST(options.BaseURL+"/tenant/:tenantId/extensions/compaction/update", wrapper.PostTenantTenantIdExtensionsCompactionUpdate)
	router.POST(options.BaseURL+"/tenant/:tenantId/extensions/serverlessbackfill/disable", wrapper.PostTenantTenantIdExtensionsServerlessbackfillDisable)
	router.POST(options.BaseURL+"/tenant/:tenantId/extensions/serverlessbackfill/enable", wrapper.PostTenantTenantIdExtensionsServerlessbackfillEnable)
	router.POST(options.BaseURL+"/tenant/:tenantId/extensions/serverlessbackfill/version", wrapper.PostTenantTenantIdExtensionsServerlessbackfillVersion)
	router.POST(options.BaseURL+"/tenant/:tenantId/migrateMetaStore", wrapper.PostTenantTenantIdMigrateMetaStore)
	router.DELETE(options.BaseURL+"/tenant/:tenantId/privatelink/:privateLinkId", wrapper.DeleteTenantTenantIdPrivatelinkPrivateLinkId)
	router.POST(options.BaseURL+"/tenant/:tenantId/resource", wrapper.PostTenantTenantIdResource)
	router.POST(options.BaseURL+"/tenant/:tenantId/resourceGroups", wrapper.PostTenantTenantIdResourceGroups)
	router.DELETE(options.BaseURL+"/tenant/:tenantId/resourceGroups/:resourceGroup", wrapper.DeleteTenantTenantIdResourceGroupsResourceGroup)
	router.POST(options.BaseURL+"/tenant/:tenantId/resourceGroups/:resourceGroup", wrapper.PostTenantTenantIdResourceGroupsResourceGroup)
	router.POST(options.BaseURL+"/tenant/:tenantId/restart", wrapper.PostTenantTenantIdRestart)
	router.POST(options.BaseURL+"/tenant/:tenantId/start", wrapper.PostTenantTenantIdStart)
	router.POST(options.BaseURL+"/tenant/:tenantId/status", wrapper.PostTenantTenantIdStatus)
	router.POST(options.BaseURL+"/tenant/:tenantId/stop", wrapper.PostTenantTenantIdStop)
	router.POST(options.BaseURL+"/tenant/:tenantId/tier", wrapper.PostTenantTenantIdTier)
	router.POST(options.BaseURL+"/tenant/:tenantId/updateVersion", wrapper.PostTenantTenantIdUpdateVersion)
	router.POST(options.BaseURL+"/tenant/:tenantId/upgradeMetaStore", wrapper.PostTenantTenantIdUpgradeMetaStore)
	router.GET(options.BaseURL+"/tenants", wrapper.GetTenants)
	router.DELETE(options.BaseURL+"/tenants/:tenantId/extensions/iceberg-compaction", wrapper.DeleteTenantsTenantIdExtensionsIcebergCompaction)
	router.GET(options.BaseURL+"/tenants/:tenantId/extensions/iceberg-compaction", wrapper.GetTenantsTenantIdExtensionsIcebergCompaction)
	router.POST(options.BaseURL+"/tenants/:tenantId/extensions/iceberg-compaction", wrapper.PostTenantsTenantIdExtensionsIcebergCompaction)
	router.PUT(options.BaseURL+"/tenants/:tenantId/extensions/iceberg-compaction", wrapper.PutTenantsTenantIdExtensionsIcebergCompaction)
	router.GET(options.BaseURL+"/version", wrapper.GetVersion)
	router.POST(options.BaseURL+"/webhooks/alert", wrapper.PostWebhooksAlert)
	router.GET(options.BaseURL+"/workflow/:id", wrapper.GetWorkflowId)
	router.POST(options.BaseURL+"/workflow/:id/cancel", wrapper.PostWorkflowIdCancel)
	router.POST(options.BaseURL+"/workflow/:id/rerun", wrapper.PostWorkflowIdRerun)
	router.POST(options.BaseURL+"/workflow/:id/resume", wrapper.PostWorkflowIdResume)
	router.POST(options.BaseURL+"/workflow/:id/schedule", wrapper.PostWorkflowIdSchedule)
	router.GET(options.BaseURL+"/workflows", wrapper.GetWorkflows)
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+xde28cN5L/KkTfAdkF2h47CQ443T9nyYpP2NhRNJJze7uCQHXXzHDVTbZJtmRF0Hc/",
	"8NVP9mse8thW/oglNR/Fqh+LVcUi+RBELM0YBSpFcPAQcBAZowL0L4c4PoNPOQh5Zv+s/hoxKoFK9SPO",
	"soREWBJGZ/8SjKq/iWgFKVY/ZZxlwCUxjaViqf6R9xkEB4GQnNBl8PgYBhw+5YRDHBz8Qxe6DF0hdv0v",
	"iGTwqErFICJOMtVVcBD8/OoVOsQxsuQFj2FwlORCAp9LLHOxBXo54Pj+Sujm1O//zmERHAT/NisZNjN1",
	"xcz2faaqGAJaI6s15xviYxi8hQXOk71jtiULFdhAHGTOKaFLpNp4DINfMEkgPuUQMRoTVW8fEWOoRFUy",
	"FfEfmPyF5TTeP5J/Rh+YRJo4RegfjN8sEnZ3sg1S74rG1G8LxlMsg4Mgz0kchAMjqNQdMxBHNzp5W4BI",
	"t2nnjyLATqE3UQRCnNAFa1Mc4SPg8hAL+I+fPVwOA6BxxohhReujZDdAh4VTtBHW+3MNXJaqpjrdDx4C",
	"oHmqWjiEBeMQhMEJlcB5nklQHH2zkMAr7Copq2muaksXlFAiCU7In7qJU85uiSCM6t9090EYnANPCcVS",
	"taZ0SAKmw1OgMaHLMxAs5xHoD0oeYXCRxa64mRJ+spyecy14JOKKnN9nYIDUaibKcu/fU0gZv/d+4qBh",
	"XPlGqIQl8Ja0mgSUdU3HRTdtmHoG6FaTtca5DaK9VHLAEhyJ7zjLM2EJPWTxfZvYBUngCEcrmJM/4d21",
	"j6AwoDiFjmGUwu5d8rqY1xyt7qnSrm+Mb7HE11jAEaMUIoXSC560R5abP8JnnGaJaiFjQi45iE/JwWxG",
	"YqCSLAjw/8oFcNXvQYaFuGM8/u8VE/IgY1zOYtvXoJJTvfmIfQfyHCim8vizBKqmo+IF1mSfYo5TkMAL",
	"C8QvI1uD8SE2nzLR3VvRShUPj2Ewj3ACmzVtmqi122BPVgx1MpfqNpqfQ6Xh1S+lTosqDE4iuAa+LPv1",
	"TWy6IMveiSAmzwRVewvkv8cULyG2q4OHdvPhSgC/JRFc4ShiecfiRxqLPKHSrGktxZBi3aidaq2GOvUG",
	"48sRdkQYaGrp8sp8ePAVkGpt0kPEsbHScHJaG3p7cW8yb5Ld7kz20UaCHpoacejUm+2vxr+wU0SuowY7",
	"KoO/bAHgDedYzxMiIR0cWQM8JYuwbqbVuujE18SuDJmtJc+15QU6SDyXjMObO3EWeyghVEhMIzhKsPDL",
	"X3QudU3J1ZoqKvaT9Sc/tQuNR0/1LLLiJh+hAW7ykXQcyyj2+alrLtiT+Fb0Mo7Ud1F2lLA8nn9KprFM",
	"EqPq+nmmS42jxErOS8hesm6+wmrMp8vueeC1QDuA7nXRKt2dWz3sfA5QIAsrllUQBvhOXPFYTZdllF1F",
	"SrD2w5/8ypVUwzOkX2VLr0PxW2Z0eduN9ZsAfmc6nOy5tgZ/ipceZyYhKZFjl0m2WAgYWbohGdNP0YRP",
	"Ogq0bxLg8gNTFq3x6HuN/s51uV5s3QXVb9NX2u4axeE9i2qufe8oJEmB5fI9ofrXehTBfkSEopTQXGrI",
	"DfC62uBlmyTtCEMvSVEuJEvnFaOkTtW1Dg8goBGLIUamOCrWcc80uAUurDE6BqtMyCLe0EfnBGtwXRvv",
	"iWy3kUaYBeEWDS7F6znFmVgxOeCd2FKjVFC4rVhbWO23a8YZr0stWbk0cYB+3FQKzjOIhtZC07xbCKvd",
	"VJ0mE6aoWau77LYDsX7AtLr2cbJuLT/28vr4c0Z4P5djHZV7U18wlO55oTSUDzKgGx1fY4jEccGDdqAi",
	"y39162I7QpjllZBZ6/N7HXrrrm6+d7fQEF+lt7Ckq9lMvdvLqVzpZ8bXGbWZxoF2Gx4+JAlEUse3b3Hi",
	"t+SPGEvesjt6Cpyw2F/G7iwd4QxHRN6fgdLOEB/mi0XNDWhXOcUcJwkkRKRd5YQCzpmJrAp/IT1ztXFl",
	"5vA5qemRSsn3+HN/U+8J7S9wypKE0GU/0zTzFdfO2Yczi31FlBgs/n/A2WCNxoxqkhS2JDvQRR/BLQTU",
	"eVRnaVteXkl3SGwISJO1gAtQ9syBjMMtYXmHSYP5EkaotKKRoko/qW79E4NL+ig11R3DL1bJTZpYcL0z",
	"GW/SRgoSb1JfOcExThjdbEOjXybKj25IpO4kmDBzboCLFowjCndI6vpIMnQNKNLbPLHybrhpEUUsTTGN",
	"g6YTR+HOdP1hnNlTK94PsKcCvi0Xlq3103VOBpakL0KVcR8/Gn+ulzxxQ7JDHN3kmQGH1lbBwQInAoou",
	"rhlLANNBH7FKsivYRaiLtZwBz+mA1qASPksveNUHdEeSRCE1Bb7UQJUMyRUgxyzkWvCQEkOC76eYvwvO",
	"UgVGmOAmu7HOoxXEeTJokfdSRPMkwdcJBAeS5zDK3D67e0vw8gwyxj17yGM0WbWF96q8DuCAIEsK8TuQ",
	"F16PvZleoiq2610OUPze0tcIOcYeQGCqcJBT8hkpbgmJ0wwJnVQjQsQ4YnIFHMmVDYGMCahlnEUgBMRT",
	"UGI06Em8VhzO+N6uhToFY5i1hSiiEpUYvafjQ0dzV2eDuKQhxjdyo+06Y04fuuJFdk2b5PbKKL7q2ZVd",
	"AU7kqpKWV+bK3FB2R4Mw+B9d5D4Igwu6sj/7wtHj90RJipdwjpf15IPbVy9fv3z9o28UCZYg5MnUalSM",
	"jCcxvhxZksPSv45M2OCu25666l2fjNrC0TksJuHoLKe0kqlUy0IKg7lkWVb+ZL7OJea2oDH6Y+1cqP4r",
	"mUwX2ZLj2P1c/NnF84oGWsb+McXXSefXt0S0P8+Vd8ETEEIt6guSJMYU6C/j66hdqtqhmuXvyZIXzNOW",
	"ofn595yAiGwmWDXqVgz+slNjds5Yt/3WCwcC/ETnI+ZZxFJCl47Hzn0eN9VzzbJJ2iEXakY1dqyWQIFr",
	"XzUjGSSEgnfkudBkr71S2Po1Fhbzq5IAYPcmy9lV1YMVXdLSEtVpVeVOU+lZPeG0QF1nVnnUrcqn5RJY",
	"9e9ZbarxT3/O5mDgc8DOGhEIHbbUavmdrrmwpK2bUW6Nx0ny2yI4+MdAbE6Vfgxbu1q6qZFs9udPuCba",
	"lHbTPiJtU0wPu9t6j/U4+mbhewXgjyzJU5OySA790a7U7VlP663Y6vZvUIxvSNfpyG8pOdNgzLB8jmrS",
	"2FYoZ5MYjpPJWhXXjvqsHe7ZMM7jd+Z6EDshoaWZ69Od/+GBWv+u2U52zMJNcmIGEn8nT4zKfJu2VNW5",
	"2LlutRVEi+Eu82UoBa6RwfbYSI0ZXb2SaTZyDtazwx4bKTpjK1fztR5r2T9jW6jkWWkHwZ/0X8kQGttw",
	"mQ9VCHJkTW0EtVZSs++vCPQikDgr0ZmXv3AwhzluiTlX8RZuIVEgeWE/lX84xIJEtb+cmz3JuVZPXFU/",
	"/PtvR/rUhv5wCDRapZjfeG1Wl58yOvd/69n7vWn7LuDWF0xcLwwwPV4oOsOFbU+/w1dOWHQzjUxunNmi",
	"Z4cZ6+QW/HEebuUPSgL6pE71b5hGkFTLXW7Hb3I5JOf+xB2fu1OrUuFvY9BhUEZ9q25OSWQfcKYp94Ir",
	"Ho3uvh3fgi9ShaXk5FrnjGlIjo/9FAHOCVHJrvSoTdKAakI5KcpXCQyrw7xsMmU9Xht+9jB8KIlzZ8cM",
	"SxLWdNLGDF/4HSz3+Q8iV5pDHtMdbsc4WB4RVYAyfkZ0cC4IHR19wxDdYhs9AL9fVDbjPydKbNBAEmmC",
	"o6+DyuZX8Prlqxc4yVY61M0yoDgjwUHw08tXL38KwiDDcqUJnKn/2S0+NQq9z6oQF7zT+3m1U+0/vnrV",
	"Naii3Kx5FNsc0J7hZlKuYR0Tnq69SbwiMAyqrOCjz+8OpQX1Zgw/1kUjeQ6PfsbUN3t++5s5qZunKeb3",
	"wYHV8khzAtFKbzqJYSmU2PVHHamqDf1S8/D6nkUv7NbB7IHEjzOsM4M1J8tDZWoe10khMWILZGsirtFN",
	"1AeFA3cU58BoypJrI6J9l2GPCCvpwiexyWHeoQx786XHy3Ar9LSPZHvOdquv+oTzYxj8PGZmeS6V0FV/",
	"Hq7aOqhfR+Y7kEhCmjGO+T0yoEI671enXMgVICs2iB2MKqB1f+lCqTFpvgaUFpsRT4DSdgr9KJT+uDV6",
	"OgyR/YaqwZKGpPIHC7jYpS9EZOF+RkQoNYtEBhFZEIhDZGsTnTuk2rAJJa7KKFCLvjWzImAR7FDFNA8h",
	"eqTWWoDUNMdJgqKSPv9oi9mrptxjuRvRHrG5NOGosp29FZvhi8PMjLdX14WdGBhmxw4g0IWA7a0QeIAd",
	"DeXuUd3uyG8xvKYX06upm2zdjYb2HBkabz1MNY2rLDbXVSDGnZYaWmjHqKOvWhWNAIPYVyQMjL9L2Oj6",
	"HtlZ0i/02UOeK9sq4ywFuYJczHBGZrevZwm+hmT2oP9Rc+VxdouTHEbB5CIn8WnR4puMfHz9q2rnV9fY",
	"R9OUd8xK1bz+cXga+O8b06wdViE2/OGz/zoCI95mCu5srI6+MM/GYeJTDubGoDUA8Luu+xVJfF2p7XKg",
	"E8R0xTE1Ybl1hXWmG/heJLbL0Y4TmwBO1tavc1P5O5DWTkeqJGVyfWYPLjV5hOdiD0mUuczP3nbVTDFM",
	"QtieuAl6nJ4hTm7P3nRZdbv3d8pRD0+jSj78tOCUF7mz6+LMS/dcq7PcnpKZSGvdmJ0eWNvZfOm8UqB3",
	"xngD7sgwE7k7AIqgZgoSI3NkjC30X6zES+v72nHVfrl87JPX7KG8Z2Cy9jECnJcXFTylKP1msqgSM6b9",
	"zhVjZzgZuBtnPFpip+u+DFpmhL7IEhzBC3uKcfrcL6FzQk9VU/aE5TOQdrVA1wBkuY0kQ1gfUy18eiwg",
	"RoxqzBSwMgPcAnC2gJdvEyi7CQt1Hl5+4u2bbjPIHftRSHRHomMsMVpwliK5IqJA4csvgOFmLu4Obatw",
	"9IyopfnuGjxddwx92xuAvn08iwUUKVaYlba5pg6iiS7IcuYyfncLptyHpbwFJUXRsbkOsBtIEj7LWZZg",
	"QuvHHI/Pj95e/X7x2/mbq8M3R387/vD26vDv58fzA/TD61fuvx/+SXW592/+9+rs+PeL4/l5u9QP/6S9",
	"4c3n3eYhN/Ci3G1WAEPFWbFp4OREELq8w7c71nduSf1kI5m2uWLkvWjoqExZbI8DDEXKJ0yOs5Ih46dI",
	"2XvjekOWJqhkcUVIz3iftgVZgbu9YmU9wH9gyo7AXD4jv4n8gjXPU+AbnQIxwUvKBFF96ncTzDFrOX0G",
	"bBqbc7ZvndeOHITR2R/oraMWmesxjI+gPxlKQpQBF0RIRPQNshjpM1I6EIKXgK7z6AZkiDCN7etGiEiB",
	"dH5y2AoT08pFLzsMEdculPHg7Z3lQexGfUIXDG3beujndAVRBWSGnacWupq7hfWBJkpwGAmQiC18VAj0",
	"F/gcJXms/EXL+7+iOyJXWtZF7hzS/ejAF0nBocQHhZbUfyVCluI4Jym4LbsnDTbUGXP2y9FPP/30nzP7",
	"7wdMGTLtKFSIwoNWg/0LocoNJrfwV5eS2lgqdHl7o56HrkmH7qdRap4qGksn0HhHVGZ4qZ9SYhS9QCt2",
	"h1JM75HBqI6cChONUAqigzh3vXefYDvo6hH0OMLEDck6yLIXA4W1xddeQ/YqXIfGyyfSfMVdSx4NeAaS",
	"E7hVWLe6waqDDl02QpEMKY0tKbwH86/dYPFqvQXIaKXXcMtTF7DPOLxYlrrfs/yRBcK3mGgBTtJy70DW",
	"1rYvHUh1XNr+Ht/TLdMlSHe5THeiZWuLdvV5vXH758flY3o7477/7bCebfXXa2Zyb+mAygoQZ0wix00n",
	"plLdrLlfv9n86BC5vi1n6hawuRBoezn0m3PenKxGUNwejO4qh4lHgrlvVFvcC6lcp7Tz5BAdEtT9aVdH",
	"wcQ+iroX2xkVlu9yI6N9gf/OcuW3GtCtwDkhC4juo8SU9M5ke+OcmEXF5XazWF82tz/7V8W9eKJ5Ax88",
	"Z7U1s9oMX8yKUgbNSkHrjShRXDCISrmPDwP5YQN071FzTL9i0PQ8p70l8Bj2fAnsVBEz2o70CLh8anWX",
	"S/Iaj732LNp7IXq37peCcCboFpDwBXJM/TArL6FdH2Jzd+HmF4GX55XczaC145P3yofRJH8tcNpsgamA",
	"Y7e26YjXSZ74Yohn1G5vOey8VWJ/kL7z6yRGvcb17ez67n6Jr2z/PgWcy5au7SXme+xYzlvEft0O5hbQ",
	"1J9LMNnPdHw199RvBU5763C20fS9O579YJrqd+4AS5W3lfYeTB+LC32mZ+C+fvmjviXv28+c3Tlqp66n",
	"FdQiydy1TC+RFadNfEAJuQH0+uWrl69ejgd3qt8DaVxavQ84ft8kzA+mfjm0UbJHZpUdYRMH5Ym68VLM",
	"OLnFEhJCb2YP9pdfCb1Z48DladnUabWh54B5/zHwHwSyjEeKd+j6vn4QqCIVseNQhD/3IWtJc+1zZH4Q",
	"Vm8L3wsVcla+FbDzc2/th1S/o3NLdkFzUwFVHmkYqcDaD6lMFbKt+eQn/bePK3MNVvc1/Y8WWc+his77",
	"4hyc0NIeVXAwbKBlHBxnD7XfJy+pTVFWnxN5FmTv6jpekPuQXNgUbFcXky4Pmoal7aujoVdDntXRYOR0",
	"C+pIYvsK8J4YVpqeZ5egfVEA5tJ/DcqABbRfEp4/y9cj3/km0rX5A/si3qfYbd5ka/lruxz7DIR+MEA7",
	"QOXzrWPhwbI9AgfLnqd+a+qzbK2Z795A3gvRnrvXhHc561Un38ecd4F8M+ntU80jcWEyNT7u2e7RRY2q",
	"XSOl1tvzox8bnslvP9cxCMElx/EGez9bcaIbJ0vT2obPDjzoi+aov5GNpR6hj0hc7YqcDp/7XV/UY842",
	"P+nRy8or7VPeayjnoEDEXAZnnn9IgUo0vxcS0tasrHv7oiPhgERwDXz5opJSNTL4KNoJASemsaNqetZz",
	"3KYvScmyvy+jTfS+erOJLLaH63ZnHjGctMbq3JgtJsiSzk78fP0iV8N0rSCjxbiezdR683hBlr2PqouN",
	"3mVuvZ/5HMXtzzIbrQz6r756RtF3vRcwEkXKNKikFnatL1VPbUuP0t7B9YqxG2Fep+3fiv/DltVvxQZr",
	"PfrEIQJya1+AFfYOGojtNSP6WdhUG1N87Iuw7iy+fmez8zoUtRq5kvqWFmSfNvbcYfJH9aHsna3Snmeg",
	"d3Bi37GwuLFg3DI79TmUliBmkb40YZyPuf7jK3Vm2YsaMKq8YO1BcSFeU340kL9QAMojwja7OfCcPi23",
	"z1SXCKMFoUSsIB7LdV1vh8Em15Pu56sIM+0/uESewlOjS/WJMDKKJJmCL03ttzCrFR/jPJnC+o3eC/CL",
	"Ym6pQBjxnFJCl4UofhAohgTfD4jEtfAEs951ta0nK/cTHqLX0MFJUkhIIMZj86imeQsofiORqtJn+TyH",
	"Byv6eyBA6JOUMnn1gQ7Du5wnwUFgn8oLFNG2UgtwTiAC4WuWuw1m0Yx/iKB9yWOrrv/qNttOeXPb4+Xj",
	"/wcAAP//LKGcTsjBAAA=",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
