// Package mgmt_spec provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package mgmt_spec

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"path"
	"strings"
	"time"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/gin-gonic/gin"
	"github.com/oapi-codegen/runtime"
	openapi_types "github.com/oapi-codegen/runtime/types"
)

const (
	ApiKeyAuthScopes = "ApiKeyAuth.Scopes"
	BearerAuthScopes = "BearerAuth.Scopes"
)

// Defines values for ClusterStatus.
const (
	ClusterStatusDeleted                 ClusterStatus = "Deleted"
	ClusterStatusFailed                  ClusterStatus = "Failed"
	ClusterStatusPendingResourceDeletion ClusterStatus = "PendingResourceDeletion"
	ClusterStatusProvisioned             ClusterStatus = "Provisioned"
	ClusterStatusReady                   ClusterStatus = "Ready"
	ClusterStatusTerminating             ClusterStatus = "Terminating"
	ClusterStatusUninitialized           ClusterStatus = "Uninitialized"
	ClusterStatusUpdating                ClusterStatus = "Updating"
)

// Defines values for KafkaConfigSaslMechanisms.
const (
	GSSAPI      KafkaConfigSaslMechanisms = "GSSAPI"
	OAUTHBEARER KafkaConfigSaslMechanisms = "OAUTHBEARER"
	PLAIN       KafkaConfigSaslMechanisms = "PLAIN"
	SCRAMSHA256 KafkaConfigSaslMechanisms = "SCRAM-SHA-256"
	SCRAMSHA512 KafkaConfigSaslMechanisms = "SCRAM-SHA-512"
)

// Defines values for KafkaConfigSecurityProtocol.
const (
	SASLPLAINTEXT KafkaConfigSecurityProtocol = "SASL_PLAINTEXT"
	SASLSSL       KafkaConfigSecurityProtocol = "SASL_SSL"
	SSL           KafkaConfigSecurityProtocol = "SSL"
)

// Defines values for MetaStoreType.
const (
	AwsRds      MetaStoreType = "aws_rds"
	AzrPostgres MetaStoreType = "azr_postgres"
	Etcd        MetaStoreType = "etcd"
	GcpCloudsql MetaStoreType = "gcp_cloudsql"
	Postgresql  MetaStoreType = "postgresql"
	SharingPg   MetaStoreType = "sharing_pg"
)

// Defines values for PostSourceRequestBodyType.
const (
	Kafka PostSourceRequestBodyType = "kafka"
)

// Defines values for PrivateLinkConnectionState.
const (
	ACCEPTED          PrivateLinkConnectionState = "ACCEPTED"
	CLOSED            PrivateLinkConnectionState = "CLOSED"
	PENDING           PrivateLinkConnectionState = "PENDING"
	REJECTED          PrivateLinkConnectionState = "REJECTED"
	STATUSUNSPECIFIED PrivateLinkConnectionState = "STATUS_UNSPECIFIED"
)

// Defines values for PrivateLinkStatus.
const (
	CREATED  PrivateLinkStatus = "CREATED"
	CREATING PrivateLinkStatus = "CREATING"
	DELETING PrivateLinkStatus = "DELETING"
	ERROR    PrivateLinkStatus = "ERROR"
	UNKNOWN  PrivateLinkStatus = "UNKNOWN"
)

// Defines values for TenantHealthStatus.
const (
	Healthy   TenantHealthStatus = "Healthy"
	Unhealthy TenantHealthStatus = "Unhealthy"
	Unknown   TenantHealthStatus = "Unknown"
)

// Defines values for TenantStatus.
const (
	TenantStatusConfigUpdating                       TenantStatus = "ConfigUpdating"
	TenantStatusCreating                             TenantStatus = "Creating"
	TenantStatusDeleting                             TenantStatus = "Deleting"
	TenantStatusExpired                              TenantStatus = "Expired"
	TenantStatusExtensionCompactionDisabling         TenantStatus = "ExtensionCompactionDisabling"
	TenantStatusExtensionCompactionEnabling          TenantStatus = "ExtensionCompactionEnabling"
	TenantStatusExtensionServerlessBackfillDisabling TenantStatus = "ExtensionServerlessBackfillDisabling"
	TenantStatusExtensionServerlessBackfillEnabling  TenantStatus = "ExtensionServerlessBackfillEnabling"
	TenantStatusExtensionServerlessBackfillUpdate    TenantStatus = "ExtensionServerlessBackfillUpdate"
	TenantStatusFailed                               TenantStatus = "Failed"
	TenantStatusMetaMigrating                        TenantStatus = "MetaMigrating"
	TenantStatusQuiesced                             TenantStatus = "Quiesced"
	TenantStatusResourceGroupsUpdating               TenantStatus = "ResourceGroupsUpdating"
	TenantStatusRestoring                            TenantStatus = "Restoring"
	TenantStatusRunning                              TenantStatus = "Running"
	TenantStatusSnapshotting                         TenantStatus = "Snapshotting"
	TenantStatusStarting                             TenantStatus = "Starting"
	TenantStatusStopped                              TenantStatus = "Stopped"
	TenantStatusStopping                             TenantStatus = "Stopping"
	TenantStatusUpdating                             TenantStatus = "Updating"
	TenantStatusUpgrading                            TenantStatus = "Upgrading"
)

// Defines values for TenantUsageType.
const (
	TenantUsageTypeGeneral  TenantUsageType = "general"
	TenantUsageTypePipeline TenantUsageType = "pipeline"
)

// Defines values for TenantRequestRequestBodyUsageType.
const (
	TenantRequestRequestBodyUsageTypeGeneral  TenantRequestRequestBodyUsageType = "general"
	TenantRequestRequestBodyUsageTypePipeline TenantRequestRequestBodyUsageType = "pipeline"
)

// Defines values for TierId.
const (
	BYOC           TierId = "BYOC"
	Benchmark      TierId = "Benchmark"
	DeveloperBasic TierId = "Developer-Basic"
	DeveloperFree  TierId = "Developer-Free"
	DeveloperTest  TierId = "Developer-Test"
	Free           TierId = "Free"
	Invited        TierId = "Invited"
	Standard       TierId = "Standard"
	Test           TierId = "Test"
)

// Defines values for QueryErrLogParamsTarget.
const (
	Message QueryErrLogParamsTarget = "message"
	Name    QueryErrLogParamsTarget = "name"
	Sink    QueryErrLogParamsTarget = "sink"
	Source  QueryErrLogParamsTarget = "source"
	Table   QueryErrLogParamsTarget = "table"
	Target  QueryErrLogParamsTarget = "target"
)

// Defines values for QueryErrLogParamsDirection.
const (
	Backward QueryErrLogParamsDirection = "backward"
	Forward  QueryErrLogParamsDirection = "forward"
)

// AvailableComponentType defines model for AvailableComponentType.
type AvailableComponentType struct {
	Cpu     string `json:"cpu"`
	Id      string `json:"id"`
	Maximum int    `json:"maximum"`
	Memory  string `json:"memory"`
}

// AvailableMetaStore defines model for AvailableMetaStore.
type AvailableMetaStore struct {
	AwsRds      *AvailableMetaStoreAwsRds      `json:"aws_rds,omitempty"`
	AzrPostgres *AvailableMetaStoreAzrPostgres `json:"azr_postgres,omitempty"`
	Etcd        *AvailableMetaStoreEtcd        `json:"etcd,omitempty"`
	GcpCloudsql *AvailableMetaStoreGcpCloudSql `json:"gcp_cloudsql,omitempty"`
	Postgresql  *AvailableMetaStorePostgreSql  `json:"postgresql,omitempty"`
	SharingPg   *AvailableMetaStoreSharingPg   `json:"sharing_pg,omitempty"`
}

// AvailableMetaStoreAwsRds defines model for AvailableMetaStoreAwsRds.
type AvailableMetaStoreAwsRds struct {
	Enabled *bool `json:"enabled,omitempty"`
}

// AvailableMetaStoreAzrPostgres defines model for AvailableMetaStoreAzrPostgres.
type AvailableMetaStoreAzrPostgres struct {
	Enabled *bool `json:"enabled,omitempty"`
}

// AvailableMetaStoreEtcd defines model for AvailableMetaStoreEtcd.
type AvailableMetaStoreEtcd struct {
	MaximumSizeGiB int                      `json:"maximumSizeGiB"`
	Nodes          []AvailableComponentType `json:"nodes"`
}

// AvailableMetaStoreGcpCloudSql defines model for AvailableMetaStoreGcpCloudSql.
type AvailableMetaStoreGcpCloudSql struct {
	Enabled *bool `json:"enabled,omitempty"`
}

// AvailableMetaStorePostgreSql defines model for AvailableMetaStorePostgreSql.
type AvailableMetaStorePostgreSql struct {
	MaximumSizeGiB int                      `json:"maximumSizeGiB"`
	Nodes          []AvailableComponentType `json:"nodes"`
}

// AvailableMetaStoreSharingPg defines model for AvailableMetaStoreSharingPg.
type AvailableMetaStoreSharingPg struct {
	Enabled *bool `json:"enabled,omitempty"`
}

// BackupSnapshotItem defines model for BackupSnapshotItem.
type BackupSnapshotItem struct {
	CreatedAtUnixMills int64              `json:"created_at_unix_mills"`
	Id                 openapi_types.UUID `json:"id"`
	MetaSnapshotId     *int64             `json:"meta_snapshot_id,omitempty"`
	RwVersion          *string            `json:"rw_version,omitempty"`
	Status             string             `json:"status"`
	Type               *string            `json:"type,omitempty"`
}

// BackupSnapshotsSizePage defines model for BackupSnapshotsSizePage.
type BackupSnapshotsSizePage struct {
	Items  []BackupSnapshotItem `json:"items"`
	Limit  uint64               `json:"limit"`
	Offset uint64               `json:"offset"`
	Size   uint64               `json:"size"`
}

// ClusterStatus defines model for ClusterStatus.
type ClusterStatus string

// ComponentResource defines model for ComponentResource.
type ComponentResource struct {
	ComponentTypeId string `json:"componentTypeId"`
	Cpu             string `json:"cpu"`
	Memory          string `json:"memory"`
	Replica         int    `json:"replica"`
}

// ComponentResourceRequest defines model for ComponentResourceRequest.
type ComponentResourceRequest struct {
	ComponentTypeId string `json:"componentTypeId"`
	Replica         int    `json:"replica"`
}

// CreateDBUserRequestBody defines model for CreateDBUserRequestBody.
type CreateDBUserRequestBody struct {
	Createdb   bool   `json:"createdb"`
	Createuser *bool  `json:"createuser,omitempty"`
	Password   string `json:"password"`
	Superuser  bool   `json:"superuser"`
	TenantId   uint64 `json:"tenantId"`
	Username   string `json:"username"`
}

// CreateTenantResponseBody defines model for CreateTenantResponseBody.
type CreateTenantResponseBody struct {
	TenantId   uint64 `json:"tenantId"`
	TenantName string `json:"tenantName"`
}

// DBUser defines model for DBUser.
type DBUser struct {
	Canlogin      bool   `json:"canlogin"`
	Usecreatedb   bool   `json:"usecreatedb"`
	Usecreateuser bool   `json:"usecreateuser"`
	Username      string `json:"username"`
	Usesuper      bool   `json:"usesuper"`
	Usesysid      uint64 `json:"usesysid"`
}

// DBUserArray defines model for DBUserArray.
type DBUserArray = []DBUser

// DBUsers defines model for DBUsers.
type DBUsers struct {
	Dbusers *DBUserArray `json:"dbusers,omitempty"`
}

// DropTenantRelationBody defines model for DropTenantRelationBody.
type DropTenantRelationBody struct {
	Database string `json:"database"`
	Password string `json:"password"`
	Relname  string `json:"relname"`
	Username string `json:"username"`
}

// Endpoint defines model for Endpoint.
type Endpoint struct {
	Database     string `json:"database"`
	Host         string `json:"host"`
	Id           int64  `json:"id"`
	InternalHost string `json:"internalHost"`
	InternalPort int    `json:"internalPort"`
	Options      string `json:"options"`
	Port         int    `json:"port"`
	TenantId     int64  `json:"tenantId"`
}

// ErrLogQueryResult defines model for ErrLogQueryResult.
type ErrLogQueryResult struct {
	Status string     `json:"status"`
	Values [][]string `json:"values"`
}

// GetDatabasesResponseBody defines model for GetDatabasesResponseBody.
type GetDatabasesResponseBody struct {
	Databases []string `json:"databases"`
}

// GetImageTagResponse defines model for GetImageTagResponse.
type GetImageTagResponse struct {
	ImageTag string `json:"imageTag"`
}

// KafkaConfig defines model for KafkaConfig.
type KafkaConfig struct {
	CaCertificate    *string                      `json:"caCertificate,omitempty"`
	SaslMechanisms   *KafkaConfigSaslMechanisms   `json:"saslMechanisms,omitempty"`
	SaslPassword     *string                      `json:"saslPassword,omitempty"`
	SaslUsername     *string                      `json:"saslUsername,omitempty"`
	SecurityProtocol *KafkaConfigSecurityProtocol `json:"securityProtocol,omitempty"`
	Server           string                       `json:"server"`
}

// KafkaConfigSaslMechanisms defines model for KafkaConfig.SaslMechanisms.
type KafkaConfigSaslMechanisms string

// KafkaConfigSecurityProtocol defines model for KafkaConfig.SecurityProtocol.
type KafkaConfigSecurityProtocol string

// LabelItem defines model for LabelItem.
type LabelItem struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

// ManagedCluster defines model for ManagedCluster.
type ManagedCluster struct {
	ClusterServiceAccount string             `json:"cluster_service_account"`
	Id                    uint64             `json:"id"`
	MasterUrl             string             `json:"master_url"`
	Name                  string             `json:"name"`
	Org                   openapi_types.UUID `json:"org"`
	ServingType           string             `json:"serving_type"`
	Settings              map[string]string  `json:"settings"`
	Status                ClusterStatus      `json:"status"`
	Token                 string             `json:"token"`
}

// ManagedClustersSizePage defines model for ManagedClustersSizePage.
type ManagedClustersSizePage struct {
	Clusters []ManagedCluster `json:"clusters"`
	Limit    uint64           `json:"limit"`
	Offset   uint64           `json:"offset"`
	Size     uint64           `json:"size"`
}

// MetaStoreAwsRds defines model for MetaStoreAwsRds.
type MetaStoreAwsRds struct {
	InstanceClass string `json:"instanceClass"`
	SizeGb        int    `json:"sizeGb"`
}

// MetaStoreAzrPostgres defines model for MetaStoreAzrPostgres.
type MetaStoreAzrPostgres struct {
	SizeGb int    `json:"sizeGb"`
	Sku    string `json:"sku"`
}

// MetaStoreEtcd defines model for MetaStoreEtcd.
type MetaStoreEtcd struct {
	Resource ComponentResource `json:"resource"`
	SizeGb   int               `json:"sizeGb"`
}

// MetaStoreGcpCloudSql defines model for MetaStoreGcpCloudSql.
type MetaStoreGcpCloudSql struct {
	SizeGb int    `json:"sizeGb"`
	Tier   string `json:"tier"`
}

// MetaStorePostgreSql defines model for MetaStorePostgreSql.
type MetaStorePostgreSql struct {
	Resource ComponentResource `json:"resource"`
	SizeGb   int               `json:"sizeGb"`
}

// MetaStoreSharingPg defines model for MetaStoreSharingPg.
type MetaStoreSharingPg struct {
	InstanceId string `json:"instanceId"`
}

// MetaStoreType defines model for MetaStoreType.
type MetaStoreType string

// MetricItem defines model for MetricItem.
type MetricItem struct {
	Labels []LabelItem   `json:"labels"`
	Values []MetricPoint `json:"values"`
}

// MetricPoint defines model for MetricPoint.
type MetricPoint struct {
	Timestamp time.Time `json:"timestamp"`
	Value     float64   `json:"value"`
}

// Metrics defines model for Metrics.
type Metrics struct {
	Items []MetricItem `json:"items"`
	Name  string       `json:"name"`
}

// Page defines model for Page.
type Page struct {
	Limit  uint64 `json:"limit"`
	Offset uint64 `json:"offset"`
}

// PostByocClusterUpdateRequestBody defines model for PostByocClusterUpdateRequestBody.
type PostByocClusterUpdateRequestBody struct {
	// CustomSettings base64 encoded custom settings
	CustomSettings *string `json:"customSettings,omitempty"`
	Version        *string `json:"version,omitempty"`
}

// PostByocClustersRequestBody defines model for PostByocClustersRequestBody.
type PostByocClustersRequestBody struct {
	Name     string            `json:"name"`
	Settings map[string]string `json:"settings"`
	Version  *string           `json:"version,omitempty"`
}

// PostPrivateLinkRequestBody defines model for PostPrivateLinkRequestBody.
type PostPrivateLinkRequestBody struct {
	ConnectionName string `json:"connectionName"`
	Target         string `json:"target"`
}

// PostPrivateLinkResponseBody defines model for PostPrivateLinkResponseBody.
type PostPrivateLinkResponseBody struct {
	ConnectionName string             `json:"connectionName"`
	Id             openapi_types.UUID `json:"id"`
}

// PostSnapshotResponseBody defines model for PostSnapshotResponseBody.
type PostSnapshotResponseBody struct {
	SnapshotId openapi_types.UUID `json:"snapshotId"`
	WorkflowId openapi_types.UUID `json:"workflowId"`
}

// PostSourceRequestBody defines model for PostSourceRequestBody.
type PostSourceRequestBody struct {
	Config PostSourceRequestBody_Config `json:"config"`
	Type   PostSourceRequestBodyType    `json:"type"`
}

// PostSourceRequestBody_Config defines model for PostSourceRequestBody.Config.
type PostSourceRequestBody_Config struct {
	union json.RawMessage
}

// PostSourceRequestBodyType defines model for PostSourceRequestBody.Type.
type PostSourceRequestBodyType string

// PostSourcesFetchKafkaTopicRequestBody defines model for PostSourcesFetchKafkaTopicRequestBody.
type PostSourcesFetchKafkaTopicRequestBody struct {
	KafkaConfig KafkaConfig `json:"kafka_config"`
}

// PostSourcesFetchKafkaTopicResponseBody defines model for PostSourcesFetchKafkaTopicResponseBody.
type PostSourcesFetchKafkaTopicResponseBody struct {
	Topics []string `json:"topics"`
}

// PostTenantResourcesRequestBody defines model for PostTenantResourcesRequestBody.
type PostTenantResourcesRequestBody struct {
	Compactor  *ComponentResourceRequest `json:"compactor,omitempty"`
	Compute    *ComponentResourceRequest `json:"compute,omitempty"`
	Frontend   *ComponentResourceRequest `json:"frontend,omitempty"`
	Meta       *ComponentResourceRequest `json:"meta,omitempty"`
	Standalone *ComponentResourceRequest `json:"standalone,omitempty"`
}

// PrivateLink defines model for PrivateLink.
type PrivateLink struct {
	ConnectionName  string                     `json:"connectionName"`
	ConnectionState PrivateLinkConnectionState `json:"connectionState"`
	Endpoint        *string                    `json:"endpoint,omitempty"`
	Id              openapi_types.UUID         `json:"id"`
	Status          PrivateLinkStatus          `json:"status"`
	Target          *string                    `json:"target,omitempty"`
	TenantId        int64                      `json:"tenantId"`
}

// PrivateLinkConnectionState defines model for PrivateLink.ConnectionState.
type PrivateLinkConnectionState string

// PrivateLinkStatus defines model for PrivateLink.Status.
type PrivateLinkStatus string

// PutByocClusterRequestBody defines model for PutByocClusterRequestBody.
type PutByocClusterRequestBody struct {
	Name     string            `json:"name"`
	Settings map[string]string `json:"settings"`
	Status   ClusterStatus     `json:"status"`
}

// RelationCounts defines model for RelationCounts.
type RelationCounts struct {
	MaterializedView uint32 `json:"materialized_view"`
	Sink             uint32 `json:"sink"`
	Source           uint32 `json:"source"`
	Table            uint32 `json:"table"`
}

// Size defines model for Size.
type Size struct {
	Size uint64 `json:"size"`
}

// SqlExecutionRequestBody defines model for SqlExecutionRequestBody.
type SqlExecutionRequestBody struct {
	Database string `json:"database"`
	Password string `json:"password"`
	Query    string `json:"query"`

	// Timeout timeout in ms
	Timeout  *int   `json:"timeout,omitempty"`
	Username string `json:"username"`
}

// Tenant defines model for Tenant.
type Tenant struct {
	ClusterName          *string            `json:"clusterName,omitempty"`
	CreatedAt            time.Time          `json:"createdAt"`
	EtcdConfig           string             `json:"etcd_config"`
	HealthStatus         TenantHealthStatus `json:"health_status"`
	Id                   uint64             `json:"id"`
	ImageTag             string             `json:"imageTag"`
	LatestImageTag       string             `json:"latestImageTag"`
	NsId                 openapi_types.UUID `json:"nsId"`
	OrgId                openapi_types.UUID `json:"orgId"`
	Region               string             `json:"region"`
	Resources            TenantResource     `json:"resources"`
	RwConfig             string             `json:"rw_config"`
	Status               TenantStatus       `json:"status"`
	TenantName           string             `json:"tenantName"`
	Tier                 TierId             `json:"tier"`
	UpcomingSnapshotTime *time.Time         `json:"upcomingSnapshotTime,omitempty"`
	UpdatedAt            time.Time          `json:"updatedAt"`
	UsageType            TenantUsageType    `json:"usageType"`
	UserId               uint64             `json:"userId"`
}

// TenantHealthStatus defines model for Tenant.HealthStatus.
type TenantHealthStatus string

// TenantStatus defines model for Tenant.Status.
type TenantStatus string

// TenantUsageType defines model for Tenant.UsageType.
type TenantUsageType string

// TenantArray defines model for TenantArray.
type TenantArray = []Tenant

// TenantClusterInfoResponseBody defines model for TenantClusterInfoResponseBody.
type TenantClusterInfoResponseBody struct {
	ClusterInfo string `json:"clusterInfo"`
	TenantId    uint64 `json:"tenantId"`
	TenantName  string `json:"tenantName"`
}

// TenantRequestRequestBody defines model for TenantRequestRequestBody.
type TenantRequestRequestBody struct {
	ClusterName *string                `json:"clusterName,omitempty"`
	ConfigId    *openapi_types.UUID    `json:"configId,omitempty"`
	EtcdConfig  *string                `json:"etcdConfig,omitempty"`
	ImageTag    *string                `json:"imageTag,omitempty"`
	Resources   *TenantResourceRequest `json:"resources,omitempty"`

	// RwConfig if config ID is not provided, use this config. currently used in tf plugin
	RwConfig   *string                            `json:"rwConfig,omitempty"`
	Sku        *string                            `json:"sku,omitempty"`
	TenantName string                             `json:"tenantName"`
	Tier       *TierId                            `json:"tier,omitempty"`
	UsageType  *TenantRequestRequestBodyUsageType `json:"usageType,omitempty"`
}

// TenantRequestRequestBodyUsageType defines model for TenantRequestRequestBody.UsageType.
type TenantRequestRequestBodyUsageType string

// TenantResource defines model for TenantResource.
type TenantResource struct {
	Components        TenantResourceComponents   `json:"components"`
	ComputeCache      TenantResourceComputeCache `json:"computeCache"`
	EtcdVolumeSizeGiB *int                       `json:"etcdVolumeSizeGiB,omitempty"`
	MetaStore         *TenantResourceMetaStore   `json:"metaStore,omitempty"`
	ResourceGroups    *TenantResourceGroupArray  `json:"resourceGroups,omitempty"`
}

// TenantResourceComponents defines model for TenantResourceComponents.
type TenantResourceComponents struct {
	Compactor  *ComponentResource `json:"compactor,omitempty"`
	Compute    *ComponentResource `json:"compute,omitempty"`
	Etcd       *ComponentResource `json:"etcd,omitempty"`
	Frontend   *ComponentResource `json:"frontend,omitempty"`
	Meta       *ComponentResource `json:"meta,omitempty"`
	Standalone *ComponentResource `json:"standalone,omitempty"`
}

// TenantResourceComputeCache defines model for TenantResourceComputeCache.
type TenantResourceComputeCache struct {
	SizeGb int `json:"sizeGb"`
}

// TenantResourceGroup defines model for TenantResourceGroup.
type TenantResourceGroup struct {
	ComputeCache TenantResourceComputeCache `json:"computeCache"`
	Name         string                     `json:"name"`
	Resource     ComponentResource          `json:"resource"`
}

// TenantResourceGroupArray defines model for TenantResourceGroupArray.
type TenantResourceGroupArray = []TenantResourceGroup

// TenantResourceMetaStore defines model for TenantResourceMetaStore.
type TenantResourceMetaStore struct {
	AwsRds      *MetaStoreAwsRds      `json:"aws_rds,omitempty"`
	AzrPostgres *MetaStoreAzrPostgres `json:"azr_postgres,omitempty"`
	Etcd        *MetaStoreEtcd        `json:"etcd,omitempty"`
	GcpCloudsql *MetaStoreGcpCloudSql `json:"gcp_cloudsql,omitempty"`
	Postgresql  *MetaStorePostgreSql  `json:"postgresql,omitempty"`
	Rwu         string                `json:"rwu"`
	SharingPg   *MetaStoreSharingPg   `json:"sharing_pg,omitempty"`
	Type        MetaStoreType         `json:"type"`
}

// TenantResourceRequest defines model for TenantResourceRequest.
type TenantResourceRequest struct {
	Components              TenantResourceRequestComponents `json:"components"`
	ComputeFileCacheSizeGiB int                             `json:"computeFileCacheSizeGiB"`
	EtcdVolumeSizeGiB       *int                            `json:"etcdVolumeSizeGiB,omitempty"`
	MetaStore               *TenantResourceRequestMetaStore `json:"metaStore,omitempty"`
}

// TenantResourceRequestComponents defines model for TenantResourceRequestComponents.
type TenantResourceRequestComponents struct {
	Compactor  *ComponentResourceRequest `json:"compactor,omitempty"`
	Compute    *ComponentResourceRequest `json:"compute,omitempty"`
	Etcd       *ComponentResourceRequest `json:"etcd,omitempty"`
	Frontend   *ComponentResourceRequest `json:"frontend,omitempty"`
	Meta       *ComponentResourceRequest `json:"meta,omitempty"`
	Standalone *ComponentResourceRequest `json:"standalone,omitempty"`
}

// TenantResourceRequestMetaStore defines model for TenantResourceRequestMetaStore.
type TenantResourceRequestMetaStore struct {
	AwsRds     *TenantResourceRequestMetaStoreAwsRds     `json:"aws_rds,omitempty"`
	Etcd       *TenantResourceRequestMetaStoreEtcd       `json:"etcd,omitempty"`
	Postgresql *TenantResourceRequestMetaStorePostgreSql `json:"postgresql,omitempty"`
	Type       MetaStoreType                             `json:"type"`
}

// TenantResourceRequestMetaStoreAwsRds defines model for TenantResourceRequestMetaStoreAwsRds.
type TenantResourceRequestMetaStoreAwsRds struct {
	InstanceClass string `json:"instanceClass"`
	SizeGb        int    `json:"sizeGb"`
}

// TenantResourceRequestMetaStoreEtcd defines model for TenantResourceRequestMetaStoreEtcd.
type TenantResourceRequestMetaStoreEtcd struct {
	ComponentTypeId string `json:"componentTypeId"`
	Replica         int    `json:"replica"`
	SizeGb          int    `json:"sizeGb"`
}

// TenantResourceRequestMetaStorePostgreSql defines model for TenantResourceRequestMetaStorePostgreSql.
type TenantResourceRequestMetaStorePostgreSql struct {
	ComponentTypeId string `json:"componentTypeId"`
	Replica         int    `json:"replica"`
	SizeGb          int    `json:"sizeGb"`
}

// TenantSizePage defines model for TenantSizePage.
type TenantSizePage struct {
	Limit   uint64      `json:"limit"`
	Offset  uint64      `json:"offset"`
	Size    uint64      `json:"size"`
	Tenants TenantArray `json:"tenants"`
}

// TenantStatusCount defines model for TenantStatusCount.
type TenantStatusCount struct {
	Count  int64  `json:"count"`
	Status string `json:"status"`
}

// TenantStatusCountArray defines model for TenantStatusCountArray.
type TenantStatusCountArray = []TenantStatusCount

// TenantStatusCounts defines model for TenantStatusCounts.
type TenantStatusCounts struct {
	Status TenantStatusCountArray `json:"status"`
}

// Tier defines model for Tier.
type Tier struct {
	AvailableCompactorNodes            []AvailableComponentType `json:"availableCompactorNodes"`
	AvailableComputeNodes              []AvailableComponentType `json:"availableComputeNodes"`
	AvailableFrontendNodes             []AvailableComponentType `json:"availableFrontendNodes"`
	AvailableMetaNodes                 []AvailableComponentType `json:"availableMetaNodes"`
	AvailableMetaStore                 *AvailableMetaStore      `json:"availableMetaStore,omitempty"`
	AvailableStandaloneNodes           []AvailableComponentType `json:"availableStandaloneNodes"`
	Id                                 *TierId                  `json:"id,omitempty"`
	MaximumComputeNodeFileCacheSizeGiB int                      `json:"maximumComputeNodeFileCacheSizeGiB"`
	RetentionPeriod                    int                      `json:"retentionPeriod"`
	ValidityPeriod                     int                      `json:"validityPeriod"`
}

// TierArray defines model for TierArray.
type TierArray = []Tier

// TierId defines model for TierId.
type TierId string

// Tiers defines model for Tiers.
type Tiers struct {
	Tiers TierArray `json:"tiers"`
}

// UpdateDBUserRequestBody defines model for UpdateDBUserRequestBody.
type UpdateDBUserRequestBody struct {
	Password string `json:"password"`
	TenantId uint64 `json:"tenantId"`
	Username string `json:"username"`
}

// WorkflowIdResponseBody defines model for WorkflowIdResponseBody.
type WorkflowIdResponseBody struct {
	WorkflowId openapi_types.UUID `json:"workflowId"`
}

// AvailabilityResponse defines model for AvailabilityResponse.
type AvailabilityResponse struct {
	Available bool   `json:"available"`
	Msg       string `json:"msg"`
}

// BadRequestResponse defines model for BadRequestResponse.
type BadRequestResponse struct {
	Msg string `json:"msg"`
}

// DefaultResponse defines model for DefaultResponse.
type DefaultResponse struct {
	Msg string `json:"msg"`
}

// FailedPreconditionResponse defines model for FailedPreconditionResponse.
type FailedPreconditionResponse struct {
	Msg string `json:"msg"`
}

// NotFoundResponse defines model for NotFoundResponse.
type NotFoundResponse struct {
	Msg string `json:"msg"`
}

// RangeNotSatisfiable defines model for RangeNotSatisfiable.
type RangeNotSatisfiable struct {
	Msg string `json:"msg"`
}

// ServiceUnavailableResponse defines model for ServiceUnavailableResponse.
type ServiceUnavailableResponse struct {
	Msg string `json:"msg"`
}

// GetAvailabilityParams defines parameters for GetAvailability.
type GetAvailabilityParams struct {
	TenantName string `form:"tenantName" json:"tenantName"`
}

// GetByocClustersParams defines parameters for GetByocClusters.
type GetByocClustersParams struct {
	Offset *uint64 `form:"offset,omitempty" json:"offset,omitempty"`
	Limit  *uint64 `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetEndpointsParams defines parameters for GetEndpoints.
type GetEndpointsParams struct {
	TenantName *string `form:"tenantName,omitempty" json:"tenantName,omitempty"`
	TenantId   *uint64 `form:"tenantId,omitempty" json:"tenantId,omitempty"`
}

// QueryErrLogParams defines parameters for QueryErrLog.
type QueryErrLogParams struct {
	TenantId  uint64                      `form:"tenantId" json:"tenantId"`
	Target    QueryErrLogParamsTarget     `form:"target" json:"target"`
	TargetId  string                      `form:"targetId" json:"targetId"`
	Start     *time.Time                  `form:"start,omitempty" json:"start,omitempty"`
	End       *time.Time                  `form:"end,omitempty" json:"end,omitempty"`
	Direction *QueryErrLogParamsDirection `form:"direction,omitempty" json:"direction,omitempty"`
	Limit     *uint64                     `form:"limit,omitempty" json:"limit,omitempty"`
}

// QueryErrLogParamsTarget defines parameters for QueryErrLog.
type QueryErrLogParamsTarget string

// QueryErrLogParamsDirection defines parameters for QueryErrLog.
type QueryErrLogParamsDirection string

// GetMetricsParams defines parameters for GetMetrics.
type GetMetricsParams struct {
	Metric    string     `form:"metric" json:"metric"`
	TenantId  uint64     `form:"tenantId" json:"tenantId"`
	Component *string    `form:"component,omitempty" json:"component,omitempty"`
	StartTime *time.Time `form:"startTime,omitempty" json:"startTime,omitempty"`
	EndTime   *time.Time `form:"endTime,omitempty" json:"endTime,omitempty"`
	Period    *uint64    `form:"period,omitempty" json:"period,omitempty"`
}

// DeleteTenantParams defines parameters for DeleteTenant.
type DeleteTenantParams struct {
	TenantId   *uint64 `form:"tenantId,omitempty" json:"tenantId,omitempty"`
	TenantName *string `form:"tenantName,omitempty" json:"tenantName,omitempty"`
}

// GetTenantParams defines parameters for GetTenant.
type GetTenantParams struct {
	TenantId   *uint64 `form:"tenantId,omitempty" json:"tenantId,omitempty"`
	TenantName *string `form:"tenantName,omitempty" json:"tenantName,omitempty"`
}

// DeleteTenantDbusersParams defines parameters for DeleteTenantDbusers.
type DeleteTenantDbusersParams struct {
	TenantId uint64 `form:"tenantId" json:"tenantId"`
	Username string `form:"username" json:"username"`
}

// GetTenantDbusersParams defines parameters for GetTenantDbusers.
type GetTenantDbusersParams struct {
	TenantId uint64 `form:"tenantId" json:"tenantId"`
}

// GetTenantTenantIdBackupParams defines parameters for GetTenantTenantIdBackup.
type GetTenantTenantIdBackupParams struct {
	Offset *uint64 `form:"offset,omitempty" json:"offset,omitempty"`
	Limit  *uint64 `form:"limit,omitempty" json:"limit,omitempty"`
}

// PutTenantTenantIdConfigEtcdTextBody defines parameters for PutTenantTenantIdConfigEtcd.
type PutTenantTenantIdConfigEtcdTextBody = string

// PutTenantTenantIdConfigRisingwaveTextBody defines parameters for PutTenantTenantIdConfigRisingwave.
type PutTenantTenantIdConfigRisingwaveTextBody = string

// PostTenantTenantIdExtensionsServerlessbackfillVersionTextBody defines parameters for PostTenantTenantIdExtensionsServerlessbackfillVersion.
type PostTenantTenantIdExtensionsServerlessbackfillVersionTextBody = string

// PostTenantTenantIdUpdateVersionJSONBody defines parameters for PostTenantTenantIdUpdateVersion.
type PostTenantTenantIdUpdateVersionJSONBody struct {
	Version *string `json:"version,omitempty"`
}

// GetTenantsParams defines parameters for GetTenants.
type GetTenantsParams struct {
	Offset *uint64 `form:"offset,omitempty" json:"offset,omitempty"`
	Limit  *uint64 `form:"limit,omitempty" json:"limit,omitempty"`
}

// PutByocClusterNameJSONRequestBody defines body for PutByocClusterName for application/json ContentType.
type PutByocClusterNameJSONRequestBody = PutByocClusterRequestBody

// PostByocClusterNameManualUpdateJSONRequestBody defines body for PostByocClusterNameManualUpdate for application/json ContentType.
type PostByocClusterNameManualUpdateJSONRequestBody = PostByocClusterUpdateRequestBody

// PostByocClusterNameUpdateJSONRequestBody defines body for PostByocClusterNameUpdate for application/json ContentType.
type PostByocClusterNameUpdateJSONRequestBody = PostByocClusterUpdateRequestBody

// PostByocClustersJSONRequestBody defines body for PostByocClusters for application/json ContentType.
type PostByocClustersJSONRequestBody = PostByocClustersRequestBody

// PostSourcePingJSONRequestBody defines body for PostSourcePing for application/json ContentType.
type PostSourcePingJSONRequestBody = PostSourceRequestBody

// PostTenantDbusersJSONRequestBody defines body for PostTenantDbusers for application/json ContentType.
type PostTenantDbusersJSONRequestBody = CreateDBUserRequestBody

// PutTenantDbusersJSONRequestBody defines body for PutTenantDbusers for application/json ContentType.
type PutTenantDbusersJSONRequestBody = UpdateDBUserRequestBody

// PutTenantTenantIdConfigEtcdTextRequestBody defines body for PutTenantTenantIdConfigEtcd for text/plain ContentType.
type PutTenantTenantIdConfigEtcdTextRequestBody = PutTenantTenantIdConfigEtcdTextBody

// PutTenantTenantIdConfigRisingwaveTextRequestBody defines body for PutTenantTenantIdConfigRisingwave for text/plain ContentType.
type PutTenantTenantIdConfigRisingwaveTextRequestBody = PutTenantTenantIdConfigRisingwaveTextBody

// PostTenantTenantIdExecutionJSONRequestBody defines body for PostTenantTenantIdExecution for application/json ContentType.
type PostTenantTenantIdExecutionJSONRequestBody = SqlExecutionRequestBody

// PostTenantTenantIdExtensionsServerlessbackfillVersionTextRequestBody defines body for PostTenantTenantIdExtensionsServerlessbackfillVersion for text/plain ContentType.
type PostTenantTenantIdExtensionsServerlessbackfillVersionTextRequestBody = PostTenantTenantIdExtensionsServerlessbackfillVersionTextBody

// PostTenantTenantIdPrivatelinksJSONRequestBody defines body for PostTenantTenantIdPrivatelinks for application/json ContentType.
type PostTenantTenantIdPrivatelinksJSONRequestBody = PostPrivateLinkRequestBody

// DeleteTenantTenantIdRelationJSONRequestBody defines body for DeleteTenantTenantIdRelation for application/json ContentType.
type DeleteTenantTenantIdRelationJSONRequestBody = DropTenantRelationBody

// PostTenantTenantIdResourceJSONRequestBody defines body for PostTenantTenantIdResource for application/json ContentType.
type PostTenantTenantIdResourceJSONRequestBody = PostTenantResourcesRequestBody

// PostTenantTenantIdSourcesFetchKafkaTopicJSONRequestBody defines body for PostTenantTenantIdSourcesFetchKafkaTopic for application/json ContentType.
type PostTenantTenantIdSourcesFetchKafkaTopicJSONRequestBody = PostSourcesFetchKafkaTopicRequestBody

// PostTenantTenantIdSourcesPingJSONRequestBody defines body for PostTenantTenantIdSourcesPing for application/json ContentType.
type PostTenantTenantIdSourcesPingJSONRequestBody = PostSourceRequestBody

// PostTenantTenantIdUpdateVersionJSONRequestBody defines body for PostTenantTenantIdUpdateVersion for application/json ContentType.
type PostTenantTenantIdUpdateVersionJSONRequestBody PostTenantTenantIdUpdateVersionJSONBody

// PostTenantsJSONRequestBody defines body for PostTenants for application/json ContentType.
type PostTenantsJSONRequestBody = TenantRequestRequestBody

// AsKafkaConfig returns the union data inside the PostSourceRequestBody_Config as a KafkaConfig
func (t PostSourceRequestBody_Config) AsKafkaConfig() (KafkaConfig, error) {
	var body KafkaConfig
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromKafkaConfig overwrites any union data inside the PostSourceRequestBody_Config as the provided KafkaConfig
func (t *PostSourceRequestBody_Config) FromKafkaConfig(v KafkaConfig) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeKafkaConfig performs a merge with any union data inside the PostSourceRequestBody_Config, using the provided KafkaConfig
func (t *PostSourceRequestBody_Config) MergeKafkaConfig(v KafkaConfig) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

func (t PostSourceRequestBody_Config) MarshalJSON() ([]byte, error) {
	b, err := t.union.MarshalJSON()
	return b, err
}

func (t *PostSourceRequestBody_Config) UnmarshalJSON(b []byte) error {
	err := t.union.UnmarshalJSON(b)
	return err
}

// RequestEditorFn  is the function signature for the RequestEditor callback function
type RequestEditorFn func(ctx context.Context, req *http.Request) error

// Doer performs HTTP requests.
//
// The standard http.Client implements this interface.
type HttpRequestDoer interface {
	Do(req *http.Request) (*http.Response, error)
}

// Client which conforms to the OpenAPI3 specification for this service.
type Client struct {
	// The endpoint of the server conforming to this interface, with scheme,
	// https://api.deepmap.com for example. This can contain a path relative
	// to the server, such as https://api.deepmap.com/dev-test, and all the
	// paths in the swagger spec will be appended to the server.
	Server string

	// Doer for performing requests, typically a *http.Client with any
	// customized settings, such as certificate chains.
	Client HttpRequestDoer

	// A list of callbacks for modifying requests which are generated before sending over
	// the network.
	RequestEditors []RequestEditorFn
}

// ClientOption allows setting custom parameters during construction
type ClientOption func(*Client) error

// Creates a new Client, with reasonable defaults
func NewClient(server string, opts ...ClientOption) (*Client, error) {
	// create a client with sane default values
	client := Client{
		Server: server,
	}
	// mutate client and add all optional params
	for _, o := range opts {
		if err := o(&client); err != nil {
			return nil, err
		}
	}
	// ensure the server URL always has a trailing slash
	if !strings.HasSuffix(client.Server, "/") {
		client.Server += "/"
	}
	// create httpClient, if not already present
	if client.Client == nil {
		client.Client = &http.Client{}
	}
	return &client, nil
}

// WithHTTPClient allows overriding the default Doer, which is
// automatically created using http.Client. This is useful for tests.
func WithHTTPClient(doer HttpRequestDoer) ClientOption {
	return func(c *Client) error {
		c.Client = doer
		return nil
	}
}

// WithRequestEditorFn allows setting up a callback function, which will be
// called right before sending the request. This can be used to mutate the request.
func WithRequestEditorFn(fn RequestEditorFn) ClientOption {
	return func(c *Client) error {
		c.RequestEditors = append(c.RequestEditors, fn)
		return nil
	}
}

// The interface specification for the client above.
type ClientInterface interface {
	// Get request
	Get(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetAvailability request
	GetAvailability(ctx context.Context, params *GetAvailabilityParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteByocClusterName request
	DeleteByocClusterName(ctx context.Context, name string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetByocClusterName request
	GetByocClusterName(ctx context.Context, name string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutByocClusterNameWithBody request with any body
	PutByocClusterNameWithBody(ctx context.Context, name string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PutByocClusterName(ctx context.Context, name string, body PutByocClusterNameJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostByocClusterNameManualUpdateWithBody request with any body
	PostByocClusterNameManualUpdateWithBody(ctx context.Context, name string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostByocClusterNameManualUpdate(ctx context.Context, name string, body PostByocClusterNameManualUpdateJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostByocClusterNameUpdateWithBody request with any body
	PostByocClusterNameUpdateWithBody(ctx context.Context, name string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostByocClusterNameUpdate(ctx context.Context, name string, body PostByocClusterNameUpdateJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetByocClusters request
	GetByocClusters(ctx context.Context, params *GetByocClustersParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostByocClustersWithBody request with any body
	PostByocClustersWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostByocClusters(ctx context.Context, body PostByocClustersJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetEndpoints request
	GetEndpoints(ctx context.Context, params *GetEndpointsParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// QueryErrLog request
	QueryErrLog(ctx context.Context, params *QueryErrLogParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetMetrics request
	GetMetrics(ctx context.Context, params *GetMetricsParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetRootca request
	GetRootca(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostSourcePingWithBody request with any body
	PostSourcePingWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostSourcePing(ctx context.Context, body PostSourcePingJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteTenant request
	DeleteTenant(ctx context.Context, params *DeleteTenantParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetTenant request
	GetTenant(ctx context.Context, params *GetTenantParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteTenantDbusers request
	DeleteTenantDbusers(ctx context.Context, params *DeleteTenantDbusersParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetTenantDbusers request
	GetTenantDbusers(ctx context.Context, params *GetTenantDbusersParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantDbusersWithBody request with any body
	PostTenantDbusersWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostTenantDbusers(ctx context.Context, body PostTenantDbusersJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutTenantDbusersWithBody request with any body
	PutTenantDbusersWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PutTenantDbusers(ctx context.Context, body PutTenantDbusersJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetTenantTags request
	GetTenantTags(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetTenantTenantIdBackup request
	GetTenantTenantIdBackup(ctx context.Context, tenantId uint64, params *GetTenantTenantIdBackupParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdBackup request
	PostTenantTenantIdBackup(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteTenantTenantIdBackupSnapshotId request
	DeleteTenantTenantIdBackupSnapshotId(ctx context.Context, tenantId uint64, snapshotId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdBackupSnapshotIdRestore request
	PostTenantTenantIdBackupSnapshotIdRestore(ctx context.Context, tenantId uint64, snapshotId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutTenantTenantIdConfigEtcdWithBody request with any body
	PutTenantTenantIdConfigEtcdWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PutTenantTenantIdConfigEtcdWithTextBody(ctx context.Context, tenantId uint64, body PutTenantTenantIdConfigEtcdTextRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutTenantTenantIdConfigRisingwaveWithBody request with any body
	PutTenantTenantIdConfigRisingwaveWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PutTenantTenantIdConfigRisingwaveWithTextBody(ctx context.Context, tenantId uint64, body PutTenantTenantIdConfigRisingwaveTextRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetTenantTenantIdDatabases request
	GetTenantTenantIdDatabases(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdExecutionWithBody request with any body
	PostTenantTenantIdExecutionWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostTenantTenantIdExecution(ctx context.Context, tenantId uint64, body PostTenantTenantIdExecutionJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdExtensionsServerlessbackfillDisable request
	PostTenantTenantIdExtensionsServerlessbackfillDisable(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdExtensionsServerlessbackfillEnable request
	PostTenantTenantIdExtensionsServerlessbackfillEnable(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdExtensionsServerlessbackfillVersionWithBody request with any body
	PostTenantTenantIdExtensionsServerlessbackfillVersionWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostTenantTenantIdExtensionsServerlessbackfillVersionWithTextBody(ctx context.Context, tenantId uint64, body PostTenantTenantIdExtensionsServerlessbackfillVersionTextRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetTenantTenantIdInfo request
	GetTenantTenantIdInfo(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetTenantTenantIdMetrics request
	GetTenantTenantIdMetrics(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteTenantTenantIdPrivatelinkPrivateLinkId request
	DeleteTenantTenantIdPrivatelinkPrivateLinkId(ctx context.Context, tenantId uint64, privateLinkId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetTenantTenantIdPrivatelinkPrivateLinkId request
	GetTenantTenantIdPrivatelinkPrivateLinkId(ctx context.Context, tenantId uint64, privateLinkId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdPrivatelinksWithBody request with any body
	PostTenantTenantIdPrivatelinksWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostTenantTenantIdPrivatelinks(ctx context.Context, tenantId uint64, body PostTenantTenantIdPrivatelinksJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteTenantTenantIdRelationWithBody request with any body
	DeleteTenantTenantIdRelationWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	DeleteTenantTenantIdRelation(ctx context.Context, tenantId uint64, body DeleteTenantTenantIdRelationJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetTenantTenantIdRelationsGetCounts request
	GetTenantTenantIdRelationsGetCounts(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdResourceWithBody request with any body
	PostTenantTenantIdResourceWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostTenantTenantIdResource(ctx context.Context, tenantId uint64, body PostTenantTenantIdResourceJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdSourcesFetchKafkaTopicWithBody request with any body
	PostTenantTenantIdSourcesFetchKafkaTopicWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostTenantTenantIdSourcesFetchKafkaTopic(ctx context.Context, tenantId uint64, body PostTenantTenantIdSourcesFetchKafkaTopicJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdSourcesPingWithBody request with any body
	PostTenantTenantIdSourcesPingWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostTenantTenantIdSourcesPing(ctx context.Context, tenantId uint64, body PostTenantTenantIdSourcesPingJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutTenantTenantIdStart request
	PutTenantTenantIdStart(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutTenantTenantIdStop request
	PutTenantTenantIdStop(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutTenantTenantIdUpdateCfgConfigId request
	PutTenantTenantIdUpdateCfgConfigId(ctx context.Context, tenantId uint64, configId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdUpdateVersionWithBody request with any body
	PostTenantTenantIdUpdateVersionWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostTenantTenantIdUpdateVersion(ctx context.Context, tenantId uint64, body PostTenantTenantIdUpdateVersionJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetTenants request
	GetTenants(ctx context.Context, params *GetTenantsParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantsWithBody request with any body
	PostTenantsWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostTenants(ctx context.Context, body PostTenantsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetTenantsStatus request
	GetTenantsStatus(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantsTenantIdUnquiesce request
	PostTenantsTenantIdUnquiesce(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetTiers request
	GetTiers(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetVersion request
	GetVersion(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)
}

func (c *Client) Get(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetAvailability(ctx context.Context, params *GetAvailabilityParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetAvailabilityRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteByocClusterName(ctx context.Context, name string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteByocClusterNameRequest(c.Server, name)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetByocClusterName(ctx context.Context, name string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetByocClusterNameRequest(c.Server, name)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutByocClusterNameWithBody(ctx context.Context, name string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutByocClusterNameRequestWithBody(c.Server, name, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutByocClusterName(ctx context.Context, name string, body PutByocClusterNameJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutByocClusterNameRequest(c.Server, name, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostByocClusterNameManualUpdateWithBody(ctx context.Context, name string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostByocClusterNameManualUpdateRequestWithBody(c.Server, name, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostByocClusterNameManualUpdate(ctx context.Context, name string, body PostByocClusterNameManualUpdateJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostByocClusterNameManualUpdateRequest(c.Server, name, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostByocClusterNameUpdateWithBody(ctx context.Context, name string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostByocClusterNameUpdateRequestWithBody(c.Server, name, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostByocClusterNameUpdate(ctx context.Context, name string, body PostByocClusterNameUpdateJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostByocClusterNameUpdateRequest(c.Server, name, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetByocClusters(ctx context.Context, params *GetByocClustersParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetByocClustersRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostByocClustersWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostByocClustersRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostByocClusters(ctx context.Context, body PostByocClustersJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostByocClustersRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetEndpoints(ctx context.Context, params *GetEndpointsParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetEndpointsRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) QueryErrLog(ctx context.Context, params *QueryErrLogParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewQueryErrLogRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetMetrics(ctx context.Context, params *GetMetricsParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetMetricsRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetRootca(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetRootcaRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostSourcePingWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostSourcePingRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostSourcePing(ctx context.Context, body PostSourcePingJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostSourcePingRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteTenant(ctx context.Context, params *DeleteTenantParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteTenantRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetTenant(ctx context.Context, params *GetTenantParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetTenantRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteTenantDbusers(ctx context.Context, params *DeleteTenantDbusersParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteTenantDbusersRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetTenantDbusers(ctx context.Context, params *GetTenantDbusersParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetTenantDbusersRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantDbusersWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantDbusersRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantDbusers(ctx context.Context, body PostTenantDbusersJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantDbusersRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutTenantDbusersWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutTenantDbusersRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutTenantDbusers(ctx context.Context, body PutTenantDbusersJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutTenantDbusersRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetTenantTags(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetTenantTagsRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetTenantTenantIdBackup(ctx context.Context, tenantId uint64, params *GetTenantTenantIdBackupParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetTenantTenantIdBackupRequest(c.Server, tenantId, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdBackup(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdBackupRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteTenantTenantIdBackupSnapshotId(ctx context.Context, tenantId uint64, snapshotId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteTenantTenantIdBackupSnapshotIdRequest(c.Server, tenantId, snapshotId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdBackupSnapshotIdRestore(ctx context.Context, tenantId uint64, snapshotId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdBackupSnapshotIdRestoreRequest(c.Server, tenantId, snapshotId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutTenantTenantIdConfigEtcdWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutTenantTenantIdConfigEtcdRequestWithBody(c.Server, tenantId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutTenantTenantIdConfigEtcdWithTextBody(ctx context.Context, tenantId uint64, body PutTenantTenantIdConfigEtcdTextRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutTenantTenantIdConfigEtcdRequestWithTextBody(c.Server, tenantId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutTenantTenantIdConfigRisingwaveWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutTenantTenantIdConfigRisingwaveRequestWithBody(c.Server, tenantId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutTenantTenantIdConfigRisingwaveWithTextBody(ctx context.Context, tenantId uint64, body PutTenantTenantIdConfigRisingwaveTextRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutTenantTenantIdConfigRisingwaveRequestWithTextBody(c.Server, tenantId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetTenantTenantIdDatabases(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetTenantTenantIdDatabasesRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdExecutionWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdExecutionRequestWithBody(c.Server, tenantId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdExecution(ctx context.Context, tenantId uint64, body PostTenantTenantIdExecutionJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdExecutionRequest(c.Server, tenantId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdExtensionsServerlessbackfillDisable(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdExtensionsServerlessbackfillDisableRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdExtensionsServerlessbackfillEnable(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdExtensionsServerlessbackfillEnableRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdExtensionsServerlessbackfillVersionWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdExtensionsServerlessbackfillVersionRequestWithBody(c.Server, tenantId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdExtensionsServerlessbackfillVersionWithTextBody(ctx context.Context, tenantId uint64, body PostTenantTenantIdExtensionsServerlessbackfillVersionTextRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdExtensionsServerlessbackfillVersionRequestWithTextBody(c.Server, tenantId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetTenantTenantIdInfo(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetTenantTenantIdInfoRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetTenantTenantIdMetrics(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetTenantTenantIdMetricsRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteTenantTenantIdPrivatelinkPrivateLinkId(ctx context.Context, tenantId uint64, privateLinkId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteTenantTenantIdPrivatelinkPrivateLinkIdRequest(c.Server, tenantId, privateLinkId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetTenantTenantIdPrivatelinkPrivateLinkId(ctx context.Context, tenantId uint64, privateLinkId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetTenantTenantIdPrivatelinkPrivateLinkIdRequest(c.Server, tenantId, privateLinkId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdPrivatelinksWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdPrivatelinksRequestWithBody(c.Server, tenantId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdPrivatelinks(ctx context.Context, tenantId uint64, body PostTenantTenantIdPrivatelinksJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdPrivatelinksRequest(c.Server, tenantId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteTenantTenantIdRelationWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteTenantTenantIdRelationRequestWithBody(c.Server, tenantId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteTenantTenantIdRelation(ctx context.Context, tenantId uint64, body DeleteTenantTenantIdRelationJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteTenantTenantIdRelationRequest(c.Server, tenantId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetTenantTenantIdRelationsGetCounts(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetTenantTenantIdRelationsGetCountsRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdResourceWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdResourceRequestWithBody(c.Server, tenantId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdResource(ctx context.Context, tenantId uint64, body PostTenantTenantIdResourceJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdResourceRequest(c.Server, tenantId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdSourcesFetchKafkaTopicWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdSourcesFetchKafkaTopicRequestWithBody(c.Server, tenantId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdSourcesFetchKafkaTopic(ctx context.Context, tenantId uint64, body PostTenantTenantIdSourcesFetchKafkaTopicJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdSourcesFetchKafkaTopicRequest(c.Server, tenantId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdSourcesPingWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdSourcesPingRequestWithBody(c.Server, tenantId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdSourcesPing(ctx context.Context, tenantId uint64, body PostTenantTenantIdSourcesPingJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdSourcesPingRequest(c.Server, tenantId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutTenantTenantIdStart(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutTenantTenantIdStartRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutTenantTenantIdStop(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutTenantTenantIdStopRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutTenantTenantIdUpdateCfgConfigId(ctx context.Context, tenantId uint64, configId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutTenantTenantIdUpdateCfgConfigIdRequest(c.Server, tenantId, configId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdUpdateVersionWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdUpdateVersionRequestWithBody(c.Server, tenantId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdUpdateVersion(ctx context.Context, tenantId uint64, body PostTenantTenantIdUpdateVersionJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdUpdateVersionRequest(c.Server, tenantId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetTenants(ctx context.Context, params *GetTenantsParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetTenantsRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantsWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantsRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenants(ctx context.Context, body PostTenantsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantsRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetTenantsStatus(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetTenantsStatusRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantsTenantIdUnquiesce(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantsTenantIdUnquiesceRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetTiers(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetTiersRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetVersion(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetVersionRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

// NewGetRequest generates requests for Get
func NewGetRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetAvailabilityRequest generates requests for GetAvailability
func NewGetAvailabilityRequest(server string, params *GetAvailabilityParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/availability")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "tenantName", runtime.ParamLocationQuery, params.TenantName); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewDeleteByocClusterNameRequest generates requests for DeleteByocClusterName
func NewDeleteByocClusterNameRequest(server string, name string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "name", runtime.ParamLocationPath, name)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/byoc-cluster/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetByocClusterNameRequest generates requests for GetByocClusterName
func NewGetByocClusterNameRequest(server string, name string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "name", runtime.ParamLocationPath, name)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/byoc-cluster/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPutByocClusterNameRequest calls the generic PutByocClusterName builder with application/json body
func NewPutByocClusterNameRequest(server string, name string, body PutByocClusterNameJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPutByocClusterNameRequestWithBody(server, name, "application/json", bodyReader)
}

// NewPutByocClusterNameRequestWithBody generates requests for PutByocClusterName with any type of body
func NewPutByocClusterNameRequestWithBody(server string, name string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "name", runtime.ParamLocationPath, name)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/byoc-cluster/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostByocClusterNameManualUpdateRequest calls the generic PostByocClusterNameManualUpdate builder with application/json body
func NewPostByocClusterNameManualUpdateRequest(server string, name string, body PostByocClusterNameManualUpdateJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostByocClusterNameManualUpdateRequestWithBody(server, name, "application/json", bodyReader)
}

// NewPostByocClusterNameManualUpdateRequestWithBody generates requests for PostByocClusterNameManualUpdate with any type of body
func NewPostByocClusterNameManualUpdateRequestWithBody(server string, name string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "name", runtime.ParamLocationPath, name)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/byoc-cluster/%s/manualUpdate", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostByocClusterNameUpdateRequest calls the generic PostByocClusterNameUpdate builder with application/json body
func NewPostByocClusterNameUpdateRequest(server string, name string, body PostByocClusterNameUpdateJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostByocClusterNameUpdateRequestWithBody(server, name, "application/json", bodyReader)
}

// NewPostByocClusterNameUpdateRequestWithBody generates requests for PostByocClusterNameUpdate with any type of body
func NewPostByocClusterNameUpdateRequestWithBody(server string, name string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "name", runtime.ParamLocationPath, name)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/byoc-cluster/%s/update", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetByocClustersRequest generates requests for GetByocClusters
func NewGetByocClustersRequest(server string, params *GetByocClustersParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/byoc-clusters")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Offset != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "offset", runtime.ParamLocationQuery, *params.Offset); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Limit != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "limit", runtime.ParamLocationQuery, *params.Limit); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostByocClustersRequest calls the generic PostByocClusters builder with application/json body
func NewPostByocClustersRequest(server string, body PostByocClustersJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostByocClustersRequestWithBody(server, "application/json", bodyReader)
}

// NewPostByocClustersRequestWithBody generates requests for PostByocClusters with any type of body
func NewPostByocClustersRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/byoc-clusters")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetEndpointsRequest generates requests for GetEndpoints
func NewGetEndpointsRequest(server string, params *GetEndpointsParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/endpoints")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.TenantName != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "tenantName", runtime.ParamLocationQuery, *params.TenantName); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.TenantId != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "tenantId", runtime.ParamLocationQuery, *params.TenantId); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewQueryErrLogRequest generates requests for QueryErrLog
func NewQueryErrLogRequest(server string, params *QueryErrLogParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/log/queryError")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "tenantId", runtime.ParamLocationQuery, params.TenantId); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "target", runtime.ParamLocationQuery, params.Target); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "targetId", runtime.ParamLocationQuery, params.TargetId); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		if params.Start != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "start", runtime.ParamLocationQuery, *params.Start); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.End != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "end", runtime.ParamLocationQuery, *params.End); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Direction != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "direction", runtime.ParamLocationQuery, *params.Direction); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Limit != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "limit", runtime.ParamLocationQuery, *params.Limit); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetMetricsRequest generates requests for GetMetrics
func NewGetMetricsRequest(server string, params *GetMetricsParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/metrics")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "metric", runtime.ParamLocationQuery, params.Metric); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "tenantId", runtime.ParamLocationQuery, params.TenantId); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		if params.Component != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "component", runtime.ParamLocationQuery, *params.Component); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.StartTime != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "startTime", runtime.ParamLocationQuery, *params.StartTime); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.EndTime != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "endTime", runtime.ParamLocationQuery, *params.EndTime); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Period != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "period", runtime.ParamLocationQuery, *params.Period); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetRootcaRequest generates requests for GetRootca
func NewGetRootcaRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/rootca")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostSourcePingRequest calls the generic PostSourcePing builder with application/json body
func NewPostSourcePingRequest(server string, body PostSourcePingJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostSourcePingRequestWithBody(server, "application/json", bodyReader)
}

// NewPostSourcePingRequestWithBody generates requests for PostSourcePing with any type of body
func NewPostSourcePingRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/source/ping")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewDeleteTenantRequest generates requests for DeleteTenant
func NewDeleteTenantRequest(server string, params *DeleteTenantParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.TenantId != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "tenantId", runtime.ParamLocationQuery, *params.TenantId); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.TenantName != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "tenantName", runtime.ParamLocationQuery, *params.TenantName); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetTenantRequest generates requests for GetTenant
func NewGetTenantRequest(server string, params *GetTenantParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.TenantId != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "tenantId", runtime.ParamLocationQuery, *params.TenantId); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.TenantName != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "tenantName", runtime.ParamLocationQuery, *params.TenantName); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewDeleteTenantDbusersRequest generates requests for DeleteTenantDbusers
func NewDeleteTenantDbusersRequest(server string, params *DeleteTenantDbusersParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/dbusers")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "tenantId", runtime.ParamLocationQuery, params.TenantId); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "username", runtime.ParamLocationQuery, params.Username); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetTenantDbusersRequest generates requests for GetTenantDbusers
func NewGetTenantDbusersRequest(server string, params *GetTenantDbusersParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/dbusers")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "tenantId", runtime.ParamLocationQuery, params.TenantId); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantDbusersRequest calls the generic PostTenantDbusers builder with application/json body
func NewPostTenantDbusersRequest(server string, body PostTenantDbusersJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostTenantDbusersRequestWithBody(server, "application/json", bodyReader)
}

// NewPostTenantDbusersRequestWithBody generates requests for PostTenantDbusers with any type of body
func NewPostTenantDbusersRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/dbusers")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPutTenantDbusersRequest calls the generic PutTenantDbusers builder with application/json body
func NewPutTenantDbusersRequest(server string, body PutTenantDbusersJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPutTenantDbusersRequestWithBody(server, "application/json", bodyReader)
}

// NewPutTenantDbusersRequestWithBody generates requests for PutTenantDbusers with any type of body
func NewPutTenantDbusersRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/dbusers")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetTenantTagsRequest generates requests for GetTenantTags
func NewGetTenantTagsRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/tags")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetTenantTenantIdBackupRequest generates requests for GetTenantTenantIdBackup
func NewGetTenantTenantIdBackupRequest(server string, tenantId uint64, params *GetTenantTenantIdBackupParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/backup", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Offset != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "offset", runtime.ParamLocationQuery, *params.Offset); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Limit != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "limit", runtime.ParamLocationQuery, *params.Limit); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantTenantIdBackupRequest generates requests for PostTenantTenantIdBackup
func NewPostTenantTenantIdBackupRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/backup", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewDeleteTenantTenantIdBackupSnapshotIdRequest generates requests for DeleteTenantTenantIdBackupSnapshotId
func NewDeleteTenantTenantIdBackupSnapshotIdRequest(server string, tenantId uint64, snapshotId openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "snapshotId", runtime.ParamLocationPath, snapshotId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/backup/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantTenantIdBackupSnapshotIdRestoreRequest generates requests for PostTenantTenantIdBackupSnapshotIdRestore
func NewPostTenantTenantIdBackupSnapshotIdRestoreRequest(server string, tenantId uint64, snapshotId openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "snapshotId", runtime.ParamLocationPath, snapshotId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/backup/%s/restore", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPutTenantTenantIdConfigEtcdRequestWithTextBody calls the generic PutTenantTenantIdConfigEtcd builder with text/plain body
func NewPutTenantTenantIdConfigEtcdRequestWithTextBody(server string, tenantId uint64, body PutTenantTenantIdConfigEtcdTextRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	bodyReader = strings.NewReader(string(body))
	return NewPutTenantTenantIdConfigEtcdRequestWithBody(server, tenantId, "text/plain", bodyReader)
}

// NewPutTenantTenantIdConfigEtcdRequestWithBody generates requests for PutTenantTenantIdConfigEtcd with any type of body
func NewPutTenantTenantIdConfigEtcdRequestWithBody(server string, tenantId uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/config/etcd", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPutTenantTenantIdConfigRisingwaveRequestWithTextBody calls the generic PutTenantTenantIdConfigRisingwave builder with text/plain body
func NewPutTenantTenantIdConfigRisingwaveRequestWithTextBody(server string, tenantId uint64, body PutTenantTenantIdConfigRisingwaveTextRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	bodyReader = strings.NewReader(string(body))
	return NewPutTenantTenantIdConfigRisingwaveRequestWithBody(server, tenantId, "text/plain", bodyReader)
}

// NewPutTenantTenantIdConfigRisingwaveRequestWithBody generates requests for PutTenantTenantIdConfigRisingwave with any type of body
func NewPutTenantTenantIdConfigRisingwaveRequestWithBody(server string, tenantId uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/config/risingwave", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetTenantTenantIdDatabasesRequest generates requests for GetTenantTenantIdDatabases
func NewGetTenantTenantIdDatabasesRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/databases", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantTenantIdExecutionRequest calls the generic PostTenantTenantIdExecution builder with application/json body
func NewPostTenantTenantIdExecutionRequest(server string, tenantId uint64, body PostTenantTenantIdExecutionJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostTenantTenantIdExecutionRequestWithBody(server, tenantId, "application/json", bodyReader)
}

// NewPostTenantTenantIdExecutionRequestWithBody generates requests for PostTenantTenantIdExecution with any type of body
func NewPostTenantTenantIdExecutionRequestWithBody(server string, tenantId uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/execution", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostTenantTenantIdExtensionsServerlessbackfillDisableRequest generates requests for PostTenantTenantIdExtensionsServerlessbackfillDisable
func NewPostTenantTenantIdExtensionsServerlessbackfillDisableRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/extensions/serverlessbackfill/disable", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantTenantIdExtensionsServerlessbackfillEnableRequest generates requests for PostTenantTenantIdExtensionsServerlessbackfillEnable
func NewPostTenantTenantIdExtensionsServerlessbackfillEnableRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/extensions/serverlessbackfill/enable", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantTenantIdExtensionsServerlessbackfillVersionRequestWithTextBody calls the generic PostTenantTenantIdExtensionsServerlessbackfillVersion builder with text/plain body
func NewPostTenantTenantIdExtensionsServerlessbackfillVersionRequestWithTextBody(server string, tenantId uint64, body PostTenantTenantIdExtensionsServerlessbackfillVersionTextRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	bodyReader = strings.NewReader(string(body))
	return NewPostTenantTenantIdExtensionsServerlessbackfillVersionRequestWithBody(server, tenantId, "text/plain", bodyReader)
}

// NewPostTenantTenantIdExtensionsServerlessbackfillVersionRequestWithBody generates requests for PostTenantTenantIdExtensionsServerlessbackfillVersion with any type of body
func NewPostTenantTenantIdExtensionsServerlessbackfillVersionRequestWithBody(server string, tenantId uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/extensions/serverlessbackfill/version", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetTenantTenantIdInfoRequest generates requests for GetTenantTenantIdInfo
func NewGetTenantTenantIdInfoRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/info", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetTenantTenantIdMetricsRequest generates requests for GetTenantTenantIdMetrics
func NewGetTenantTenantIdMetricsRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/metrics", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewDeleteTenantTenantIdPrivatelinkPrivateLinkIdRequest generates requests for DeleteTenantTenantIdPrivatelinkPrivateLinkId
func NewDeleteTenantTenantIdPrivatelinkPrivateLinkIdRequest(server string, tenantId uint64, privateLinkId openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "privateLinkId", runtime.ParamLocationPath, privateLinkId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/privatelink/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetTenantTenantIdPrivatelinkPrivateLinkIdRequest generates requests for GetTenantTenantIdPrivatelinkPrivateLinkId
func NewGetTenantTenantIdPrivatelinkPrivateLinkIdRequest(server string, tenantId uint64, privateLinkId openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "privateLinkId", runtime.ParamLocationPath, privateLinkId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/privatelink/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantTenantIdPrivatelinksRequest calls the generic PostTenantTenantIdPrivatelinks builder with application/json body
func NewPostTenantTenantIdPrivatelinksRequest(server string, tenantId uint64, body PostTenantTenantIdPrivatelinksJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostTenantTenantIdPrivatelinksRequestWithBody(server, tenantId, "application/json", bodyReader)
}

// NewPostTenantTenantIdPrivatelinksRequestWithBody generates requests for PostTenantTenantIdPrivatelinks with any type of body
func NewPostTenantTenantIdPrivatelinksRequestWithBody(server string, tenantId uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/privatelinks", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewDeleteTenantTenantIdRelationRequest calls the generic DeleteTenantTenantIdRelation builder with application/json body
func NewDeleteTenantTenantIdRelationRequest(server string, tenantId uint64, body DeleteTenantTenantIdRelationJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewDeleteTenantTenantIdRelationRequestWithBody(server, tenantId, "application/json", bodyReader)
}

// NewDeleteTenantTenantIdRelationRequestWithBody generates requests for DeleteTenantTenantIdRelation with any type of body
func NewDeleteTenantTenantIdRelationRequestWithBody(server string, tenantId uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/relation", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetTenantTenantIdRelationsGetCountsRequest generates requests for GetTenantTenantIdRelationsGetCounts
func NewGetTenantTenantIdRelationsGetCountsRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/relations/getCounts", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantTenantIdResourceRequest calls the generic PostTenantTenantIdResource builder with application/json body
func NewPostTenantTenantIdResourceRequest(server string, tenantId uint64, body PostTenantTenantIdResourceJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostTenantTenantIdResourceRequestWithBody(server, tenantId, "application/json", bodyReader)
}

// NewPostTenantTenantIdResourceRequestWithBody generates requests for PostTenantTenantIdResource with any type of body
func NewPostTenantTenantIdResourceRequestWithBody(server string, tenantId uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/resource", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostTenantTenantIdSourcesFetchKafkaTopicRequest calls the generic PostTenantTenantIdSourcesFetchKafkaTopic builder with application/json body
func NewPostTenantTenantIdSourcesFetchKafkaTopicRequest(server string, tenantId uint64, body PostTenantTenantIdSourcesFetchKafkaTopicJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostTenantTenantIdSourcesFetchKafkaTopicRequestWithBody(server, tenantId, "application/json", bodyReader)
}

// NewPostTenantTenantIdSourcesFetchKafkaTopicRequestWithBody generates requests for PostTenantTenantIdSourcesFetchKafkaTopic with any type of body
func NewPostTenantTenantIdSourcesFetchKafkaTopicRequestWithBody(server string, tenantId uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/sources/fetchKafkaTopic", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostTenantTenantIdSourcesPingRequest calls the generic PostTenantTenantIdSourcesPing builder with application/json body
func NewPostTenantTenantIdSourcesPingRequest(server string, tenantId uint64, body PostTenantTenantIdSourcesPingJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostTenantTenantIdSourcesPingRequestWithBody(server, tenantId, "application/json", bodyReader)
}

// NewPostTenantTenantIdSourcesPingRequestWithBody generates requests for PostTenantTenantIdSourcesPing with any type of body
func NewPostTenantTenantIdSourcesPingRequestWithBody(server string, tenantId uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/sources/ping", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPutTenantTenantIdStartRequest generates requests for PutTenantTenantIdStart
func NewPutTenantTenantIdStartRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/start", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPutTenantTenantIdStopRequest generates requests for PutTenantTenantIdStop
func NewPutTenantTenantIdStopRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/stop", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPutTenantTenantIdUpdateCfgConfigIdRequest generates requests for PutTenantTenantIdUpdateCfgConfigId
func NewPutTenantTenantIdUpdateCfgConfigIdRequest(server string, tenantId uint64, configId openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "configId", runtime.ParamLocationPath, configId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/update-cfg/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantTenantIdUpdateVersionRequest calls the generic PostTenantTenantIdUpdateVersion builder with application/json body
func NewPostTenantTenantIdUpdateVersionRequest(server string, tenantId uint64, body PostTenantTenantIdUpdateVersionJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostTenantTenantIdUpdateVersionRequestWithBody(server, tenantId, "application/json", bodyReader)
}

// NewPostTenantTenantIdUpdateVersionRequestWithBody generates requests for PostTenantTenantIdUpdateVersion with any type of body
func NewPostTenantTenantIdUpdateVersionRequestWithBody(server string, tenantId uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/updateVersion", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetTenantsRequest generates requests for GetTenants
func NewGetTenantsRequest(server string, params *GetTenantsParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenants")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Offset != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "offset", runtime.ParamLocationQuery, *params.Offset); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Limit != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "limit", runtime.ParamLocationQuery, *params.Limit); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantsRequest calls the generic PostTenants builder with application/json body
func NewPostTenantsRequest(server string, body PostTenantsJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostTenantsRequestWithBody(server, "application/json", bodyReader)
}

// NewPostTenantsRequestWithBody generates requests for PostTenants with any type of body
func NewPostTenantsRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenants")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetTenantsStatusRequest generates requests for GetTenantsStatus
func NewGetTenantsStatusRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenants/status")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantsTenantIdUnquiesceRequest generates requests for PostTenantsTenantIdUnquiesce
func NewPostTenantsTenantIdUnquiesceRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenants/%s/unquiesce", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetTiersRequest generates requests for GetTiers
func NewGetTiersRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tiers")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetVersionRequest generates requests for GetVersion
func NewGetVersionRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/version")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

func (c *Client) applyEditors(ctx context.Context, req *http.Request, additionalEditors []RequestEditorFn) error {
	for _, r := range c.RequestEditors {
		if err := r(ctx, req); err != nil {
			return err
		}
	}
	for _, r := range additionalEditors {
		if err := r(ctx, req); err != nil {
			return err
		}
	}
	return nil
}

// ClientWithResponses builds on ClientInterface to offer response payloads
type ClientWithResponses struct {
	ClientInterface
}

// NewClientWithResponses creates a new ClientWithResponses, which wraps
// Client with return type handling
func NewClientWithResponses(server string, opts ...ClientOption) (*ClientWithResponses, error) {
	client, err := NewClient(server, opts...)
	if err != nil {
		return nil, err
	}
	return &ClientWithResponses{client}, nil
}

// WithBaseURL overrides the baseURL.
func WithBaseURL(baseURL string) ClientOption {
	return func(c *Client) error {
		newBaseURL, err := url.Parse(baseURL)
		if err != nil {
			return err
		}
		c.Server = newBaseURL.String()
		return nil
	}
}

// ClientWithResponsesInterface is the interface specification for the client with responses above.
type ClientWithResponsesInterface interface {
	// GetWithResponse request
	GetWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetResponse, error)

	// GetAvailabilityWithResponse request
	GetAvailabilityWithResponse(ctx context.Context, params *GetAvailabilityParams, reqEditors ...RequestEditorFn) (*GetAvailabilityResponse, error)

	// DeleteByocClusterNameWithResponse request
	DeleteByocClusterNameWithResponse(ctx context.Context, name string, reqEditors ...RequestEditorFn) (*DeleteByocClusterNameResponse, error)

	// GetByocClusterNameWithResponse request
	GetByocClusterNameWithResponse(ctx context.Context, name string, reqEditors ...RequestEditorFn) (*GetByocClusterNameResponse, error)

	// PutByocClusterNameWithBodyWithResponse request with any body
	PutByocClusterNameWithBodyWithResponse(ctx context.Context, name string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutByocClusterNameResponse, error)

	PutByocClusterNameWithResponse(ctx context.Context, name string, body PutByocClusterNameJSONRequestBody, reqEditors ...RequestEditorFn) (*PutByocClusterNameResponse, error)

	// PostByocClusterNameManualUpdateWithBodyWithResponse request with any body
	PostByocClusterNameManualUpdateWithBodyWithResponse(ctx context.Context, name string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostByocClusterNameManualUpdateResponse, error)

	PostByocClusterNameManualUpdateWithResponse(ctx context.Context, name string, body PostByocClusterNameManualUpdateJSONRequestBody, reqEditors ...RequestEditorFn) (*PostByocClusterNameManualUpdateResponse, error)

	// PostByocClusterNameUpdateWithBodyWithResponse request with any body
	PostByocClusterNameUpdateWithBodyWithResponse(ctx context.Context, name string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostByocClusterNameUpdateResponse, error)

	PostByocClusterNameUpdateWithResponse(ctx context.Context, name string, body PostByocClusterNameUpdateJSONRequestBody, reqEditors ...RequestEditorFn) (*PostByocClusterNameUpdateResponse, error)

	// GetByocClustersWithResponse request
	GetByocClustersWithResponse(ctx context.Context, params *GetByocClustersParams, reqEditors ...RequestEditorFn) (*GetByocClustersResponse, error)

	// PostByocClustersWithBodyWithResponse request with any body
	PostByocClustersWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostByocClustersResponse, error)

	PostByocClustersWithResponse(ctx context.Context, body PostByocClustersJSONRequestBody, reqEditors ...RequestEditorFn) (*PostByocClustersResponse, error)

	// GetEndpointsWithResponse request
	GetEndpointsWithResponse(ctx context.Context, params *GetEndpointsParams, reqEditors ...RequestEditorFn) (*GetEndpointsResponse, error)

	// QueryErrLogWithResponse request
	QueryErrLogWithResponse(ctx context.Context, params *QueryErrLogParams, reqEditors ...RequestEditorFn) (*QueryErrLogResponse, error)

	// GetMetricsWithResponse request
	GetMetricsWithResponse(ctx context.Context, params *GetMetricsParams, reqEditors ...RequestEditorFn) (*GetMetricsResponse, error)

	// GetRootcaWithResponse request
	GetRootcaWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetRootcaResponse, error)

	// PostSourcePingWithBodyWithResponse request with any body
	PostSourcePingWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostSourcePingResponse, error)

	PostSourcePingWithResponse(ctx context.Context, body PostSourcePingJSONRequestBody, reqEditors ...RequestEditorFn) (*PostSourcePingResponse, error)

	// DeleteTenantWithResponse request
	DeleteTenantWithResponse(ctx context.Context, params *DeleteTenantParams, reqEditors ...RequestEditorFn) (*DeleteTenantResponse, error)

	// GetTenantWithResponse request
	GetTenantWithResponse(ctx context.Context, params *GetTenantParams, reqEditors ...RequestEditorFn) (*GetTenantResponse, error)

	// DeleteTenantDbusersWithResponse request
	DeleteTenantDbusersWithResponse(ctx context.Context, params *DeleteTenantDbusersParams, reqEditors ...RequestEditorFn) (*DeleteTenantDbusersResponse, error)

	// GetTenantDbusersWithResponse request
	GetTenantDbusersWithResponse(ctx context.Context, params *GetTenantDbusersParams, reqEditors ...RequestEditorFn) (*GetTenantDbusersResponse, error)

	// PostTenantDbusersWithBodyWithResponse request with any body
	PostTenantDbusersWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantDbusersResponse, error)

	PostTenantDbusersWithResponse(ctx context.Context, body PostTenantDbusersJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantDbusersResponse, error)

	// PutTenantDbusersWithBodyWithResponse request with any body
	PutTenantDbusersWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutTenantDbusersResponse, error)

	PutTenantDbusersWithResponse(ctx context.Context, body PutTenantDbusersJSONRequestBody, reqEditors ...RequestEditorFn) (*PutTenantDbusersResponse, error)

	// GetTenantTagsWithResponse request
	GetTenantTagsWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetTenantTagsResponse, error)

	// GetTenantTenantIdBackupWithResponse request
	GetTenantTenantIdBackupWithResponse(ctx context.Context, tenantId uint64, params *GetTenantTenantIdBackupParams, reqEditors ...RequestEditorFn) (*GetTenantTenantIdBackupResponse, error)

	// PostTenantTenantIdBackupWithResponse request
	PostTenantTenantIdBackupWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PostTenantTenantIdBackupResponse, error)

	// DeleteTenantTenantIdBackupSnapshotIdWithResponse request
	DeleteTenantTenantIdBackupSnapshotIdWithResponse(ctx context.Context, tenantId uint64, snapshotId openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteTenantTenantIdBackupSnapshotIdResponse, error)

	// PostTenantTenantIdBackupSnapshotIdRestoreWithResponse request
	PostTenantTenantIdBackupSnapshotIdRestoreWithResponse(ctx context.Context, tenantId uint64, snapshotId openapi_types.UUID, reqEditors ...RequestEditorFn) (*PostTenantTenantIdBackupSnapshotIdRestoreResponse, error)

	// PutTenantTenantIdConfigEtcdWithBodyWithResponse request with any body
	PutTenantTenantIdConfigEtcdWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutTenantTenantIdConfigEtcdResponse, error)

	PutTenantTenantIdConfigEtcdWithTextBodyWithResponse(ctx context.Context, tenantId uint64, body PutTenantTenantIdConfigEtcdTextRequestBody, reqEditors ...RequestEditorFn) (*PutTenantTenantIdConfigEtcdResponse, error)

	// PutTenantTenantIdConfigRisingwaveWithBodyWithResponse request with any body
	PutTenantTenantIdConfigRisingwaveWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutTenantTenantIdConfigRisingwaveResponse, error)

	PutTenantTenantIdConfigRisingwaveWithTextBodyWithResponse(ctx context.Context, tenantId uint64, body PutTenantTenantIdConfigRisingwaveTextRequestBody, reqEditors ...RequestEditorFn) (*PutTenantTenantIdConfigRisingwaveResponse, error)

	// GetTenantTenantIdDatabasesWithResponse request
	GetTenantTenantIdDatabasesWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*GetTenantTenantIdDatabasesResponse, error)

	// PostTenantTenantIdExecutionWithBodyWithResponse request with any body
	PostTenantTenantIdExecutionWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExecutionResponse, error)

	PostTenantTenantIdExecutionWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdExecutionJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExecutionResponse, error)

	// PostTenantTenantIdExtensionsServerlessbackfillDisableWithResponse request
	PostTenantTenantIdExtensionsServerlessbackfillDisableWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsServerlessbackfillDisableResponse, error)

	// PostTenantTenantIdExtensionsServerlessbackfillEnableWithResponse request
	PostTenantTenantIdExtensionsServerlessbackfillEnableWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsServerlessbackfillEnableResponse, error)

	// PostTenantTenantIdExtensionsServerlessbackfillVersionWithBodyWithResponse request with any body
	PostTenantTenantIdExtensionsServerlessbackfillVersionWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsServerlessbackfillVersionResponse, error)

	PostTenantTenantIdExtensionsServerlessbackfillVersionWithTextBodyWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdExtensionsServerlessbackfillVersionTextRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsServerlessbackfillVersionResponse, error)

	// GetTenantTenantIdInfoWithResponse request
	GetTenantTenantIdInfoWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*GetTenantTenantIdInfoResponse, error)

	// GetTenantTenantIdMetricsWithResponse request
	GetTenantTenantIdMetricsWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*GetTenantTenantIdMetricsResponse, error)

	// DeleteTenantTenantIdPrivatelinkPrivateLinkIdWithResponse request
	DeleteTenantTenantIdPrivatelinkPrivateLinkIdWithResponse(ctx context.Context, tenantId uint64, privateLinkId openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteTenantTenantIdPrivatelinkPrivateLinkIdResponse, error)

	// GetTenantTenantIdPrivatelinkPrivateLinkIdWithResponse request
	GetTenantTenantIdPrivatelinkPrivateLinkIdWithResponse(ctx context.Context, tenantId uint64, privateLinkId openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetTenantTenantIdPrivatelinkPrivateLinkIdResponse, error)

	// PostTenantTenantIdPrivatelinksWithBodyWithResponse request with any body
	PostTenantTenantIdPrivatelinksWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdPrivatelinksResponse, error)

	PostTenantTenantIdPrivatelinksWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdPrivatelinksJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdPrivatelinksResponse, error)

	// DeleteTenantTenantIdRelationWithBodyWithResponse request with any body
	DeleteTenantTenantIdRelationWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*DeleteTenantTenantIdRelationResponse, error)

	DeleteTenantTenantIdRelationWithResponse(ctx context.Context, tenantId uint64, body DeleteTenantTenantIdRelationJSONRequestBody, reqEditors ...RequestEditorFn) (*DeleteTenantTenantIdRelationResponse, error)

	// GetTenantTenantIdRelationsGetCountsWithResponse request
	GetTenantTenantIdRelationsGetCountsWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*GetTenantTenantIdRelationsGetCountsResponse, error)

	// PostTenantTenantIdResourceWithBodyWithResponse request with any body
	PostTenantTenantIdResourceWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdResourceResponse, error)

	PostTenantTenantIdResourceWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdResourceJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdResourceResponse, error)

	// PostTenantTenantIdSourcesFetchKafkaTopicWithBodyWithResponse request with any body
	PostTenantTenantIdSourcesFetchKafkaTopicWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdSourcesFetchKafkaTopicResponse, error)

	PostTenantTenantIdSourcesFetchKafkaTopicWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdSourcesFetchKafkaTopicJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdSourcesFetchKafkaTopicResponse, error)

	// PostTenantTenantIdSourcesPingWithBodyWithResponse request with any body
	PostTenantTenantIdSourcesPingWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdSourcesPingResponse, error)

	PostTenantTenantIdSourcesPingWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdSourcesPingJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdSourcesPingResponse, error)

	// PutTenantTenantIdStartWithResponse request
	PutTenantTenantIdStartWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PutTenantTenantIdStartResponse, error)

	// PutTenantTenantIdStopWithResponse request
	PutTenantTenantIdStopWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PutTenantTenantIdStopResponse, error)

	// PutTenantTenantIdUpdateCfgConfigIdWithResponse request
	PutTenantTenantIdUpdateCfgConfigIdWithResponse(ctx context.Context, tenantId uint64, configId openapi_types.UUID, reqEditors ...RequestEditorFn) (*PutTenantTenantIdUpdateCfgConfigIdResponse, error)

	// PostTenantTenantIdUpdateVersionWithBodyWithResponse request with any body
	PostTenantTenantIdUpdateVersionWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdUpdateVersionResponse, error)

	PostTenantTenantIdUpdateVersionWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdUpdateVersionJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdUpdateVersionResponse, error)

	// GetTenantsWithResponse request
	GetTenantsWithResponse(ctx context.Context, params *GetTenantsParams, reqEditors ...RequestEditorFn) (*GetTenantsResponse, error)

	// PostTenantsWithBodyWithResponse request with any body
	PostTenantsWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantsResponse, error)

	PostTenantsWithResponse(ctx context.Context, body PostTenantsJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantsResponse, error)

	// GetTenantsStatusWithResponse request
	GetTenantsStatusWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetTenantsStatusResponse, error)

	// PostTenantsTenantIdUnquiesceWithResponse request
	PostTenantsTenantIdUnquiesceWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PostTenantsTenantIdUnquiesceResponse, error)

	// GetTiersWithResponse request
	GetTiersWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetTiersResponse, error)

	// GetVersionWithResponse request
	GetVersionWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetVersionResponse, error)
}

type GetResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r GetResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetAvailabilityResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *AvailabilityResponse
	JSON422      *AvailabilityResponse
}

// Status returns HTTPResponse.Status
func (r GetAvailabilityResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetAvailabilityResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteByocClusterNameResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
	JSON404      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r DeleteByocClusterNameResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteByocClusterNameResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetByocClusterNameResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ManagedCluster
	JSON404      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r GetByocClusterNameResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetByocClusterNameResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutByocClusterNameResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
	JSON404      *DefaultResponse
	JSON409      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r PutByocClusterNameResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutByocClusterNameResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostByocClusterNameManualUpdateResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *DefaultResponse
	JSON400      *DefaultResponse
	JSON404      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r PostByocClusterNameManualUpdateResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostByocClusterNameManualUpdateResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostByocClusterNameUpdateResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *DefaultResponse
	JSON400      *DefaultResponse
	JSON404      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r PostByocClusterNameUpdateResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostByocClusterNameUpdateResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetByocClustersResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ManagedClustersSizePage
}

// Status returns HTTPResponse.Status
func (r GetByocClustersResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetByocClustersResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostByocClustersResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
	JSON409      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r PostByocClustersResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostByocClustersResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetEndpointsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *Endpoint
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r GetEndpointsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetEndpointsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type QueryErrLogResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ErrLogQueryResult
	JSON400      *BadRequestResponse
}

// Status returns HTTPResponse.Status
func (r QueryErrLogResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r QueryErrLogResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetMetricsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *Metrics
	JSON404      *NotFoundResponse
	JSON416      *RangeNotSatisfiable
}

// Status returns HTTPResponse.Status
func (r GetMetricsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetMetricsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetRootcaResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r GetRootcaResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetRootcaResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostSourcePingResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *AvailabilityResponse
	JSON400      *BadRequestResponse
}

// Status returns HTTPResponse.Status
func (r PostSourcePingResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostSourcePingResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteTenantResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *DefaultResponse
	JSON403      *DefaultResponse
	JSON404      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r DeleteTenantResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteTenantResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetTenantResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *Tenant
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r GetTenantResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetTenantResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteTenantDbusersResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
	JSON400      *FailedPreconditionResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r DeleteTenantDbusersResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteTenantDbusersResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetTenantDbusersResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DBUsers
	JSON404      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r GetTenantDbusersResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetTenantDbusersResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantDbusersResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DBUser
	JSON400      *FailedPreconditionResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantDbusersResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantDbusersResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutTenantDbusersResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
	JSON400      *FailedPreconditionResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PutTenantDbusersResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutTenantDbusersResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetTenantTagsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *GetImageTagResponse
}

// Status returns HTTPResponse.Status
func (r GetTenantTagsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetTenantTagsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetTenantTenantIdBackupResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *BackupSnapshotsSizePage
}

// Status returns HTTPResponse.Status
func (r GetTenantTenantIdBackupResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetTenantTenantIdBackupResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdBackupResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *PostSnapshotResponseBody
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdBackupResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdBackupResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteTenantTenantIdBackupSnapshotIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r DeleteTenantTenantIdBackupSnapshotIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteTenantTenantIdBackupSnapshotIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdBackupSnapshotIdRestoreResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdBackupSnapshotIdRestoreResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdBackupSnapshotIdRestoreResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutTenantTenantIdConfigEtcdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON404      *NotFoundResponse
	JSON409      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r PutTenantTenantIdConfigEtcdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutTenantTenantIdConfigEtcdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutTenantTenantIdConfigRisingwaveResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON404      *NotFoundResponse
	JSON409      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r PutTenantTenantIdConfigRisingwaveResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutTenantTenantIdConfigRisingwaveResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetTenantTenantIdDatabasesResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *GetDatabasesResponseBody
}

// Status returns HTTPResponse.Status
func (r GetTenantTenantIdDatabasesResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetTenantTenantIdDatabasesResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdExecutionResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
	JSON400      *FailedPreconditionResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdExecutionResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdExecutionResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdExtensionsServerlessbackfillDisableResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *WorkflowIdResponseBody
	JSON400      *FailedPreconditionResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdExtensionsServerlessbackfillDisableResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdExtensionsServerlessbackfillDisableResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdExtensionsServerlessbackfillEnableResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *WorkflowIdResponseBody
	JSON400      *FailedPreconditionResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdExtensionsServerlessbackfillEnableResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdExtensionsServerlessbackfillEnableResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdExtensionsServerlessbackfillVersionResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *WorkflowIdResponseBody
	JSON400      *FailedPreconditionResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdExtensionsServerlessbackfillVersionResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdExtensionsServerlessbackfillVersionResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetTenantTenantIdInfoResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *TenantClusterInfoResponseBody
}

// Status returns HTTPResponse.Status
func (r GetTenantTenantIdInfoResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetTenantTenantIdInfoResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetTenantTenantIdMetricsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r GetTenantTenantIdMetricsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetTenantTenantIdMetricsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteTenantTenantIdPrivatelinkPrivateLinkIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *DefaultResponse
	JSON400      *BadRequestResponse
}

// Status returns HTTPResponse.Status
func (r DeleteTenantTenantIdPrivatelinkPrivateLinkIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteTenantTenantIdPrivatelinkPrivateLinkIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetTenantTenantIdPrivatelinkPrivateLinkIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *PrivateLink
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r GetTenantTenantIdPrivatelinkPrivateLinkIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetTenantTenantIdPrivatelinkPrivateLinkIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdPrivatelinksResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *PostPrivateLinkResponseBody
	JSON400      *BadRequestResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdPrivatelinksResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdPrivatelinksResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteTenantTenantIdRelationResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
	JSON400      *FailedPreconditionResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r DeleteTenantTenantIdRelationResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteTenantTenantIdRelationResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetTenantTenantIdRelationsGetCountsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *RelationCounts
}

// Status returns HTTPResponse.Status
func (r GetTenantTenantIdRelationsGetCountsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetTenantTenantIdRelationsGetCountsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdResourceResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *DefaultResponse
	JSON400      *BadRequestResponse
	JSON422      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdResourceResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdResourceResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdSourcesFetchKafkaTopicResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *PostSourcesFetchKafkaTopicResponseBody
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdSourcesFetchKafkaTopicResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdSourcesFetchKafkaTopicResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdSourcesPingResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *AvailabilityResponse
	JSON400      *BadRequestResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdSourcesPingResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdSourcesPingResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutTenantTenantIdStartResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON404      *NotFoundResponse
	JSON409      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r PutTenantTenantIdStartResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutTenantTenantIdStartResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutTenantTenantIdStopResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *DefaultResponse
	JSON404      *NotFoundResponse
	JSON409      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r PutTenantTenantIdStopResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutTenantTenantIdStopResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutTenantTenantIdUpdateCfgConfigIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON404      *NotFoundResponse
	JSON409      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r PutTenantTenantIdUpdateCfgConfigIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutTenantTenantIdUpdateCfgConfigIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdUpdateVersionResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *DefaultResponse
	JSON404      *DefaultResponse
	JSON409      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdUpdateVersionResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdUpdateVersionResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetTenantsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *TenantSizePage
}

// Status returns HTTPResponse.Status
func (r GetTenantsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetTenantsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *CreateTenantResponseBody
	JSON400      *BadRequestResponse
	JSON422      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetTenantsStatusResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *TenantStatusCounts
}

// Status returns HTTPResponse.Status
func (r GetTenantsStatusResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetTenantsStatusResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantsTenantIdUnquiesceResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r PostTenantsTenantIdUnquiesceResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantsTenantIdUnquiesceResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetTiersResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *Tiers
}

// Status returns HTTPResponse.Status
func (r GetTiersResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetTiersResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetVersionResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r GetVersionResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetVersionResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

// GetWithResponse request returning *GetResponse
func (c *ClientWithResponses) GetWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetResponse, error) {
	rsp, err := c.Get(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetResponse(rsp)
}

// GetAvailabilityWithResponse request returning *GetAvailabilityResponse
func (c *ClientWithResponses) GetAvailabilityWithResponse(ctx context.Context, params *GetAvailabilityParams, reqEditors ...RequestEditorFn) (*GetAvailabilityResponse, error) {
	rsp, err := c.GetAvailability(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetAvailabilityResponse(rsp)
}

// DeleteByocClusterNameWithResponse request returning *DeleteByocClusterNameResponse
func (c *ClientWithResponses) DeleteByocClusterNameWithResponse(ctx context.Context, name string, reqEditors ...RequestEditorFn) (*DeleteByocClusterNameResponse, error) {
	rsp, err := c.DeleteByocClusterName(ctx, name, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteByocClusterNameResponse(rsp)
}

// GetByocClusterNameWithResponse request returning *GetByocClusterNameResponse
func (c *ClientWithResponses) GetByocClusterNameWithResponse(ctx context.Context, name string, reqEditors ...RequestEditorFn) (*GetByocClusterNameResponse, error) {
	rsp, err := c.GetByocClusterName(ctx, name, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetByocClusterNameResponse(rsp)
}

// PutByocClusterNameWithBodyWithResponse request with arbitrary body returning *PutByocClusterNameResponse
func (c *ClientWithResponses) PutByocClusterNameWithBodyWithResponse(ctx context.Context, name string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutByocClusterNameResponse, error) {
	rsp, err := c.PutByocClusterNameWithBody(ctx, name, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutByocClusterNameResponse(rsp)
}

func (c *ClientWithResponses) PutByocClusterNameWithResponse(ctx context.Context, name string, body PutByocClusterNameJSONRequestBody, reqEditors ...RequestEditorFn) (*PutByocClusterNameResponse, error) {
	rsp, err := c.PutByocClusterName(ctx, name, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutByocClusterNameResponse(rsp)
}

// PostByocClusterNameManualUpdateWithBodyWithResponse request with arbitrary body returning *PostByocClusterNameManualUpdateResponse
func (c *ClientWithResponses) PostByocClusterNameManualUpdateWithBodyWithResponse(ctx context.Context, name string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostByocClusterNameManualUpdateResponse, error) {
	rsp, err := c.PostByocClusterNameManualUpdateWithBody(ctx, name, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostByocClusterNameManualUpdateResponse(rsp)
}

func (c *ClientWithResponses) PostByocClusterNameManualUpdateWithResponse(ctx context.Context, name string, body PostByocClusterNameManualUpdateJSONRequestBody, reqEditors ...RequestEditorFn) (*PostByocClusterNameManualUpdateResponse, error) {
	rsp, err := c.PostByocClusterNameManualUpdate(ctx, name, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostByocClusterNameManualUpdateResponse(rsp)
}

// PostByocClusterNameUpdateWithBodyWithResponse request with arbitrary body returning *PostByocClusterNameUpdateResponse
func (c *ClientWithResponses) PostByocClusterNameUpdateWithBodyWithResponse(ctx context.Context, name string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostByocClusterNameUpdateResponse, error) {
	rsp, err := c.PostByocClusterNameUpdateWithBody(ctx, name, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostByocClusterNameUpdateResponse(rsp)
}

func (c *ClientWithResponses) PostByocClusterNameUpdateWithResponse(ctx context.Context, name string, body PostByocClusterNameUpdateJSONRequestBody, reqEditors ...RequestEditorFn) (*PostByocClusterNameUpdateResponse, error) {
	rsp, err := c.PostByocClusterNameUpdate(ctx, name, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostByocClusterNameUpdateResponse(rsp)
}

// GetByocClustersWithResponse request returning *GetByocClustersResponse
func (c *ClientWithResponses) GetByocClustersWithResponse(ctx context.Context, params *GetByocClustersParams, reqEditors ...RequestEditorFn) (*GetByocClustersResponse, error) {
	rsp, err := c.GetByocClusters(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetByocClustersResponse(rsp)
}

// PostByocClustersWithBodyWithResponse request with arbitrary body returning *PostByocClustersResponse
func (c *ClientWithResponses) PostByocClustersWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostByocClustersResponse, error) {
	rsp, err := c.PostByocClustersWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostByocClustersResponse(rsp)
}

func (c *ClientWithResponses) PostByocClustersWithResponse(ctx context.Context, body PostByocClustersJSONRequestBody, reqEditors ...RequestEditorFn) (*PostByocClustersResponse, error) {
	rsp, err := c.PostByocClusters(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostByocClustersResponse(rsp)
}

// GetEndpointsWithResponse request returning *GetEndpointsResponse
func (c *ClientWithResponses) GetEndpointsWithResponse(ctx context.Context, params *GetEndpointsParams, reqEditors ...RequestEditorFn) (*GetEndpointsResponse, error) {
	rsp, err := c.GetEndpoints(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetEndpointsResponse(rsp)
}

// QueryErrLogWithResponse request returning *QueryErrLogResponse
func (c *ClientWithResponses) QueryErrLogWithResponse(ctx context.Context, params *QueryErrLogParams, reqEditors ...RequestEditorFn) (*QueryErrLogResponse, error) {
	rsp, err := c.QueryErrLog(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseQueryErrLogResponse(rsp)
}

// GetMetricsWithResponse request returning *GetMetricsResponse
func (c *ClientWithResponses) GetMetricsWithResponse(ctx context.Context, params *GetMetricsParams, reqEditors ...RequestEditorFn) (*GetMetricsResponse, error) {
	rsp, err := c.GetMetrics(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetMetricsResponse(rsp)
}

// GetRootcaWithResponse request returning *GetRootcaResponse
func (c *ClientWithResponses) GetRootcaWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetRootcaResponse, error) {
	rsp, err := c.GetRootca(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetRootcaResponse(rsp)
}

// PostSourcePingWithBodyWithResponse request with arbitrary body returning *PostSourcePingResponse
func (c *ClientWithResponses) PostSourcePingWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostSourcePingResponse, error) {
	rsp, err := c.PostSourcePingWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostSourcePingResponse(rsp)
}

func (c *ClientWithResponses) PostSourcePingWithResponse(ctx context.Context, body PostSourcePingJSONRequestBody, reqEditors ...RequestEditorFn) (*PostSourcePingResponse, error) {
	rsp, err := c.PostSourcePing(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostSourcePingResponse(rsp)
}

// DeleteTenantWithResponse request returning *DeleteTenantResponse
func (c *ClientWithResponses) DeleteTenantWithResponse(ctx context.Context, params *DeleteTenantParams, reqEditors ...RequestEditorFn) (*DeleteTenantResponse, error) {
	rsp, err := c.DeleteTenant(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteTenantResponse(rsp)
}

// GetTenantWithResponse request returning *GetTenantResponse
func (c *ClientWithResponses) GetTenantWithResponse(ctx context.Context, params *GetTenantParams, reqEditors ...RequestEditorFn) (*GetTenantResponse, error) {
	rsp, err := c.GetTenant(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetTenantResponse(rsp)
}

// DeleteTenantDbusersWithResponse request returning *DeleteTenantDbusersResponse
func (c *ClientWithResponses) DeleteTenantDbusersWithResponse(ctx context.Context, params *DeleteTenantDbusersParams, reqEditors ...RequestEditorFn) (*DeleteTenantDbusersResponse, error) {
	rsp, err := c.DeleteTenantDbusers(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteTenantDbusersResponse(rsp)
}

// GetTenantDbusersWithResponse request returning *GetTenantDbusersResponse
func (c *ClientWithResponses) GetTenantDbusersWithResponse(ctx context.Context, params *GetTenantDbusersParams, reqEditors ...RequestEditorFn) (*GetTenantDbusersResponse, error) {
	rsp, err := c.GetTenantDbusers(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetTenantDbusersResponse(rsp)
}

// PostTenantDbusersWithBodyWithResponse request with arbitrary body returning *PostTenantDbusersResponse
func (c *ClientWithResponses) PostTenantDbusersWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantDbusersResponse, error) {
	rsp, err := c.PostTenantDbusersWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantDbusersResponse(rsp)
}

func (c *ClientWithResponses) PostTenantDbusersWithResponse(ctx context.Context, body PostTenantDbusersJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantDbusersResponse, error) {
	rsp, err := c.PostTenantDbusers(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantDbusersResponse(rsp)
}

// PutTenantDbusersWithBodyWithResponse request with arbitrary body returning *PutTenantDbusersResponse
func (c *ClientWithResponses) PutTenantDbusersWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutTenantDbusersResponse, error) {
	rsp, err := c.PutTenantDbusersWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutTenantDbusersResponse(rsp)
}

func (c *ClientWithResponses) PutTenantDbusersWithResponse(ctx context.Context, body PutTenantDbusersJSONRequestBody, reqEditors ...RequestEditorFn) (*PutTenantDbusersResponse, error) {
	rsp, err := c.PutTenantDbusers(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutTenantDbusersResponse(rsp)
}

// GetTenantTagsWithResponse request returning *GetTenantTagsResponse
func (c *ClientWithResponses) GetTenantTagsWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetTenantTagsResponse, error) {
	rsp, err := c.GetTenantTags(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetTenantTagsResponse(rsp)
}

// GetTenantTenantIdBackupWithResponse request returning *GetTenantTenantIdBackupResponse
func (c *ClientWithResponses) GetTenantTenantIdBackupWithResponse(ctx context.Context, tenantId uint64, params *GetTenantTenantIdBackupParams, reqEditors ...RequestEditorFn) (*GetTenantTenantIdBackupResponse, error) {
	rsp, err := c.GetTenantTenantIdBackup(ctx, tenantId, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetTenantTenantIdBackupResponse(rsp)
}

// PostTenantTenantIdBackupWithResponse request returning *PostTenantTenantIdBackupResponse
func (c *ClientWithResponses) PostTenantTenantIdBackupWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PostTenantTenantIdBackupResponse, error) {
	rsp, err := c.PostTenantTenantIdBackup(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdBackupResponse(rsp)
}

// DeleteTenantTenantIdBackupSnapshotIdWithResponse request returning *DeleteTenantTenantIdBackupSnapshotIdResponse
func (c *ClientWithResponses) DeleteTenantTenantIdBackupSnapshotIdWithResponse(ctx context.Context, tenantId uint64, snapshotId openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteTenantTenantIdBackupSnapshotIdResponse, error) {
	rsp, err := c.DeleteTenantTenantIdBackupSnapshotId(ctx, tenantId, snapshotId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteTenantTenantIdBackupSnapshotIdResponse(rsp)
}

// PostTenantTenantIdBackupSnapshotIdRestoreWithResponse request returning *PostTenantTenantIdBackupSnapshotIdRestoreResponse
func (c *ClientWithResponses) PostTenantTenantIdBackupSnapshotIdRestoreWithResponse(ctx context.Context, tenantId uint64, snapshotId openapi_types.UUID, reqEditors ...RequestEditorFn) (*PostTenantTenantIdBackupSnapshotIdRestoreResponse, error) {
	rsp, err := c.PostTenantTenantIdBackupSnapshotIdRestore(ctx, tenantId, snapshotId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdBackupSnapshotIdRestoreResponse(rsp)
}

// PutTenantTenantIdConfigEtcdWithBodyWithResponse request with arbitrary body returning *PutTenantTenantIdConfigEtcdResponse
func (c *ClientWithResponses) PutTenantTenantIdConfigEtcdWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutTenantTenantIdConfigEtcdResponse, error) {
	rsp, err := c.PutTenantTenantIdConfigEtcdWithBody(ctx, tenantId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutTenantTenantIdConfigEtcdResponse(rsp)
}

func (c *ClientWithResponses) PutTenantTenantIdConfigEtcdWithTextBodyWithResponse(ctx context.Context, tenantId uint64, body PutTenantTenantIdConfigEtcdTextRequestBody, reqEditors ...RequestEditorFn) (*PutTenantTenantIdConfigEtcdResponse, error) {
	rsp, err := c.PutTenantTenantIdConfigEtcdWithTextBody(ctx, tenantId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutTenantTenantIdConfigEtcdResponse(rsp)
}

// PutTenantTenantIdConfigRisingwaveWithBodyWithResponse request with arbitrary body returning *PutTenantTenantIdConfigRisingwaveResponse
func (c *ClientWithResponses) PutTenantTenantIdConfigRisingwaveWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutTenantTenantIdConfigRisingwaveResponse, error) {
	rsp, err := c.PutTenantTenantIdConfigRisingwaveWithBody(ctx, tenantId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutTenantTenantIdConfigRisingwaveResponse(rsp)
}

func (c *ClientWithResponses) PutTenantTenantIdConfigRisingwaveWithTextBodyWithResponse(ctx context.Context, tenantId uint64, body PutTenantTenantIdConfigRisingwaveTextRequestBody, reqEditors ...RequestEditorFn) (*PutTenantTenantIdConfigRisingwaveResponse, error) {
	rsp, err := c.PutTenantTenantIdConfigRisingwaveWithTextBody(ctx, tenantId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutTenantTenantIdConfigRisingwaveResponse(rsp)
}

// GetTenantTenantIdDatabasesWithResponse request returning *GetTenantTenantIdDatabasesResponse
func (c *ClientWithResponses) GetTenantTenantIdDatabasesWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*GetTenantTenantIdDatabasesResponse, error) {
	rsp, err := c.GetTenantTenantIdDatabases(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetTenantTenantIdDatabasesResponse(rsp)
}

// PostTenantTenantIdExecutionWithBodyWithResponse request with arbitrary body returning *PostTenantTenantIdExecutionResponse
func (c *ClientWithResponses) PostTenantTenantIdExecutionWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExecutionResponse, error) {
	rsp, err := c.PostTenantTenantIdExecutionWithBody(ctx, tenantId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdExecutionResponse(rsp)
}

func (c *ClientWithResponses) PostTenantTenantIdExecutionWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdExecutionJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExecutionResponse, error) {
	rsp, err := c.PostTenantTenantIdExecution(ctx, tenantId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdExecutionResponse(rsp)
}

// PostTenantTenantIdExtensionsServerlessbackfillDisableWithResponse request returning *PostTenantTenantIdExtensionsServerlessbackfillDisableResponse
func (c *ClientWithResponses) PostTenantTenantIdExtensionsServerlessbackfillDisableWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsServerlessbackfillDisableResponse, error) {
	rsp, err := c.PostTenantTenantIdExtensionsServerlessbackfillDisable(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdExtensionsServerlessbackfillDisableResponse(rsp)
}

// PostTenantTenantIdExtensionsServerlessbackfillEnableWithResponse request returning *PostTenantTenantIdExtensionsServerlessbackfillEnableResponse
func (c *ClientWithResponses) PostTenantTenantIdExtensionsServerlessbackfillEnableWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsServerlessbackfillEnableResponse, error) {
	rsp, err := c.PostTenantTenantIdExtensionsServerlessbackfillEnable(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdExtensionsServerlessbackfillEnableResponse(rsp)
}

// PostTenantTenantIdExtensionsServerlessbackfillVersionWithBodyWithResponse request with arbitrary body returning *PostTenantTenantIdExtensionsServerlessbackfillVersionResponse
func (c *ClientWithResponses) PostTenantTenantIdExtensionsServerlessbackfillVersionWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsServerlessbackfillVersionResponse, error) {
	rsp, err := c.PostTenantTenantIdExtensionsServerlessbackfillVersionWithBody(ctx, tenantId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdExtensionsServerlessbackfillVersionResponse(rsp)
}

func (c *ClientWithResponses) PostTenantTenantIdExtensionsServerlessbackfillVersionWithTextBodyWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdExtensionsServerlessbackfillVersionTextRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsServerlessbackfillVersionResponse, error) {
	rsp, err := c.PostTenantTenantIdExtensionsServerlessbackfillVersionWithTextBody(ctx, tenantId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdExtensionsServerlessbackfillVersionResponse(rsp)
}

// GetTenantTenantIdInfoWithResponse request returning *GetTenantTenantIdInfoResponse
func (c *ClientWithResponses) GetTenantTenantIdInfoWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*GetTenantTenantIdInfoResponse, error) {
	rsp, err := c.GetTenantTenantIdInfo(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetTenantTenantIdInfoResponse(rsp)
}

// GetTenantTenantIdMetricsWithResponse request returning *GetTenantTenantIdMetricsResponse
func (c *ClientWithResponses) GetTenantTenantIdMetricsWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*GetTenantTenantIdMetricsResponse, error) {
	rsp, err := c.GetTenantTenantIdMetrics(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetTenantTenantIdMetricsResponse(rsp)
}

// DeleteTenantTenantIdPrivatelinkPrivateLinkIdWithResponse request returning *DeleteTenantTenantIdPrivatelinkPrivateLinkIdResponse
func (c *ClientWithResponses) DeleteTenantTenantIdPrivatelinkPrivateLinkIdWithResponse(ctx context.Context, tenantId uint64, privateLinkId openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteTenantTenantIdPrivatelinkPrivateLinkIdResponse, error) {
	rsp, err := c.DeleteTenantTenantIdPrivatelinkPrivateLinkId(ctx, tenantId, privateLinkId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteTenantTenantIdPrivatelinkPrivateLinkIdResponse(rsp)
}

// GetTenantTenantIdPrivatelinkPrivateLinkIdWithResponse request returning *GetTenantTenantIdPrivatelinkPrivateLinkIdResponse
func (c *ClientWithResponses) GetTenantTenantIdPrivatelinkPrivateLinkIdWithResponse(ctx context.Context, tenantId uint64, privateLinkId openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetTenantTenantIdPrivatelinkPrivateLinkIdResponse, error) {
	rsp, err := c.GetTenantTenantIdPrivatelinkPrivateLinkId(ctx, tenantId, privateLinkId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetTenantTenantIdPrivatelinkPrivateLinkIdResponse(rsp)
}

// PostTenantTenantIdPrivatelinksWithBodyWithResponse request with arbitrary body returning *PostTenantTenantIdPrivatelinksResponse
func (c *ClientWithResponses) PostTenantTenantIdPrivatelinksWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdPrivatelinksResponse, error) {
	rsp, err := c.PostTenantTenantIdPrivatelinksWithBody(ctx, tenantId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdPrivatelinksResponse(rsp)
}

func (c *ClientWithResponses) PostTenantTenantIdPrivatelinksWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdPrivatelinksJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdPrivatelinksResponse, error) {
	rsp, err := c.PostTenantTenantIdPrivatelinks(ctx, tenantId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdPrivatelinksResponse(rsp)
}

// DeleteTenantTenantIdRelationWithBodyWithResponse request with arbitrary body returning *DeleteTenantTenantIdRelationResponse
func (c *ClientWithResponses) DeleteTenantTenantIdRelationWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*DeleteTenantTenantIdRelationResponse, error) {
	rsp, err := c.DeleteTenantTenantIdRelationWithBody(ctx, tenantId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteTenantTenantIdRelationResponse(rsp)
}

func (c *ClientWithResponses) DeleteTenantTenantIdRelationWithResponse(ctx context.Context, tenantId uint64, body DeleteTenantTenantIdRelationJSONRequestBody, reqEditors ...RequestEditorFn) (*DeleteTenantTenantIdRelationResponse, error) {
	rsp, err := c.DeleteTenantTenantIdRelation(ctx, tenantId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteTenantTenantIdRelationResponse(rsp)
}

// GetTenantTenantIdRelationsGetCountsWithResponse request returning *GetTenantTenantIdRelationsGetCountsResponse
func (c *ClientWithResponses) GetTenantTenantIdRelationsGetCountsWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*GetTenantTenantIdRelationsGetCountsResponse, error) {
	rsp, err := c.GetTenantTenantIdRelationsGetCounts(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetTenantTenantIdRelationsGetCountsResponse(rsp)
}

// PostTenantTenantIdResourceWithBodyWithResponse request with arbitrary body returning *PostTenantTenantIdResourceResponse
func (c *ClientWithResponses) PostTenantTenantIdResourceWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdResourceResponse, error) {
	rsp, err := c.PostTenantTenantIdResourceWithBody(ctx, tenantId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdResourceResponse(rsp)
}

func (c *ClientWithResponses) PostTenantTenantIdResourceWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdResourceJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdResourceResponse, error) {
	rsp, err := c.PostTenantTenantIdResource(ctx, tenantId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdResourceResponse(rsp)
}

// PostTenantTenantIdSourcesFetchKafkaTopicWithBodyWithResponse request with arbitrary body returning *PostTenantTenantIdSourcesFetchKafkaTopicResponse
func (c *ClientWithResponses) PostTenantTenantIdSourcesFetchKafkaTopicWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdSourcesFetchKafkaTopicResponse, error) {
	rsp, err := c.PostTenantTenantIdSourcesFetchKafkaTopicWithBody(ctx, tenantId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdSourcesFetchKafkaTopicResponse(rsp)
}

func (c *ClientWithResponses) PostTenantTenantIdSourcesFetchKafkaTopicWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdSourcesFetchKafkaTopicJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdSourcesFetchKafkaTopicResponse, error) {
	rsp, err := c.PostTenantTenantIdSourcesFetchKafkaTopic(ctx, tenantId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdSourcesFetchKafkaTopicResponse(rsp)
}

// PostTenantTenantIdSourcesPingWithBodyWithResponse request with arbitrary body returning *PostTenantTenantIdSourcesPingResponse
func (c *ClientWithResponses) PostTenantTenantIdSourcesPingWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdSourcesPingResponse, error) {
	rsp, err := c.PostTenantTenantIdSourcesPingWithBody(ctx, tenantId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdSourcesPingResponse(rsp)
}

func (c *ClientWithResponses) PostTenantTenantIdSourcesPingWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdSourcesPingJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdSourcesPingResponse, error) {
	rsp, err := c.PostTenantTenantIdSourcesPing(ctx, tenantId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdSourcesPingResponse(rsp)
}

// PutTenantTenantIdStartWithResponse request returning *PutTenantTenantIdStartResponse
func (c *ClientWithResponses) PutTenantTenantIdStartWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PutTenantTenantIdStartResponse, error) {
	rsp, err := c.PutTenantTenantIdStart(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutTenantTenantIdStartResponse(rsp)
}

// PutTenantTenantIdStopWithResponse request returning *PutTenantTenantIdStopResponse
func (c *ClientWithResponses) PutTenantTenantIdStopWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PutTenantTenantIdStopResponse, error) {
	rsp, err := c.PutTenantTenantIdStop(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutTenantTenantIdStopResponse(rsp)
}

// PutTenantTenantIdUpdateCfgConfigIdWithResponse request returning *PutTenantTenantIdUpdateCfgConfigIdResponse
func (c *ClientWithResponses) PutTenantTenantIdUpdateCfgConfigIdWithResponse(ctx context.Context, tenantId uint64, configId openapi_types.UUID, reqEditors ...RequestEditorFn) (*PutTenantTenantIdUpdateCfgConfigIdResponse, error) {
	rsp, err := c.PutTenantTenantIdUpdateCfgConfigId(ctx, tenantId, configId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutTenantTenantIdUpdateCfgConfigIdResponse(rsp)
}

// PostTenantTenantIdUpdateVersionWithBodyWithResponse request with arbitrary body returning *PostTenantTenantIdUpdateVersionResponse
func (c *ClientWithResponses) PostTenantTenantIdUpdateVersionWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdUpdateVersionResponse, error) {
	rsp, err := c.PostTenantTenantIdUpdateVersionWithBody(ctx, tenantId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdUpdateVersionResponse(rsp)
}

func (c *ClientWithResponses) PostTenantTenantIdUpdateVersionWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdUpdateVersionJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdUpdateVersionResponse, error) {
	rsp, err := c.PostTenantTenantIdUpdateVersion(ctx, tenantId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdUpdateVersionResponse(rsp)
}

// GetTenantsWithResponse request returning *GetTenantsResponse
func (c *ClientWithResponses) GetTenantsWithResponse(ctx context.Context, params *GetTenantsParams, reqEditors ...RequestEditorFn) (*GetTenantsResponse, error) {
	rsp, err := c.GetTenants(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetTenantsResponse(rsp)
}

// PostTenantsWithBodyWithResponse request with arbitrary body returning *PostTenantsResponse
func (c *ClientWithResponses) PostTenantsWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantsResponse, error) {
	rsp, err := c.PostTenantsWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantsResponse(rsp)
}

func (c *ClientWithResponses) PostTenantsWithResponse(ctx context.Context, body PostTenantsJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantsResponse, error) {
	rsp, err := c.PostTenants(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantsResponse(rsp)
}

// GetTenantsStatusWithResponse request returning *GetTenantsStatusResponse
func (c *ClientWithResponses) GetTenantsStatusWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetTenantsStatusResponse, error) {
	rsp, err := c.GetTenantsStatus(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetTenantsStatusResponse(rsp)
}

// PostTenantsTenantIdUnquiesceWithResponse request returning *PostTenantsTenantIdUnquiesceResponse
func (c *ClientWithResponses) PostTenantsTenantIdUnquiesceWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PostTenantsTenantIdUnquiesceResponse, error) {
	rsp, err := c.PostTenantsTenantIdUnquiesce(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantsTenantIdUnquiesceResponse(rsp)
}

// GetTiersWithResponse request returning *GetTiersResponse
func (c *ClientWithResponses) GetTiersWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetTiersResponse, error) {
	rsp, err := c.GetTiers(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetTiersResponse(rsp)
}

// GetVersionWithResponse request returning *GetVersionResponse
func (c *ClientWithResponses) GetVersionWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetVersionResponse, error) {
	rsp, err := c.GetVersion(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetVersionResponse(rsp)
}

// ParseGetResponse parses an HTTP response from a GetWithResponse call
func ParseGetResponse(rsp *http.Response) (*GetResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseGetAvailabilityResponse parses an HTTP response from a GetAvailabilityWithResponse call
func ParseGetAvailabilityResponse(rsp *http.Response) (*GetAvailabilityResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetAvailabilityResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest AvailabilityResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest AvailabilityResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseDeleteByocClusterNameResponse parses an HTTP response from a DeleteByocClusterNameWithResponse call
func ParseDeleteByocClusterNameResponse(rsp *http.Response) (*DeleteByocClusterNameResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteByocClusterNameResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetByocClusterNameResponse parses an HTTP response from a GetByocClusterNameWithResponse call
func ParseGetByocClusterNameResponse(rsp *http.Response) (*GetByocClusterNameResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetByocClusterNameResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ManagedCluster
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePutByocClusterNameResponse parses an HTTP response from a PutByocClusterNameWithResponse call
func ParsePutByocClusterNameResponse(rsp *http.Response) (*PutByocClusterNameResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutByocClusterNameResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 409:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON409 = &dest

	}

	return response, nil
}

// ParsePostByocClusterNameManualUpdateResponse parses an HTTP response from a PostByocClusterNameManualUpdateWithResponse call
func ParsePostByocClusterNameManualUpdateResponse(rsp *http.Response) (*PostByocClusterNameManualUpdateResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostByocClusterNameManualUpdateResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostByocClusterNameUpdateResponse parses an HTTP response from a PostByocClusterNameUpdateWithResponse call
func ParsePostByocClusterNameUpdateResponse(rsp *http.Response) (*PostByocClusterNameUpdateResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostByocClusterNameUpdateResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetByocClustersResponse parses an HTTP response from a GetByocClustersWithResponse call
func ParseGetByocClustersResponse(rsp *http.Response) (*GetByocClustersResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetByocClustersResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ManagedClustersSizePage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePostByocClustersResponse parses an HTTP response from a PostByocClustersWithResponse call
func ParsePostByocClustersResponse(rsp *http.Response) (*PostByocClustersResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostByocClustersResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 409:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON409 = &dest

	}

	return response, nil
}

// ParseGetEndpointsResponse parses an HTTP response from a GetEndpointsWithResponse call
func ParseGetEndpointsResponse(rsp *http.Response) (*GetEndpointsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetEndpointsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest Endpoint
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseQueryErrLogResponse parses an HTTP response from a QueryErrLogWithResponse call
func ParseQueryErrLogResponse(rsp *http.Response) (*QueryErrLogResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &QueryErrLogResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ErrLogQueryResult
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	}

	return response, nil
}

// ParseGetMetricsResponse parses an HTTP response from a GetMetricsWithResponse call
func ParseGetMetricsResponse(rsp *http.Response) (*GetMetricsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetMetricsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest Metrics
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 416:
		var dest RangeNotSatisfiable
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON416 = &dest

	}

	return response, nil
}

// ParseGetRootcaResponse parses an HTTP response from a GetRootcaWithResponse call
func ParseGetRootcaResponse(rsp *http.Response) (*GetRootcaResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetRootcaResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParsePostSourcePingResponse parses an HTTP response from a PostSourcePingWithResponse call
func ParsePostSourcePingResponse(rsp *http.Response) (*PostSourcePingResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostSourcePingResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest AvailabilityResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	}

	return response, nil
}

// ParseDeleteTenantResponse parses an HTTP response from a DeleteTenantWithResponse call
func ParseDeleteTenantResponse(rsp *http.Response) (*DeleteTenantResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteTenantResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 403:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON403 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetTenantResponse parses an HTTP response from a GetTenantWithResponse call
func ParseGetTenantResponse(rsp *http.Response) (*GetTenantResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetTenantResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest Tenant
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseDeleteTenantDbusersResponse parses an HTTP response from a DeleteTenantDbusersWithResponse call
func ParseDeleteTenantDbusersResponse(rsp *http.Response) (*DeleteTenantDbusersResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteTenantDbusersResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest FailedPreconditionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetTenantDbusersResponse parses an HTTP response from a GetTenantDbusersWithResponse call
func ParseGetTenantDbusersResponse(rsp *http.Response) (*GetTenantDbusersResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetTenantDbusersResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DBUsers
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostTenantDbusersResponse parses an HTTP response from a PostTenantDbusersWithResponse call
func ParsePostTenantDbusersResponse(rsp *http.Response) (*PostTenantDbusersResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantDbusersResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DBUser
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest FailedPreconditionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePutTenantDbusersResponse parses an HTTP response from a PutTenantDbusersWithResponse call
func ParsePutTenantDbusersResponse(rsp *http.Response) (*PutTenantDbusersResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutTenantDbusersResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest FailedPreconditionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetTenantTagsResponse parses an HTTP response from a GetTenantTagsWithResponse call
func ParseGetTenantTagsResponse(rsp *http.Response) (*GetTenantTagsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetTenantTagsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest GetImageTagResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseGetTenantTenantIdBackupResponse parses an HTTP response from a GetTenantTenantIdBackupWithResponse call
func ParseGetTenantTenantIdBackupResponse(rsp *http.Response) (*GetTenantTenantIdBackupResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetTenantTenantIdBackupResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest BackupSnapshotsSizePage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdBackupResponse parses an HTTP response from a PostTenantTenantIdBackupWithResponse call
func ParsePostTenantTenantIdBackupResponse(rsp *http.Response) (*PostTenantTenantIdBackupResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdBackupResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest PostSnapshotResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	}

	return response, nil
}

// ParseDeleteTenantTenantIdBackupSnapshotIdResponse parses an HTTP response from a DeleteTenantTenantIdBackupSnapshotIdWithResponse call
func ParseDeleteTenantTenantIdBackupSnapshotIdResponse(rsp *http.Response) (*DeleteTenantTenantIdBackupSnapshotIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteTenantTenantIdBackupSnapshotIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParsePostTenantTenantIdBackupSnapshotIdRestoreResponse parses an HTTP response from a PostTenantTenantIdBackupSnapshotIdRestoreWithResponse call
func ParsePostTenantTenantIdBackupSnapshotIdRestoreResponse(rsp *http.Response) (*PostTenantTenantIdBackupSnapshotIdRestoreResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdBackupSnapshotIdRestoreResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParsePutTenantTenantIdConfigEtcdResponse parses an HTTP response from a PutTenantTenantIdConfigEtcdWithResponse call
func ParsePutTenantTenantIdConfigEtcdResponse(rsp *http.Response) (*PutTenantTenantIdConfigEtcdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutTenantTenantIdConfigEtcdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 409:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON409 = &dest

	}

	return response, nil
}

// ParsePutTenantTenantIdConfigRisingwaveResponse parses an HTTP response from a PutTenantTenantIdConfigRisingwaveWithResponse call
func ParsePutTenantTenantIdConfigRisingwaveResponse(rsp *http.Response) (*PutTenantTenantIdConfigRisingwaveResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutTenantTenantIdConfigRisingwaveResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 409:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON409 = &dest

	}

	return response, nil
}

// ParseGetTenantTenantIdDatabasesResponse parses an HTTP response from a GetTenantTenantIdDatabasesWithResponse call
func ParseGetTenantTenantIdDatabasesResponse(rsp *http.Response) (*GetTenantTenantIdDatabasesResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetTenantTenantIdDatabasesResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest GetDatabasesResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdExecutionResponse parses an HTTP response from a PostTenantTenantIdExecutionWithResponse call
func ParsePostTenantTenantIdExecutionResponse(rsp *http.Response) (*PostTenantTenantIdExecutionResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdExecutionResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest FailedPreconditionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdExtensionsServerlessbackfillDisableResponse parses an HTTP response from a PostTenantTenantIdExtensionsServerlessbackfillDisableWithResponse call
func ParsePostTenantTenantIdExtensionsServerlessbackfillDisableResponse(rsp *http.Response) (*PostTenantTenantIdExtensionsServerlessbackfillDisableResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdExtensionsServerlessbackfillDisableResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest WorkflowIdResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest FailedPreconditionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdExtensionsServerlessbackfillEnableResponse parses an HTTP response from a PostTenantTenantIdExtensionsServerlessbackfillEnableWithResponse call
func ParsePostTenantTenantIdExtensionsServerlessbackfillEnableResponse(rsp *http.Response) (*PostTenantTenantIdExtensionsServerlessbackfillEnableResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdExtensionsServerlessbackfillEnableResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest WorkflowIdResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest FailedPreconditionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdExtensionsServerlessbackfillVersionResponse parses an HTTP response from a PostTenantTenantIdExtensionsServerlessbackfillVersionWithResponse call
func ParsePostTenantTenantIdExtensionsServerlessbackfillVersionResponse(rsp *http.Response) (*PostTenantTenantIdExtensionsServerlessbackfillVersionResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdExtensionsServerlessbackfillVersionResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest WorkflowIdResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest FailedPreconditionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetTenantTenantIdInfoResponse parses an HTTP response from a GetTenantTenantIdInfoWithResponse call
func ParseGetTenantTenantIdInfoResponse(rsp *http.Response) (*GetTenantTenantIdInfoResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetTenantTenantIdInfoResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest TenantClusterInfoResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseGetTenantTenantIdMetricsResponse parses an HTTP response from a GetTenantTenantIdMetricsWithResponse call
func ParseGetTenantTenantIdMetricsResponse(rsp *http.Response) (*GetTenantTenantIdMetricsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetTenantTenantIdMetricsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseDeleteTenantTenantIdPrivatelinkPrivateLinkIdResponse parses an HTTP response from a DeleteTenantTenantIdPrivatelinkPrivateLinkIdWithResponse call
func ParseDeleteTenantTenantIdPrivatelinkPrivateLinkIdResponse(rsp *http.Response) (*DeleteTenantTenantIdPrivatelinkPrivateLinkIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteTenantTenantIdPrivatelinkPrivateLinkIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	}

	return response, nil
}

// ParseGetTenantTenantIdPrivatelinkPrivateLinkIdResponse parses an HTTP response from a GetTenantTenantIdPrivatelinkPrivateLinkIdWithResponse call
func ParseGetTenantTenantIdPrivatelinkPrivateLinkIdResponse(rsp *http.Response) (*GetTenantTenantIdPrivatelinkPrivateLinkIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetTenantTenantIdPrivatelinkPrivateLinkIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest PrivateLink
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdPrivatelinksResponse parses an HTTP response from a PostTenantTenantIdPrivatelinksWithResponse call
func ParsePostTenantTenantIdPrivatelinksResponse(rsp *http.Response) (*PostTenantTenantIdPrivatelinksResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdPrivatelinksResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest PostPrivateLinkResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	}

	return response, nil
}

// ParseDeleteTenantTenantIdRelationResponse parses an HTTP response from a DeleteTenantTenantIdRelationWithResponse call
func ParseDeleteTenantTenantIdRelationResponse(rsp *http.Response) (*DeleteTenantTenantIdRelationResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteTenantTenantIdRelationResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest FailedPreconditionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetTenantTenantIdRelationsGetCountsResponse parses an HTTP response from a GetTenantTenantIdRelationsGetCountsWithResponse call
func ParseGetTenantTenantIdRelationsGetCountsResponse(rsp *http.Response) (*GetTenantTenantIdRelationsGetCountsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetTenantTenantIdRelationsGetCountsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest RelationCounts
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdResourceResponse parses an HTTP response from a PostTenantTenantIdResourceWithResponse call
func ParsePostTenantTenantIdResourceResponse(rsp *http.Response) (*PostTenantTenantIdResourceResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdResourceResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdSourcesFetchKafkaTopicResponse parses an HTTP response from a PostTenantTenantIdSourcesFetchKafkaTopicWithResponse call
func ParsePostTenantTenantIdSourcesFetchKafkaTopicResponse(rsp *http.Response) (*PostTenantTenantIdSourcesFetchKafkaTopicResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdSourcesFetchKafkaTopicResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest PostSourcesFetchKafkaTopicResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdSourcesPingResponse parses an HTTP response from a PostTenantTenantIdSourcesPingWithResponse call
func ParsePostTenantTenantIdSourcesPingResponse(rsp *http.Response) (*PostTenantTenantIdSourcesPingResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdSourcesPingResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest AvailabilityResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	}

	return response, nil
}

// ParsePutTenantTenantIdStartResponse parses an HTTP response from a PutTenantTenantIdStartWithResponse call
func ParsePutTenantTenantIdStartResponse(rsp *http.Response) (*PutTenantTenantIdStartResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutTenantTenantIdStartResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 409:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON409 = &dest

	}

	return response, nil
}

// ParsePutTenantTenantIdStopResponse parses an HTTP response from a PutTenantTenantIdStopWithResponse call
func ParsePutTenantTenantIdStopResponse(rsp *http.Response) (*PutTenantTenantIdStopResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutTenantTenantIdStopResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 409:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON409 = &dest

	}

	return response, nil
}

// ParsePutTenantTenantIdUpdateCfgConfigIdResponse parses an HTTP response from a PutTenantTenantIdUpdateCfgConfigIdWithResponse call
func ParsePutTenantTenantIdUpdateCfgConfigIdResponse(rsp *http.Response) (*PutTenantTenantIdUpdateCfgConfigIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutTenantTenantIdUpdateCfgConfigIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 409:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON409 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdUpdateVersionResponse parses an HTTP response from a PostTenantTenantIdUpdateVersionWithResponse call
func ParsePostTenantTenantIdUpdateVersionResponse(rsp *http.Response) (*PostTenantTenantIdUpdateVersionResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdUpdateVersionResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 409:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON409 = &dest

	}

	return response, nil
}

// ParseGetTenantsResponse parses an HTTP response from a GetTenantsWithResponse call
func ParseGetTenantsResponse(rsp *http.Response) (*GetTenantsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetTenantsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest TenantSizePage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePostTenantsResponse parses an HTTP response from a PostTenantsWithResponse call
func ParsePostTenantsResponse(rsp *http.Response) (*PostTenantsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest CreateTenantResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseGetTenantsStatusResponse parses an HTTP response from a GetTenantsStatusWithResponse call
func ParseGetTenantsStatusResponse(rsp *http.Response) (*GetTenantsStatusResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetTenantsStatusResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest TenantStatusCounts
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePostTenantsTenantIdUnquiesceResponse parses an HTTP response from a PostTenantsTenantIdUnquiesceWithResponse call
func ParsePostTenantsTenantIdUnquiesceResponse(rsp *http.Response) (*PostTenantsTenantIdUnquiesceResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantsTenantIdUnquiesceResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseGetTiersResponse parses an HTTP response from a GetTiersWithResponse call
func ParseGetTiersResponse(rsp *http.Response) (*GetTiersResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetTiersResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest Tiers
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseGetVersionResponse parses an HTTP response from a GetVersionWithResponse call
func ParseGetVersionResponse(rsp *http.Response) (*GetVersionResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetVersionResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ServerInterface represents all server handlers.
type ServerInterface interface {

	// (GET /)
	Get(c *gin.Context)

	// (GET /availability)
	GetAvailability(c *gin.Context, params GetAvailabilityParams)
	// Delete a BYOC cluster
	// (DELETE /byoc-cluster/{name})
	DeleteByocClusterName(c *gin.Context, name string)
	// Get a BYOC cluster
	// (GET /byoc-cluster/{name})
	GetByocClusterName(c *gin.Context, name string)
	// Update a BYOC cluster
	// (PUT /byoc-cluster/{name})
	PutByocClusterName(c *gin.Context, name string)
	// update the BYOC cluster version, if version is not specified, update it to the default version
	// (POST /byoc-cluster/{name}/manualUpdate)
	PostByocClusterNameManualUpdate(c *gin.Context, name string)
	// update the BYOC cluster version, if version is not specified, update it to the default version
	// (POST /byoc-cluster/{name}/update)
	PostByocClusterNameUpdate(c *gin.Context, name string)
	// List BYOC clusters
	// (GET /byoc-clusters)
	GetByocClusters(c *gin.Context, params GetByocClustersParams)
	// Create a BYOC cluster
	// (POST /byoc-clusters)
	PostByocClusters(c *gin.Context)
	// Get an endpoint of tenant by tenantName or tenantId
	// (GET /endpoints)
	GetEndpoints(c *gin.Context, params GetEndpointsParams)
	// Query error logs over a range of time
	// (GET /log/queryError)
	QueryErrLog(c *gin.Context, params QueryErrLogParams)
	// Get metrics by tenant ID
	// (GET /metrics)
	GetMetrics(c *gin.Context, params GetMetricsParams)

	// (GET /rootca)
	GetRootca(c *gin.Context)

	// (POST /source/ping)
	PostSourcePing(c *gin.Context)
	// delete a tenant by tenantId or tenantName
	// (DELETE /tenant)
	DeleteTenant(c *gin.Context, params DeleteTenantParams)
	// get a tenant by tenantId or tenantName
	// (GET /tenant)
	GetTenant(c *gin.Context, params GetTenantParams)
	// delete database user by name
	// (DELETE /tenant/dbusers)
	DeleteTenantDbusers(c *gin.Context, params DeleteTenantDbusersParams)

	// (GET /tenant/dbusers)
	GetTenantDbusers(c *gin.Context, params GetTenantDbusersParams)
	// Create a database user with options SUPERUSER/NOSUPERUSER, CREATEDB/NOCREATEDB, CREATEUSER/NOCREATEUSER PASSWORD
	// (POST /tenant/dbusers)
	PostTenantDbusers(c *gin.Context)
	// Alter db user's password
	// (PUT /tenant/dbusers)
	PutTenantDbusers(c *gin.Context)

	// (GET /tenant/tags)
	GetTenantTags(c *gin.Context)

	// (GET /tenant/{tenantId}/backup)
	GetTenantTenantIdBackup(c *gin.Context, tenantId uint64, params GetTenantTenantIdBackupParams)

	// (POST /tenant/{tenantId}/backup)
	PostTenantTenantIdBackup(c *gin.Context, tenantId uint64)

	// (DELETE /tenant/{tenantId}/backup/{snapshotId})
	DeleteTenantTenantIdBackupSnapshotId(c *gin.Context, tenantId uint64, snapshotId openapi_types.UUID)

	// (POST /tenant/{tenantId}/backup/{snapshotId}/restore)
	PostTenantTenantIdBackupSnapshotIdRestore(c *gin.Context, tenantId uint64, snapshotId openapi_types.UUID)
	// Apply new etcd configuration.
	// (PUT /tenant/{tenantId}/config/etcd)
	PutTenantTenantIdConfigEtcd(c *gin.Context, tenantId uint64)
	// Apply new risingwave configuration.
	// (PUT /tenant/{tenantId}/config/risingwave)
	PutTenantTenantIdConfigRisingwave(c *gin.Context, tenantId uint64)

	// (GET /tenant/{tenantId}/databases)
	GetTenantTenantIdDatabases(c *gin.Context, tenantId uint64)

	// (POST /tenant/{tenantId}/execution)
	PostTenantTenantIdExecution(c *gin.Context, tenantId uint64)
	// Disable the risingwave extensions for serverless backfilling
	// (POST /tenant/{tenantId}/extensions/serverlessbackfill/disable)
	PostTenantTenantIdExtensionsServerlessbackfillDisable(c *gin.Context, tenantId uint64)
	// Enable the risingwave extensions for serverless backfilling
	// (POST /tenant/{tenantId}/extensions/serverlessbackfill/enable)
	PostTenantTenantIdExtensionsServerlessbackfillEnable(c *gin.Context, tenantId uint64)
	// Update the risingwave extensions for serverless backfilling to version. Version format like 1.0.0.
	// (POST /tenant/{tenantId}/extensions/serverlessbackfill/version)
	PostTenantTenantIdExtensionsServerlessbackfillVersion(c *gin.Context, tenantId uint64)

	// (GET /tenant/{tenantId}/info)
	GetTenantTenantIdInfo(c *gin.Context, tenantId uint64)

	// (GET /tenant/{tenantId}/metrics)
	GetTenantTenantIdMetrics(c *gin.Context, tenantId uint64)
	// delete a private link by id
	// (DELETE /tenant/{tenantId}/privatelink/{privateLinkId})
	DeleteTenantTenantIdPrivatelinkPrivateLinkId(c *gin.Context, tenantId uint64, privateLinkId openapi_types.UUID)
	// get a private link by id
	// (GET /tenant/{tenantId}/privatelink/{privateLinkId})
	GetTenantTenantIdPrivatelinkPrivateLinkId(c *gin.Context, tenantId uint64, privateLinkId openapi_types.UUID)
	// for a tenant to link to the source/sink in the user's VPC
	// (POST /tenant/{tenantId}/privatelinks)
	PostTenantTenantIdPrivatelinks(c *gin.Context, tenantId uint64)

	// (DELETE /tenant/{tenantId}/relation)
	DeleteTenantTenantIdRelation(c *gin.Context, tenantId uint64)

	// (GET /tenant/{tenantId}/relations/getCounts)
	GetTenantTenantIdRelationsGetCounts(c *gin.Context, tenantId uint64)
	// update a tenant resource by tenantId
	// (POST /tenant/{tenantId}/resource)
	PostTenantTenantIdResource(c *gin.Context, tenantId uint64)

	// (POST /tenant/{tenantId}/sources/fetchKafkaTopic)
	PostTenantTenantIdSourcesFetchKafkaTopic(c *gin.Context, tenantId uint64)

	// (POST /tenant/{tenantId}/sources/ping)
	PostTenantTenantIdSourcesPing(c *gin.Context, tenantId uint64)
	// Start a cluster that has been stopped
	// (PUT /tenant/{tenantId}/start)
	PutTenantTenantIdStart(c *gin.Context, tenantId uint64)
	// Stop a running a cluster
	// (PUT /tenant/{tenantId}/stop)
	PutTenantTenantIdStop(c *gin.Context, tenantId uint64)
	// Apply new RisingWave cluster configuration.
	// (PUT /tenant/{tenantId}/update-cfg/{configId})
	PutTenantTenantIdUpdateCfgConfigId(c *gin.Context, tenantId uint64, configId openapi_types.UUID)
	// update the tenant rw version to latest
	// (POST /tenant/{tenantId}/updateVersion)
	PostTenantTenantIdUpdateVersion(c *gin.Context, tenantId uint64)
	// Get all the tenants owned by the user
	// (GET /tenants)
	GetTenants(c *gin.Context, params GetTenantsParams)
	// Create tenants with tenantName
	// (POST /tenants)
	PostTenants(c *gin.Context)

	// (GET /tenants/status)
	GetTenantsStatus(c *gin.Context)
	// Resume a quiesced tenant.
	// (POST /tenants/{tenantId}/unquiesce)
	PostTenantsTenantIdUnquiesce(c *gin.Context, tenantId uint64)

	// (GET /tiers)
	GetTiers(c *gin.Context)

	// (GET /version)
	GetVersion(c *gin.Context)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandler       func(*gin.Context, error, int)
}

type MiddlewareFunc func(c *gin.Context)

// Get operation middleware
func (siw *ServerInterfaceWrapper) Get(c *gin.Context) {

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.Get(c)
}

// GetAvailability operation middleware
func (siw *ServerInterfaceWrapper) GetAvailability(c *gin.Context) {

	var err error

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetAvailabilityParams

	// ------------- Required query parameter "tenantName" -------------

	if paramValue := c.Query("tenantName"); paramValue != "" {

	} else {
		siw.ErrorHandler(c, fmt.Errorf("Query argument tenantName is required, but not found"), http.StatusBadRequest)
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "tenantName", c.Request.URL.Query(), &params.TenantName)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantName: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetAvailability(c, params)
}

// DeleteByocClusterName operation middleware
func (siw *ServerInterfaceWrapper) DeleteByocClusterName(c *gin.Context) {

	var err error

	// ------------- Path parameter "name" -------------
	var name string

	err = runtime.BindStyledParameterWithOptions("simple", "name", c.Param("name"), &name, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter name: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteByocClusterName(c, name)
}

// GetByocClusterName operation middleware
func (siw *ServerInterfaceWrapper) GetByocClusterName(c *gin.Context) {

	var err error

	// ------------- Path parameter "name" -------------
	var name string

	err = runtime.BindStyledParameterWithOptions("simple", "name", c.Param("name"), &name, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter name: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetByocClusterName(c, name)
}

// PutByocClusterName operation middleware
func (siw *ServerInterfaceWrapper) PutByocClusterName(c *gin.Context) {

	var err error

	// ------------- Path parameter "name" -------------
	var name string

	err = runtime.BindStyledParameterWithOptions("simple", "name", c.Param("name"), &name, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter name: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PutByocClusterName(c, name)
}

// PostByocClusterNameManualUpdate operation middleware
func (siw *ServerInterfaceWrapper) PostByocClusterNameManualUpdate(c *gin.Context) {

	var err error

	// ------------- Path parameter "name" -------------
	var name string

	err = runtime.BindStyledParameterWithOptions("simple", "name", c.Param("name"), &name, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter name: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostByocClusterNameManualUpdate(c, name)
}

// PostByocClusterNameUpdate operation middleware
func (siw *ServerInterfaceWrapper) PostByocClusterNameUpdate(c *gin.Context) {

	var err error

	// ------------- Path parameter "name" -------------
	var name string

	err = runtime.BindStyledParameterWithOptions("simple", "name", c.Param("name"), &name, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter name: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostByocClusterNameUpdate(c, name)
}

// GetByocClusters operation middleware
func (siw *ServerInterfaceWrapper) GetByocClusters(c *gin.Context) {

	var err error

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetByocClustersParams

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", c.Request.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter offset: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", c.Request.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter limit: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetByocClusters(c, params)
}

// PostByocClusters operation middleware
func (siw *ServerInterfaceWrapper) PostByocClusters(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostByocClusters(c)
}

// GetEndpoints operation middleware
func (siw *ServerInterfaceWrapper) GetEndpoints(c *gin.Context) {

	var err error

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetEndpointsParams

	// ------------- Optional query parameter "tenantName" -------------

	err = runtime.BindQueryParameter("form", true, false, "tenantName", c.Request.URL.Query(), &params.TenantName)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantName: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "tenantId" -------------

	err = runtime.BindQueryParameter("form", true, false, "tenantId", c.Request.URL.Query(), &params.TenantId)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetEndpoints(c, params)
}

// QueryErrLog operation middleware
func (siw *ServerInterfaceWrapper) QueryErrLog(c *gin.Context) {

	var err error

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params QueryErrLogParams

	// ------------- Required query parameter "tenantId" -------------

	if paramValue := c.Query("tenantId"); paramValue != "" {

	} else {
		siw.ErrorHandler(c, fmt.Errorf("Query argument tenantId is required, but not found"), http.StatusBadRequest)
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "tenantId", c.Request.URL.Query(), &params.TenantId)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Required query parameter "target" -------------

	if paramValue := c.Query("target"); paramValue != "" {

	} else {
		siw.ErrorHandler(c, fmt.Errorf("Query argument target is required, but not found"), http.StatusBadRequest)
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "target", c.Request.URL.Query(), &params.Target)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter target: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Required query parameter "targetId" -------------

	if paramValue := c.Query("targetId"); paramValue != "" {

	} else {
		siw.ErrorHandler(c, fmt.Errorf("Query argument targetId is required, but not found"), http.StatusBadRequest)
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "targetId", c.Request.URL.Query(), &params.TargetId)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter targetId: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "start" -------------

	err = runtime.BindQueryParameter("form", true, false, "start", c.Request.URL.Query(), &params.Start)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter start: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "end" -------------

	err = runtime.BindQueryParameter("form", true, false, "end", c.Request.URL.Query(), &params.End)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter end: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "direction" -------------

	err = runtime.BindQueryParameter("form", true, false, "direction", c.Request.URL.Query(), &params.Direction)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter direction: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", c.Request.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter limit: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.QueryErrLog(c, params)
}

// GetMetrics operation middleware
func (siw *ServerInterfaceWrapper) GetMetrics(c *gin.Context) {

	var err error

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetMetricsParams

	// ------------- Required query parameter "metric" -------------

	if paramValue := c.Query("metric"); paramValue != "" {

	} else {
		siw.ErrorHandler(c, fmt.Errorf("Query argument metric is required, but not found"), http.StatusBadRequest)
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "metric", c.Request.URL.Query(), &params.Metric)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter metric: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Required query parameter "tenantId" -------------

	if paramValue := c.Query("tenantId"); paramValue != "" {

	} else {
		siw.ErrorHandler(c, fmt.Errorf("Query argument tenantId is required, but not found"), http.StatusBadRequest)
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "tenantId", c.Request.URL.Query(), &params.TenantId)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "component" -------------

	err = runtime.BindQueryParameter("form", true, false, "component", c.Request.URL.Query(), &params.Component)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter component: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "startTime" -------------

	err = runtime.BindQueryParameter("form", true, false, "startTime", c.Request.URL.Query(), &params.StartTime)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter startTime: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "endTime" -------------

	err = runtime.BindQueryParameter("form", true, false, "endTime", c.Request.URL.Query(), &params.EndTime)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter endTime: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "period" -------------

	err = runtime.BindQueryParameter("form", true, false, "period", c.Request.URL.Query(), &params.Period)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter period: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetMetrics(c, params)
}

// GetRootca operation middleware
func (siw *ServerInterfaceWrapper) GetRootca(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetRootca(c)
}

// PostSourcePing operation middleware
func (siw *ServerInterfaceWrapper) PostSourcePing(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostSourcePing(c)
}

// DeleteTenant operation middleware
func (siw *ServerInterfaceWrapper) DeleteTenant(c *gin.Context) {

	var err error

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params DeleteTenantParams

	// ------------- Optional query parameter "tenantId" -------------

	err = runtime.BindQueryParameter("form", true, false, "tenantId", c.Request.URL.Query(), &params.TenantId)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "tenantName" -------------

	err = runtime.BindQueryParameter("form", true, false, "tenantName", c.Request.URL.Query(), &params.TenantName)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantName: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteTenant(c, params)
}

// GetTenant operation middleware
func (siw *ServerInterfaceWrapper) GetTenant(c *gin.Context) {

	var err error

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetTenantParams

	// ------------- Optional query parameter "tenantId" -------------

	err = runtime.BindQueryParameter("form", true, false, "tenantId", c.Request.URL.Query(), &params.TenantId)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "tenantName" -------------

	err = runtime.BindQueryParameter("form", true, false, "tenantName", c.Request.URL.Query(), &params.TenantName)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantName: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetTenant(c, params)
}

// DeleteTenantDbusers operation middleware
func (siw *ServerInterfaceWrapper) DeleteTenantDbusers(c *gin.Context) {

	var err error

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params DeleteTenantDbusersParams

	// ------------- Required query parameter "tenantId" -------------

	if paramValue := c.Query("tenantId"); paramValue != "" {

	} else {
		siw.ErrorHandler(c, fmt.Errorf("Query argument tenantId is required, but not found"), http.StatusBadRequest)
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "tenantId", c.Request.URL.Query(), &params.TenantId)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Required query parameter "username" -------------

	if paramValue := c.Query("username"); paramValue != "" {

	} else {
		siw.ErrorHandler(c, fmt.Errorf("Query argument username is required, but not found"), http.StatusBadRequest)
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "username", c.Request.URL.Query(), &params.Username)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter username: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteTenantDbusers(c, params)
}

// GetTenantDbusers operation middleware
func (siw *ServerInterfaceWrapper) GetTenantDbusers(c *gin.Context) {

	var err error

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetTenantDbusersParams

	// ------------- Required query parameter "tenantId" -------------

	if paramValue := c.Query("tenantId"); paramValue != "" {

	} else {
		siw.ErrorHandler(c, fmt.Errorf("Query argument tenantId is required, but not found"), http.StatusBadRequest)
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "tenantId", c.Request.URL.Query(), &params.TenantId)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetTenantDbusers(c, params)
}

// PostTenantDbusers operation middleware
func (siw *ServerInterfaceWrapper) PostTenantDbusers(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantDbusers(c)
}

// PutTenantDbusers operation middleware
func (siw *ServerInterfaceWrapper) PutTenantDbusers(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PutTenantDbusers(c)
}

// GetTenantTags operation middleware
func (siw *ServerInterfaceWrapper) GetTenantTags(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetTenantTags(c)
}

// GetTenantTenantIdBackup operation middleware
func (siw *ServerInterfaceWrapper) GetTenantTenantIdBackup(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetTenantTenantIdBackupParams

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", c.Request.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter offset: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", c.Request.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter limit: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetTenantTenantIdBackup(c, tenantId, params)
}

// PostTenantTenantIdBackup operation middleware
func (siw *ServerInterfaceWrapper) PostTenantTenantIdBackup(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantTenantIdBackup(c, tenantId)
}

// DeleteTenantTenantIdBackupSnapshotId operation middleware
func (siw *ServerInterfaceWrapper) DeleteTenantTenantIdBackupSnapshotId(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "snapshotId" -------------
	var snapshotId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "snapshotId", c.Param("snapshotId"), &snapshotId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter snapshotId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteTenantTenantIdBackupSnapshotId(c, tenantId, snapshotId)
}

// PostTenantTenantIdBackupSnapshotIdRestore operation middleware
func (siw *ServerInterfaceWrapper) PostTenantTenantIdBackupSnapshotIdRestore(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "snapshotId" -------------
	var snapshotId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "snapshotId", c.Param("snapshotId"), &snapshotId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter snapshotId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantTenantIdBackupSnapshotIdRestore(c, tenantId, snapshotId)
}

// PutTenantTenantIdConfigEtcd operation middleware
func (siw *ServerInterfaceWrapper) PutTenantTenantIdConfigEtcd(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PutTenantTenantIdConfigEtcd(c, tenantId)
}

// PutTenantTenantIdConfigRisingwave operation middleware
func (siw *ServerInterfaceWrapper) PutTenantTenantIdConfigRisingwave(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PutTenantTenantIdConfigRisingwave(c, tenantId)
}

// GetTenantTenantIdDatabases operation middleware
func (siw *ServerInterfaceWrapper) GetTenantTenantIdDatabases(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetTenantTenantIdDatabases(c, tenantId)
}

// PostTenantTenantIdExecution operation middleware
func (siw *ServerInterfaceWrapper) PostTenantTenantIdExecution(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantTenantIdExecution(c, tenantId)
}

// PostTenantTenantIdExtensionsServerlessbackfillDisable operation middleware
func (siw *ServerInterfaceWrapper) PostTenantTenantIdExtensionsServerlessbackfillDisable(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantTenantIdExtensionsServerlessbackfillDisable(c, tenantId)
}

// PostTenantTenantIdExtensionsServerlessbackfillEnable operation middleware
func (siw *ServerInterfaceWrapper) PostTenantTenantIdExtensionsServerlessbackfillEnable(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantTenantIdExtensionsServerlessbackfillEnable(c, tenantId)
}

// PostTenantTenantIdExtensionsServerlessbackfillVersion operation middleware
func (siw *ServerInterfaceWrapper) PostTenantTenantIdExtensionsServerlessbackfillVersion(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: false})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantTenantIdExtensionsServerlessbackfillVersion(c, tenantId)
}

// GetTenantTenantIdInfo operation middleware
func (siw *ServerInterfaceWrapper) GetTenantTenantIdInfo(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetTenantTenantIdInfo(c, tenantId)
}

// GetTenantTenantIdMetrics operation middleware
func (siw *ServerInterfaceWrapper) GetTenantTenantIdMetrics(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetTenantTenantIdMetrics(c, tenantId)
}

// DeleteTenantTenantIdPrivatelinkPrivateLinkId operation middleware
func (siw *ServerInterfaceWrapper) DeleteTenantTenantIdPrivatelinkPrivateLinkId(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "privateLinkId" -------------
	var privateLinkId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "privateLinkId", c.Param("privateLinkId"), &privateLinkId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter privateLinkId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteTenantTenantIdPrivatelinkPrivateLinkId(c, tenantId, privateLinkId)
}

// GetTenantTenantIdPrivatelinkPrivateLinkId operation middleware
func (siw *ServerInterfaceWrapper) GetTenantTenantIdPrivatelinkPrivateLinkId(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "privateLinkId" -------------
	var privateLinkId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "privateLinkId", c.Param("privateLinkId"), &privateLinkId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter privateLinkId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetTenantTenantIdPrivatelinkPrivateLinkId(c, tenantId, privateLinkId)
}

// PostTenantTenantIdPrivatelinks operation middleware
func (siw *ServerInterfaceWrapper) PostTenantTenantIdPrivatelinks(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantTenantIdPrivatelinks(c, tenantId)
}

// DeleteTenantTenantIdRelation operation middleware
func (siw *ServerInterfaceWrapper) DeleteTenantTenantIdRelation(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteTenantTenantIdRelation(c, tenantId)
}

// GetTenantTenantIdRelationsGetCounts operation middleware
func (siw *ServerInterfaceWrapper) GetTenantTenantIdRelationsGetCounts(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetTenantTenantIdRelationsGetCounts(c, tenantId)
}

// PostTenantTenantIdResource operation middleware
func (siw *ServerInterfaceWrapper) PostTenantTenantIdResource(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantTenantIdResource(c, tenantId)
}

// PostTenantTenantIdSourcesFetchKafkaTopic operation middleware
func (siw *ServerInterfaceWrapper) PostTenantTenantIdSourcesFetchKafkaTopic(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantTenantIdSourcesFetchKafkaTopic(c, tenantId)
}

// PostTenantTenantIdSourcesPing operation middleware
func (siw *ServerInterfaceWrapper) PostTenantTenantIdSourcesPing(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantTenantIdSourcesPing(c, tenantId)
}

// PutTenantTenantIdStart operation middleware
func (siw *ServerInterfaceWrapper) PutTenantTenantIdStart(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PutTenantTenantIdStart(c, tenantId)
}

// PutTenantTenantIdStop operation middleware
func (siw *ServerInterfaceWrapper) PutTenantTenantIdStop(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PutTenantTenantIdStop(c, tenantId)
}

// PutTenantTenantIdUpdateCfgConfigId operation middleware
func (siw *ServerInterfaceWrapper) PutTenantTenantIdUpdateCfgConfigId(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "configId" -------------
	var configId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "configId", c.Param("configId"), &configId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter configId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PutTenantTenantIdUpdateCfgConfigId(c, tenantId, configId)
}

// PostTenantTenantIdUpdateVersion operation middleware
func (siw *ServerInterfaceWrapper) PostTenantTenantIdUpdateVersion(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantTenantIdUpdateVersion(c, tenantId)
}

// GetTenants operation middleware
func (siw *ServerInterfaceWrapper) GetTenants(c *gin.Context) {

	var err error

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetTenantsParams

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", c.Request.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter offset: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", c.Request.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter limit: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetTenants(c, params)
}

// PostTenants operation middleware
func (siw *ServerInterfaceWrapper) PostTenants(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenants(c)
}

// GetTenantsStatus operation middleware
func (siw *ServerInterfaceWrapper) GetTenantsStatus(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetTenantsStatus(c)
}

// PostTenantsTenantIdUnquiesce operation middleware
func (siw *ServerInterfaceWrapper) PostTenantsTenantIdUnquiesce(c *gin.Context) {

	var err error

	// ------------- Path parameter "tenantId" -------------
	var tenantId uint64

	err = runtime.BindStyledParameterWithOptions("simple", "tenantId", c.Param("tenantId"), &tenantId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter tenantId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTenantsTenantIdUnquiesce(c, tenantId)
}

// GetTiers operation middleware
func (siw *ServerInterfaceWrapper) GetTiers(c *gin.Context) {

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetTiers(c)
}

// GetVersion operation middleware
func (siw *ServerInterfaceWrapper) GetVersion(c *gin.Context) {

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetVersion(c)
}

// GinServerOptions provides options for the Gin server.
type GinServerOptions struct {
	BaseURL      string
	Middlewares  []MiddlewareFunc
	ErrorHandler func(*gin.Context, error, int)
}

// RegisterHandlers creates http.Handler with routing matching OpenAPI spec.
func RegisterHandlers(router gin.IRouter, si ServerInterface) {
	RegisterHandlersWithOptions(router, si, GinServerOptions{})
}

// RegisterHandlersWithOptions creates http.Handler with additional options
func RegisterHandlersWithOptions(router gin.IRouter, si ServerInterface, options GinServerOptions) {
	errorHandler := options.ErrorHandler
	if errorHandler == nil {
		errorHandler = func(c *gin.Context, err error, statusCode int) {
			c.JSON(statusCode, gin.H{"msg": err.Error()})
		}
	}

	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandler:       errorHandler,
	}

	router.GET(options.BaseURL+"/", wrapper.Get)
	router.GET(options.BaseURL+"/availability", wrapper.GetAvailability)
	router.DELETE(options.BaseURL+"/byoc-cluster/:name", wrapper.DeleteByocClusterName)
	router.GET(options.BaseURL+"/byoc-cluster/:name", wrapper.GetByocClusterName)
	router.PUT(options.BaseURL+"/byoc-cluster/:name", wrapper.PutByocClusterName)
	router.POST(options.BaseURL+"/byoc-cluster/:name/manualUpdate", wrapper.PostByocClusterNameManualUpdate)
	router.POST(options.BaseURL+"/byoc-cluster/:name/update", wrapper.PostByocClusterNameUpdate)
	router.GET(options.BaseURL+"/byoc-clusters", wrapper.GetByocClusters)
	router.POST(options.BaseURL+"/byoc-clusters", wrapper.PostByocClusters)
	router.GET(options.BaseURL+"/endpoints", wrapper.GetEndpoints)
	router.GET(options.BaseURL+"/log/queryError", wrapper.QueryErrLog)
	router.GET(options.BaseURL+"/metrics", wrapper.GetMetrics)
	router.GET(options.BaseURL+"/rootca", wrapper.GetRootca)
	router.POST(options.BaseURL+"/source/ping", wrapper.PostSourcePing)
	router.DELETE(options.BaseURL+"/tenant", wrapper.DeleteTenant)
	router.GET(options.BaseURL+"/tenant", wrapper.GetTenant)
	router.DELETE(options.BaseURL+"/tenant/dbusers", wrapper.DeleteTenantDbusers)
	router.GET(options.BaseURL+"/tenant/dbusers", wrapper.GetTenantDbusers)
	router.POST(options.BaseURL+"/tenant/dbusers", wrapper.PostTenantDbusers)
	router.PUT(options.BaseURL+"/tenant/dbusers", wrapper.PutTenantDbusers)
	router.GET(options.BaseURL+"/tenant/tags", wrapper.GetTenantTags)
	router.GET(options.BaseURL+"/tenant/:tenantId/backup", wrapper.GetTenantTenantIdBackup)
	router.POST(options.BaseURL+"/tenant/:tenantId/backup", wrapper.PostTenantTenantIdBackup)
	router.DELETE(options.BaseURL+"/tenant/:tenantId/backup/:snapshotId", wrapper.DeleteTenantTenantIdBackupSnapshotId)
	router.POST(options.BaseURL+"/tenant/:tenantId/backup/:snapshotId/restore", wrapper.PostTenantTenantIdBackupSnapshotIdRestore)
	router.PUT(options.BaseURL+"/tenant/:tenantId/config/etcd", wrapper.PutTenantTenantIdConfigEtcd)
	router.PUT(options.BaseURL+"/tenant/:tenantId/config/risingwave", wrapper.PutTenantTenantIdConfigRisingwave)
	router.GET(options.BaseURL+"/tenant/:tenantId/databases", wrapper.GetTenantTenantIdDatabases)
	router.POST(options.BaseURL+"/tenant/:tenantId/execution", wrapper.PostTenantTenantIdExecution)
	router.POST(options.BaseURL+"/tenant/:tenantId/extensions/serverlessbackfill/disable", wrapper.PostTenantTenantIdExtensionsServerlessbackfillDisable)
	router.POST(options.BaseURL+"/tenant/:tenantId/extensions/serverlessbackfill/enable", wrapper.PostTenantTenantIdExtensionsServerlessbackfillEnable)
	router.POST(options.BaseURL+"/tenant/:tenantId/extensions/serverlessbackfill/version", wrapper.PostTenantTenantIdExtensionsServerlessbackfillVersion)
	router.GET(options.BaseURL+"/tenant/:tenantId/info", wrapper.GetTenantTenantIdInfo)
	router.GET(options.BaseURL+"/tenant/:tenantId/metrics", wrapper.GetTenantTenantIdMetrics)
	router.DELETE(options.BaseURL+"/tenant/:tenantId/privatelink/:privateLinkId", wrapper.DeleteTenantTenantIdPrivatelinkPrivateLinkId)
	router.GET(options.BaseURL+"/tenant/:tenantId/privatelink/:privateLinkId", wrapper.GetTenantTenantIdPrivatelinkPrivateLinkId)
	router.POST(options.BaseURL+"/tenant/:tenantId/privatelinks", wrapper.PostTenantTenantIdPrivatelinks)
	router.DELETE(options.BaseURL+"/tenant/:tenantId/relation", wrapper.DeleteTenantTenantIdRelation)
	router.GET(options.BaseURL+"/tenant/:tenantId/relations/getCounts", wrapper.GetTenantTenantIdRelationsGetCounts)
	router.POST(options.BaseURL+"/tenant/:tenantId/resource", wrapper.PostTenantTenantIdResource)
	router.POST(options.BaseURL+"/tenant/:tenantId/sources/fetchKafkaTopic", wrapper.PostTenantTenantIdSourcesFetchKafkaTopic)
	router.POST(options.BaseURL+"/tenant/:tenantId/sources/ping", wrapper.PostTenantTenantIdSourcesPing)
	router.PUT(options.BaseURL+"/tenant/:tenantId/start", wrapper.PutTenantTenantIdStart)
	router.PUT(options.BaseURL+"/tenant/:tenantId/stop", wrapper.PutTenantTenantIdStop)
	router.PUT(options.BaseURL+"/tenant/:tenantId/update-cfg/:configId", wrapper.PutTenantTenantIdUpdateCfgConfigId)
	router.POST(options.BaseURL+"/tenant/:tenantId/updateVersion", wrapper.PostTenantTenantIdUpdateVersion)
	router.GET(options.BaseURL+"/tenants", wrapper.GetTenants)
	router.POST(options.BaseURL+"/tenants", wrapper.PostTenants)
	router.GET(options.BaseURL+"/tenants/status", wrapper.GetTenantsStatus)
	router.POST(options.BaseURL+"/tenants/:tenantId/unquiesce", wrapper.PostTenantsTenantIdUnquiesce)
	router.GET(options.BaseURL+"/tiers", wrapper.GetTiers)
	router.GET(options.BaseURL+"/version", wrapper.GetVersion)
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+x9+3PbNvL4v4Lh52Z6N0NLzqOdOc98f5BlJfU1cVTJbu+mybgwCUk4UwADgHbdjP/3",
	"7+BFghRIQQ87di+/JLKEx2J3sbtY7C6+RAld5pQgInh09CViiOeUcKT+GNxAnMErnGFxNzE/yO8TSgQi",
	"Qn6EeZ7hBApMSf+/nBL5HU8WaAnlp5zRHDGB9XBQD5epMcRdjqKj6IrSDEES3cfRks+dH7hgmMyj+/s4",
	"YuhzgRlKo6PfnDF0h0+x7UCv/osSEd3LHiniCcO5hCo6qi0D2AXKGY9hOkGfC8TFHlYXBH4oyK8PD8Ex",
	"TIEBTwJ7gmawyJ4cpAasEq8cMCQKRjCZAznGfRy9gThD6ZihhJIUy35PEd0aSuCCKYE/o+INLUj69EB+",
	"Dc6oAAo4CegEkjk6o2IKBeYzbPfZk4D1xQ9AgacgdgG8j6MpYjc4QRek3NpPDdXnCwSSgjFEBOAaWoA5",
	"KCqI1dAaFldwZmhoheu5mqQJZpIXHjDjCKfer5fwD7wsls5vmAg0R0z9iJaU3a1fNU6jWE1cdqkGXsVG",
	"XK3lPRJwKijzrAPe8kuWqo9/Y2gWHUX/16/0St9gpr861OCWT1Iup4F/ssuccjFnaJtx/mRj2/k+jpBI",
	"0s0HGcle93E0T/LLJKNFyj9nm4/yNsmHsvP0cyYHs2vaZiizJDMSX0BJ0Mt8vvlIU913rNkhgMaGMCuU",
	"RkQ2S30aPHBgh1L7H31kCN+QBpq9p/hP9BYf+7cPoalujAVahnNgfYNXQELG4N3K3tOTxE2Awrady1j7",
	"x5zDa39R/FV7YFfsHcPkusinBOZ8QcWpQEuPaGcICpReQnFZEPzH5RJnmfphRtkSCo28H15HsQeXWvyX",
	"DYtCyexVdYAEvOQGistGp/bR2e3lDWIcaw26MioXUBTc+5MwSixEwXiXX47+aS1WuSTuGM7VhDDLPsyi",
	"o9+6mUq1vo+7G8lhVaM6uUquDWJfDwOsY1097uq6V76Joz8OJLUYgVl0JFiB7uNomBVcIDYtaYOItAN+",
	"iy4IJlhgmOE/kcT7mNEbLGmr/pogmEr9fo7YEhMoJLnkSSJDQrdGJMVkPkGcFixB6gfJF3F0kae2uTaM",
	"HdArhih3rx3Bsw/cDX7qt2vazKBWk0aiVhmDPlnUQHwTgKpvwwzy8eTKAu2BbKt17gNoL5Rqs50cX3DE",
	"DIDHNL1rFUpX/gO4/rXgiPl/zyHnt5T518aLHLH2vgIRSMRpQ7C1Cyk5EoHLAHFTjux0cmB1IYsrBLSj",
	"8VyNZ88gfjxuuhrd/mzT9TjdfPBqgnuoDElG55j4KVFw1M0GZYN2anZQR/2oUN7ald9xHIi6Bm7KzjVa",
	"uytypm+uJK4Q047NgRLeoXrAUGBF9tvRPCZuelXYH9aPPChVySq4jOaWVTN1KPazagoFvILcT6rOHc1Q",
	"1kXjwO1Zzl8N6N+oPpqMSJpTTMSGy1pQLaFbztUhFphRwD+2jmQajCkTfoOYKveB35DKW3t5JUvg7sCV",
	"zFDiQ6HBTBa7hLCgNdbZWJWXIIy9o/OfC8TuJogXmYcyHfbjDcyKNnPL5+w41T++jKMlJtUfzb3WaXcZ",
	"cMrJfat6i8SJwQ7vFvwWiWtg74SoGqMFltMlnKNzOHf9YA1r1bQIMMZtS99cP8HZNRxSMsNznxYZyj9m",
	"OIHCv8045Nl7lCwgwXxZs0o/DC7OfzweDSajSRRHb6fTwfhUmpvvBqdnURxNh5PB+4Ppj4ODl9//UPv7",
	"+xcvvYamnGrcaX5Anl10aSWOkoJhcTdmVNCEZi6408H03eV0+k6Cov+VXyhoz0f/PvdDhNhNTce1EMC0",
	"86H/HbxCmf/0eI3u2nfQ+klld9vYN/N7SOAcpeZU4aG9/uHSeDsvYZLQggTJ1A4raAnVoAXLvAO1ko6y",
	"edB5WEFL5pctx1TZQMgzjXZZptrHD7Nx3bRr2csV8ioB16XA6yc2OQ69RiTw9CxXbBASVxLMwV/cSiI7",
	"UQMdzuI/rTDAI5+1Dejhx+0Gu64TsOX4256213pBMeECkgQNM8j9XMPxn+jtVcBJrz5U2dG7a4N8qO0z",
	"xxG/LgIk1nURCIff28ocX0DnDllxHmyCt3KWMFA7naddKBM4RMqrVmGQdDlanyTqOvymlnm9/o4WRj9N",
	"u6ezN2VWOau7nNo9SlzeNzXuahq3SLUrE58Of48Ew4lfA2dSOYeLqEqXew6DHru3U9wpsMbq4LNO1hkw",
	"O81bd7xVVwZeIi7gMq+p2BQKdCB/8unZ0gipmtNCh2SYtqRYXnkYr5qr0zhR4Pqk7kY+Woe4HpKEHV6N",
	"Bm7z3caRVZoN3sFLLEKtIjqbcSS2cofoecohvABSLo7vaGJ0p3Lrom4fYcEFXU4dU6l+Gy6PLj+8Bogk",
	"NEUp0M1BaV34+KX1tuF+PcS8E9gOc38fpl4n5B5GqdlYciFjhm+gQO8wue5GOiUEJRLIs7YVCcjmSASo",
	"It0ubo7Zxh01ELvOvgEwBl1cea+LwmC1Ny7dgNorsdOwa7Rbyq5nGb093QJ4p2/sztu6APcSoRXN5jxO",
	"CQqwxN1D/P0n55bOKtBr2cCj/5p8o48JZvruBfA3SCQLNfM5zXHSuSA1/2W1rODFNA+17jibwtfpzZdN",
	"dvHomAHaYCpvFDRsa6i/zGEiKNvYBHRCBWXzQqBdhpgxFXiV7jLGEgm4S39pL6Ywo2SHlXg1TCXxtpJy",
	"VRN5uq/tten54PxienlxNh2PhqdvTkcnURyNR2cnp2dvozgaDIej8bn6cjL612ioPw7ffZiOTrwWKnIc",
	"4NtJW9dfYaEcTkaDcw2R+qigOBm9G5kvR5PJh0kURxdnP519+PXMC1mrOtqvD7t0fjSRHqYzCteU+JqW",
	"xFZOoxYjw6KkZm3YW6AhLUwodzOGRyBm4gQubzC6XbE4X7302qfcbJOgtuXpNaS1sDGq6xuv2DgmAnxl",
	"USUMBnAfVyi3lNcJsJUVrjp65/mcjf5ASaEjnjt4b/s7us8FagmQkGctWghtv6sQ7ejo+8PDw2Zwq2kH",
	"MAFLvuNVvHPF5L+K1+D6cKV1ZKsnul0W64vfgQg/viKRpI45snp7iGAmFpfcE2tzTegtieLoR9XkTopI",
	"sjCffUIy3Dfu3uigP+Aylzsjujnsvei9eOlbRQYF4uVVUXA3wgOtYsrmgS0ZmrfFkllP01rBVzeRTJRa",
	"B408Ok0ygg5YmhSEOJFOtSimOJoKmufVJ/3rVEBmGo7+yBU/x5G2Q51IqIt8zmBqP5df21NJOYBAhCtR",
	"rGw5TMmIwKus9dcTzFd/nqp7owxxfgyT6xnOMn12727jm2i1lTvheyTgezxnJfIQF5Tpzz8XGPHERJJp",
	"0rxltMh5uXivZdAV7FI5VDvZASN2qmKxizyhS0zmFsfneInCt3qhULaRdCi43FGNI9QcEcRgJoUZzlGG",
	"CfKuXAq90+0iW8qYlmbgT7m/HMVvvM3V7nLloCNLVqSEu61c7DSFnpETVgrUZaaLo3ZRvlkgjRH/Hked",
	"/sUYRqdkRtc4KaqG4bbpg0ZuxTWY2hFWZqJ1nQ7XaUNFoUC5LWk6bBexfo2kGcqvBraS9c55j91W4NSN",
	"FDwDemng9ARgDggVIGf0BqcojUHBERALzE2bnk3Wye7kT6k0bcQM5Fkxx8R7QLouOnhlX4LMlSulReZI",
	"lo1kjZfzWo9BDe3aHru6Ie2GVb/K6zCEyQJtPk7Z03DmLzQrlqgz5WDpJiSFz1blMTlcq/XaZgOpPgP/",
	"FXSFmQZi1tNnWKPGvtxDu/iFQlOqvB239iRt7ULa0Xfkdxp1cOwG98meo2PL9auH1fzssJ8t1+r+2OFK",
	"2u++cG6fN9wYzn7bzK6oY7HVyFgVENsmOu6Y37hLWuNO2Yy7JzG25C6yW7+KDc9p9KUyVncdQT11ipn/",
	"2kMCuJ4D12d/bGf9eBXpG5zprdGpBB9WVxr4HJUZoOhWAA9G7MOovv3ejGylCP9HblbWMM+24rR72ErG",
	"hpCmeywrNMMFXvd4dSm4J2n1aWPMP604xgASBMehdnD5/q2yzRdTD/Z7Vkt63JhgfYINlAT+M5cdYqNF",
	"Kn/X0Aa3NzWO+TogYag166UtHUUPHgTZNiavu7BWg9dpxLsSeTaabtCVhuNdL/alIEC3QoDS+2cPUmEg",
	"rs9UCPTg87wxFsCDTyTF0KNMEmRZegrJuONMS5vkoUDGabi7zlSYcFgizBxnSCAiMCVjxDBN/Y1uYIZT",
	"LO7a2/iPzq24amPiuHUbtbKjl32C8LGyrlVstG3/DSUc9ufcGuo59zZvGJJ4OyU3WBc8OEE3KJNi5sD8",
	"VH1xDDlOat+ca0+3Rra6vz7+z4ehKqegfjhGJFksIbv2XgZJaLgv3jog+7fCiS/RwC9G9fVgQBGAzoCC",
	"r5an71vSr2V0Zfelz54iOD21wKq8wakkjSmLmOOf0N2gEIvVmwpTzQyYdCwAcwyu0V0P/IhgihjQwIHv",
	"FLcBHUv99799uUZ390d/+6Ly1cX9P76LTDkxpAOuFWMa2BZCKB/WMYIMMQvFlfrrjV36v349rw2hfm2O",
	"ca/ymM1FGRY6dOBF5IQ9Ry96hwcwyxdQZzQjAnMcHUWveoe9V4p6YqFQ0pf/mHgwSRkViiTJEb1Vsci1",
	"spIvDw/bNkDZrt8sd6ig9eRq9aFT3dGBok6W4QIl10AsEHCbAzpT32nWBIYlV+B360eqRTO4RDqB7Td5",
	"hoqOTGCLdaM2728tt0mQY6dmXZMzP22DKG+Rzvs4ev3y5badHbZXS3RZ7bdP0nh3t8Bvn5SBDee82uYq",
	"JM1Hras7mhyYa8z+F4mue02tDGm3TB37ulSNE8NX4nRnfoqj14evt+HDLZDDi+USsrtyQQACqUqAQURU",
	"oc/NXfTgr22LhWEouJjiJsmYq/USP/z0tbD7FokQ1N77N7GUZtUeJlvs3rzwkKcehOoIBUdD74Uy7dGu",
	"93WtZ5jpK24i2e+fj84e2kQK3Xwwy+gtSg8omx8Yqy0apDeQ6Iiold3ZIt76S0gKaKO3lP31IKxnKpU0",
	"eK+eTCWZ770LzgMx4rqks3vDkDX+e7klHz0f4a8DrpTJ4XIgMPZWDPDMfrZRLjxHCZ5hFeaie2MBBFVj",
	"mCgS22U7Rg7X0/3iqbDwN+b9xryhzMu7TiZunmmYZW9SbV1ODon19A9m83c3GuvTo9l2VVmQFiNvN4Z6",
	"h7mosRJvNdZCBAN/HHnAH82uenz7SBdA3It91LIzbT5b564clY02P2y3K5i4q7fONXsq27Ase7fb4Wrl",
	"6YJ9nK4IsDRU3hPtObm6AxUVAGXADYWuOQYM79g9c5AjtsTceJtK7uhl2IQY9DM67yuSjRjTsR9eB8+v",
	"DOY5YhKmd/Qagx/Pz8dgMD4FB2AhRM6P+v05gzNIYC+hy35KE97P6DXu64DmPkMzxBBJUB/muP9/asYD",
	"2eCA3iB2AA8YJHN0QGc2caDOtT8bCN/R+SZMayq7ttlI22uWsghA++B1/BUcgQwTdCkZGGLCpZ6e4Uxq",
	"+IzOeQxQb94Dv380+XWXOP0YHb34PZZf6dk+RkcfowWeLy7/SzG5hMs809XjMCUfI90SSSKqhjP93Iag",
	"IIeMIyB3CMjhXUZh+jH6XTLR7x/VclRzpBLpZN/fnVjpeq5fXOYFlss3ZugScS71mC+QuguBa+jTyKQz",
	"XVYwl9AiS8EVAhrc0zQGEl75v+4DDHwxUDc0fbtY5ZGMgUKabRMFL4ELyOrWRR3eyZvhq1ev/tk3/59B",
	"Qo17WqAUlEkyQM8D/q7GA0sECQcfi8PDV+j//SO2dpziF0oQWNCCATinUexPuCFFZt4A0tgMXAwi6R6X",
	"gkjqLiRprIPQ2z2Dn2KmU5Ubi7BpAFcwub7Vd0yWt2eUmW/KH8PZd9WyLOd6cXgYeyTLEhP9tsiLx1Z2",
	"KyVFPVrP0yjw9OR54+k+jr7XXeuTnBpzBeiajXrf7ao0FcxmC0txAKQ+ARAofaJUqElEM2oyo/MuFZnR",
	"eU+KNqUbl1WlpDZbyhZTClJKeryNju3xV1FvJaW3sfiUHDvHDXMxJDuwQzjtdbzc3p8/nYOhYaN9G6Rx",
	"9PrFD+s7+t622oMxa/ZPZb6C0xNnK9rt1bEdTRO9Jf1HHkap0DX/vYarhEMsEJDNwHAgLQdk7yUnmGMy",
	"/xXeoPKIrJLZFpiDMit0ZctP9IR+blidvDYxL5IEcT4rsuzuMeSkRbVB0icl1rSZ1Fep2UdfytN/HYix",
	"VOSqJJAxq6TiTsprXg3ad7x237uCrapi0FhnPD+UA2G17tO+XAet17/bKsddyGgzQLV/oEixOCA0hQKW",
	"C/TtEFHWfui+BjZJwnvzvb56Nr7X1N4aN8/bp2l12jYekOZFfLvwMifynsG6bKi/OiixX+I+6rh8bqPL",
	"/tSPTQ9/Uu6QubpsfhCKGHWymR9hL36DMF/ap2rb9p0HLUK274lp/hS8JE4c2gMH6Wx799PxdOrXYnsj",
	"i2ytHyCRKNmf1JndskUHt9sh1EspVgqtlTNfhYEe0ra2T8X4Htd1kczB11FDW1NUO3G7rm+aFH0I86vt",
	"caxwA2yPVPYRWZduAemVobKxwZ+zjCgvkupS4haLBTCPz4DpxXg0uZiOJv2zD+XnGJiChMf9sw/2o/3S",
	"NK7+AOPBdPrrh8lJm9xZNUFDeVcTRXFvS0zXY/BuW0z3g947Pk+WGyiHu9lE33HgVJ3bM2uYMJh7xwbS",
	"M3xZo7nO4Zw/pJXsezrII2/mxuOgb74AU06GW+1koEUKVOEfIOB8T4HADp6+WH18379ST2gGIM300E9u",
	"fgvSaD4+2hmkoW5sswyUKT1Aox3YUtW8txuRDRU7TALdwjEG1oaO7dFmW2d5rDCXz7OwPyeQr3p5y3vz",
	"hk45owlKC6YenFeOa5T2nPCQ1Y7mPKrzLQFf2LtHposigis0owzpseTfwjPZozGFvqLsdAutyoz+l6rQ",
	"+n3osbNO6WlVqf0xOTL2js5dYELG96c1ffJz7yqH2PlAap77beOyx+GB6ty3NRNIs6IsMLHRlq8YYWKG",
	"+N/ih2kpBigwSFQ2O5gxutQXDRacR2MIA4clTThn6PJ/fVsFpNtwt3ygSw6OzCNDj6mc2g8MAv0h+nkG",
	"cUPBNEm8ojnk0gEiN7Zaohmzp4N3RufDk8v3g39fTkY/X4ym55fH/zkfTY/Ax+jF4cvXH6OPH4lq8vPF",
	"h/PB5fFg+NPo7KTZKFp/8PDwmXuvBDgiKYD8jiQLRgktODCokFwoVewdIOgWqMXohRSagr3dbhq/QkTl",
	"oHM127iGPaeP1S1Q2fSbboRJ1fP5b4cKDT1Bl1lzVzweK7tnrL8KQ7evaf9sXXt2N+y8WD7o+/h8/HDH",
	"e/8rxR7Ov+A6U2P644dfwcngfHA8mI6mIKHLJSQqQpEv6K06G5a43VHBv/8Fo9sg73B1GAzT68g+otAe",
	"DaDfWUAAAv45035GWx8YsFuQMJQiIjBUT+WtMw3LRxuejgjcnmvaHqH45kLcY+xCnVtN3X3e52Xl/StT",
	"eb+fqtL7gUl0O+QmhB6DylcC+HQF2BMD6wO6RVoqfHgk2iBJUC50Zslz9VEbhOp4r0p9VhwDZpSBimuA",
	"pYQOTgossbApSyLybDhyRL4x5D4ZUuNzX/zoJoWZQCKbF2YqMMWmgFIZaITTA8lgB4rzDjKaQFXgteKz",
	"PfG485Dnk2fyX8qM2M2PRNVDDS96L1WJHs8hKejQ8203bVteYpvdJC1yw6M9YBjA1orK8DUCL3qHvcPe",
	"U9t0tnpU2JFMPYDyVzmNdb9L4+HwqTxwSd6wifMSeTrSG3NzbbPjEQybF2YCwwvDaByQZFInc3fKydei",
	"9NZOpHGRZc09bRMH9kq9gHQDh4BlRkYoHXP96mmGyXX/S149gbrFJdq4GmrsDhQ9ZlmMfcWue6KsDXaA",
	"XCC4ugM43cin5SA3PKxxc9zuT5a5D+I+xRjrfdKjI7T6MS/58hXabn3Pt3a7843N3seKuxi7QD5c9k3L",
	"K/yPbAx3Pbbv2XUmhtKhI2BfX0ZKE7ZMehBUb0lT0sfkbXH5lUpTQzYY75fxcPsda0Mx/XzOzLvHdR1W",
	"R2XKaA5sQ/D3JRQ3GN3yGKh6ATw2kHOdl89j0Ov1/iGXYMCvhcjZoijr1aR9kvmv4EY+YTS3Tx3oVX3z",
	"Ind5kXVJCvm/ey+yg1vZsi/vz5GoSvaHGRWWZvxt2fevcg5rvHveEgRZbv7ENtslWdVis0uElY02O225",
	"z62FatFJ9aLas5cz1ersqvh2KvvxDh2BpZYfphxfqYst47iZiDvcxDs85c0KnSYwa9XJhnD9GRLJ4ic4",
	"u4bnNMdJ+/XtG9nQZHML2TTkmlbnVPM3jUn+IpvAv7qvmMDUBVS3JbttqcDAm9l2ljYde00+DJbGlpEf",
	"qRSBl79NZYK/EFN/K4MQyn4qPD44gm9qCn59TcPOEzBn/d26ftgCcnCFEAGYYIFheeXzjGLgFJ4BLB35",
	"YgGddXFB8xztontbde5m2RISkE2Yh+ZPgXceph7H02EdmgNYJuNAT5HV/XELzcOZRY91kMzm/S86nNPc",
	"C4Rxj776HM7mQ9P3CeRRJBUoD5xFMUFlOhWdAfPGLCA0Rdwj755z0O9qRap9Bv9uxqy/ODEdgSfli1rH",
	"Z2JU1d+6ciJZVh+2aj5ftcdC9c/nlQ+nUL09G9+WlekFNXnPe4pSD3DCfassXz0T+qAF5W22c0V5Dugt",
	"QanyiZjbiM0qUlvCh5YyeahbLOsSM4eUr3aHpS+lSgddyAVW5Z96rq622kK4zi7oKPAVxFDmUqvFdhsz",
	"eoNVD1fU9KsXeTtLSZJieaVLoad4psqal/ngouTSNmE1tQ8UP7QocN8e9nCPetWzKjFv0N5c0D5KVGwk",
	"ANoNFF6zUMjnAiMe5sfnpXlS9npyh/hGonCxdBVsb9cdNtEjQmDWn5YDNxOI+VYZxIpKeM1jMPrB1odk",
	"fNxS5kttXNys8LVR7VW1RMc2bFukG1z8ME9yKk6QsGnGLVgWHUXqVYWbF5Gz+5or+2BB5QBe0UI4sqrG",
	"8Fy9Hr9dX09X50RzcgxsMSdbtt1UDAqYcVlGPdaKaQf1zejc6ZjReXT/6f7/BwAA//+VnFPNmscAAA==",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
